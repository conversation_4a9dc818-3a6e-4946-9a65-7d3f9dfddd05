import { ASSOCIATED_COLUMN } from "src/shared/enums";
import { ASSOCIATED_TYPE } from "src/shared/enums/associated-type.enum";
export declare class DeductionDto {
    id: number;
    amount: number;
    updatedOn: Date;
    afeProposalId: number;
    data: JSON;
}
export declare class GetAggregateLimitbalanceDTO {
    entityId: string;
    entityTitle: string;
    entityCode: string;
    entityType: string;
    entityShortName: string;
    title: string;
    associateLevel: string;
    associateRole: string;
    associateType: ASSOCIATED_TYPE;
    associatedColumn: ASSOCIATED_COLUMN;
    associatedUser: string;
    aggregateLimit: number;
    limitDeduction: number;
    deductions: DeductionDto[];
}
