{"version": 3, "file": "workflow-shared-bucket.service.js", "sourceRoot": "", "sources": ["../../../src/workflow/services/workflow-shared-bucket.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAE5C,kDAAsD;AAEtD,8CAAwF;AACxF,kFAAwE;AACxE,wDAAsD;AACtD,kDAAmF;AAGnF,gHAAiJ;AACjJ,kDAAoG;AAGpG,IAAa,2BAA2B,GAAxC,MAAa,2BAA2B;IACvC,YACkB,4BAA0D,EAC1D,mCAAwE,EACxE,gBAAkC;QAFlC,iCAA4B,GAA5B,4BAA4B,CAA8B;QAC1D,wCAAmC,GAAnC,mCAAmC,CAAqC;QACxE,qBAAgB,GAAhB,gBAAgB,CAAkB;IAChD,CAAC;IAMQ,wBAAwB,CAAC,MAAc;;YAEnD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAE7F,IAAI,UAAU,EAAE;gBAGf,IAAI,UAAU,CAAC,cAAc,IAAI,CAAC,EAAG;oBACpC,MAAM,IAAI,0BAAa,CAAC,4CAA4C,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;iBAC5F;gBAGD,IAAI,UAAU,CAAC,aAAa,KAAK,sCAAe,CAAC,IAAI,EAAG;oBACvD,MAAM,IAAI,0BAAa,CAAC,uCAAuC,EAAE,kBAAU,CAAC,cAAc,CAAC,CAAC;iBAC5F;gBAGD,IAAI,CAAC,UAAU,CAAC,qBAAqB;oBACpC,CAAC,UAAU,CAAC,qBAAqB;wBAChC,CAAC,UAAU,CAAC,qBAAqB,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,CACtF,EACA;oBACD,MAAM,IAAI,0BAAa,CAAC,gDAAgD,EAAE,kBAAU,CAAC,cAAc,CAAC,CAAC;iBACrG;gBAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAAC,2BAA2B,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;gBAE/H,OAAO,IAAA,gCAAsB,EAAC,8EAAuC,EAAE;oBACtE,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE,IAAA,+BAAqB,EAAC,sEAA+B,EAAE,UAAU,CAAC;iBAC9E,CAAC,CAAC;aAEH;iBAAM;gBACN,MAAM,IAAI,0BAAa,CAAC,+BAA+B,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aAC/E;QAEF,CAAC;KAAA;IAMY,kBAAkB,CAC9B,MAAc,EACd,8BAA8D,EAC9D,cAA8B;;YAE9B,IAAG,8BAA8B,CAAC,QAAQ,KAAK,8BAA8B,CAAC,cAAc,EAAE;gBAC7F,MAAM,IAAI,0BAAa,CAAC,wCAAwC,EAAE,kBAAU,CAAC,cAAc,CAAC,CAAC;aAC7F;YAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,4BAA4B,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAE7F,IAAG,UAAU,EAAE;gBAGd,IAAI,UAAU,CAAC,cAAc,IAAK,CAAC,EAAG;oBACrC,MAAM,IAAI,0BAAa,CAAC,4CAA4C,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;iBAC5F;gBAGD,IAAI,CAAC,UAAU,CAAC,qBAAqB;oBACpC,CAAC,UAAU,CAAC,qBAAqB;wBAChC,CAAC,UAAU,CAAC,qBAAqB,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,CACtF,EACA;oBACD,MAAM,IAAI,0BAAa,CAAC,gDAAgD,EAAE,kBAAU,CAAC,cAAc,CAAC,CAAC;iBACrG;gBAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAAC,4BAA4B,CAAC;oBAC9F,oBAAoB,EAAE,UAAU,CAAC,oBAAoB;iBACrD,CAAC,CAAC;gBAEH,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;oBAGpC,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,YAAiB,EAAE,EAAE;wBAC5D,OAAO,CAAC,8BAA8B,CAAC,QAAQ,KAAK,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAC5E,CAAC,CAAC,CAAC;oBAEH,IAAI,cAAc,EAAE;wBACnB,MAAM,IAAI,0BAAa,CAAC,6BAA6B,EAAE,kBAAU,CAAC,cAAc,CAAC,CAAC;qBAClF;oBAID,MAAM,uBAAuB,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,YAAiB,EAAE,EAAE;wBACrE,OAAO,CAAC,8BAA8B,CAAC,cAAc,KAAK,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAClF,CAAC,CAAC,CAAC;oBAEH,IAAI,uBAAuB,EAAE;wBAE5B,MAAM,IAAI,0BAAa,CAAC,8BAA8B,CAAC,gBAAgB,GAAG,gFAAgF,EAAE,kBAAU,CAAC,cAAc,CAAC,CAAC;qBACvL;oBAID,MAAM,mBAAmB,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,YAAiB,EAAE,EAAE;wBACjE,OAAO,CAAC,8BAA8B,CAAC,QAAQ,KAAK,YAAY,CAAC,cAAc,CAAC,CAAC;oBAClF,CAAC,CAAC,CAAC;oBAEH,IAAI,mBAAmB,EAAE;wBACxB,MAAM,IAAI,0BAAa,CAAC,8BAA8B,CAAC,UAAU,GAAG,gFAAgF,EAAE,kBAAU,CAAC,cAAc,CAAC,CAAC;qBACjL;iBAED;gBAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAAC,WAAW,iCAC3E,8BAA8B,KACjC,oBAAoB,EAAE,UAAU,CAAC,oBAAoB,EACrD,uBAAuB,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,CAAC,KACpJ,cAAc,CAAC,CAAC;gBAEnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;oBAC7C,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,QAAQ;oBACxC,SAAS,EAAE,UAAU,CAAC,oBAAoB;oBAC1C,WAAW,EAAE,2BAAmB,CAAC,aAAa;oBAC9C,gBAAgB,EAAE,2BAAmB,CAAC,6BAA6B;oBACnE,QAAQ,EAAE,8BAA8B,CAAC,UAAU,GAAG,iCAAiC,GAAG,8BAA8B,CAAC,gBAAgB;oBACzI,eAAe,EAAE;wBAChB,YAAY,kCACR,8BAA8B,KACjC,oBAAoB,EAAE,UAAU,CAAC,oBAAoB,EACrD,uBAAuB,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,CAAC,GACtJ;qBACD;iBACD,CAAC,CAAC;gBAEH,OAAO,YAAY,CAAC;aACpB;iBAAM;gBACN,MAAM,IAAI,0BAAa,CAAC,+BAA+B,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aAC/E;QACF,CAAC;KAAA;IAMY,iBAAiB,CAAC,EAAU,EAAE,cAA8B;;YAExE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC;YAElG,IAAI,YAAY,EAAE;gBACjB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mCAAmC,CAAC,iBAAiB,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;gBAE5G,IAAG,cAAc,EAAE;oBAClB,MAAM,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;wBAC7C,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,QAAQ;wBACxC,SAAS,EAAE,YAAY,CAAC,oBAAoB;wBAC5C,WAAW,EAAE,2BAAmB,CAAC,aAAa;wBAC9C,gBAAgB,EAAE,2BAAmB,CAAC,6BAA6B;wBACnE,QAAQ,EAAE,iBAAiB,GAAG,YAAY,CAAC,UAAU,GAAI,6BAA6B,GAAG,YAAY,CAAC,gBAAgB,GAAG,mBAAmB;qBAC5I,CAAC,CAAC;oBAEH,OAAO,EAAC,OAAO,EAAE,sCAAsC,EAAC,CAAA;iBACxD;gBAED,MAAM,IAAI,0BAAa,CAAC,oCAAoC,EAAE,kBAAU,CAAC,WAAW,CAAC,CAAC;aACtF;iBAAM;gBACN,MAAM,IAAI,0BAAa,CAAC,8BAA8B,EAAE,kBAAU,CAAC,WAAW,CAAC,CAAC;aAChF;QACF,CAAC;KAAA;CACD,CAAA;AA9KY,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAGoC,2CAA4B;QACrB,kDAAmC;QACtD,0BAAgB;GAJxC,2BAA2B,CA8KvC;AA9KY,kEAA2B"}