"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../business-entity/repositories");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const dtos_1 = require("../dtos");
const repositories_2 = require("../repositories");
const class_transformer_1 = require("class-transformer");
let FinanceService = class FinanceService {
    constructor(naturalAccountNumberRepository, costCenterRepository, currencyTypeRepository, entitySetupRepository, companyCodeRepository, analysisCodeRepository, adminApiClient) {
        this.naturalAccountNumberRepository = naturalAccountNumberRepository;
        this.costCenterRepository = costCenterRepository;
        this.currencyTypeRepository = currencyTypeRepository;
        this.entitySetupRepository = entitySetupRepository;
        this.companyCodeRepository = companyCodeRepository;
        this.analysisCodeRepository = analysisCodeRepository;
        this.adminApiClient = adminApiClient;
    }
    checkCompanyCodeExistsForBusinessEntity(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.companyCodeRepository.isCompanyCodeExistEntityId(entityId);
        });
    }
    getCompanyDetailForBusinessEntity(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const companyDetail = this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CompanyCodeResponseDto, companyDetail);
        });
    }
    getCompanyCodeListExcluding(excludedCode) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.companyCodeRepository.getAllCompanyCodeExcluding(excludedCode);
        });
    }
    getNaturalAccountNumbersByRequestType(requestTypeId, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const company = yield this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
            if (!company) {
                throw new exceptions_1.HttpException(`Company doesn't exist for the business entity.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const accountNumbers = yield this.naturalAccountNumberRepository.getNaturalAccountNumbersByRequestType(requestTypeId, company.id);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.NaturalAccountResponseDto, accountNumbers);
        });
    }
    getAnalysisCodesByRequestType(requestTypeId, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const company = yield this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
            if (!company) {
                throw new exceptions_1.HttpException(`Company doesn't exist for the business entity.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const accountNumbers = yield this.analysisCodeRepository.getAnalysisCodesByRequestType(requestTypeId, company.id);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AnalysisCodeResponseDto, accountNumbers);
        });
    }
    getCostCentersWithCompanyCodeByEntity(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const costCenters = yield this.costCenterRepository.getCostCentersWithCompanyCodeByEntity(entityId);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.CostCenterResponseDto, costCenters);
        });
    }
    getCurrencyConversionRateToPrimary(currencyType) {
        return __awaiter(this, void 0, void 0, function* () {
            const currency = yield this.currencyTypeRepository.getCurrencyConversionRateToPrimary(currencyType);
            const primaryCurrency = yield this.currencyTypeRepository.getPrimaryCurrency();
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CurrencyConversionResponseDto, Object.assign(Object.assign({}, currency), { primaryCurrency: primaryCurrency.currency }));
        });
    }
    getCurrencyTypeForEntity(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const primaryCurrency = yield this.currencyTypeRepository.getPrimaryCurrency();
            const entityParentIds = yield this.adminApiClient.getParentIdsOfEntity(entityId);
            const entitySetup = yield this.entitySetupRepository.getEntitySetupWithCurrency(entityParentIds);
            const entityCurrency = !entitySetup ? primaryCurrency : entitySetup.currencyType;
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CurrencyConversionResponseDto, Object.assign(Object.assign({}, entityCurrency), { primaryCurrency: primaryCurrency.currency }));
        });
    }
    getCostCenterByEntityIds(filter) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityIds } = filter;
            const costCenters = yield this.costCenterRepository.getCostCentersWithCompanyCodeByEntityIds(entityIds);
            const mappedCostCenters = costCenters.map(costCenter => (0, class_transformer_1.instanceToPlain)(new dtos_1.CostCenterMappedResponseDto(costCenter)));
            return (0, helpers_1.multiObjectToInstance)(dtos_1.CostCenterFilterResponseDto, mappedCostCenters);
        });
    }
    getAllCostCenters() {
        return __awaiter(this, void 0, void 0, function* () {
            const costCenters = yield this.costCenterRepository.getAllCostCenters();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AllCostCenterResponseDto, costCenters);
        });
    }
};
FinanceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_2.NaturalAccountNumberRepository,
        repositories_2.CostCenterRepository,
        repositories_2.CurrencyTypeRepository,
        repositories_1.EntitySetupRepository,
        repositories_2.CompanyCodeRepository,
        repositories_2.AnalysisCodeRepository,
        clients_1.AdminApiClient])
], FinanceService);
exports.FinanceService = FinanceService;
//# sourceMappingURL=finance.service.js.map