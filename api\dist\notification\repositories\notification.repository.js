"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationRepository = void 0;
const common_1 = require("@nestjs/common");
const sequelize_1 = require("sequelize");
const repositories_1 = require("../../shared/repositories");
const models_1 = require("../models");
const helpers_1 = require("../../shared/helpers");
let NotificationRepository = class NotificationRepository extends repositories_1.BaseRepository {
    constructor(sequlizeOperator) {
        super(models_1.Notification);
        this.sequlizeOperator = sequlizeOperator;
    }
    createNotification(payload, currentContext) {
        const entity = new models_1.Notification(payload);
        return this.save(entity, currentContext);
    }
    bulkNotificationsInsert(records, currentContext) {
        return this.insertMany(records, currentContext);
    }
    addNotificationsViewerToViewedByList(notificationIds, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            yield this.update({
                viewedBy: (0, sequelize_1.literal)(`(CASE WHEN viewed_by IS NULL THEN '[]'::JSONB ELSE viewed_by END) || '["${user.username.toLowerCase()}"]'::jsonb`)
            }, currentContext, {
                where: { id: { [sequelize_1.Op.in]: notificationIds } }
            });
        });
    }
    getPaginatedNotificationListForUser(userId, limit, page) {
        return this.findAndCountAll(Object.assign(Object.assign(Object.assign({ where: {
                subscribers: { [sequelize_1.Op.contains]: [userId] },
                expireAt: { [sequelize_1.Op.gt]: new Date() },
                [sequelize_1.Op.or]: [{ viewedBy: null }, { [sequelize_1.Op.not]: { viewedBy: { [sequelize_1.Op.contains]: [userId] } } }]
            } }, (page && { offset: (page - 1) * limit })), (limit && { limit })), { order: [['createdOn', 'desc']] }));
    }
    getAllMatchingSubscriberOrViewer(userEmail) {
        return this.findAll({
            attributes: ['id', 'subscribers', 'viewedBy'],
            where: this.sequlizeOperator.orOperator({
                subscribers: { [sequelize_1.Op.contains]: [userEmail] },
                viewedBy: { [sequelize_1.Op.contains]: [userEmail] },
            })
        });
    }
    updateNotificationByConditionWoUser(condition, data, params = {
        includeDeleted: false,
        includeInactive: false,
        throwException: true,
    }) {
        return this.updateWithoutUser(data, {
            where: condition,
        }, params);
    }
};
NotificationRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [helpers_1.SequlizeOperator])
], NotificationRepository);
exports.NotificationRepository = NotificationRepository;
//# sourceMappingURL=notification.repository.js.map