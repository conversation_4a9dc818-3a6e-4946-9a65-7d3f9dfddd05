export declare type RoleBasedRecipients = {
    role: string;
    level: string;
};
export declare type AfeRecipients = 'AFE_PROJECT_LEADER' | 'AFE_READER' | 'AFE_SUBMITTER';
export declare type SchedulerRecipients = {
    fixed?: {
        to: string[];
        cc: string[];
        bcc: string[];
    };
    roleBased?: {
        to: RoleBasedRecipients[];
        cc: RoleBasedRecipients[];
        bcc: RoleBasedRecipients[];
    };
    afeBased?: {
        to: AfeRecipients[];
        cc: AfeRecipients[];
        bcc: AfeRecipients[];
    };
};
