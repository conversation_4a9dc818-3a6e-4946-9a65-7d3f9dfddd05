import { Pagination } from 'src/core/pagination';
import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { AfeDraftRequestDto, AfeDraftResponseDto, GetAfeDraftResponseDto, UpdateDraftRequestDto } from '../dtos';
import { AfeDraftService } from '../services';
export declare class AfeDraftController {
    private readonly afeDraftService;
    constructor(afeDraftService: AfeDraftService);
    getAllDraftsByActiveUser(request: RequestContext, limit?: number, page?: number): Promise<Pagination<GetAfeDraftResponseDto>>;
    getDraftsByIdAndActiveUser(id: number, request: RequestContext): Promise<GetAfeDraftResponseDto>;
    saveDraft(request: RequestContext, draftAfeDto: AfeDraftRequestDto): Promise<AfeDraftResponseDto>;
    updateOwnDraft(updateAfeProposalDto: UpdateDraftRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    deleteDraftById(id: number, request: RequestContext): Promise<MessageResponseDto>;
}
