"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowSharedChildLimitController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const enums_1 = require("../../shared/enums");
const new_shared_child_limit_request_dto_1 = require("../dtos/request/new-shared-child-limit-request.dto");
const update_shared_child_limit_request_dto_1 = require("../dtos/request/update-shared-child-limit-request.dto");
const get_child_shared_limit_response_dto_1 = require("../dtos/response/get-child-shared-limit-response.dto");
const services_1 = require("../services");
let WorkflowSharedChildLimitController = class WorkflowSharedChildLimitController {
    constructor(workflowSharedChildLimitService) {
        this.workflowSharedChildLimitService = workflowSharedChildLimitService;
    }
    addNewSharedLimit(request, newSharedChildLimitRequestDTO) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowSharedChildLimitService.addNewSharedLimit(newSharedChildLimitRequestDTO, request.currentContext);
        });
    }
    getChilSharedLimitList(parentStepId, entityId) {
        return this.workflowSharedChildLimitService.getChildSharedLimitList(parentStepId, entityId);
    }
    deleteChilSharedLimit(id, entityId, request) {
        return this.workflowSharedChildLimitService.deleteChildSharedLimit(id, entityId, request.currentContext);
    }
    updateChilSharedLimit(id, updateSharedChildLimitRequestDTO, request) {
        return this.workflowSharedChildLimitService.updateChildSharedLimit(id, updateSharedChildLimitRequestDTO, request.currentContext);
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT, { checkEntity: true }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add New Shared Limit.',
        type: get_child_shared_limit_response_dto_1.GetChildSharedLimitResponseDTO,
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, new_shared_child_limit_request_dto_1.NewSharedChildLimitRequestDTO]),
    __metadata("design:returntype", Promise)
], WorkflowSharedChildLimitController.prototype, "addNewSharedLimit", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT, { checkEntity: true }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get child shared limit list by step id.',
        type: [get_child_shared_limit_response_dto_1.GetChildSharedLimitResponseDTO],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Parent Entity Id to get the shared limit of child.',
        required: true,
    }),
    (0, common_1.Get)('/:parentStepId'),
    __param(0, (0, common_1.Param)('parentStepId')),
    __param(1, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], WorkflowSharedChildLimitController.prototype, "getChilSharedLimitList", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT, { checkEntity: true }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete child shared limit list by id.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Entity id of child to whom limit is shared.',
        required: true,
    }),
    (0, common_1.Delete)('/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('entityId')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Object]),
    __metadata("design:returntype", Promise)
], WorkflowSharedChildLimitController.prototype, "deleteChilSharedLimit", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT, { checkEntity: true }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update child shared single & aggregate limit by id.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Entity id of child to whom limit is shared.',
        required: true,
    }),
    (0, common_1.Put)('/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_shared_child_limit_request_dto_1.UpdateSharedChildLimitRequestDTO, Object]),
    __metadata("design:returntype", Promise)
], WorkflowSharedChildLimitController.prototype, "updateChilSharedLimit", null);
WorkflowSharedChildLimitController = __decorate([
    (0, swagger_1.ApiTags)('Workflow Shared Child Limit'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('workflow-shared-child-limit'),
    __metadata("design:paramtypes", [services_1.WorkflowSharedChildLimitService])
], WorkflowSharedChildLimitController);
exports.WorkflowSharedChildLimitController = WorkflowSharedChildLimitController;
//# sourceMappingURL=workflow-shared-child-limit.controller.js.map