"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../afe-proposal/repositories");
const config_service_1 = require("../../config/config.service");
const repositories_2 = require("../../queue/repositories");
const clients_1 = require("../../shared/clients");
const constants_1 = require("../../shared/constants");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const scheduler_type_enum_1 = require("../../shared/enums/scheduler-type.enum");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const repositories_3 = require("../repositories");
const services_2 = require("../../oracle-fusion/services");
const services_3 = require("../../core/services");
const services_4 = require("../../business-entity/services");
const sftp_service_1 = require("../../sftp-service/sftp.service");
let SchedulerService = class SchedulerService {
    constructor(schedulerRepository, queueLogRepository, adminApiClient, sharedNotificationService, afeProposalRepository, afeProposalApproverRepository, configService, mSGraphApiClient, oracleFusionService, loggerService, businessEntityService, sftpService, taskApiClient) {
        this.schedulerRepository = schedulerRepository;
        this.queueLogRepository = queueLogRepository;
        this.adminApiClient = adminApiClient;
        this.sharedNotificationService = sharedNotificationService;
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.configService = configService;
        this.mSGraphApiClient = mSGraphApiClient;
        this.oracleFusionService = oracleFusionService;
        this.loggerService = loggerService;
        this.businessEntityService = businessEntityService;
        this.sftpService = sftpService;
        this.taskApiClient = taskApiClient;
        this.RETRY_THRESHOLD = 5;
    }
    runScheduler(type) {
        return __awaiter(this, void 0, void 0, function* () {
            this.loggerService.log(`Started the ${type} scheduler.`);
            switch (type) {
                case scheduler_type_enum_1.SCHEDULER_TYPE.DAILY:
                case scheduler_type_enum_1.SCHEDULER_TYPE.WEEKLY:
                    yield this.runNonLiveScheduler(type);
                    break;
                case scheduler_type_enum_1.SCHEDULER_TYPE.LIVE:
                    yield this.runLiveScheduler();
                    break;
                case scheduler_type_enum_1.SCHEDULER_TYPE.REMINDER:
                    yield this.runReminderScheduler();
                    break;
                case scheduler_type_enum_1.SCHEDULER_TYPE.SUBMITTER_REMINDER:
                    yield this.runSubmitterScheduler();
                    break;
                case scheduler_type_enum_1.SCHEDULER_TYPE.FUSION:
                    yield this.oracleFusionService.sendFusionEnabledApprovedAfe();
                    break;
                case scheduler_type_enum_1.SCHEDULER_TYPE.DATA_SHARING_SERVICE:
                    yield this.sftpService.run();
                    break;
                default:
                    throw Error('Invalid scheduler type. Type should be (LIVE, DAILY, WEEKLY, REMINDER, SUBMITTER_REMINDER, FUSION, DATA_SHARING_SERVICE).');
            }
            this.loggerService.log(`Ended the ${type} scheduler.`);
        });
    }
    runNonLiveScheduler(type) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const schedulers = yield this.schedulerRepository.getSchedulerByType(type);
            for (const scheduler of schedulers) {
                const currentTimestamp = new Date();
                const { entities, rule, actions, finalApproval, lastRunAt, forLevel } = scheduler;
                const allChildEntitiesIds = yield this.businessEntityService.getBusinessEntitiesChildIds(entities);
                const logs = yield this.queueLogRepository.getQueueLogsByLogConditionAfterLastRunAt(allChildEntitiesIds, actions, finalApproval, rule, lastRunAt);
                if (logs === null || logs === void 0 ? void 0 : logs.length) {
                    if (forLevel) {
                        const groupedLogsBySameLevel = new Map();
                        for (const log of logs) {
                            const { data } = log;
                            const entityId = (_a = data.businessUnitHierarchy.find(b => b.level === forLevel)) === null || _a === void 0 ? void 0 : _a.id;
                            if (entityId) {
                                const key = `${forLevel}-${entityId}`;
                                if (groupedLogsBySameLevel.has(key)) {
                                    const existingValue = groupedLogsBySameLevel.get(key);
                                    groupedLogsBySameLevel.set(key, [...existingValue, log]);
                                }
                                else {
                                    groupedLogsBySameLevel.set(key, [log]);
                                }
                            }
                        }
                        try {
                            for (let [_, value] of groupedLogsBySameLevel) {
                                yield this.sendNotificationOfAfeList(value, scheduler, value[value.length - 1].data.businessUnitHierarchy);
                            }
                        }
                        catch (error) {
                            this.loggerService.error(error === null || error === void 0 ? void 0 : error.message, error);
                        }
                    }
                    else {
                        try {
                            yield this.sendNotificationOfAfeList(logs, scheduler);
                        }
                        catch (error) {
                            this.loggerService.error(error === null || error === void 0 ? void 0 : error.message, error);
                        }
                    }
                }
                yield this.schedulerRepository.updateLastRunAt(scheduler.id, currentTimestamp, constants_1.SYSTEM_USER);
            }
        });
    }
    sendNotificationOfAfeList(logs, scheduler, businessLevelHierarchy) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id: schedulerId, recipients, templateName, placeholderFields } = scheduler;
            const afeProposalIds = logs.map(log => log.data.proposalId);
            let tableData = yield this.getAFEListData(afeProposalIds);
            if (placeholderFields === null || placeholderFields === void 0 ? void 0 : placeholderFields.length) {
                tableData = this.filterTableData(tableData, placeholderFields);
            }
            const placeholderValues = {
                afe_listing: (0, helpers_1.jsonToHtmlTable)({
                    data: tableData,
                    border: 1,
                    cellspacing: 0,
                    cellpadding: 0,
                    css: `table {
					border-collapse: collapse;
					width: 100%;
				  }
			  
				  th, td {
					border: 1px solid #333; /* Dark lines for cells */
					padding: 8px;
					text-align: left;
				  }
			  
				  th {
					background-color: #10004F; /* Blue header */
					color: #fff; /* White text */
				  }`,
                }),
            };
            const emailList = yield this.returnEmailsListFromRecipients(recipients, businessLevelHierarchy);
            yield this.sharedNotificationService.sendNotification(schedulerId, enums_1.NOTIFICATION_ENTITY_TYPE.SCHEDULER_NOTIFICATION, emailList, templateName, placeholderValues);
        });
    }
    runLiveScheduler() {
        return __awaiter(this, void 0, void 0, function* () {
            const queueLogs = yield this.queueLogRepository.getUnprocessedQueueLogs(this.RETRY_THRESHOLD);
            const config = this.configService.getAppConfig();
            if (queueLogs === null || queueLogs === void 0 ? void 0 : queueLogs.length) {
                const liveSchedulers = yield this.schedulerRepository.getSchedulerByType(scheduler_type_enum_1.SCHEDULER_TYPE.LIVE);
                for (const log of queueLogs) {
                    const { id: logId, entityId, action, finalApproval, data, errors } = log;
                    const placeholdersValues = {
                        afeDetailLink: `${config.uiClient.baseUrl}/afe/afe-detail/${data.proposalId}`
                    };
                    const filteredSchedulers = [];
                    try {
                        for (let scheduler of liveSchedulers) {
                            const { entities, actions, finalApproval: schedulerFinalApproval } = scheduler;
                            let isEntityConditionPassed = true;
                            let isActionConditionPassed = true;
                            if (entities === null || entities === void 0 ? void 0 : entities.length) {
                                const allChildEntitiesIds = yield this.businessEntityService.getBusinessEntitiesChildIds(entities);
                                isEntityConditionPassed = (allChildEntitiesIds === null || allChildEntitiesIds === void 0 ? void 0 : allChildEntitiesIds.includes(entityId)) || false;
                            }
                            if (action === null || action === void 0 ? void 0 : action.length) {
                                isActionConditionPassed = (actions === null || actions === void 0 ? void 0 : actions.includes(action)) || false;
                            }
                            if (isEntityConditionPassed &&
                                isActionConditionPassed &&
                                schedulerFinalApproval === finalApproval) {
                                filteredSchedulers.push(scheduler);
                            }
                        }
                        for (const scheduler of filteredSchedulers) {
                            const { rule, templateName, recipients } = scheduler;
                            const emailList = yield this.returnEmailsListFromRecipients(recipients, data.businessUnitHierarchy);
                            const isRuleConditionSatisfied = !!rule
                                ? yield this.isRuleConditionsValid(rule, data)
                                : true;
                            if (isRuleConditionSatisfied) {
                                yield this.sharedNotificationService.sendNotificationForAfeProposal(data.proposalId, scheduler.id, enums_1.NOTIFICATION_ENTITY_TYPE.SCHEDULER_NOTIFICATION, emailList, templateName, false, placeholdersValues);
                            }
                        }
                        yield this.queueLogRepository.markQueueLogAsProcessed(logId, constants_1.SYSTEM_USER);
                    }
                    catch (error) {
                        this.loggerService.error(error === null || error === void 0 ? void 0 : error.message, error);
                        let errorsValues;
                        if (!(errors === null || errors === void 0 ? void 0 : errors.length)) {
                            errorsValues = [error];
                        }
                        else {
                            errors.push(error);
                            errorsValues = errors;
                        }
                        this.queueLogRepository.appendError(logId, errorsValues, constants_1.SYSTEM_USER);
                        this.queueLogRepository.incrementRetryCount(logId);
                    }
                }
            }
        });
    }
    isRuleConditionsValid(rule, data) {
        return __awaiter(this, void 0, void 0, function* () {
            const isRuleConditionsSatisfied = yield (0, helpers_1.ruleValidator)(rule, data);
            return isRuleConditionsSatisfied;
        });
    }
    runReminderScheduler() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const lastNumberOfDays = 90;
            const approvalSteps = yield this.afeProposalApproverRepository.getInprogressApproversAssignedBeforeDaysAgo(lastNumberOfDays);
            const userAfeMap = new Map();
            const config = this.configService.getAppConfig();
            let inProgressAprroversLogInIds = [];
            for (const step of approvalSteps) {
                const { id: approverStepId, otherInfo, assginedType, assignedTo, assignedEntityId, afeProposalId, } = step;
                let approversIds = [];
                if (assginedType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE) {
                    if (assignedTo != 'GroupCEO' && assignedEntityId) {
                        const approvers = yield this.adminApiClient.getUsersByRoleOfAnEntity(assignedTo, assignedEntityId);
                        approversIds = approvers === null || approvers === void 0 ? void 0 : approvers.map(user => user.user_name.toLowerCase());
                        inProgressAprroversLogInIds.push(...approversIds);
                    }
                }
                else if (assginedType === associated_type_enum_1.ASSOCIATED_TYPE.USER ||
                    assginedType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER) {
                    approversIds = otherInfo.usersDetail.map(user => user.loginId.toLowerCase());
                    inProgressAprroversLogInIds.push(...approversIds);
                }
                approversIds = approversIds.filter(id => !!id);
                const tasks = yield this.taskApiClient.getAllTasks(approverStepId, enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK);
                if (tasks === null || tasks === void 0 ? void 0 : tasks.length) {
                    const taskId = tasks[tasks.length - 1].id;
                    for (const approver of approversIds) {
                        if (userAfeMap.has(approver)) {
                            userAfeMap.get(approver).push({ afeProposalId, approverStepId, taskId });
                        }
                        else {
                            userAfeMap.set(approver, [{ afeProposalId, approverStepId, taskId }]);
                        }
                    }
                }
            }
            if (inProgressAprroversLogInIds === null || inProgressAprroversLogInIds === void 0 ? void 0 : inProgressAprroversLogInIds.length) {
                const usersDetails = yield this.mSGraphApiClient.getUsersDetails([
                    ...new Set(inProgressAprroversLogInIds),
                ]);
                for (const [loginId, detail] of userAfeMap) {
                    try {
                        let tableData = yield this.getAFEListData(detail.map(d => d.afeProposalId));
                        const placeholderFields = [
                            'AFE Number',
                            'Business Unit',
                            'Project Name',
                            'AFE Type',
                            'Budget Type',
                            'Total Expenditure Amount (In USD)',
                            'Projects',
                            'Status',
                            'Task Assigned On',
                            'Task Link',
                        ];
                        tableData = tableData.map(data => (Object.assign(Object.assign({ 'AFE Number': `<a href="${config.uiClient.baseUrl}/mytask/task/${data.Id}?taskId=${detail.find(d => d.afeProposalId == data.Id).taskId}">${data['AFE Reference Number']}</a>` }, data), { 'Task Link': `<a href="${config.uiClient.baseUrl}/mytask/task/${data.Id}?taskId=${detail.find(d => d.afeProposalId == data.Id).taskId}">Click Here</a>` })));
                        if (placeholderFields === null || placeholderFields === void 0 ? void 0 : placeholderFields.length) {
                            tableData = this.filterTableData(tableData, placeholderFields);
                        }
                        const placeholderValues = {
                            afe_listing: (0, helpers_1.jsonToHtmlTable)({
                                data: tableData,
                                border: 1,
                                cellspacing: 0,
                                cellpadding: 0,
                                css: `table {
								border-collapse: collapse;
								width: 100%;
							  }
						  
							  th, td {
								border: 1px solid #333; /* Dark lines for cells */
								padding: 8px;
								text-align: left;
							  }
						  
							  th {
								background-color: #10004F; /* Blue header */
								color: #fff; /* White text */
							  }`,
                            }),
                        };
                        const userDetail = usersDetails.find(user => {
                            var _a, _b;
                            return ((_a = user.userPrincipalName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === loginId.toLowerCase() ||
                                ((_b = user.mail) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === loginId.toLocaleUpperCase();
                        });
                        if ((userDetail === null || userDetail === void 0 ? void 0 : userDetail.mail) &&
                            ((_a = userDetail === null || userDetail === void 0 ? void 0 : userDetail.mail) === null || _a === void 0 ? void 0 : _a.toLowerCase()) != '<EMAIL>') {
                            yield this.sharedNotificationService.sendNotification(-1, enums_1.NOTIFICATION_ENTITY_TYPE.REMINDER_NOTIFICATION, { to: [userDetail.mail] }, 'AFE.TASK.REMINDER', placeholderValues);
                        }
                    }
                    catch (error) {
                        this.loggerService.error(error === null || error === void 0 ? void 0 : error.message, error);
                    }
                }
            }
        });
    }
    runSubmitterScheduler() {
        return __awaiter(this, void 0, void 0, function* () {
            const thresholdDaysForReminder = 90;
            const afes = yield this.afeProposalRepository.getInprogressAfesAfterGivenLastUpdated(thresholdDaysForReminder);
            const userAfeMap = new Map();
            let inProgressSubmitterLogInIds = afes.map(afe => afe.submitterId);
            for (const afe of afes) {
                const submitterId = afe.submitterId;
                if (userAfeMap.has(submitterId)) {
                    userAfeMap.get(submitterId).push(afe.id);
                }
                else {
                    userAfeMap.set(submitterId, [afe.id]);
                }
            }
            if (inProgressSubmitterLogInIds === null || inProgressSubmitterLogInIds === void 0 ? void 0 : inProgressSubmitterLogInIds.length) {
                const usersDetails = yield this.mSGraphApiClient.getUsersDetails([
                    ...new Set(inProgressSubmitterLogInIds),
                ]);
                for (const [loginId, afeProposalIds] of userAfeMap) {
                    try {
                        let tableData = yield this.getAFEListData(afeProposalIds);
                        const placeholderFields = [
                            'AFE Reference Number',
                            'Business Unit',
                            'Project Name',
                            'AFE Type',
                            'Budget Type',
                            'Total Expenditure Amount (In USD)',
                            'Projects',
                            'Status',
                            'Task Assigned On',
                            'Link to AFE',
                        ];
                        if (placeholderFields === null || placeholderFields === void 0 ? void 0 : placeholderFields.length) {
                            tableData = this.filterTableData(tableData, placeholderFields);
                        }
                        const placeholderValues = {
                            afe_listing: (0, helpers_1.jsonToHtmlTable)({
                                data: tableData,
                                border: 1,
                                cellspacing: 0,
                                cellpadding: 0,
                                css: `table {
								border-collapse: collapse;
								width: 100%;
							  }
						  
							  th, td {
								border: 1px solid #333; /* Dark lines for cells */
								padding: 8px;
								text-align: left;
							  }
						  
							  th {
								background-color: #10004F; /* Blue header */
								color: #fff; /* White text */
							  }`,
                            }),
                        };
                        const userDetail = usersDetails.find(user => {
                            var _a, _b;
                            return ((_a = user.userPrincipalName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === loginId.toLowerCase() ||
                                ((_b = user.mail) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === loginId.toLocaleUpperCase();
                        });
                        if (userDetail === null || userDetail === void 0 ? void 0 : userDetail.mail) {
                            yield this.sharedNotificationService.sendNotification(-1, enums_1.NOTIFICATION_ENTITY_TYPE.SUBMITTER_REMINDER_NOTIFICATION, { to: [userDetail.mail] }, 'AFE.SCHEDULER.DAILY.SUBMITTER_AFES', placeholderValues);
                        }
                    }
                    catch (error) {
                        this.loggerService.error(error === null || error === void 0 ? void 0 : error.message, error);
                    }
                }
            }
        });
    }
    returnEmailsListFromRecipients(recipients, parentEntities, afeProposalId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { fixed, roleBased, afeBased } = recipients;
            const emailList = { to: [], cc: [], bcc: [] };
            if (roleBased && parentEntities) {
                const { to, cc, bcc } = roleBased;
                (to === null || to === void 0 ? void 0 : to.length) &&
                    emailList.to.push(...(yield this.getUsersEmailByRoleAndLevel(to, parentEntities)));
                (cc === null || cc === void 0 ? void 0 : cc.length) &&
                    emailList.cc.push(...(yield this.getUsersEmailByRoleAndLevel(cc, parentEntities)));
                (bcc === null || bcc === void 0 ? void 0 : bcc.length) &&
                    emailList.bcc.push(...(yield this.getUsersEmailByRoleAndLevel(bcc, parentEntities)));
            }
            if (fixed) {
                const { to, cc, bcc } = fixed;
                (to === null || to === void 0 ? void 0 : to.length) && emailList.to.push(...to);
                (cc === null || cc === void 0 ? void 0 : cc.length) && emailList.cc.push(...cc);
                (bcc === null || bcc === void 0 ? void 0 : bcc.length) && emailList.bcc.push(...bcc);
            }
            if (afeBased && afeProposalId) {
                const afeDetails = yield this.afeProposalRepository.getAfeProposalById(afeProposalId);
                const { to, cc, bcc } = afeBased;
                (to === null || to === void 0 ? void 0 : to.length) && emailList.to.push(...(yield this.getAfeBasedUsersEmail(to, afeDetails)));
                (cc === null || cc === void 0 ? void 0 : cc.length) && emailList.cc.push(...(yield this.getAfeBasedUsersEmail(cc, afeDetails)));
                (bcc === null || bcc === void 0 ? void 0 : bcc.length) && emailList.bcc.push(...(yield this.getAfeBasedUsersEmail(bcc, afeDetails)));
            }
            return emailList;
        });
    }
    getAfeBasedUsersEmail(afeRecipients, afeDetails) {
        return __awaiter(this, void 0, void 0, function* () {
            const emails = [];
            const { readers, data, submitterId } = afeDetails;
            const { projectDetails } = data;
            const { projectLeader } = projectDetails;
            for (const afeRecipient of afeRecipients) {
                switch (afeRecipient) {
                    case 'AFE_PROJECT_LEADER':
                        projectLeader.mail && emails.push(projectLeader.mail);
                        break;
                    case 'AFE_READER':
                        const readerEmails = readers === null || readers === void 0 ? void 0 : readers.map(reader => reader === null || reader === void 0 ? void 0 : reader.mail).filter(reader => !!reader);
                        emails.push(...readerEmails);
                        break;
                    case 'AFE_SUBMITTER':
                        const submitterDetail = yield this.mSGraphApiClient.getUserDetails(submitterId);
                        if (submitterDetail) {
                            emails.push(submitterDetail.mail);
                        }
                        break;
                }
            }
            return emails;
        });
    }
    getUsersEmailByRoleAndLevel(recipients, entityParents) {
        return __awaiter(this, void 0, void 0, function* () {
            const userEmails = [];
            for (const recipient of recipients) {
                const { role, level } = recipient;
                const associatedLevelEntity = entityParents.find(e => e.level === level);
                const associatedLevelEntityId = associatedLevelEntity
                    ? Number(associatedLevelEntity.id)
                    : Number(entityParents[0].id);
                const users = yield this.adminApiClient.getUsersByRoleOfAnEntity(role, associatedLevelEntityId);
                const userIds = users === null || users === void 0 ? void 0 : users.map(user => user.user_name.toLowerCase());
                const userAdDetails = userIds.length
                    ? yield this.mSGraphApiClient.getUsersDetails(userIds)
                    : [];
                userEmails.push(...userAdDetails.map(user => user.mail));
            }
            return userEmails;
        });
    }
    getAFEListData(ids) {
        return __awaiter(this, void 0, void 0, function* () {
            let tableData = yield this.afeProposalRepository.callGetAfeProposalsByIdsFunc(ids);
            const config = this.configService.getAppConfig();
            return tableData.map(row => (Object.assign(Object.assign({}, row), { 'Link to AFE': `<a href="${config.uiClient.baseUrl}/afe/afe-detail/${row['Id']}">Click Here</a>` })));
        });
    }
    filterTableData(data, placeholderFields) {
        const tableData = data.map(obj => {
            const filteredObj = Object.fromEntries(Object.entries(obj).filter(([key]) => placeholderFields.includes(key)));
            return filteredObj;
        });
        return tableData;
    }
};
SchedulerService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_3.SchedulerRepository,
        repositories_2.QueueLogRepository,
        clients_1.AdminApiClient,
        services_1.SharedNotificationService,
        repositories_1.AfeProposalRepository,
        repositories_1.AfeProposalApproverRepository,
        config_service_1.ConfigService,
        clients_1.MSGraphApiClient,
        services_2.OracleFusionService,
        services_3.LoggerService,
        services_4.BusinessEntityService,
        sftp_service_1.SftpService,
        clients_1.TaskApiClient])
], SchedulerService);
exports.SchedulerService = SchedulerService;
//# sourceMappingURL=scheduler.service.js.map