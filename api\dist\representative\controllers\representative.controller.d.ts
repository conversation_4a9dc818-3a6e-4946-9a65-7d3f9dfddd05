import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { AddRepresentativeRequestDto } from '../dtos';
import { UpdateRepresentativeRequestDto } from '../dtos/request/update-representative-request.dto';
import { RepresentativeService } from '../services';
export declare class RepresentativeController {
    private readonly representativeService;
    constructor(representativeService: RepresentativeService);
    addRepresentative(request: RequestContext, addRepresentativeRequestDto: AddRepresentativeRequestDto): Promise<MessageResponseDto>;
    getRepresentativeOfUser(request: RequestContext, filterQuery?: string): Promise<any>;
    deleteRepresentativeById(id: number, request: RequestContext): Promise<MessageResponseDto>;
    updateRepresentative(request: RequestContext, updateRepresentativeRequestDto: UpdateRepresentativeRequestDto): Promise<MessageResponseDto>;
}
