"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TASK_ACTION_WITH_EMAIL_TEMPLATE = void 0;
const enums_1 = require("../../shared/enums");
exports.TASK_ACTION_WITH_EMAIL_TEMPLATE = {
    [enums_1.TASK_ACTION.APPROVE]: 'AFE.TASK.APPROVAL.APPROVED',
    [enums_1.TASK_ACTION.AUTO_APPROVE]: 'AFE.TASK.APPROVAL.APPROVED',
    [enums_1.TASK_ACTION.REJECT]: 'AFE.TASK.APPROVAL.REJECTED',
    [enums_1.TASK_ACTION.MORE_DETAIL]: 'AFE.TASK.APPROVAL.MORE_DETAIL',
    [enums_1.TASK_ACTION.MORE_DETAIL_SUBMITTED]: 'AFE.TASK.APPROVAL.MORE_DETAIL_SUBMITTED',
    [enums_1.TASK_ACTION.REASSIGNE]: 'AFE.TASK.ASSIGNMENT.REASSIGNED',
    [enums_1.TASK_ACTION.DELEGATE]: 'AFE.TASK.APPROVAL.DELEGATED',
    [enums_1.TASK_ACTION.SEND_BACK]: 'AFE.TASK.APPROVAL.SENT_BACK',
    [enums_1.TASK_ACTION.DISCARD]: 'AFE.TASK.APPROVAL.DISCARDED',
    [enums_1.TASK_ACTION.RESUBMIT]: 'AFE.TASK.APPROVAL.RESUBMITTED',
};
//# sourceMappingURL=task-action-with-email-template.mapping.js.map