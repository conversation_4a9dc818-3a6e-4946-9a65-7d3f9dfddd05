import SftpClient from 'ssh2-sftp-client';
import { AfeProposalAmountSplitRepository, AfeProposalApproverRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { NotificationApiClient } from 'src/shared/clients';
import { DataSharingSchedulerRepository } from './repositories/data_sharing_scheduler.repository';
import { BusinessEntityService } from 'src/business-entity/services';
export declare class SftpService {
    private readonly sftpClient;
    private readonly dataSharingSchedulerRepository;
    private readonly afeProposalRepository;
    private readonly afeProposalAmountSplitRepository;
    private readonly afeProposalApproverRepository;
    private readonly notificationApiClient;
    private readonly businessEntityService;
    constructor(sftpClient: SftpClient, dataSharingSchedulerRepository: DataSharingSchedulerRepository, afeProposalRepository: AfeProposalRepository, afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository, afeProposalApproverRepository: AfeProposalApproverRepository, notificationApiClient: NotificationApiClient, businessEntityService: BusinessEntityService);
    run(): Promise<void>;
    private flatBuHierarchyFromTree;
    private getLastLevelEntities;
    private createAndUploadCsvFile;
    private uploadToSftp;
    private sendFileViaEmail;
    private ensureRemoteDirExists;
    private deleteAllLocalFolder;
    private deleteAllPreviousFolders;
}
