"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const database_module_1 = require("./database/database.module");
const config_module_1 = require("./config/config.module");
const core_module_1 = require("./core/core.module");
const auth_module_1 = require("./auth/auth.module");
const core_1 = require("@nestjs/core");
const logger_interceptor_1 = require("./core/interceptors/logger.interceptor");
const report_module_1 = require("./report/report.module");
const afe_proposal_module_1 = require("./afe-proposal/afe-proposal.module");
const shared_module_1 = require("./shared/shared.module");
const sequelize_1 = require("@nestjs/sequelize");
const orm_config_1 = require("./database/orm-config");
const interceptors_1 = require("./core/interceptors");
const workflow_module_1 = require("./workflow/workflow.module");
const task_module_1 = require("./task/task.module");
const vacation_module_1 = require("./vacation/vacation.module");
const business_entity_module_1 = require("./business-entity/business-entity.module");
const finance_module_1 = require("./finance/finance.module");
const project_component_module_1 = require("./project-component/project-component.module");
const afe_config_module_1 = require("./afe-config/afe-config.module");
const permission_module_1 = require("./permission/permission.module");
const config_service_1 = require("./config/config.service");
const afe_draft_module_1 = require("./afe-draft/afe-draft.module");
const attachments_module_1 = require("./attachment/attachments.module");
const notification_module_1 = require("./notification/notification.module");
const settings_module_1 = require("./settings/settings.module");
const dashboard_module_1 = require("./dashboard/dashboard.module");
const scheduler_module_1 = require("./scheduler/scheduler.module");
const queue_module_1 = require("./queue/queue.module");
const oracle_fusion_module_1 = require("./oracle-fusion/oracle-fusion.module");
const one_app_module_1 = require("./one-app/one-app.module");
const pdf_generator_module_1 = require("./pdf-generator/pdf-generator.module");
const representative_module_1 = require("./representative/representative.module");
const graph_user_module_1 = require("./graph-user/graph-user.module");
const admin_module_1 = require("./admin/admin.module");
let AppModule = class AppModule {
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            database_module_1.DatabaseModule,
            config_module_1.ConfigModule,
            core_module_1.CoreModule,
            auth_module_1.AuthModule,
            report_module_1.ReportModule,
            afe_proposal_module_1.AfeProposalModule,
            shared_module_1.SharedModule,
            sequelize_1.SequelizeModule.forRootAsync({
                imports: [config_module_1.ConfigModule],
                useFactory: (configService) => __awaiter(void 0, void 0, void 0, function* () {
                    const { database } = configService.getAppConfig();
                    const { dialect, host, password, db, port, username, schema, enableSSL } = database;
                    return Object.assign({ dialect: dialect, host,
                        port, database: db, username,
                        password,
                        schema, logging: false }, (0, orm_config_1.getSequelizeOrmConfig)(enableSSL));
                }),
                inject: [config_service_1.ConfigService],
            }),
            workflow_module_1.WorkflowModule,
            task_module_1.TaskModule,
            vacation_module_1.VacationModule,
            business_entity_module_1.BusinessEntityModule,
            finance_module_1.FinanceModule,
            project_component_module_1.ProjectComponentModule,
            afe_config_module_1.AfeConfigModule,
            permission_module_1.PermissionModule,
            afe_draft_module_1.AfeDraftModule,
            attachments_module_1.AttachmentModule,
            notification_module_1.NotificationModule,
            settings_module_1.SettingsModule,
            dashboard_module_1.DashboardModule,
            scheduler_module_1.SchedulerModule,
            queue_module_1.QueueModule,
            oracle_fusion_module_1.OracleFusionModule,
            one_app_module_1.OneAppModule,
            pdf_generator_module_1.PdfGeneratorModule,
            representative_module_1.RepresentativeModule,
            graph_user_module_1.GraphUserModule,
            admin_module_1.AdminModule
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            common_1.Logger,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: logger_interceptor_1.LoggingInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: interceptors_1.HttpRequestInterceptor,
            },
        ],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map