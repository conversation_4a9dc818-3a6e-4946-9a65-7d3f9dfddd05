import { CurrentContext } from 'src/shared/types';
import { ReplaceUserRequestDto } from '../dtos';
import { DraftAfeRepository } from 'src/afe-draft/repositories';
import { DatabaseHelper, SequlizeOperator } from 'src/shared/helpers';
import { AfeProposalApproverRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { CostCenterRepository } from 'src/finance/repositories';
import { WorkflowMasterStepRepository } from 'src/workflow/repositories';
import { NotificationRepository } from 'src/notification/repositories';
export declare class AdminService {
    private readonly draftRepository;
    private readonly databaseHelper;
    private readonly afeProposalRepository;
    private readonly costCenterRepository;
    private readonly workflowMasterStepRepository;
    private readonly afeProposalApproverRepository;
    private readonly notificationRepository;
    private readonly sequlizeOperator;
    constructor(draftRepository: DraftAfeRepository, databaseHelper: DatabaseHelper, afeProposalRepository: AfeProposalRepository, costCenterRepository: CostCenterRepository, workflowMasterStepRepository: WorkflowMasterStepRepository, afeProposalApproverRepository: AfeProposalApproverRepository, notificationRepository: NotificationRepository, sequlizeOperator: SequlizeOperator);
    replaceUserEmail(replaceUserRequestDto: ReplaceUserRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
}
