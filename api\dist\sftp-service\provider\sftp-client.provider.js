"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SftpClientProvider = void 0;
const ssh2_sftp_client_1 = __importDefault(require("ssh2-sftp-client"));
exports.SftpClientProvider = {
    provide: ssh2_sftp_client_1.default,
    useFactory: () => {
        return new ssh2_sftp_client_1.default();
    },
};
//# sourceMappingURL=sftp-client.provider.js.map