import { HistoryApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { CurrentContext } from 'src/shared/types';
import { NewSharedBucketLimitRequestDTO } from '../dtos/request/new-shared-bucket-limit-request.dto';
import { GetBucketSharedLimitResponseDTO, GetBucketSharedLimitWithStepResponseDTO } from '../dtos/response/get-bucket-shared-limit-response.dto';
import { WorkflowMasterStepRepository, WorkflowSharedBucketLimitRepository } from '../repositories';
export declare class WorkflowSharedBucketService {
    private readonly workflowMasterStepRepository;
    private readonly workflowSharedBucketLimitRepository;
    private readonly historyApiClient;
    constructor(workflowMasterStepRepository: WorkflowMasterStepRepository, workflowSharedBucketLimitRepository: WorkflowSharedBucketLimitRepository, historyApiClient: HistoryApiClient);
    getSharedBucketLimitList(stepId: number): Promise<GetBucketSharedLimitWithStepResponseDTO>;
    addNewBucketEntity(stepId: number, newSharedBucketLimitRequestDTO: NewSharedBucketLimitRequestDTO, currentContext: CurrentContext): Promise<GetBucketSharedLimitResponseDTO>;
    deleteBucketLimit(id: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
}
