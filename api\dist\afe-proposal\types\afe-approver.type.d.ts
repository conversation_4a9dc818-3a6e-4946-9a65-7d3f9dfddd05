import { APPROVER_STATUS, ASSIGNED_TYPE, ASSOCIATED_COLUMN } from 'src/shared/enums';
import { OtherInfomation } from './other-info.type';
import { UserDetail } from './user-detail.type';
export interface AfeApproverType {
    afeProposalId: number;
    title: string;
    assignedLevel: string;
    assignedTo: string;
    assginedType: ASSIGNED_TYPE;
    associatedColumn: ASSOCIATED_COLUMN;
    parallelIdentifier: ASSOCIATED_COLUMN;
    approvalSequence: number;
    userDetail: UserDetail;
    comment?: string;
    actionBy: string;
    otherInfo?: OtherInfomation;
    workflowMasterStepsId: number;
    actionStatus: APPROVER_STATUS;
}
