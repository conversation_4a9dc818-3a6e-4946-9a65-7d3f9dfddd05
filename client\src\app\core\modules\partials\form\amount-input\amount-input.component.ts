import { CurrencyPipe } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  forwardRef,
  HostListener,
  Input,
  OnInit,
} from '@angular/core';
import {
  ControlContainer,
  ControlValueAccessor,
  FormControl,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { toNumber } from 'lodash';

@Component({
  selector: 'app-input-amount[type=currency]',
  templateUrl: './amount-input.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputAmountComponent),
      multi: true,
    },
  ],
})

export class InputAmountComponent implements ControlValueAccessor, OnInit {
  @Input() placeholder: string;
  @Input() formControlName: string;
  @Input() min: number;
  @Input() max: number;
  @Input() isDisabled: boolean = false;

  public value: any;
  onChange: () => any;
  onTouche: () => any;

  public formGroup: FormGroup;
  public formControl: FormControl;

  constructor(
    private readonly controlContainer: ControlContainer,
    private readonly currencyPipe: CurrencyPipe,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this.setStateInitialization();
  }

  private setStateInitialization(): void {
    this.formGroup = this.controlContainer.control as FormGroup;
    this.formControl = this.formGroup.get(this.formControlName) as FormControl;
    console.log('this.formControl');
    console.log(this.formControl);
  }

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }
  
  registerOnTouched(fn: any): void {
    this.onTouche = fn;
  }

  @HostListener('focusout', ['$event'])
  private _onHostListenerFocusout(event: InputEvent): void {
    const inputElement = event.target as HTMLInputElement;
    let value: string | number = inputElement.value;
    

    console.log('Focus Out - ', value);
    if (this.isValueValid(value)) {
      value = +inputElement.value.replace(/[^\d.]/g, '');
      value = this.toFixed(value, 2);
    };
    
    this.formControl.patchValue(this.isValueValid(value) ? toNumber(value) : null);
    this.cdr.detectChanges();

    inputElement.value = this.isValueValid(value)
      ? (this.currencyPipe.transform(value, '', '', '') || '')
      : '';

    this.formControl.markAllAsTouched();
    this.cdr.detectChanges();
  }

  @HostListener('input', ['$event'])
  private _onHostListenerInput(event: InputEvent): void {
    const inputElement = event.target as HTMLInputElement;
    let value: string | number = inputElement.value;
    
    console.log('input - ', value);

    if (this.isValueValid(value)) {
      // value = toNumber(inputElement.value.replace(/[^\d.]/g, ''));
      value = inputElement.value.replace(/[^\d.]/g, '');
      value = this.toFixed(value, 2);
    };

    // console.log('Input Update - ' + value);

    inputElement.value = value.toString();
    this.formControl.patchValue(this.isValueValid(value) ? toNumber(value) : null);
    this.cdr.detectChanges();
  }

  isValueValid(value: string | number): boolean {
    if(value !== undefined && value !== null) {
      return value.toString().trim() !== '';
    }
    return false;
  }

  toFixed(num: any, fixed: any) {
    if(this.isValueValid(num)) {
      var re = new RegExp('^-?\\d+(?:\.\\d{0,' + (fixed || -1) + '})?');
      return num.toString().match(re)[0];
    }

    return '';
    
  }
}
