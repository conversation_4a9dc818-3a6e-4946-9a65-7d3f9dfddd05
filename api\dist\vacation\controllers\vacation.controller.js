"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VacationController = void 0;
const common_1 = require("@nestjs/common");
const decorators_1 = require("@nestjs/common/decorators");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const dtos_1 = require("../dtos");
const services_1 = require("../services");
let VacationController = class VacationController {
    constructor(vacationService) {
        this.vacationService = vacationService;
    }
    getAllUpcomingDelegations(request, filterQuery) {
        return this.vacationService.getAllUpcomingDelegations(request.currentContext, filterQuery);
    }
    addUpcomingDelegation(request, addDelegationDto) {
        return this.vacationService.addUpcomingDelegation(addDelegationDto, request.currentContext);
    }
    updateUpcomingDelegation(request, updateDelegationDto) {
        return this.vacationService.updateUpcomingDelegation(updateDelegationDto, request.currentContext);
    }
    deleteUpcomingDelegation(request, id) {
        return this.vacationService.deleteUpcomingDelegation(id, request.currentContext);
    }
    deleteAllUpcomingDelegation(request, deleteDelegationDto) {
        return this.vacationService.deleteAllUpcomingDelegation(deleteDelegationDto, request.currentContext);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all upcoming vacation delegations of user.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'delegateFor',
        type: String,
        description: 'Get upcoming vacation delegations list for specific user. (Only For Admin)',
        required: false,
        allowEmptyValue: true,
    }),
    (0, decorators_1.Get)('/upcoming-delegations'),
    __param(0, (0, decorators_1.Req)()),
    __param(1, (0, decorators_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], VacationController.prototype, "getAllUpcomingDelegations", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Add new upcoming vacation delegation.',
    }),
    (0, decorators_1.Post)('/upcoming-delegation'),
    __param(0, (0, decorators_1.Req)()),
    __param(1, (0, decorators_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.SetupNewVacationDelegationRequestDto]),
    __metadata("design:returntype", void 0)
], VacationController.prototype, "addUpcomingDelegation", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update upcoming vacation delegation.',
    }),
    (0, decorators_1.Put)('/upcoming-delegation'),
    __param(0, (0, decorators_1.Req)()),
    __param(1, (0, decorators_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.UpdateVacationDelegationRequestDto]),
    __metadata("design:returntype", void 0)
], VacationController.prototype, "updateUpcomingDelegation", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete upcoming vacation delegation by id.',
    }),
    (0, decorators_1.Delete)('/upcoming-delegation/:id'),
    __param(0, (0, decorators_1.Req)()),
    __param(1, (0, decorators_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], VacationController.prototype, "deleteUpcomingDelegation", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete all upcoming vacation delegations.',
    }),
    (0, decorators_1.Post)('/delete-all-upcoming-delegation'),
    __param(0, (0, decorators_1.Req)()),
    __param(1, (0, decorators_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.DeleteVacationDelegationRequestDto]),
    __metadata("design:returntype", void 0)
], VacationController.prototype, "deleteAllUpcomingDelegation", null);
VacationController = __decorate([
    (0, swagger_1.ApiTags)('Vacation Delegation Setup'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, decorators_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('vacation'),
    __metadata("design:paramtypes", [services_1.VacationService])
], VacationController);
exports.VacationController = VacationController;
//# sourceMappingURL=vacation.controller.js.map