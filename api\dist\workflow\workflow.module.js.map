{"version": 3, "file": "workflow.module.js", "sourceRoot": "", "sources": ["../../src/workflow/workflow.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAuD;AACvD,+CAAuG;AACvG,iDAMwB;AACxB,yGAAkG;AAClG,2EAAuE;AACvE,+DAAqP;AACrP,0DAAgE;AAChE,yCAA4J;AAC5J,+CAAyH;AACzH,oEAAgF;AAChF,4FAAwF;AACxF,iDAAiF;AACjF,+CAAsE;AACtE,uGAAiG;AACjG,8FAAwF;AACxF,0DAKiC;AACjC,kEAAwE;AACxE,kDAAsD;AACtD,2DAA+D;AAC/D,mDAAwD;AACxD,qDAA6D;AAC7D,+CAAkD;AAElD,MAAM,YAAY,GAAG;IACpB,8CAA+B;IAC/B,2CAA4B;IAC5B,qCAAsB;IACtB,kDAAmC;IACnC,iDAAkC;IAClC,kDAAmC;IACnC,mCAAoB;IACpB,yCAA0B;IAC1B,qCAAsB;IACtB,6CAA8B;IAC9B,oCAAqB;IACrB,oCAAqB;IACrB,qCAAsB;IACtB,iCAAkB;IAClB,4CAA6B;IAC7B,oCAAqB;IACrB,+CAAgC;IAChC,8CAA+B;IAC/B,oDAAqC;CACrC,CAAC;AAsCF,IAAa,cAAc,GAA3B,MAAa,cAAc;CAAI,CAAA;AAAlB,cAAc;IApC1B,IAAA,eAAM,EAAC;QACP,SAAS,EAAE;YACV,0BAAe;YACf,8BAAmB;YACnB,uCAA4B;YAC5B,kCAAuB;YACvB,mDAAuB;YACvB,oCAAyB;YACzB,mDAAuB;YACvB,0CAA+B;YAC/B,oCAAyB;YACzB,mDAAuB;YACvB,4DAA2B;YAC3B,wBAAc;YACd,wBAAc;YACd,0BAAgB;YAChB,yBAAc;YACd,0BAAgB;YAChB,0BAAe;YACf,iCAAoB;YACpB,0BAAgB;YAChB,4BAAiB;YACjB,uBAAa;YACb,wBAAa;YACb,sBAAa;YACb,GAAG,YAAY;SACf;QACD,WAAW,EAAE;YACZ,wCAAkB;YAClB,0CAA4B;YAC5B,mEAA8B;YAC9B,oCAAsB;YACtB,gDAAkC;YAClC,kEAA8B;SAC9B;KACD,CAAC;GACW,cAAc,CAAI;AAAlB,wCAAc"}