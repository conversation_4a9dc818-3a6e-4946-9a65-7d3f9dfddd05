"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeProposalApproverService = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const repositories_1 = require("../repositories");
let AfeProposalApproverService = class AfeProposalApproverService {
    constructor(afeProposalApproverRepository, afeProposalRepository, adminApiClient, databaseHelper, historyApiClient) {
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.afeProposalRepository = afeProposalRepository;
        this.adminApiClient = adminApiClient;
        this.databaseHelper = databaseHelper;
        this.historyApiClient = historyApiClient;
    }
    updateAfeProposalApprover(afeProposalId, updateApproversListRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { approversList: updatedApproversList } = updateApproversListRequestDto;
            const hasAdminPermission = yield this.adminApiClient.hasPermissionToUser(currentContext.user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            const { submitterId, internalStatus } = yield this.afeProposalRepository.getAfeProposalById(afeProposalId);
            if (!hasAdminPermission && submitterId !== currentContext.user.username) {
                throw new exceptions_1.HttpException('You are not allowed to update this AFE Proposal approvers list.', enums_1.HttpStatus.FORBIDDEN);
            }
            if (![enums_1.AFE_PROPOSAL_STATUS.IN_PROGRESS, enums_1.AFE_PROPOSAL_STATUS.SUBMITTED].includes(internalStatus)) {
                throw new exceptions_1.HttpException('AFE approval is not in inprogress state.', enums_1.HttpStatus.FORBIDDEN);
            }
            const currentApproversList = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
            let updatedListPointer = 0;
            let currentListPointer = 0;
            const deletedApproversIds = [];
            const updatedApprovers = [];
            const newApproverDetail = (approver) => {
                var _a;
                const { approvers, sequenceNumber, parallelIdentifier, associatedColumn } = approver;
                const newCustomApprover = {
                    assignedTo: approvers[approvers.length - 1].loginId.toLowerCase(),
                    title: ((_a = approvers[approvers.length - 1]) === null || _a === void 0 ? void 0 : _a.title) || approvers[approvers.length - 1].loginId.toLowerCase(),
                    afeProposalId: afeProposalId,
                    userDetail: null,
                    otherInfo: { usersDetail: approvers, approvalType: enums_1.APPROVAL_TYPE.APPROVAL },
                    assignedLevel: null,
                    assginedType: enums_1.ASSIGNED_TYPE.USER,
                    assignedEntityId: null,
                    associatedColumn: associatedColumn,
                    parallelIdentifier: parallelIdentifier,
                    approvalSequence: sequenceNumber,
                    workflowMasterStepsId: null
                };
                return newCustomApprover;
            };
            let newListPointer = 1;
            let isNewApproverAdded = false;
            let isAdditionalApproverRemoved = false;
            while (updatedListPointer < updatedApproversList.length && currentListPointer < currentApproversList.length) {
                if (updatedApproversList[updatedListPointer].id === currentApproversList[currentListPointer].id) {
                    updatedApprovers.push(Object.assign(Object.assign({}, currentApproversList[currentListPointer]), { approvalSequence: newListPointer }));
                    currentListPointer++;
                    updatedListPointer++;
                    newListPointer++;
                }
                else {
                    if (updatedApproversList[updatedListPointer].id) {
                        if (currentApproversList[currentListPointer].workflowMasterStepsId) {
                            throw new exceptions_1.HttpException('You are not allowed to remove mandatory approvers.', enums_1.HttpStatus.BAD_REQUEST);
                        }
                        if (currentApproversList[currentListPointer].actionBy) {
                            throw new exceptions_1.HttpException('You are not allowed to remove approvers who have already performed approval action on AFE.', enums_1.HttpStatus.BAD_REQUEST);
                        }
                        isAdditionalApproverRemoved = true;
                        deletedApproversIds.push(currentApproversList[currentListPointer].id);
                        currentListPointer++;
                    }
                    else {
                        if (updatedApprovers[updatedListPointer - 1].actionStatus !== enums_1.APPROVER_STATUS.IN_PROGRESS && updatedApprovers[updatedListPointer - 1].actionStatus !== enums_1.APPROVER_STATUS.NOT_INITIATED && updatedApprovers[updatedListPointer - 1].actionStatus !== undefined) {
                            throw new exceptions_1.HttpException('You are not allowed to add approvers after the approver who has already performed approval action on AFE.', enums_1.HttpStatus.BAD_REQUEST);
                        }
                        isNewApproverAdded = true;
                        updatedApprovers.push(Object.assign(Object.assign({}, newApproverDetail(updatedApproversList[updatedListPointer])), { approvalSequence: newListPointer }));
                        newListPointer++;
                        updatedListPointer++;
                    }
                }
            }
            while (updatedListPointer < updatedApproversList.length) {
                isNewApproverAdded = true;
                updatedApprovers.push(Object.assign(Object.assign({}, newApproverDetail(updatedApproversList[updatedListPointer])), { approvalSequence: newListPointer }));
                newListPointer++;
                updatedListPointer++;
            }
            while (currentListPointer < currentApproversList.length) {
                if (currentApproversList[currentListPointer].workflowMasterStepsId) {
                    throw new exceptions_1.HttpException('You are not allowed to remove mandatory approvers.', enums_1.HttpStatus.BAD_REQUEST);
                }
                isAdditionalApproverRemoved = true;
                deletedApproversIds.push(currentApproversList[currentListPointer].id);
                currentListPointer++;
            }
            if (isAdditionalApproverRemoved || isNewApproverAdded) {
                yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    let comment;
                    if (isNewApproverAdded && isAdditionalApproverRemoved) {
                        comment = `Added and removed additional approvers.`;
                    }
                    else if (isNewApproverAdded) {
                        comment = `Added new additional approvers`;
                    }
                    else if (isAdditionalApproverRemoved) {
                        comment = `Removed additional approvers`;
                    }
                    if (deletedApproversIds.length) {
                        yield this.afeProposalApproverRepository.deleteApproversByIds(deletedApproversIds, currentContext);
                    }
                    if (updatedApprovers.length) {
                        yield this.afeProposalApproverRepository.upsertApproversList(afeProposalId, updatedApprovers, currentContext);
                    }
                    const addHistoryPayload = {
                        created_by: currentContext.user.username,
                        entity_id: afeProposalId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.APPROVERS_LIST_UPDATED,
                        comments: comment,
                    };
                    yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                }));
            }
            return { message: 'AFE deleted successfully.' };
        });
    }
};
AfeProposalApproverService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.AfeProposalApproverRepository,
        repositories_1.AfeProposalRepository,
        clients_1.AdminApiClient,
        helpers_1.DatabaseHelper,
        clients_1.HistoryApiClient])
], AfeProposalApproverService);
exports.AfeProposalApproverService = AfeProposalApproverService;
//# sourceMappingURL=afe-proposal-approver.service.js.map