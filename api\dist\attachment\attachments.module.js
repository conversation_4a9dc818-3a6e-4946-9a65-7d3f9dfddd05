"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../afe-proposal/repositories");
const repositories_2 = require("../finance/repositories");
const clients_1 = require("../shared/clients");
const attachment_api_client_1 = require("../shared/clients/attachment-api.client");
const helpers_1 = require("../shared/helpers");
const services_1 = require("../shared/services");
const validators_1 = require("../shared/validators");
const attachment_controller_1 = require("./controllers/attachment.controller");
const attachment_service_1 = require("./services/attachment.service");
const repositories_3 = require("../afe-draft/repositories");
const repositories = [
    repositories_1.AfeProposalRepository,
    repositories_1.AfeProposalApproverRepository,
    repositories_2.CostCenterRepository,
    repositories_3.DraftAfeRepository
];
let AttachmentModule = class AttachmentModule {
};
AttachmentModule = __decorate([
    (0, common_1.Module)({
        controllers: [attachment_controller_1.AttachmentController],
        providers: [
            ...repositories,
            services_1.SharedPermissionService,
            attachment_service_1.AttachmentService,
            helpers_1.DatabaseHelper,
            clients_1.AdminApiClient,
            attachment_api_client_1.AttachmentApiClient,
            helpers_1.SequlizeOperator,
            validators_1.AfeProposalValidator,
            clients_1.TaskApiClient,
            repositories_1.UserCostCenterMappingRepository,
            repositories_1.UserProjectComponentMappingRepository
        ],
    })
], AttachmentModule);
exports.AttachmentModule = AttachmentModule;
//# sourceMappingURL=attachments.module.js.map