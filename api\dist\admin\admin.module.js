"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminModule = void 0;
const common_1 = require("@nestjs/common");
const admin_controller_1 = require("./controllers/admin.controller");
const admin_service_1 = require("./services/admin.service");
const repositories_1 = require("../afe-draft/repositories");
const repositories_2 = require("../afe-proposal/repositories");
const helpers_1 = require("../shared/helpers");
const clients_1 = require("../shared/clients");
const services_1 = require("../shared/services");
const repositories_3 = require("../finance/repositories");
const repositories_4 = require("../workflow/repositories");
const repositories_5 = require("../notification/repositories");
let AdminModule = class AdminModule {
};
AdminModule = __decorate([
    (0, common_1.Module)({
        controllers: [admin_controller_1.AdminController],
        providers: [
            admin_service_1.AdminService,
            services_1.ConditionCreatorService,
            repositories_1.DraftAfeRepository,
            repositories_2.UserProjectComponentMappingRepository,
            repositories_2.UserCostCenterMappingRepository,
            repositories_2.AfeProposalRepository,
            helpers_1.DatabaseHelper,
            helpers_1.SequlizeOperator,
            clients_1.AdminApiClient,
            services_1.SharedPermissionService,
            repositories_4.WorkflowMasterStepRepository,
            repositories_3.CostCenterRepository,
            repositories_2.AfeProposalApproverRepository,
            repositories_5.NotificationRepository
        ],
    })
], AdminModule);
exports.AdminModule = AdminModule;
//# sourceMappingURL=admin.module.js.map