<ng-container *ngIf="!loading; else loadingPage">
  <div
    *ngIf="costCenters.length > 0; else noDataMessage"
    class="card mb-5 mb-xl-8"
  >
    <!-- begin::Header -->
    <div class="card-header border-0 pt-5">
      <h3 class="card-title align-items-start flex-column">
        <span class="card-label fw-bolder fs-3 mb-1">{{
          "MENU.COST_CENTER_LIST" | translate
        }}</span>
      </h3>

      <div class="row">
        <div class="d-flex justify-content-end">
          <button
            appToggleProfileMenu
            [transform]="'-30px, 50.5px, 0px'"
            type="button"
            class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
          >
            <img
              width="45px"
              src="./assets/media/svg/icons/dpw-icons/menu.png"
              alt="next"
            />
          </button>
          <span
            class="fs-6 fw-bold menu menu-column menu-gray-600 menu-rounded menu-state-bg menu-state-primary menu-sub menu-sub-dropdown py-4 w-275px"
          >
            <div class="menu-item px-3">
              <div
                class="menu-content fs-6 text-dark fw-bolder px-3 py-4"
                translate="MENU.QUICK_ACTION"
              ></div>
            </div>

            <div class="separator mb-3 opacity-75"></div>

            <div class="menu-item px-3">
              <a
                title="{{ 'FORM.BUTTON.ADD_COST_CENTER_BUTTON' | translate }}"
                (click)="openCostCenterEditorModal()"
                class="menu-link px-3 cursor-pointer"
                translate="{{
                  'FORM.BUTTON.ADD_COST_CENTER_BUTTON' | translate
                }}"
              >
              </a>
            </div>

            <div class="menu-item px-3 mb-2">
              <a
                title="{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}"
                (click)="exportToExcelSheet()"
                class="menu-link px-3 cursor-pointer"
                translate="{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}"
              >
              </a>
            </div>
          </span>
        </div>
      </div>

      <!-- 
            <div>
                <button type="button" title="{{ 'FORM.BUTTON.ADD_BUTTON' | translate }}"
                    (click)="openCostCenterEditorModal()" class="btn btn-sm btn-primary mx-2"
                    translate="{{ 'FORM.BUTTON.ADD_BUTTON' | translate }}">
                </button>
                <button
                    type="button"
                    title="{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}"
                    class="btn btn-sm btn-primary mx-2"
                    translate="{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}"
                    (click)="exportToExcelSheet()"
                ></button>
            </div> -->
    </div>
    <!-- end::Header -->
    <!-- begin::Body -->
    <div class="card-body py-3">
      <div class="row">
        <div class="col-12 align-middle">
          <div
            class="row solid-hr fw-bolder text-muted font-size-10 py-3 d-md-flex d-none my-1 mx-2 bg-light"
          >
            <div class="col-md-3">
              {{ "LIST.NAME" | translate }}
            </div>
            <div class="col-md-2">
              {{ "LIST.CODE" | translate }}
            </div>
            <div class="col-md-2">
              {{ "FORM.LABEL.SECTIONS" | translate }}
            </div>
            <div class="col-md-2">
              {{ "LIST.DEPARTMENT_HEAD" | translate }}
            </div>
            <div class="col-md-2">
              {{ "LIST.UPDATED_AT" | translate }}
            </div>
            <div class="col-md-1 text-center">
              {{ "LIST.ACTION" | translate }}
            </div>
          </div>
          <ng-container
            *ngFor="
              let costCenter of costCenters
                | paginate
                  : {
                      id: 'costCenterList',
                      itemsPerPage: pagination.limit,
                      currentPage: pagination.page,
                      totalItems: totalRecords
                    };
              let i = index
            "
          >
            <div
              class="row font-size-14 my-2 my-md-1 py-3 card-body p-5 solid-hr note-box-sm align-items-center"
            >
              <div class="col-md-3">
                <div class="d-md-none mt-3">
                  {{ "LIST.NAME" | translate }}
                </div>
                <div class="text-none">
                  <a
                    class="d-value text-dark fw-bolder text-hover-primary fs-8"
                  >
                    {{ costCenter.name }}
                  </a>
                </div>
              </div>

              <div class="col-md-2">
                <div class="d-md-none mt-3">
                  {{ "LIST.CODE" | translate }}
                </div>
                <div class="text-none">
                  {{ costCenter.code }}
                </div>
              </div>

              <div class="col-md-2">
                <div class="d-md-none mt-3">
                  {{ "FORM.LABEL.SECTIONS" | translate }}
                </div>
                <div class="text-none">
                  <div class="text-none">
                    <span>
                      {{
                        costCenter.sectionHead.length
                          ? costCenter.sectionHead[0].title
                          : "-"
                      }}
                    </span>
                    <span
                      (click)="openCostCenterEditorModal(true, costCenter.id)"
                      *ngIf="costCenter.sectionHead.length >= 2"
                      class="badge badge-success ml-2 cursor-pointer"
                    >
                      +{{ costCenter.sectionHead.length - 1 }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="col-md-2">
                <div class="d-md-none mt-3">
                  {{ "LIST.DEPARTMENT_HEAD" | translate }}
                </div>
                <div class="text-none">
                  {{ costCenter?.departmentHead?.displayName || "-" }}
                </div>
              </div>

              <div class="col-md-2">
                <div class="d-md-none mt-3">
                  {{ "LIST.UPDATED_AT" | translate }}
                </div>
                <div class="text-none">
                  {{ costCenter.updatedOn | date : "medium" }}
                </div>
              </div>

              <div class="col-md-1">
                <div class="row">
                  <div class="d-flex justify-content-center">
                    <button
                      appToggleProfileMenu
                      [transform]="'-30px, 50.5px, 0px'"
                      type="button"
                      class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
                    >
                      <img
                        width="45px"
                        src="./assets/media/svg/icons/dpw-icons/menu.png"
                        alt="next"
                      />
                    </button>
                    <span
                      class="fs-6 fw-bold menu menu-column menu-gray-600 menu-rounded menu-state-bg menu-state-primary menu-sub menu-sub-dropdown py-4 w-275px"
                    >
                      <div class="menu-item px-3">
                        <div
                          class="menu-content fs-6 text-dark fw-bolder px-3 py-4"
                          translate="MENU.QUICK_ACTION"
                        ></div>
                      </div>

                      <div class="separator mb-3 opacity-75"></div>

                      <div class="menu-item px-3">
                        <a
                          title="{{ 'FORM.BUTTON.EDIT_BUTTON' | translate }}"
                          (click)="
                            openCostCenterEditorModal(true, costCenter.id)
                          "
                          translate="{{
                            'FORM.BUTTON.EDIT_BUTTON' | translate
                          }}"
                          class="menu-link px-3 cursor-pointer"
                        >
                        </a>
                      </div>

                      <div class="menu-item px-3">
                        <a
                          title="{{ 'COMMON.REMOVE' | translate }}"
                          (click)="deleteCostCenter(costCenter.id)"
                          translate="{{
                            'WORKFLOW.RULE.BUTTON.DELETE' | translate
                          }}"
                          class="menu-link px-3 cursor-pointer"
                        >
                        </a>
                      </div>

                      <div class="menu-item px-3">
                        <a
                          title="{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}"
                          (click)="openHistoryModel(costCenter.id)"
                          translate="{{
                            'FORM.BUTTON.HISTORY_BUTTON' | translate
                          }}"
                          class="menu-link px-3 cursor-pointer"
                        >
                        </a>
                      </div>
                    </span>
                  </div>
                </div>

                <!-- <div class="d-flex justify-content-center ml-5">
                                    <div class="outline-btn-light">
                                        <button type="button" title="{{ 'COMMON.REMOVE' | translate }}"
                                        class="btn btn-sm outline-btn-light mx-2"
                                        (click)="deleteCostCenter(costCenter.id)"
                                        translate="{{ 'WORKFLOW.RULE.BUTTON.DELETE' | translate }}"></button>
                                    </div>
                                    <button type="button" title="{{ 'FORM.BUTTON.EDIT_BUTTON' | translate }}"
                                        class="btn btn-sm btn-primary mx-2"
                                        (click)="openCostCenterEditorModal(true, costCenter.id)"
                                        translate="{{ 'FORM.BUTTON.EDIT_BUTTON' | translate }}">
                                    </button>
                                </div> -->
              </div>
            </div>
          </ng-container>
        </div>
        <div class="col-md-12 text-end">
          <div class="separator separator-dashed separator-border-1 my-5"></div>

          <div class="d-flex justify-content-between">
            <div class="fw-bold">
              {{ "COMMON.SHOWING" | translate }}
              {{
                pagination.page === 1
                  ? 1
                  : (pagination.page - 1) * pagination.limit + 1
              }}
              {{ "COMMON.TO" | translate }}
              {{
                pagination.limit * pagination.page <= totalRecords
                  ? pagination.limit * pagination.page
                  : totalRecords
              }}
              {{ "COMMON.RECORD" | translate | lowercase }}
              {{ "COMMON.OF" | translate }} {{ totalRecords }}
            </div>

            <pagination-controls
              id="costCenterList"
              previousLabel="Prev"
              nextLabel="Next"
              [responsive]="true"
              (pageChange)="handlePageChange($event)"
            ></pagination-controls>
          </div>
        </div>
      </div>
    </div>
    <!-- begin::Body -->
  </div>

  <ng-template #noDataMessage>
    <app-empty-state
      [message]="'EMPTY_STATE.EMPTY_COST_CENTER_LIST' | translate"
    >
      <span class="action position-relative d-inline-block text-danger">
        <a
          class="text-danger opacity-75-hover cursor-pointer"
          (click)="openCostCenterEditorModal()"
        >
          {{ "FORM.LABEL.SETUP_COST_CENTER" | translate }}
        </a>
        <span
          class="position-absolute opacity-15 bottom-0 start-0 border-4 border-danger border-bottom w-100"
        >
        </span>
      </span>
    </app-empty-state>
  </ng-template>
</ng-container>

<ng-template #loadingPage>
  <app-list-skeleton-loader [loaderCount]="4"></app-list-skeleton-loader>
</ng-template>

<app-modal #costCenterEditorModal [modalConfig]="costCenterEditorModalConfig">
  <div
    *ngIf="!hideForm"
    class="w-100 px-5 bg-body rounded"
    [formGroup]="costCenterFormGroup"
  >
    <div class="row">
      <div class="col-lg-6 col-md-6 col-6 mb-6 mb-lg-5">
        <label
          class="fw-bold text-muted"
          translate="FORM.LABEL.COMPANY_FUSSION_NUMBER"
        ></label>
        <div class="h4 text-gray-800">
          {{ selectedCompanyCode.code }}
        </div>
      </div>
      <div class="col-lg-6 col-md-6 col-6 mb-6 mb-lg-5">
        <label class="fw-bold text-muted" translate="LIST.ENTITY_CODE"></label>
        <div class="h4 text-gray-800">
          {{ selectedCompanyCode.entityCode }}
        </div>
      </div>
    </div>
    <div class="separator my-2"></div>

    <div class="row m-3" *ngIf="!isEditMode">
      <div class="form-check form-check-inline col">
        <input
          [(ngModel)]="isMulti"
          (click)="changeAddOption('manual')"
          [ngModelOptions]="{ standalone: true }"
          class="form-check-input success"
          name="addAnalysisCode"
          type="radio"
          id="singleAddition"
          [value]="false"
        />
        <label
          class="form-check-label fw-bold"
          for="singleAddition"
          translate="COMMON.ADD_MANUALLY"
        ></label>
      </div>
      <div class="form-check form-check-inline col">
        <input
          [(ngModel)]="isMulti"
          (click)="changeAddOption('import')"
          [ngModelOptions]="{ standalone: true }"
          class="form-check-input danger"
          name="addAnalysisCode"
          type="radio"
          id="multiAddition"
          [value]="true"
        />
        <label
          class="form-check-label fw-bold"
          for="multiAddition"
          translate="COMMON.IMPORT"
        ></label>
      </div>
    </div>

    <ng-container *ngIf="!isMulti">
      <div class="row">
        <div class="col-md-12 mb-5">
          <label class="form-label required">
            {{ "FORM.PLACEHOLDER.ENTER" | translate }}
            {{ "FORM.LABEL.NAME" | translate }}
          </label>
          <input
            name="title"
            placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
              'FORM.LABEL.NAME' | translate | lowercase
            }}"
            class="form-control form-control-lg form-control-solid"
            formControlName="name"
          />
          <ng-container *ngIf="isRequiredError('name')">
            <app-input-error-message
              errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
            >
            </app-input-error-message>
          </ng-container>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-5">
          <label class="form-label required">
            {{ "FORM.PLACEHOLDER.ENTER" | translate }}
            {{ "FORM.LABEL.COST_CENTER_CODE" | translate }}
          </label>
          <input
            name="title"
            placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
              'FORM.LABEL.COST_CENTER_CODE' | translate | lowercase
            }}"
            class="form-control form-control-lg form-control-solid"
            pattern="[a-zA-Z0-9]{4}"
            formControlName="code"
          />
          <ng-container *ngIf="isRequiredError('code')">
            <app-input-error-message
              errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
            >
            </app-input-error-message>
          </ng-container>
          <ng-container *ngIf="isPatternError('code')">
            <app-input-error-message
              errorMessage="{{
                'FORM.VALIDATION.LENGTH_ERROR'
                  | translate
                    : { name: 'FORM.LABEL.CODE' | translate, length: 4 }
              }}"
            >
            </app-input-error-message>
          </ng-container>
        </div>
        <div class="col-md-6 mb-5">
          <label
            class="form-label required"
            translate="FORM.LABEL.IS_COST_CENTER_OPERATING"
          >
          </label>
          <select
            name="operating"
            formControlName="operating"
            class="form-select form-select-lg form-select-solid"
          >
            <option value="null">
              {{ "FORM.PLACEHOLDER.SELECT" | translate }}
              {{ "LIST.COST_CENTER_OPERATING" | translate }}
            </option>
            <option value="false">No</option>
            <option value="true">Yes</option>
          </select>
          <ng-container *ngIf="isRequiredError('operating')">
            <app-input-error-message
              errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
            >
            </app-input-error-message>
          </ng-container>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-5">
          <label
            class="form-label required"
            translate="FORM.LABEL.DEPARTMENT_HEAD"
          >
          </label>
          <app-ad-user-search
            placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
              'FORM.LABEL.DEPARTMENT_HEAD' | translate | lowercase
            }}"
            [isMultiSelect]="false"
            (userSelected)="costCenterHeadAdded($event, 'departmentHead')"
            [defaultUsers]="departmentHead"
          >
          </app-ad-user-search>
          <ng-container *ngIf="isRequiredError('departmentHead')">
            <app-input-error-message
              errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
            >
            </app-input-error-message>
          </ng-container>
        </div>
        <!--
                    <div class="col-md-6 mb-5">
                        <label class="form-label required" translate="FORM.LABEL.SECTION_HEAD">
                        </label>
                        <app-ad-user-search placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
                            'FORM.LABEL.SECTION_HEAD' | translate | lowercase
                            }}" [isMultiSelect]="false" (userSelected)="costCenterHeadAdded($event, 'sectionHead')"
                            [defaultUsers]="sectionHead">
                        </app-ad-user-search>
        
                        <ng-container *ngIf="isRequiredError('sectionHead')">
                            <app-input-error-message errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}">
                            </app-input-error-message>
                        </ng-container>
                    </div>
                -->
        <div></div>

        <div class="separator my-2" *ngIf="sections?.controls?.length"></div>

        <div formArrayName="sectionHead" class="row">
          <h3
            translate="FORM.LABEL.SECTIONS"
            *ngIf="sections?.controls?.length"
            class="fw-bold d-flex align-items-center mb-2"
          ></h3>
          <ng-container
            *ngFor="let section of sections.controls; let i = index"
            [formGroupName]="i"
          >
            <div *ngIf="i" class="separator mb-3 opacity-75"></div>

            <div *ngIf="sections.controls.length > 1" class="row">
              <div class="col-md-12">
                <h5 class="fw-bold d-flex align-items-center">
                  {{ "FORM.LABEL.SECTION" | translate }} {{ i + 1 }} {{ "FORM.LABEL.DETAIL" | translate }}
                </h5>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6 mb-5">
                <label class="form-label required">
                  {{ i + 1 }}. {{ "FORM.LABEL.NAME" | translate }}
                </label>
                <input
                  (input)="sectionExist()"
                  name="title"
                  placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                    'FORM.LABEL.NAME' | translate | lowercase
                  }}"
                  class="form-control form-control-lg form-control-solid"
                  formControlName="title"
                />
                <ng-container
                  *ngIf="
                    section.get('title')?.hasError('required') &&
                    (isSubmitted || section.get('title')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>

                <ng-container
                  *ngIf="
                    section.get('title')?.hasError('alreadyExist') &&
                    (isSubmitted || section.get('title')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.NAME_ALREADY_EXIST' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>

              <!-- Section Code START -->
              <div class="col-md-6 mb-5">
                <label class="form-label">
                  {{ "FORM.LABEL.CODE" | translate }}
                </label>
                <input
                  name="code"
                  placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                    'FORM.LABEL.CODE' | translate | lowercase
                  }}"
                  class="form-control form-control-lg form-control-solid"
                  formControlName="code"
                />
                <!-- <ng-container
                                    *ngIf="
                                        section
                                        .get('code')
                                        ?.hasError('required') &&
                                        (isSubmitted ||
                                        section.get('code')?.touched)
                                    "
                                    >
                                    <app-input-error-message
                                        errorMessage="{{
                                        'FORM.VALIDATION.REQUIRED_FIELD' | translate
                                        }}"
                                    >
                                    </app-input-error-message>
                                </ng-container>

                                <ng-container
                                    *ngIf="
                                        section
                                        .get('code')
                                        ?.hasError('alreadyExist') &&
                                        (isSubmitted ||
                                        section.get('code')?.touched)
                                    "
                                    >
                                    <app-input-error-message
                                        errorMessage="{{
                                        'FORM.VALIDATION.CODE_ALREADY_EXIST' | translate
                                        }}"
                                    >
                                    </app-input-error-message>
                                </ng-container> -->
              </div>
              <!-- Section Code END -->

              <div class="col-md-11 mb-5">
                <label class="form-label required" translate="FORM.LABEL.USER">
                </label>
                <app-ad-user-search
                  placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
                    'FORM.LABEL.USER' | translate | lowercase
                  }}"
                  [isMultiSelect]="false"
                  (userSelected)="costCenterSectionHeadAdded($event, i)"
                  [defaultUsers]="getSectionHeadUser(i)"
                >
                </app-ad-user-search>
                <ng-container
                  *ngIf="
                    section.get('user')?.hasError('required') &&
                    (isSubmitted || section.get('user')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>
              <div
                (click)="removeSection(i)"
                class="col-md-1 cursor-pointer mt-5 mb-5 d-flex justify-content-center align-items-center"
              >
                <img
                  width="32px"
                  src="./assets/media/svg/icons/dpw-icons/cross2.png"
                  alt="Remove"
                />
              </div>
            </div>
          </ng-container>
          <div class="d-flex justify-content-end pt-4">
            <button
              (click)="addNewSection()"
              type="button"
              class="btn btn-sm btn-primary ps-4 pe-4 py-1"
            >
              <img
                width="12px"
                src="./assets/media/svg/icons/dpw-icons/plus.png"
                alt="Next"
              />
              <span class="px-1 indicator-label">
                {{ "FORM.BUTTON.ADD_BUTTON" | translate }}
                {{ "FORM.LABEL.SECTION" | translate }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </ng-container>

    <div class="row" *ngIf="isMulti">
      <app-add-attachment
        [formatMessage]="'COMMON.COST_CENTER_SECTION_FORMAT'"
        [isSubmitted]="isSubmitted"
        [labelTitle]="'FORM.LABEL.UPLOAD_COST_CENTER'"
        [data]="{ isDescriptionRequired: false }"
        (bufferAttachment)="addBufferAttachment($event)"
      ></app-add-attachment>

      <div class="d-flex justify-content-end">
        <button
          type="button"
          title="{{ 'FORM.BUTTON.EXPORT_FORMAT' | translate }}"
          class="btn btn-sm btn-primary mx-2"
          translate="{{ 'FORM.BUTTON.EXPORT_FORMAT' | translate }}"
          (click)="exportBlankFormat()"
        ></button>
      </div>
    </div>
  </div>

  <ng-template #loadingPage>
    <app-form-skeleton-loader [loaderCount]="2"></app-form-skeleton-loader>
  </ng-template>
</app-modal>

<app-modal #historyModal [modalConfig]="historyModalConfig">
  <div class="w-100 px-1 bg-body rounded py-1">
    <app-history-logs
      [title]="'FORM.BUTTON.COMPANY_HISTORY_BUTTON' | translate"
      [historyLogs]="companyHistory"
    >
    </app-history-logs>
  </div>
</app-modal>
