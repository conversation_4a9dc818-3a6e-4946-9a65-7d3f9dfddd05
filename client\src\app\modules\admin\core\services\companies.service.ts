import { Injectable } from "@angular/core";
import { IPagination } from "@core/interfaces/api";
import { GlobalUrls, ReplaceUrlVariable } from "@core/constants";
import { NetworkService } from "src/app/core/services/network/network.service";
import { CompanyCodeResponse, CompanyCodesListResponse, CreateAnalysisCodeRequest, CreateCompanyCodeRequest, CreateCostCenterRequest, CreateNaturalAccountNumberRequest, FussionIntegrationUpdateRequest, ImportRequest, UpdateAnalysisCodeRequest, UpdateCompanyCodeRequest, UpdateCostCenterRequest, UpdateNaturalAccountNumberRequest } from "../models";
import { ToggleActiveStateCompanyCodeRequest } from "../models/toggle-company-active-state-request.model";
import { Attachment } from "@core/models";

@Injectable({ providedIn: 'root' })
export class CompaniesService {

    constructor(private readonly networkService: NetworkService) { }

    public getActiveCompanyCodeByEntityId(entityId: number) {
        return this.networkService.get<CompanyCodeResponse>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COMPANIES.GET_COMPANY_CODE_BY_ENTITY_ID, { entityId }));
    }

    public getCompaniesCodeByEntityId(entityId: number, pagination: IPagination) {
        return this.networkService.get<CompanyCodesListResponse>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COMPANIES.GET_COMPANIES_BY_ENTITY_ID,
                { ...pagination, entityId: entityId })
        );
    }

    public createCompanyCode(requestPayload: CreateCompanyCodeRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.COMPANIES.CREATE_COMPANY_CODE,
            requestPayload
        );
    }

    public updateCompanyCode(requestPayload: UpdateCompanyCodeRequest) {
        return this.networkService.put(
            GlobalUrls.AFE_API.ADMIN.COMPANIES.UPDATE_COMPANY_CODE,
            requestPayload
        );
    }

    public getCostCentersByCompanyId(companyId: number, pagination: IPagination) {
        return this.networkService.get<any>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COST_CENTER.GET_COST_CENTERS_BY_COMPANY_ID,
                { ...pagination, companyId })
        );
    }

    public getCostCentersByEntityIds(entityIds: number[]) {
        return this.networkService.post<any>(GlobalUrls.AFE_API.GET_COST_CENTERS_BY_ENTITY_IDS, {
            entityIds
        });
    }

    public createCostCenter(requestPayload: CreateCostCenterRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.COST_CENTER.CREATE_COST_CENTER,
            requestPayload
        );
    }

    public updateCostCenter(requestPayload: UpdateCostCenterRequest) {
        return this.networkService.put(
            GlobalUrls.AFE_API.ADMIN.COST_CENTER.UPDATE_COST_CENTER,
            requestPayload
        );
    }

    public deleteCostCenterById(id: number) {
        return this.networkService.delete(ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COST_CENTER.DELETE_COST_CENTER_BY_ID, { id }));
    }

    public getAnalysisCodesByCompanyId(companyId: number, pagination: IPagination) {
        return this.networkService.get<any>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.ANALYSIS_CODE.GET_ANALYSIS_CODE_BY_COMPANY_ID,
                { ...pagination, companyId })
        );
    }

    public createAnalysisCode(requestPayload: CreateAnalysisCodeRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.ANALYSIS_CODE.CREATE_ANALYSIS_CODE,
            requestPayload
        );
    }

    public updateAnalysisCode(requestPayload: UpdateAnalysisCodeRequest) {
        return this.networkService.put(
            GlobalUrls.AFE_API.ADMIN.ANALYSIS_CODE.UPDATE_ANALYSIS_CODE,
            requestPayload
        );
    }

    public updateFusionIntegration(requestPayload: FussionIntegrationUpdateRequest) {
        return this.networkService.put(
            GlobalUrls.AFE_API.ADMIN.ANALYSIS_CODE.UPDATE_FUSION_INTEGRATION,
            requestPayload
        );
    }

    public deleteAnalysisCodeById(id: number) {
        return this.networkService.delete(ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.ANALYSIS_CODE.DELETE_ANALYSIS_CODE_BY_ID, { id }));
    }

    public getNaturalAccountNumberByCompanyId(companyId: number, pagination: IPagination) {
        return this.networkService.get<any>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.NATURAL_ACCOUNT_NUMBER.GET_NATURAL_ACCOUNT_NUMBER_BY_ENTITY_ID,
                { ...pagination, companyId })
        );
    }

    public createNaturalAccountNumber(requestPayload: CreateNaturalAccountNumberRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.NATURAL_ACCOUNT_NUMBER.CREATE_NATURAL_ACCOUNT_NUMBER,
            requestPayload
        );
    }

    public updateNaturalAccountNumber(requestPayload: UpdateNaturalAccountNumberRequest) {
        return this.networkService.put(
            GlobalUrls.AFE_API.ADMIN.NATURAL_ACCOUNT_NUMBER.UPDATE_NATURAL_ACCOUNT_NUMBER,
            requestPayload
        );
    }

    public deleteNaturalAccountNumberById(id: number) {
        return this.networkService.delete(ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.NATURAL_ACCOUNT_NUMBER.DELETE_NATURAL_ACCOUNT_NUMBER_ID, { id }));
    }

    public toggleActiveStateOfCompany(requestPayload: ToggleActiveStateCompanyCodeRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.COMPANIES.TOGGLE_COMPANY_ACTIVE_STATE,
            requestPayload
        );
    }

    public deactivateCompanyCode(companyId: number) {
        return this.networkService.post(ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COMPANIES.DEACTIVATE_COMPANY_CODE, { companyId }));
    }

    public updateMultiNaturalAccount(companyId: number, enableMultiNaturalAccount: boolean) {
        return this.networkService.post(GlobalUrls.AFE_API.ADMIN.COMPANIES.UPDATE_MULTI_NATURAL_ACCOUNT, { companyId, enableMultiNaturalAccount });
    }

    public importAnalysisCode(requestPayload: ImportRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.ANALYSIS_CODE.IMPORT_ANALYSIS_CODE,
            requestPayload
        );
    }

    public importNaturalAccount(requestPayload: ImportRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.NATURAL_ACCOUNT_NUMBER.IMPORT_NATURAL_ACCOUNT,
            requestPayload
        );
    }

    public importCostCenter(requestPayload: ImportRequest) {
        return this.networkService.post(
            GlobalUrls.AFE_API.ADMIN.COST_CENTER.IMPORT_COST_CENTER,
            requestPayload
        );
    }

    public getCompanyHistory(entityId: number) {
        return this.networkService.get<any>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COMPANIES.HISTORY_COMPANY,
                { entityId })
        );
    }

    public getCostCenterHistory(costCenterId: number) {
        return this.networkService.get<any>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COST_CENTER.HISTORY,
                { costCenterId })
        );
    }

    public updateUserLoginId(sourceLoginId: string, targetLoginId: string) {
        return this.networkService.post<any>(
            GlobalUrls.AFE_API.ADMIN.UPDATE_USER_LOGIN_ID,
            { sourceLoginId, targetLoginId }
        );
    }

    uploadEvidence(companyId: number, payload: Attachment[]) {
        return this.networkService.post(
            ReplaceUrlVariable(
                GlobalUrls.AFE_API.ADMIN.COMPANIES.EVIDENCE_UPLOAD,
                { companyId }
            ),
            {
                evidences: payload
            }
        );
    }

    getEvidences(companyId: number) {
        return this.networkService.get<any>(
            ReplaceUrlVariable(GlobalUrls.AFE_API.ADMIN.COMPANIES.GET_EVIDENCES,
                { companyId })
        );

    }
}

