import { AfeProposalLimitDeductionRepository } from 'src/afe-proposal/repositories';
import { Pagination } from 'src/core/pagination';
import { FinanceService } from 'src/finance/services';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper, SequlizeOperator } from 'src/shared/helpers';
import { IWorkFlowFilter } from 'src/shared/interfaces/filter.interface';
import { CurrentContext } from 'src/shared/types';
import { CopyStepRequestDto, DeleteStepRequestDto, GetMasterStepsResponseDTO, UpdateStepLimitShareRequestDTO } from '../dtos';
import { NewMasterStepRequestDto } from '../dtos/request/new-master-step-request.dto';
import { StepMovementRequestDto, UpdateStepRequestDto } from '../dtos/request/update-master-step-request.dto';
import { GetAggregateLimitbalanceDTO } from '../dtos/response/get-aggregate-limit-balance.dto';
import { GetChildSharedLimitWithParentDetailDTO } from '../dtos/response/get-child-shared-limit-response.dto';
import { GetExceptionStepsResponseDTO } from '../dtos/response/get-exception-steps-response.dto';
import { GetRoleBasedStepsResponse } from '../dtos/response/get-role-based-steps-response.dto';
import { WorkflowMasterSettingRepository, WorkflowMasterStepRepository, WorkflowSharedBucketLimitRepository, WorkflowSharedChildLimitRepository } from '../repositories';
import { StepPositionActionEnum } from '../types';
import { WorkflowSharedChildLimitService } from './workflow-shared-child-limit.service';
export declare class WorkflowMasterStepService {
    private readonly workflowMasterSettingRepository;
    private readonly workflowMasterStepRepository;
    private readonly adminApiClient;
    private readonly limitDeductionRepository;
    private readonly workflowSharedChildLimitRepository;
    private readonly afeProposalLimitDeductionRepository;
    private readonly workflowSharedChildLimitService;
    private readonly workflowSharedBucketLimitRepository;
    private readonly databaseHelper;
    private readonly sequlizeOperator;
    private readonly financeService;
    private readonly historyApiClient;
    constructor(workflowMasterSettingRepository: WorkflowMasterSettingRepository, workflowMasterStepRepository: WorkflowMasterStepRepository, adminApiClient: AdminApiClient, limitDeductionRepository: AfeProposalLimitDeductionRepository, workflowSharedChildLimitRepository: WorkflowSharedChildLimitRepository, afeProposalLimitDeductionRepository: AfeProposalLimitDeductionRepository, workflowSharedChildLimitService: WorkflowSharedChildLimitService, workflowSharedBucketLimitRepository: WorkflowSharedBucketLimitRepository, databaseHelper: DatabaseHelper, sequlizeOperator: SequlizeOperator, financeService: FinanceService, historyApiClient: HistoryApiClient);
    addNewWorkflowStep(newMasterStepRequestDto: NewMasterStepRequestDto, currentContext: CurrentContext): Promise<GetMasterStepsResponseDTO>;
    copyStepToOverridden(copyStepRequestDto: CopyStepRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    deleteStepFromOverridden(deleteStepRequestDto: DeleteStepRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
        overiddenWorkflowIds: any[];
    }>;
    updateWorkflowStep(updateWorkflowStepRequestDto: UpdateStepRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    deleteStep(stepId: number, currentContext: CurrentContext, isUnpublishedVersion?: boolean): Promise<MessageResponseDto>;
    stepMovement(stepId: number, stepMovementRequestDto: StepMovementRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    private moveStepUp;
    private moveStepDown;
    stepUpdateAllowedBasedOnLevel(workflowEntityLevel: any, stepEntityLevel: any): Promise<boolean>;
    validateLimitWithParentChild(newUpdateWorkflowStepRequestDto: any, stepDetail: any, workflowParentStepList?: any, isUnpublishedVersion?: boolean): Promise<boolean>;
    validateParentChildShareMovement(stepDetail: any, position: StepPositionActionEnum, isUnpublishedVersion?: boolean): Promise<void>;
    validateLimitWithParentChildMovement(stepDetail: any, position: StepPositionActionEnum, isUnpublishedVersion?: boolean): Promise<boolean>;
    validateAndUpdatePayload(requestPayload: any, workflowMasterSetting: any): Promise<any>;
    childLimitShareStep(stepId: number, updateStepLimitShareRequestDTO: UpdateStepLimitShareRequestDTO, currentContext: CurrentContext): Promise<{
        message: string;
        data: any;
    }>;
    getStepDetailWithId(stepId: number): Promise<GetChildSharedLimitWithParentDetailDTO>;
    getRoleBasedSteps(currentContext: CurrentContext, limit: number, page: number, filter: IWorkFlowFilter): Promise<Pagination<GetRoleBasedStepsResponse>>;
    getStepBalance(stepId: number, entityId: string | null, currentContext: CurrentContext): Promise<GetAggregateLimitbalanceDTO[]>;
    private getEntityBasedBalance;
    private getCostCenterBasedBalance;
    getWorkflowStepHistory(id: number): Promise<Record<string, any>>;
    getExceptionalOverriddenSteps(stepId: number): Promise<GetExceptionStepsResponseDTO[]>;
    private validateParentEntityHasSameLimit;
}
