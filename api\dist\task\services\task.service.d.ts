import { ParallelIdentifierRepository } from 'src/afe-config/repositories/parallel-identifier.repository';
import { AfeProposal, AfeProposalApprover } from 'src/afe-proposal/models';
import { AfeProposalAmountSplitRepository, AfeProposalApproverRepository, AfeProposalLimitDeductionRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { ConfigService } from 'src/config/config.service';
import { FinanceAdminService } from 'src/finance/services';
import { NotificationRepository } from 'src/notification/repositories';
import { QueueLogRepository } from 'src/queue/repositories';
import { AdminApiClient, HistoryApiClient, MSGraphApiClient, TaskApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { TASK_ACTION } from 'src/shared/enums';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedAttachmentService, SharedNotificationService } from 'src/shared/services';
import { CurrentContext, TaskData } from 'src/shared/types';
import { WorkflowService } from 'src/workflow/services';
import { CurrentTaskOfUserResponseDto, DelegateeRequestDto, PerformActionOnAfeProposalRequestDto } from '../dtos';
export declare class TaskService {
    private readonly taskApiClient;
    private readonly afeProposalRepository;
    private readonly afeProposalApproverRepository;
    private readonly afeProposalAmountSplitRepository;
    private readonly workflowService;
    private readonly configService;
    private readonly adminApiClient;
    private readonly afeProposalLimitDeductionRepository;
    private readonly databaseHelper;
    private readonly sharedAttachmentService;
    private readonly historyApiClient;
    private readonly notificationRepository;
    private readonly mSGraphApiClient;
    private readonly queueLogRepository;
    private readonly sharedNotificationService;
    private readonly financeAdminService;
    private readonly parallelIdentifierRepository;
    constructor(taskApiClient: TaskApiClient, afeProposalRepository: AfeProposalRepository, afeProposalApproverRepository: AfeProposalApproverRepository, afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository, workflowService: WorkflowService, configService: ConfigService, adminApiClient: AdminApiClient, afeProposalLimitDeductionRepository: AfeProposalLimitDeductionRepository, databaseHelper: DatabaseHelper, sharedAttachmentService: SharedAttachmentService, historyApiClient: HistoryApiClient, notificationRepository: NotificationRepository, mSGraphApiClient: MSGraphApiClient, queueLogRepository: QueueLogRepository, sharedNotificationService: SharedNotificationService, financeAdminService: FinanceAdminService, parallelIdentifierRepository: ParallelIdentifierRepository);
    performActionOnAfeAproposalTask(actionType: TASK_ACTION, performActionOnAfeProposalRequestDto: PerformActionOnAfeProposalRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    private createStepUniqueKey;
    private addNewWorkflowSteps;
    getCurrentTaskSteps(userId: string, approvers: AfeProposalApprover[]): Promise<AfeProposalApprover[] | null>;
    private createNotificationOnAfeApproval;
    private updateAfeProposalLimitDeduction;
    private getCurrentInprogressWorkflowSteps;
    private getLatestWorkflowApproversList;
    performApprovalAction(actionType: TASK_ACTION, afeDetails: AfeProposal, currentApprover: AfeProposalApprover, approvers: AfeProposalApprover[], currentContext: CurrentContext, task: TaskData, comments?: string, delegatee?: DelegateeRequestDto, assignedToAfeSubmitter?: boolean): Promise<void>;
    private cancelInprogressTasks;
    private createApprovalHistorty;
    changeAfeStatusToInprogress(afeProposalId: number, currentContext: CurrentContext, actionType?: TASK_ACTION): Promise<void>;
    private getUserDetails;
    createQueueLogOnApprovalAction(actionType: TASK_ACTION, afeDetails: AfeProposal, isNotALastStep: boolean, approvers: AfeProposalApprover[], currentContext: CurrentContext): Promise<void>;
    createNextTasks(afeProposalId: number, actionType: TASK_ACTION, currentContext: CurrentContext, isMailApprovalTask?: boolean, taskOriginalApproverId?: string, task?: TaskData, isFirstTimeCall?: boolean): Promise<void>;
    recreateTask(afeProposalId: number, proposalDetail: AfeProposal, approvalType?: string, isEmailApprover?: boolean): Promise<void>;
    private createTask;
    private createTaskAssignmentNotificationsForNextApprover;
    private createRelativeUrlForTask;
    private createFullUrlForTask;
    private createTaskTitle;
    private sendTaskAssignmentNotification;
    getAllPendingUserTasks(currentContext: CurrentContext, assignedTo?: string | null): Promise<Record<string, any>>;
    getTaskDetailById(id: number): Promise<Record<string, any>>;
    getCurrentTaskOfUserByAfeId(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<CurrentTaskOfUserResponseDto>;
    taskApprovalByTaskId(taskId: string, userId: string, action: TASK_ACTION): Promise<{
        message: string;
    }>;
    sendReminder(approverId: number, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
}
