"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Settings = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
let Settings = class Settings extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'key', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Settings.prototype, "key", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'settings', type: sequelize_typescript_1.DataType.HSTORE, allowNull: false }),
    __metadata("design:type", Object)
], Settings.prototype, "settings", void 0);
Settings = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'settings' })
], Settings);
exports.Settings = Settings;
//# sourceMappingURL=settings.model.js.map