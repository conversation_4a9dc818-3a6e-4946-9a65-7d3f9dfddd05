import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class CompanyCodeResponseDto {
    @ApiProperty({ type: Number })
    @Expose()
    public id: number;

    @ApiProperty({ type: String })
    @Expose()
    public code: string;

    @ApiProperty({ type: String })
    @Expose()
    public name: string;

    @ApiProperty({ type: Number })
    @Expose()
    public entityId: number;

    @ApiProperty({ type: String })
    @Expose()
    public entityCode: string;

    @ApiProperty({ type: String })
    @Expose()
    public entityType: string;

    @ApiProperty()
    @Expose()
    public fusionIntegrationForRequestTypeIds: number[];

    @ApiProperty({ type: Boolean })
    @Expose()
    public enableMultiNaturalAccount: boolean;
}