"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetChildSharedLimitWithParentDetailDTO = exports.GetChildSharedLimitResponseDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const get_master_setting_response_dto_1 = require("./get-master-setting-response.dto");
const get_master_steps_response_dto_1 = require("./get-master-steps-response.dto");
class GetChildSharedLimitResponseDTO {
    constructor(partial = {}) {
        Object.assign(this, partial);
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetChildSharedLimitResponseDTO.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetChildSharedLimitResponseDTO.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitResponseDTO.prototype, "entityCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitResponseDTO.prototype, "entityTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetChildSharedLimitResponseDTO.prototype, "singleLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetChildSharedLimitResponseDTO.prototype, "aggregateLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetChildSharedLimitResponseDTO.prototype, "createdOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetChildSharedLimitResponseDTO.prototype, "updatedOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitResponseDTO.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitResponseDTO.prototype, "updatedBy", void 0);
exports.GetChildSharedLimitResponseDTO = GetChildSharedLimitResponseDTO;
class GetChildSharedLimitWithParentDetailDTO {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitWithParentDetailDTO.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitWithParentDetailDTO.prototype, "associateLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitWithParentDetailDTO.prototype, "associateRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitWithParentDetailDTO.prototype, "associateType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitWithParentDetailDTO.prototype, "associatedColumn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitWithParentDetailDTO.prototype, "associatedUser", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetChildSharedLimitWithParentDetailDTO.prototype, "singleLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetChildSharedLimitWithParentDetailDTO.prototype, "aggregateLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetChildSharedLimitWithParentDetailDTO.prototype, "projectComponentTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Boolean }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], GetChildSharedLimitWithParentDetailDTO.prototype, "canShareLimitToChild", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Boolean }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], GetChildSharedLimitWithParentDetailDTO.prototype, "isMandatory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Boolean }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], GetChildSharedLimitWithParentDetailDTO.prototype, "includeBelowSteps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: get_master_setting_response_dto_1.GetMasterSettingResponseDTO }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", get_master_setting_response_dto_1.GetMasterSettingResponseDTO)
], GetChildSharedLimitWithParentDetailDTO.prototype, "workflowMasterSetting", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", get_master_steps_response_dto_1.GetMasterStepsResponseDTO)
], GetChildSharedLimitWithParentDetailDTO.prototype, "sharedLimitMasterStepChild", void 0);
exports.GetChildSharedLimitWithParentDetailDTO = GetChildSharedLimitWithParentDetailDTO;
//# sourceMappingURL=get-child-shared-limit-response.dto.js.map