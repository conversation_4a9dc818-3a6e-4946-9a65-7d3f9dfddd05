"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowRuleService = void 0;
const common_1 = require("@nestjs/common");
const pagination_1 = require("../../core/pagination");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const dtos_1 = require("../dtos");
const repositories_1 = require("../repositories");
let WorkflowRuleService = class WorkflowRuleService {
    constructor(workflowRuleRepository, workflowMasterStepRepository) {
        this.workflowRuleRepository = workflowRuleRepository;
        this.workflowMasterStepRepository = workflowMasterStepRepository;
    }
    createWorkflowRule(createWorkflowRuleRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const rule = yield this.workflowRuleRepository.createWorkflowRule(createWorkflowRuleRequestDto, currentContext);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CreateWorkflowRuleResponseDto, rule);
        });
    }
    deleteWorkflowRuleById(ruleId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const isRuleIdExistInWorkflowStep = yield this.workflowMasterStepRepository.hasRuleExistInWorkflowStep(ruleId);
            if (isRuleIdExistInWorkflowStep) {
                throw new exceptions_1.HttpException(`Rule can't be deleted, It exists in the workflow step.`, enums_1.HttpStatus.CONFLICT);
            }
            const result = yield this.workflowRuleRepository.deleteWorkflowRuleById(ruleId, currentContext);
            if (result) {
                return { message: 'Workflow rule has been deleted successfully.' };
            }
            throw new exceptions_1.HttpException('Unable to delete the workflow rule.', enums_1.HttpStatus.BAD_REQUEST);
        });
    }
    updateWorkflowRule(updateWorkflowRuleRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, title, description, rule } = updateWorkflowRuleRequestDto;
            const isRuleIdExists = yield this.workflowRuleRepository.isRuleIdExists(id);
            if (!isRuleIdExists) {
                throw new exceptions_1.HttpException('Rule has been deleted or does not exist.', enums_1.HttpStatus.BAD_REQUEST);
            }
            yield this.workflowRuleRepository.updateWorkflowRuleById(id, { title, description, rule }, currentContext);
            return { message: 'Workflow rule has been updated successfully.' };
        });
    }
    getWorkflowRuleById(ruleId) {
        return __awaiter(this, void 0, void 0, function* () {
            const rule = yield this.workflowRuleRepository.getWorkflowRuleById(ruleId);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.GetWorkflowRuleResponseDto, rule);
        });
    }
    getWorkflowRulesList(page = 0, limit = 10, searchTerm = '', noLimit = false) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowRuleRepository.getWorkflowRules(page, limit, searchTerm, noLimit);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.GetWorkflowRuleResponseDto, result.rows);
            return new pagination_1.Pagination({ records, total: result.count });
        });
    }
};
WorkflowRuleService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.WorkflowRuleRepository,
        repositories_1.WorkflowMasterStepRepository])
], WorkflowRuleService);
exports.WorkflowRuleService = WorkflowRuleService;
//# sourceMappingURL=workflow-rule.service.js.map