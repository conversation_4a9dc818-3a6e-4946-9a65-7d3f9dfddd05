"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PdfGeneratorService = void 0;
const common_1 = require("@nestjs/common");
const Handlebars = __importStar(require("handlebars"));
const puppeteer = __importStar(require("puppeteer"));
const fs = __importStar(require("fs"));
const path_1 = __importDefault(require("path"));
const helpers_1 = require("../shared/helpers");
const constants_1 = require("../shared/constants");
let PdfGeneratorService = class PdfGeneratorService {
    generatePdf(data, templateName) {
        return __awaiter(this, void 0, void 0, function* () {
            const templatePath = path_1.default.join(__dirname, `templates/${templateName}.hbs`);
            this.setCustomHandlebarsHelpers();
            const template = Handlebars.compile(fs.readFileSync(templatePath, 'utf8'));
            const html = template(data);
            const browser = yield puppeteer.launch({
                headless: true,
                args: ['--font-render-hinting=none', '--no-sandbox', '--disabled-setupid-sandbox']
            });
            const page = yield browser.newPage();
            yield page.setContent(html);
            const footerContent = `
            <div style="position: fixed; bottom: 0; left: 0; right: 0; text-align: center;">
                
                <img style="width: 100px; height: auto;" src="${constants_1.BASE64_DP_WORLD_LOGO}" alt="DP World Logo">
            </div>
        `;
            yield page.evaluate((footerContent) => {
                document.body.insertAdjacentHTML('beforeend', footerContent);
            }, footerContent);
            const pdf = yield page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '20px',
                    bottom: '20px',
                    left: '20px',
                    right: '20px'
                },
            });
            yield browser.close();
            return pdf;
        });
    }
    setCustomHandlebarsHelpers() {
        Handlebars.registerHelper('eq', (value1, value2) => {
            return value1 === value2;
        });
        Handlebars.registerHelper('neq', (value1, value2) => {
            return value1 !== value2;
        });
        Handlebars.registerHelper('or', (value1, value2) => {
            return value1 || value2;
        });
        Handlebars.registerHelper('and', (value1, value2) => {
            return value1 && value2;
        });
        Handlebars.registerHelper('contains', (str, substr) => {
            if (str.indexOf(substr) !== -1) {
                return true;
            }
            else {
                return false;
            }
        });
        Handlebars.registerHelper('formattedCurrency', (amount, currency) => {
            return (0, helpers_1.currencyFormatter)(amount, currency);
        });
        Handlebars.registerHelper('formattedDate', (date) => {
            return (0, helpers_1.formatDateString)(date);
        });
        Handlebars.registerHelper('newlineToBr', function (text) {
            if (!text)
                return '';
            text = Handlebars.Utils.escapeExpression(text);
            return new Handlebars.SafeString(text.replace(/\n/g, '<br>'));
        });
    }
};
PdfGeneratorService = __decorate([
    (0, common_1.Injectable)()
], PdfGeneratorService);
exports.PdfGeneratorService = PdfGeneratorService;
//# sourceMappingURL=pdf-generator.service.js.map