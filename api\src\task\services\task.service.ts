import { Injectable } from '@nestjs/common';
import _ from 'lodash';
import { ParallelIdentifierRepository } from 'src/afe-config/repositories/parallel-identifier.repository';
import { AfeProposal, AfeProposalApprover } from 'src/afe-proposal/models';
import {
	AfeProposalAmountSplitRepository,
	AfeProposalApproverRepository,
	AfeProposalLimitDeductionRepository,
	AfeProposalRepository,
} from 'src/afe-proposal/repositories';
import { LimitDeductionAfeData, UserDetail } from 'src/afe-proposal/types';
import { ConfigService } from 'src/config/config.service';
import { FinanceAdminService } from 'src/finance/services';
import { NotificationRepository } from 'src/notification/repositories';
import { QueueLogRepository } from 'src/queue/repositories';
import {
	AdminApiClient,
	HistoryApiClient,
	MSGraphApiClient,
	TaskApiClient,
} from 'src/shared/clients';
import {
	AFE_USER_STATUS,
	ATTACHMENT_REL_PATH,
	NOTIFICATION_TITLES,
	NOTIFICATION_URLS,
} from 'src/shared/constants';
import { MessageResponseDto } from 'src/shared/dtos';
import {
	AD_USER_TYPE,
	AFE_CATEGORY,
	AFE_LIMIT_DEDUCATION_STATUS,
	AFE_PROPOSAL_STATUS,
	AMOUNT_SPLIT,
	APPROVAL_ACTION_ID_TYPE,
	APPROVAL_TYPE,
	APPROVER_STATUS,
	ASSIGNED_TYPE,
	ASSOCIATED_COLUMN,
	ATTACHMENT_ENTITY_TYPE,
	BUDGET_TYPE,
	HISTORY_ACTION_TYPE,
	HISTORY_ENTITY_TYPE,
	HttpStatus,
	NOTIFICATION_ENTITY_TYPE,
	NOTIFICATION_TYPE,
	PERMISSIONS,
	QUEUE_LOG_ACTION,
	TASK_ACTION,
	TASK_ENTITY_TYPE,
} from 'src/shared/enums';
import { ASSOCIATED_TYPE } from 'src/shared/enums/associated-type.enum';
import { HttpException } from 'src/shared/exceptions';
import {
	DatabaseHelper,
	getNotificationExpiryDate,
	instanceToPlain,
	serializeBudgetBasedProjectAmountSplits,
	singleObjectToInstance,
	stringPlaceholderReplacer,
} from 'src/shared/helpers';
import {
	AFE_REQUEST_TYPE_ID_URL_MAPPING,
	TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING,
	TASK_ACTION_WITH_QUEUE_LOG_ACTION_MAPPING,
} from 'src/shared/mappings';
import { SharedAttachmentService, SharedNotificationService } from 'src/shared/services';
import {
	AddHistoryRequest,
	CreateTask,
	CurrentContext,
	NonAuthTokenUser,
	TaskData,
} from 'src/shared/types';
import {
	WorkflowResponseDto,
	ComputeAfeApproversListDto,
	AfeApproversStepsResponseDto,
	MasterSettingWorkflowsResponseDto,
} from 'src/workflow/dtos';
import { WorkflowService } from 'src/workflow/services';
import {
	CurrentTaskOfUserResponseDto,
	DelegateeRequestDto,
	PerformActionOnAfeProposalRequestDto,
	TaskDetailResponseDto,
} from '../dtos';
import { TASK_ACTION_WITH_EMAIL_TEMPLATE } from '../mappings/task-action-with-email-template.mapping';

@Injectable()
export class TaskService {
	constructor(
		private readonly taskApiClient: TaskApiClient,
		private readonly afeProposalRepository: AfeProposalRepository,
		private readonly afeProposalApproverRepository: AfeProposalApproverRepository,
		private readonly afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository,
		private readonly workflowService: WorkflowService,
		private readonly configService: ConfigService,
		private readonly adminApiClient: AdminApiClient,
		private readonly afeProposalLimitDeductionRepository: AfeProposalLimitDeductionRepository,
		private readonly databaseHelper: DatabaseHelper,
		private readonly sharedAttachmentService: SharedAttachmentService,
		private readonly historyApiClient: HistoryApiClient,
		private readonly notificationRepository: NotificationRepository,
		private readonly mSGraphApiClient: MSGraphApiClient,
		private readonly queueLogRepository: QueueLogRepository,
		private readonly sharedNotificationService: SharedNotificationService,
		private readonly financeAdminService: FinanceAdminService,
		private readonly parallelIdentifierRepository: ParallelIdentifierRepository,
	) { }

	public async performActionOnAfeAproposalTask(
		actionType: TASK_ACTION,
		performActionOnAfeProposalRequestDto: PerformActionOnAfeProposalRequestDto,
		currentContext: CurrentContext,
	): Promise<MessageResponseDto> {
		const { user } = currentContext;
		const { id, idType, attachments, comments, delegatee, assignedToAfeSubmitter, taskId } =
			performActionOnAfeProposalRequestDto;
		/**
		 * STEP 1: Find the type of id we have and if it is task Id
		 * then first find the Afe Proposal id for that task
		 */
		let afeProposalId: number;
		let task: TaskData;
		if (idType === APPROVAL_ACTION_ID_TYPE.TASK) {
			task = await this.taskApiClient.getTaskById(id);
			if (!task || task.task_status !== 'Not Started') {
				throw new HttpException(`Task doesn't exist with this id.`, HttpStatus.BAD_REQUEST);
			}
			afeProposalId = task.additional_info.proposal_id;
		} else {
			afeProposalId = id;
		}

		if (
			!delegatee &&
			(actionType === TASK_ACTION.DELEGATE || actionType === TASK_ACTION.REASSIGNE)
		) {
			throw new HttpException(`Delegatee details are missing.`, HttpStatus.BAD_REQUEST);
		}

		if (actionType === TASK_ACTION.MORE_DETAIL && !delegatee && !assignedToAfeSubmitter) {
			throw new HttpException(
				`Either delegatee or afe submitter is required.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		/**
		 * STEP 2: Find approvers step list by afe proposal id
		 */
		const approvers = await this.afeProposalApproverRepository.getApproversByProposalId(
			afeProposalId,
		);
		const { steps: latestWorkflowSteps, masterSettingWorkflows } =
			await this.getLatestWorkflowApproversList(afeProposalId);
		const afeDetails = await this.afeProposalRepository.getAfeProposalById(afeProposalId);

		//Update user details in approvers table with latest user details.
		const alreadyTraverseApproverIds = new Set();
		const updateUsersDetails = latestWorkflowSteps
			.map(step => {
				const approver = approvers.find(
					a =>
						step?.stepId &&
						a.workflowMasterStepsId === step?.stepId &&
						!alreadyTraverseApproverIds.has(a.id),
				);
				if (!approver) {
					return null;
				}
				alreadyTraverseApproverIds.add(approver.id);
				return {
					id: approver.id,
					user: step.approvers,
					assignedTo:
						(step.associateType === ASSOCIATED_TYPE.COST_CENTER ||
							step.associateType === ASSOCIATED_TYPE.USER) &&
							step?.approvers?.length
							? step.approvers[step.approvers.length - 1].loginId
							: null,
				};
			})
			.filter(step => step !== null);

		await this.afeProposalApproverRepository.updateUserDetailsByApproverIds(
			updateUsersDetails,
			currentContext,
		);

		// Attachment validation to be added.
		if (attachments?.length) {
			this.sharedAttachmentService.validateAttachment(attachments);
		}

		//Start database transaction to update multiple tables atomically.
		await this.databaseHelper.startTransaction(async () => {
			const updatedApprovers = await this.addNewWorkflowSteps(
				approvers,
				latestWorkflowSteps,
				currentContext,
			);
			await this.updateAfeProposalLimitDeduction(
				afeProposalId,
				masterSettingWorkflows,
				currentContext,
			);
			/**
			 * STEP 3: Validate the user has right to perform the action
			 */
			let userCurrentStep: AfeProposalApprover = null;
			if (taskId) {
				const { assigned_to, is_group_assignment, entity_id, business_entity_id } = task;

				if (is_group_assignment) {
					const isUserInGroup = await this.adminApiClient.hasUserRole(
						user.username,
						assigned_to,
						business_entity_id,
					);
					if (!isUserInGroup) {
						throw new HttpException(
							`User doesn't has permission to perform this action.`,
							HttpStatus.FORBIDDEN,
						);
					}
				} else if (assigned_to?.toLowerCase() !== user.username.toLowerCase()) {
					throw new HttpException(
						`User doesn't has permission to perform this action.`,
						HttpStatus.FORBIDDEN,
					);
				}
				userCurrentStep = updatedApprovers.find(approver => approver.id === entity_id);

				if (!userCurrentStep || userCurrentStep?.actionStatus !== APPROVER_STATUS.IN_PROGRESS) {
					throw new HttpException(
						`User doesn't has permission to perform this action.`,
						HttpStatus.FORBIDDEN,
					);
				}
			} else {
				const currentSteps = await this.getCurrentTaskSteps(user.username, updatedApprovers);
				if (!currentSteps.length) {
					throw new HttpException(
						`User doesn't has permission to perform this action.`,
						HttpStatus.FORBIDDEN,
					);
				}
				userCurrentStep = currentSteps[currentSteps.length - 1];
			}

			/**
			 * Check if user as permission to perform action from current tasks by task ID.
			 */
			const { otherInfo } = userCurrentStep;
			if (
				(actionType === TASK_ACTION.MORE_DETAIL_SUBMITTED &&
					otherInfo?.approvalType !== APPROVAL_TYPE.MORE_DETAIL) ||
				(actionType === TASK_ACTION.DISCARD &&
					otherInfo?.approvalType !== APPROVAL_TYPE.RESUBMISSION)
			) {
				throw new HttpException(`Can't perform this action on this task.`, HttpStatus.BAD_REQUEST);
			}

			/**
			 * STEP 4: Perform action on the task.
			 */
			await this.performApprovalAction(
				actionType,
				afeDetails,
				userCurrentStep,
				updatedApprovers,
				currentContext,
				task,
				comments,
				delegatee,
				assignedToAfeSubmitter,
			);

			/**
			 * STEP 5: Upload the attachments for the approver task if any.
			 */
			if (attachments?.length) {
				await this.sharedAttachmentService.addBulkAttachment(
					attachments,
					userCurrentStep.id,
					ATTACHMENT_ENTITY_TYPE.AFE_ACTION,
					ATTACHMENT_REL_PATH.AFE_ACTION,
					user.username,
					afeProposalId,
				);
			}
		});
		return { message: 'Action has been executed successfully.' };
	}

	/**
	 * Creating unique key for the workflow step.
	 * @param associatedLevel
	 * @param associateRole
	 * @param associateType
	 * @param associatedColumn
	 * @returns
	 */
	private createStepUniqueKey(
		associatedLevel: string,
		associateRole: string,
		associateType: ASSOCIATED_TYPE,
		associatedColumn: ASSOCIATED_COLUMN,
	): string {
		return `associatedLevel:${associatedLevel || ''}#associateRole:${associateRole || ''
			}#associateType:${associateType || ''}#associatedColumn:${associatedColumn || ''}`;
	}

	/**
	 * Add new approvers if there is any.
	 * @param approvers
	 * @param latestWorkflowSteps
	 * @param currentContext
	 */
	private async addNewWorkflowSteps(
		approvers: AfeProposalApprover[],
		latestWorkflowSteps: AfeApproversStepsResponseDto[],
		currentContext: CurrentContext,
	): Promise<AfeProposalApprover[]> {
		let {
			assignedLevel,
			associatedColumn,
			assignedTo,
			assginedType,
			afeProposalId,
			approvalSequence: lastApproverSequence,
		} = approvers[approvers.length - 1];
		const lastStepkey = this.createStepUniqueKey(
			assignedLevel,
			assignedTo,
			assginedType,
			associatedColumn,
		);
		let newStepsIndex = null;

		for (let i = 0; i < latestWorkflowSteps.length; i++) {
			const { associateLevel, associatedColumn, associateRole, associateType } =
				latestWorkflowSteps[i];
			const stepKey = this.createStepUniqueKey(
				associateLevel,
				associateRole,
				associateType,
				associatedColumn,
			);
			if (lastStepkey === stepKey) {
				newStepsIndex = i + 1;
			}
		}

		const currentStepsSet = new Set();
		for (let i = 0; i < approvers.length; i++) {
			const { assignedLevel, associatedColumn, assignedTo, assginedType } = approvers[i];
			const key = this.createStepUniqueKey(
				assignedLevel,
				assignedTo,
				assginedType,
				associatedColumn,
			);
			currentStepsSet.add(key);
		}

		const newApprovers = [];
		if (newStepsIndex) {
			for (let i = newStepsIndex; i < latestWorkflowSteps.length; i++) {
				const {
					associateLevel,
					associatedColumn,
					associateRole,
					associateType,
					approvers,
					parallelIdentifier,
					title,
					stepId,
					associatedCostCenterId,
					section,
					associatedLevelEntityId,
				} = latestWorkflowSteps[i];
				const stepKey = this.createStepUniqueKey(
					associateLevel,
					associateRole,
					associateType,
					associatedColumn,
				);
				if (!currentStepsSet.has(stepKey)) {
					lastApproverSequence += 1;
					const approver = {
						assignedTo: associateRole || approvers[approvers.length - 1].loginId,
						title: title,
						afeProposalId: afeProposalId,
						userDetail: null,
						otherInfo: {
							usersDetail: approvers,
							approvalType: APPROVAL_TYPE.APPROVAL,
							...(associateType === ASSOCIATED_TYPE.COST_CENTER && section && { section: section }),
						},
						assignedLevel: associateLevel,
						assginedType: associateType || ASSIGNED_TYPE.USER,
						associatedColumn: associatedColumn,
						parallelIdentifier: parallelIdentifier,
						approvalSequence: lastApproverSequence,
						workflowMasterStepsId: stepId,
						associatedCostCenterId: associatedCostCenterId || null,
						assignedEntityId: associatedLevelEntityId,
					};
					newApprovers.push(approver);
				}
			}
		}

		if (newApprovers.length) {
			await this.afeProposalApproverRepository.bulkApproversInsert(newApprovers, currentContext);
		}
		return this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
	}

	/**
	 * Check if user with given role as right to perform action on the current task.
	 * @param userId
	 * @param approvers
	 * @returns
	 */
	public async getCurrentTaskSteps(
		userId: string,
		approvers: AfeProposalApprover[],
	): Promise<AfeProposalApprover[] | null> {
		const userInprogressSteps: AfeProposalApprover[] = [];
		const currentWorkflowSteps = this.getCurrentInprogressWorkflowSteps(approvers);
		for (const step of currentWorkflowSteps) {
			const { assignedTo, assginedType, assignedEntityId } = step;
			if (
				(assginedType === ASSOCIATED_TYPE.COST_CENTER || assginedType === ASSOCIATED_TYPE.USER) &&
				assignedTo.toLowerCase() === userId
			) {
				userInprogressSteps.push(step);
			} else if (assginedType === ASSOCIATED_TYPE.ROLE) {
				const hasUserRole = await this.adminApiClient.hasUserRole(
					userId,
					assignedTo,
					assignedEntityId,
				);
				if (hasUserRole) {
					userInprogressSteps.push(step);
				}
			} else {
				continue;
			}
		}
		return userInprogressSteps;
	}

	private async createNotificationOnAfeApproval(
		taskAction: TASK_ACTION,
		afeDetails: AfeProposal,
		currentContext: CurrentContext,
		comments: string,
		assignedToAfeSubmitter: boolean,
		delegateeDetails: DelegateeRequestDto,
	) {
		const url = `${stringPlaceholderReplacer(NOTIFICATION_URLS.AFE_DETAILS_URL, {
			afeId: `${afeDetails.id}`,
		})}`;

		const expireAt = getNotificationExpiryDate();
		const description = comments ? `With comments: ${comments}` : null;
		const commonPayload = { expireAt, url, description };

		const { user } = currentContext;
		const { name: approverName, username: approverId } = user;
		const {
			data,
			projectReferenceNumber: afeReferenceNumber,
			submitterId,
			subscribers,
		} = afeDetails;
		let submitterDetails: UserDetail = data.submitterDetails;
		let submitterName = `${submitterDetails.firstName} ${submitterDetails.lastName}`;
		let notifications = [];

		switch (taskAction) {
			case TASK_ACTION.APPROVE:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.SUCCESS,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.APPROVE.APPROVER,
							{ afeReferenceNumber },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.SUCCESS,
						subscribers: [submitterId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.APPROVE.SUBMITTER,
							{ afeReferenceNumber, approverName },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.SUCCESS,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.APPROVE.SUBSCRIBER,
							{ afeReferenceNumber, approverName },
						),
					},
				];
				break;
			case TASK_ACTION.DELEGATE:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.APPROVER,
							{
								afeReferenceNumber,
								delegateeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: [submitterId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.SUBMITTER,
							{
								afeReferenceNumber,
								approverName,
								delegateeName: assignedToAfeSubmitter
									? 'you'
									: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.SUBSCRIBER,
							{
								afeReferenceNumber,
								approverName,
								delegateeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: [delegateeDetails.loginId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.DELEGATEE,
							{ afeReferenceNumber, approverName },
						),
					},
				];
				break;
			case TASK_ACTION.REJECT:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.REJECT,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REJECT.APPROVER,
							{ afeReferenceNumber },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.REJECT,
						subscribers: [submitterId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REJECT.SUBMITTER,
							{ afeReferenceNumber, approverName },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.REJECT,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REJECT.SUBSCRIBER,
							{ afeReferenceNumber, approverName },
						),
					},
				];
				break;
			case TASK_ACTION.MORE_DETAIL:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.APPROVER,
							{
								afeReferenceNumber,
								delegateeName: assignedToAfeSubmitter
									? submitterName
									: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: [submitterId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.SUBMITTER,
							{
								afeReferenceNumber,
								approverName,
								delegateeName: assignedToAfeSubmitter
									? 'you'
									: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.SUBSCRIBER,
							{
								afeReferenceNumber,
								approverName,
								delegateeName: assignedToAfeSubmitter
									? submitterName
									: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
				];
				if (!assignedToAfeSubmitter) {
					notifications.push({
						...commonPayload,
						type: NOTIFICATION_TYPE.INFO,
						subscribers: [delegateeDetails.loginId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.DELEGATEE,
							{ afeReferenceNumber, approverName },
						),
					});
				}
				break;
			case TASK_ACTION.MORE_DETAIL_SUBMITTED:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.SUCCESS,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL_SUBMITTED.APPROVER,
							{ afeReferenceNumber },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.SUCCESS,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL_SUBMITTED.SUBSCRIBER,
							{ afeReferenceNumber, approverName },
						),
					},
				];
				if (submitterId !== approverId) {
					notifications.push({
						...commonPayload,
						type: NOTIFICATION_TYPE.SUCCESS,
						subscribers: [submitterId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL_SUBMITTED.SUBMITTER,
							{ afeReferenceNumber, approverName },
						),
					});
				}
				break;
			case TASK_ACTION.SEND_BACK:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.WARNING,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.SEND_BACK.APPROVER,
							{ afeReferenceNumber, submitterName: submitterName },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.WARNING,
						subscribers: [submitterId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.SEND_BACK.SUBMITTER,
							{ afeReferenceNumber, approverName },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.WARNING,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.SEND_BACK.SUBSCRIBER,
							{ afeReferenceNumber, approverName, submitterName: submitterName },
						),
					},
				];
				break;
			case TASK_ACTION.DISCARD:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.REJECT,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DISCARD.APPROVER,
							{ afeReferenceNumber },
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.REJECT,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DISCARD.SUBSCRIBER,
							{ afeReferenceNumber, approverName },
						),
					},
				];
				break;
			case TASK_ACTION.REASSIGNE:
				notifications = [
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.WARNING,
						subscribers: [approverId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.APPROVER,
							{
								afeReferenceNumber,
								assigneeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.WARNING,
						subscribers: [submitterId],
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.SUBMITTER,
							{
								afeReferenceNumber,
								assigneeName:
									delegateeDetails.loginId === submitterId
										? 'you'
										: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
								approverName,
							},
						),
					},
					{
						...commonPayload,
						type: NOTIFICATION_TYPE.WARNING,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.SUBSCRIBER,
							{
								afeReferenceNumber,
								approverName,
								assigneeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
							},
						),
					},
				];
				if (delegateeDetails.loginId !== submitterId) {
					notifications.push({
						...commonPayload,
						type: NOTIFICATION_TYPE.WARNING,
						subscribers: subscribers,
						title: stringPlaceholderReplacer(
							NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.DELEGATEE,
							{ afeReferenceNumber, approverName },
						),
					});
				}
				break;
		}
		await this.notificationRepository.bulkNotificationsInsert(notifications, currentContext);
	}

	/**
	 * Add the new afe proposal deduction entries and cancel the invalid entries.
	 * @param afeProposalId
	 * @param newMasterSettingWorkflows
	 * @param currentContext
	 */
	private async updateAfeProposalLimitDeduction(
		afeProposalId: number,
		newMasterSettingWorkflows: MasterSettingWorkflowsResponseDto[],
		currentContext: CurrentContext,
	): Promise<void> {
		const currentDeductionEntries =
			await this.afeProposalLimitDeductionRepository.getCurrentLimitDeductionsForAfeProposal(
				afeProposalId,
			);

		const cancelEntriesIds = currentDeductionEntries
			.filter(entry => {
				const index = newMasterSettingWorkflows.findIndex(
					e =>
						e.deductionStepId === entry.workflowMasterStepId &&
						e.workflowMasterSettingId === entry.workflowMasterSettingId,
				);
				return index === -1;
			})
			.map(e => e.id);

		const afeProposal = await this.afeProposalRepository.getAfeProposalById(afeProposalId);
		const limitDeductionAfeData: LimitDeductionAfeData = {
			afeReferenceNumber: afeProposal.projectReferenceNumber,
			projectName: afeProposal.name,
			requestTypeId: afeProposal.afeRequestTypeId,
			isSupplemental: afeProposal.category === AFE_CATEGORY.SUPPLEMENTAL,
			submitterId: afeProposal.submitterId,
			budgetType: afeProposal.budgetType || null,
			year: afeProposal.workflowYear,
			isApprovedByBoard: afeProposal.isApprovedByBoard,
			parentAfeId: afeProposal.parentAfeId || null,
		};

		const newdeductionEntries = newMasterSettingWorkflows
			.filter(entry => {
				const index = currentDeductionEntries.findIndex(
					e =>
						e.workflowMasterStepId === entry.deductionStepId &&
						e.workflowMasterSettingId === entry.workflowMasterSettingId,
				);
				return index === -1;
			})
			.map(e => ({
				afeProposalId: afeProposalId,
				entityId: e.associatedEntityId,
				entityTitle: e.associatedEntityTitle,
				entityCode: e.associatedEntityCode,
				workflowMasterSettingId: e.workflowMasterSettingId,
				workflowMasterStepId: e.deductionStepId,
				costCenterId: e.costCenterId,
				amount: e.amount,
				data: limitDeductionAfeData,
				status: AFE_LIMIT_DEDUCATION_STATUS.IN_PROGRESS,
			}));

		if (cancelEntriesIds.length) {
			await this.afeProposalLimitDeductionRepository.changeStatusByIds(
				cancelEntriesIds,
				AFE_LIMIT_DEDUCATION_STATUS.CANCELLED,
				currentContext,
			);
		}

		if (newdeductionEntries.length) {
			await this.afeProposalLimitDeductionRepository.bulkInsertAfeProposalLimitDeductions(
				newdeductionEntries,
				currentContext,
			);

			const updatedDeductions =
				await this.afeProposalLimitDeductionRepository.getCurrentLimitDeductionsForAfeProposal(
					afeProposalId,
				);

			for (const deduction of updatedDeductions) {
				const workflowDeduction = newMasterSettingWorkflows.find(
					workflow => workflow.deductionStepId === deduction.workflowMasterStepId,
				);
				if (workflowDeduction?.parentDeductionStepId) {
					const parentDeduction = updatedDeductions.find(
						d =>
							d.workflowMasterStepId === workflowDeduction.parentDeductionStepId &&
							d.amount === workflowDeduction.amount,
					);
					if (parentDeduction) {
						await this.afeProposalLimitDeductionRepository.updateParentDeductionId(
							deduction.id,
							parentDeduction.id,
							currentContext,
						);
					}
				}
			}
		}
	}

	/**
	 * Get current workflow step where the task is in progress.
	 * @param approvers
	 * @returns
	 */
	private getCurrentInprogressWorkflowSteps(
		approvers: AfeProposalApprover[],
	): AfeProposalApprover[] {
		const inProgressSteps = [];
		for (const approver of approvers) {
			if (approver.actionStatus === APPROVER_STATUS.IN_PROGRESS) {
				inProgressSteps.push(approver);
			}
		}
		return inProgressSteps;
	}

	/**
	 * Compute the latest or updated list of approvers for the AFE.
	 * @param afeProposalId
	 * @returns
	 */
	private async getLatestWorkflowApproversList(
		afeProposalId: number,
	): Promise<WorkflowResponseDto> {
		const afeProposal = await this.afeProposalRepository.getAfeProposalById(afeProposalId);
		const {
			afeRequestTypeId,
			budgetType,
			isApprovedByBoard,
			entityId,
			yearOfCommitment: lengthOfCommitment,
			totalAmount,
			workflowYear,
			data,
			parentAfeId,
			category,
			submitterId,
		} = afeProposal;

		const computeApproversListPayload: ComputeAfeApproversListDto = {
			requestTypeId: afeRequestTypeId,
			budgetType: budgetType,
			entityId: entityId,
			totalAmount: totalAmount,
			isApprovedByBoard: isApprovedByBoard,
			lengthOfCommitment,
			year: workflowYear,
			afeSubType: data.subType || null,
			afeType: data.type || null,
			parentAfeId: parentAfeId || null,
			isSupplemental: category === AFE_CATEGORY.SUPPLEMENTAL,
			projectLeaderId: data?.projectDetails?.projectLeader?.loginId || null,
		};

		const projectComponentSplit =
			await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(
				afeProposalId,
				AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT,
			);
		if (projectComponentSplit?.length) {
			computeApproversListPayload.projectComponentSplits = projectComponentSplit.map(split => ({
				id: split.objectId,
				title: split.objectTitle,
				amount: split.amount,
				currency: split.currency,
			}));
		}

		const budgetTypeSplit =
			await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(
				afeProposalId,
				AMOUNT_SPLIT.BUDGET_TYPE_SPLIT,
			);
		if (budgetTypeSplit?.length) {
			computeApproversListPayload.budgetTypeSplits = budgetTypeSplit.map(split => ({
				id: split.objectId,
				title: split.objectTitle as BUDGET_TYPE,
				amount: split.amount,
				currency: split.currency,
			}));
		}

		const costCenterSplit =
			await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(
				afeProposalId,
				AMOUNT_SPLIT.COST_CENTER_SPLIT,
			);
		if (costCenterSplit?.length) {
			computeApproversListPayload.costCenters = costCenterSplit
				.map(split => ({
					id: split.objectId,
					code: split.objectTitle,
					amount: split.amount,
					section: split?.additionalInfo?.section || null,
				}))
				.filter(split => split.id !== 0);
		}

		const projectComponentSplitByBudgetType =
			await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(
				afeProposalId,
				AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT,
			);

		if (projectComponentSplitByBudgetType?.length && budgetType === BUDGET_TYPE.MIXED) {
			computeApproversListPayload.budgetBasedProjectSplit = serializeBudgetBasedProjectAmountSplits(
				projectComponentSplitByBudgetType,
				false,
			);
		}

		//Get the lastest or updated list of approvers
		return this.workflowService.computeAfeApproversList(computeApproversListPayload, submitterId);
	}

	/**
	 * Perform action on the task according to TASK_ACTION.
	 * @param actionType
	 * @param approvers
	 * @param currentApprover
	 * @param currentContext
	 * @param delegatee
	 */
	public async performApprovalAction(
		actionType: TASK_ACTION,
		afeDetails: AfeProposal,
		currentApprover: AfeProposalApprover,
		approvers: AfeProposalApprover[],
		currentContext: CurrentContext,
		task: TaskData,
		comments?: string,
		delegatee?: DelegateeRequestDto,
		assignedToAfeSubmitter?: boolean,
	) {
		const { id, afeProposalId, approvalSequence } = currentApprover;
		let actionPerformed: HISTORY_ACTION_TYPE;
		let canCallNextTask: boolean = true;
		let isNotALastStep: boolean = true;
		let originalApproverDetail: UserDetail = null;
		const { original_owner: originalApproverId, delegated_task_type: delegateTaskType } = task;
		if (originalApproverId && delegateTaskType === 'representative') {
			originalApproverDetail = await this.getUserDetails(originalApproverId);
		}

		let delegateeDetails: UserDetail = null;
		if (delegatee) {
			delegateeDetails = await this.getUserDetails(delegatee.loginId);
		}

		switch (actionType) {
			case TASK_ACTION.APPROVE:
			case TASK_ACTION.AUTO_APPROVE:
				const approvalStatus =
					TASK_ACTION.AUTO_APPROVE === actionType
						? APPROVER_STATUS.AUTO_APPROVED
						: APPROVER_STATUS.APPROVED;

				await this.afeProposalApproverRepository.updateApproverStatusOnAction(
					id,
					approvalStatus,
					currentContext,
					originalApproverDetail,
					comments,
				);

				isNotALastStep =
					await this.afeProposalApproverRepository.isAnyNotInitiatedAndInprogressApproverExist(
						afeProposalId,
					);

				if (!isNotALastStep) {
					await this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(
						afeProposalId,
						AFE_LIMIT_DEDUCATION_STATUS.APPROVED,
						currentContext,
					);
					const companyDetail = await this.financeAdminService.getEntityActiveCompanyCodeDetails(
						afeDetails.entityId,
						false,
					);

					const isFusionIntegrationEnabled =
						companyDetail?.fusionIntegrationForRequestTypeIds?.includes(
							afeDetails.afeRequestTypeId,
						);

					await this.afeProposalRepository.changeStatus(
						afeProposalId,
						AFE_PROPOSAL_STATUS.APPROVED,
						AFE_USER_STATUS.APPROVED,
						currentContext,
						isFusionIntegrationEnabled,
					);
					canCallNextTask = false;
				}

				actionPerformed =
					TASK_ACTION.AUTO_APPROVE === actionType
						? HISTORY_ACTION_TYPE.AUTO_APPROVED
						: HISTORY_ACTION_TYPE.APPROVED;
				break;
			case TASK_ACTION.REJECT:
				await this.afeProposalApproverRepository.updateApproverStatusOnAction(
					id,
					APPROVER_STATUS.REJECTED,
					currentContext,
					originalApproverDetail,
					comments,
				);
				await this.afeProposalRepository.changeStatus(
					afeProposalId,
					AFE_PROPOSAL_STATUS.REJECTED,
					AFE_USER_STATUS.REJECTED,
					currentContext,
				);
				await this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(
					afeProposalId,
					AFE_LIMIT_DEDUCATION_STATUS.REJECTED,
					currentContext,
				);
				canCallNextTask = false;
				actionPerformed = HISTORY_ACTION_TYPE.REJECTED;
				this.cancelInprogressTasks(afeProposalId, currentContext);
				break;
			case TASK_ACTION.DELEGATE:
				await this.afeProposalApproverRepository.updateApproverStatusOnAction(
					id,
					APPROVER_STATUS.DELEGATED,
					currentContext,
					originalApproverDetail,
					comments,
				);
				await this.afeProposalApproverRepository.incrementSequenceNumber(
					afeProposalId,
					approvalSequence,
					1,
				);
				const { loginId: delegateeLoginId } = delegatee;
				const delegateApprovalStep = {
					assignedTo: delegateeLoginId.toLowerCase(),
					title: delegateeDetails?.title || delegateeLoginId.toLowerCase(),
					afeProposalId: afeProposalId,
					userDetail: null,
					otherInfo: { usersDetail: [delegateeDetails], approvalType: APPROVAL_TYPE.APPROVAL },
					assignedLevel: null,
					assginedType: ASSIGNED_TYPE.USER,
					associatedColumn: null,
					parallelIdentifier: currentApprover.parallelIdentifier,
					approvalSequence: approvalSequence + 1,
					workflowMasterStepsId: null,
					assignedEntityId: null,
				};
				await this.afeProposalApproverRepository.createAfeProposalApprover(
					delegateApprovalStep,
					currentContext,
				);
				actionPerformed = HISTORY_ACTION_TYPE.DELEGATED;
				break;
			case TASK_ACTION.MORE_DETAIL:
			case TASK_ACTION.REASSIGNE:
				const status =
					actionType === TASK_ACTION.MORE_DETAIL
						? APPROVER_STATUS.MORE_DETAIL
						: APPROVER_STATUS.REASSIGNED;
				await this.afeProposalApproverRepository.updateApproverStatusOnAction(
					id,
					status,
					currentContext,
					originalApproverDetail,
					comments,
				);

				await this.afeProposalApproverRepository.incrementSequenceNumber(
					afeProposalId,
					approvalSequence,
					2,
				);

				/**
				 * Creating an entry for the assignee in the approvers table.
				 */
				let assigneeDetails: UserDetail[] = [delegateeDetails];
				let assigneeLoginId: string;
				let title: string;
				if (assignedToAfeSubmitter) {
					assigneeLoginId = afeDetails.submitterId;
					assigneeDetails = [afeDetails.data.submitterDetails];
					title = afeDetails.data?.submitterDetails?.title || afeDetails.submitterId.toLowerCase();
				} else {
					assigneeLoginId = delegatee.loginId;
					title = delegateeDetails?.title || delegatee.loginId.toLowerCase();
				}
				const assigneeApprovalStep = {
					assignedTo: assigneeLoginId.toLowerCase(),
					title: title,
					afeProposalId: afeProposalId,
					userDetail: null,
					otherInfo: {
						usersDetail: assigneeDetails,
						approvalType:
							actionType === TASK_ACTION.MORE_DETAIL
								? APPROVAL_TYPE.MORE_DETAIL
								: APPROVAL_TYPE.APPROVAL,
					},
					assignedLevel: null,
					assginedType: ASSIGNED_TYPE.USER,
					associatedColumn: null,
					parallelIdentifier: currentApprover.parallelIdentifier,
					approvalSequence: currentApprover.approvalSequence + 1,
					workflowMasterStepsId: null,
					assignedEntityId: null,
				};
				const assignee = await this.afeProposalApproverRepository.createAfeProposalApprover(
					assigneeApprovalStep,
					currentContext,
				);

				const asssignerApprovalStep = {
					assignedTo: currentApprover.assignedTo,
					title: currentApprover.title,
					afeProposalId: afeProposalId,
					userDetail: null,
					otherInfo: currentApprover.otherInfo,
					assignedLevel: currentApprover.assignedLevel,
					assginedType: currentApprover.assginedType,
					associatedColumn: currentApprover.associatedColumn,
					parallelIdentifier: null,
					approvalSequence: currentApprover.approvalSequence + 2,
					workflowMasterStepsId: currentApprover.workflowMasterStepsId,
					assignedEntityId: currentApprover.assignedEntityId,
				};
				const assigner = await this.afeProposalApproverRepository.createAfeProposalApprover(
					asssignerApprovalStep,
					currentContext,
				);
				await this.afeProposalApproverRepository.updateOriginalApproverId(
					assignee.id,
					assigner.id,
					currentContext,
				);

				actionPerformed =
					actionType === TASK_ACTION.MORE_DETAIL
						? HISTORY_ACTION_TYPE.MORE_DETAIL
						: HISTORY_ACTION_TYPE.REASSIGNED;
				break;
			case TASK_ACTION.MORE_DETAIL_SUBMITTED:
				await this.afeProposalApproverRepository.updateApproverStatusOnAction(
					id,
					APPROVER_STATUS.MORE_DETAIL_SUBMITTED,
					currentContext,
					originalApproverDetail,
					comments,
				);
				actionPerformed = HISTORY_ACTION_TYPE.MORE_DETAIL_SUBMITTED;
				break;
			case TASK_ACTION.SEND_BACK:
				await this.afeProposalRepository.changeStatus(
					afeProposalId,
					AFE_PROPOSAL_STATUS.SENT_BACK,
					AFE_USER_STATUS.SENT_BACK,
					currentContext,
				);
				await this.afeProposalApproverRepository.updateApproverStatusOnAction(
					id,
					APPROVER_STATUS.SEND_BACK,
					currentContext,
					originalApproverDetail,
					comments,
				);
				await this.cancelInprogressTasks(afeProposalId, currentContext);
				const afeCreator = await this.afeProposalRepository.getAfeProposalById(afeProposalId);
				const { submitterId: afeCreatorId } = afeCreator;
				const afeCreatorApprover = {
					assignedTo: afeCreatorId.toLowerCase(),
					title: afeCreatorId,
					afeProposalId: afeProposalId,
					userDetail: null,
					otherInfo: {
						usersDetail: [{ loginId: afeCreatorId }],
						approvalType: APPROVAL_TYPE.RESUBMISSION,
					},
					assignedLevel: null,
					assginedType: ASSIGNED_TYPE.USER,
					associatedColumn: null,
					parallelIdentifier: null,
					approvalSequence: currentApprover.approvalSequence,
					workflowMasterStepsId: null,
					assignedEntityId: null,
				};
				await this.afeProposalApproverRepository.createAfeProposalApprover(
					afeCreatorApprover,
					currentContext,
				);
				actionPerformed = HISTORY_ACTION_TYPE.SENT_BACK;
				break;
			case TASK_ACTION.DISCARD:
				await this.afeProposalApproverRepository.updateApproverStatusOnAction(
					id,
					APPROVER_STATUS.DISCARDED,
					currentContext,
					originalApproverDetail,
					comments,
				);
				actionPerformed = HISTORY_ACTION_TYPE.DISCARDED;
				await this.afeProposalRepository.changeStatus(
					afeProposalId,
					AFE_PROPOSAL_STATUS.CANCELLED,
					AFE_USER_STATUS.CANCELLED,
					currentContext,
				);
				await this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(
					afeProposalId,
					AFE_LIMIT_DEDUCATION_STATUS.CANCELLED,
					currentContext,
				);
				canCallNextTask = false;
				break;
			default:
				throw new HttpException('Invalid action type.', HttpStatus.BAD_REQUEST);
		}

		/**
		 * All createNextTasks function to assign task to next approver
		 * if current step is not the last step.
		 */
		const isMailApprovalTask = actionType !== TASK_ACTION.MORE_DETAIL;
		if (canCallNextTask) {
			await this.createNextTasks(
				afeProposalId,
				actionType,
				currentContext,
				isMailApprovalTask,
				originalApproverId,
				task,
				false,
			);
		}

		if (actionType !== TASK_ACTION.AUTO_APPROVE) {
			//Check if any approver is not initiated or inprogress
			isNotALastStep =
				await this.afeProposalApproverRepository.isAnyNotInitiatedAndInprogressApproverExist(
					afeProposalId,
				);
			/**
			 * Add approval action on AFE to history.
			 */
			await this.createNotificationOnAfeApproval(
				actionType,
				afeDetails,
				currentContext,
				comments,
				assignedToAfeSubmitter,
				delegatee,
			);
			await this.afeProposalRepository.addContextUserToSubscriberList(
				afeDetails.id,
				currentContext,
			);

			await this.createQueueLogOnApprovalAction(
				actionType,
				afeDetails,
				isNotALastStep,
				approvers,
				currentContext,
			);

			//Send Notification on task action completion.
			const submitterDetails = await this.mSGraphApiClient.getUserDetails(afeDetails.submitterId);
			const config = this.configService.getAppConfig();
			const placeholdersValues = {
				afeDetailLink: `${config.uiClient.baseUrl}/afe/afe-detail/${afeDetails.id}`,
				approvers: `${currentContext.user.name}`,
				approver: `${currentApprover.title}`,
				comments: comments || '-',
				...(delegatee && { delegatee: `${delegatee.firstName} ${delegatee.lastName}` }),
			};

			if (submitterDetails?.mail) {
				await this.sharedNotificationService.sendNotificationForAfeProposal(
					afeDetails.id,
					currentApprover.id,
					NOTIFICATION_ENTITY_TYPE.AFE_TASK_APPROVAL_NOTIFICATION,
					{ to: [submitterDetails.mail] },
					TASK_ACTION_WITH_EMAIL_TEMPLATE[actionType],
					false,
					placeholdersValues,
				);
			}

			//Final Approval notification.
			if (!isNotALastStep && submitterDetails?.mail) {
				await this.sharedNotificationService.sendNotificationForAfeProposal(
					afeDetails.id,
					currentApprover.id,
					NOTIFICATION_ENTITY_TYPE.AFE_APPROVAL_NOTIFICATION,
					{ to: [submitterDetails.mail] },
					'AFE.TASK.APPROVAL.FINAL_APPROVAL',
					false,
					placeholdersValues,
				);
			}

			await this.createApprovalHistorty(
				afeProposalId,
				actionPerformed,
				comments,
				currentContext,
				originalApproverDetail,
			);
		}

		/**
		 * Mark all the pending task as completed.
		 */
		await this.taskApiClient.completeAllTasks({
			entity_id: id,
			entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
			outcome: actionType,
		});
	}

	/**
	 * Cancel inprogress tasks.
	 * @param afeProposalId
	 * @param currentContext
	 */
	private async cancelInprogressTasks(afeProposalId: number, currentContext: CurrentContext) {
		const { user } = currentContext;
		const approvers =
			await this.afeProposalApproverRepository.getInProgressApproversListByProposalId(
				afeProposalId,
			);
		if (approvers?.length) {
			let approverIds = [];

			for (let i = 0; i < approvers.length; i++) {
				approverIds.push(approvers[i].id);

				await this.taskApiClient.cancelAllTasks({
					entity_id: approvers[i].id,
					entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
					comments: 'Cancelled',
					created_by: user.username.toLowerCase(),
				});
			}

			await this.afeProposalApproverRepository.updateStatusOnActionByIds(
				approverIds,
				APPROVER_STATUS.DISCARDED,
				currentContext,
				'Cancelled',
			);
		}
	}

	/**
	 * Create Approval History for approval action.
	 * @param afeProposalId
	 * @param actionPerformed
	 * @param comments
	 * @param currentContext
	 * @param originalApproverDetail
	 */
	private async createApprovalHistorty(
		afeProposalId: number,
		actionPerformed: HISTORY_ACTION_TYPE,
		comments: string,
		currentContext: CurrentContext,
		originalApproverDetail?: UserDetail,
	) {
		const addHistoryPayload: AddHistoryRequest = {
			created_by: currentContext.user.username,
			entity_id: afeProposalId,
			entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
			action_performed: actionPerformed,
			comments: comments,
		};
		if (originalApproverDetail) {
			await Promise.all([
				this.historyApiClient.addRequestHistory({
					...addHistoryPayload,
					created_by: originalApproverDetail.loginId,
				}),
				this.historyApiClient.addRequestHistory({
					...addHistoryPayload,
					created_by: currentContext.user.username,
					additional_info: { hidden: true },
				}),
			]);
		} else {
			await this.historyApiClient.addRequestHistory(addHistoryPayload);
		}
	}

	/**
	 * Change AFE status to in progress if any approver is in progress.
	 * @param afeProposalId
	 */
	public async changeAfeStatusToInprogress(
		afeProposalId: number,
		currentContext: CurrentContext,
		actionType?: TASK_ACTION,
	): Promise<void> {
		const approvers = await this.afeProposalApproverRepository.getApproversByProposalId(
			afeProposalId,
		);
		const inProgressStep = approvers.find(
			approver => approver.actionStatus === APPROVER_STATUS.IN_PROGRESS,
		);
		if (inProgressStep) {
			const parallelIdentifiers =
				await this.parallelIdentifierRepository.getAllParallelIdentifiers();
			let afeUserStatus = inProgressStep.title;
			if (inProgressStep?.parallelIdentifier) {
				const parallelIdentifier = parallelIdentifiers.find(
					parallelIdentifier => parallelIdentifier.identifier === inProgressStep.parallelIdentifier,
				);
				afeUserStatus = parallelIdentifier?.title || afeUserStatus;
			}

			await this.afeProposalRepository.changeStatus(
				afeProposalId,
				TASK_ACTION.SEND_BACK === actionType
					? AFE_PROPOSAL_STATUS.SENT_BACK
					: AFE_PROPOSAL_STATUS.IN_PROGRESS,
				`Pending with ${afeUserStatus}`,
				currentContext,
			);
		}
	}

	/**
	 * Get user details from graph api.
	 * @param userId
	 * @returns
	 */
	private async getUserDetails(userId: string): Promise<UserDetail> {
		const {
			givenName: firstName,
			surname: lastName,
			userPrincipalName: upn,
			userType,
			mail: email,
			jobTitle: title,
		} = await this.mSGraphApiClient.getUserDetails(userId);
		const loginId = userType == AD_USER_TYPE.GUEST ? email.toLowerCase() : upn.toLowerCase();
		return { firstName, lastName, loginId, email, title };
	}

	/**
	 * Create queue log on any approval action.
	 * @param actionType
	 * @param afeDetails
	 * @param isNotALastStep
	 * @param approvers
	 * @param currentContext
	 */
	public async createQueueLogOnApprovalAction(
		actionType: TASK_ACTION,
		afeDetails: AfeProposal,
		isNotALastStep: boolean,
		approvers: AfeProposalApprover[],
		currentContext: CurrentContext,
	): Promise<void> {
		const {
			id: proposalId,
			entityId,
			budgetType,
			totalAmount,
			afeRequestTypeId,
			data,
		} = afeDetails;
		const splits = await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndTypes(
			proposalId,
			[AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT, AMOUNT_SPLIT.COST_CENTER_SPLIT],
		);
		const entityParents = await this.adminApiClient.getParentsOfEntity(entityId);

		const logPayload = {
			entityId: entityId,
			action: TASK_ACTION_WITH_QUEUE_LOG_ACTION_MAPPING[actionType],
			finalApproval: !isNotALastStep || actionType === TASK_ACTION.REJECT,
			data: {
				proposalId: proposalId,
				budgetType: budgetType,
				totalAmount: totalAmount,
				requestTypeId: afeRequestTypeId,
				afeType: data.type,
				afeSubType: data.subType,
				projectComponentId:
					splits
						?.filter(p => p.type === AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT)
						?.map(p => p.objectId) || null,
				costCenterId:
					splits?.filter(c => c.type === AMOUNT_SPLIT.COST_CENTER_SPLIT)?.map(c => c.objectId) ||
					null,
				approversLevel: [...new Set(approvers.map(step => step.assignedLevel))].filter(a => !!a),
				businessUnitHierarchy: entityParents.map(entity => ({
					id: entity.id,
					code: entity.code,
					level: entity.entity_type,
				})),
				status: !isNotALastStep
					? QUEUE_LOG_ACTION.FINAL_APPROVAL
					: TASK_ACTION_WITH_QUEUE_LOG_ACTION_MAPPING[actionType],
			},
		};
		await this.queueLogRepository.createQueueLogEntry(logPayload, currentContext);
	}

	/**
	 * Create next task for the AFE proposal approvers.
	 * @param afeProposalId
	 */
	public async createNextTasks(
		afeProposalId: number,
		actionType: TASK_ACTION,
		currentContext: CurrentContext,
		isMailApprovalTask: boolean = true,
		taskOriginalApproverId?: string,
		task?: TaskData,
		isFirstTimeCall = true,
	): Promise<void> {
		

		const approvers = await this.afeProposalApproverRepository.getApproversByProposalId(
			afeProposalId,
		);
		

		const afeProposal = await this.afeProposalRepository.getAfeProposalById(afeProposalId);

		

		let currentParallelIdentifier = null;
		let i = 0;
		let isAnyParallelApproverInProgress: boolean = false;

		while (i < approvers.length) {
			const {
				id: currentApproverId,
				parallelIdentifier,
				actionStatus,
				originalApproverId,
				otherInfo,
			} = approvers[i];
			

			/**
			 * Will not create next task if there is any approver status in progress without parallel identifier.
			 */
			if (actionStatus === APPROVER_STATUS.IN_PROGRESS && !parallelIdentifier) {
				break;
			}

			/**
			 * Set flag if there is any parallel approver status is in-progress.
			 */
			if (actionStatus === APPROVER_STATUS.IN_PROGRESS && parallelIdentifier) {
				isAnyParallelApproverInProgress = true;
			}
			

			if (
				actionStatus === APPROVER_STATUS.IN_PROGRESS &&
				parallelIdentifier &&
				(currentParallelIdentifier === null || currentParallelIdentifier === parallelIdentifier)
			) {
				currentParallelIdentifier = parallelIdentifier;

				if (
					(actionType === TASK_ACTION.APPROVE || actionType === TASK_ACTION.AUTO_APPROVE) &&
					otherInfo.usersDetail.some(
						user =>
							user.loginId.toLowerCase() ===
							(taskOriginalApproverId?.toLowerCase() ||
								currentContext.user.username.toLowerCase()) && !isFirstTimeCall,
					)
				) {
					
					await this.performApprovalAction(
						TASK_ACTION.AUTO_APPROVE,
						afeProposal,
						approvers[i],
						approvers,
						currentContext,
						task,
						'Auto Approved',
					);
					

				}

				i += 1;
				continue;
			}
			

			if (
				actionStatus === APPROVER_STATUS.NOT_INITIATED &&
				(currentParallelIdentifier === null || currentParallelIdentifier === parallelIdentifier)
			) {
				

				const dependentApprover = approvers.find(a => a.originalApproverId === currentApproverId);
				if (dependentApprover?.actionStatus === APPROVER_STATUS.IN_PROGRESS) {
					i += 1;
					continue;
				}
				

				/**
				 * Not allow to create next task for next not-initiated and non-parallel
				 * approver if any if the parallel approver status in-progress
				 */
				if (!parallelIdentifier && isAnyParallelApproverInProgress) {
					break;
				}

				if (parallelIdentifier !== null) {
					if (
						(actionType === TASK_ACTION.APPROVE || actionType === TASK_ACTION.AUTO_APPROVE) &&
						otherInfo.usersDetail.some(
							user =>
								user.loginId.toLowerCase() ===
								(taskOriginalApproverId?.toLowerCase() ||
									currentContext.user.username.toLowerCase()) && !isFirstTimeCall,
						)
					) {
						await this.performApprovalAction(
							TASK_ACTION.AUTO_APPROVE,
							afeProposal,
							approvers[i],
							approvers,
							currentContext,
							task,
							'Auto Approved',
						);
					} else {
						await this.createTask(
							afeProposal,
							approvers[i],
							afeProposal.entityId,
							currentContext,
							isMailApprovalTask,
							TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING[actionType],
							actionType,
						);
					}
					/**
					 * Skip original approver task step which can't be inprogress until the
					 * delegatee approver doesn't take the action on his assigned task.
					 */
					i =
						originalApproverId &&
							i + 1 < approvers.length &&
							approvers[i + 1].id === originalApproverId
							? i + 2
							: i + 1;
					currentParallelIdentifier = parallelIdentifier;
					continue;
				}
				if (
					(actionType === TASK_ACTION.APPROVE || actionType === TASK_ACTION.AUTO_APPROVE) &&
					otherInfo.usersDetail.some(
						user =>
							user.loginId.toLowerCase() ===
							(taskOriginalApproverId?.toLowerCase() ||
								currentContext.user.username.toLowerCase()) && !isFirstTimeCall,
					)
				) {
					

					await this.performApprovalAction(
						TASK_ACTION.AUTO_APPROVE,
						afeProposal,
						approvers[i],
						approvers,
						currentContext,
						task,
						'Auto Approved',
					);
					

				} else {
					await this.createTask(
						afeProposal,
						approvers[i],
						afeProposal.entityId,
						currentContext,
						isMailApprovalTask,
						TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING[actionType],
						actionType,
					);
					

				}
				break;
			}

			

			i += 1;

		}

		

	}

	public async recreateTask(
		afeProposalId: number,
		proposalDetail: AfeProposal,
		approvalType = TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING.APPROVE,
		isEmailApprover: boolean = false,
	) {
		const approvers = await this.afeProposalApproverRepository.getApproversByProposalId(
			afeProposalId,
		);

		let i = 0;

		while (i < approvers.length) {
			const {
				id: currentApproverId,
				assignedTo,
				assginedType,
				assignedLevel,
				actionStatus,
				otherInfo,
			} = approvers[i];

			if (actionStatus === APPROVER_STATUS.IN_PROGRESS) {
				const { uiClient } = this.configService.getAppConfig();

				let businessEntityId = proposalDetail.entityId;
				if (assginedType !== ASSOCIATED_TYPE.COST_CENTER && assginedType !== ASSOCIATED_TYPE.USER) {
					const associatedLevelEntity =
						await this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(
							proposalDetail.entityId,
							assignedLevel,
						);
					businessEntityId = associatedLevelEntity.id;
				}

				const payload: CreateTask = {
					title: this.createTaskTitle(
						proposalDetail.projectReferenceNumber,
						otherInfo.approvalType,
					),
					assigned_to: assignedTo,
					entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
					entity_id: `${currentApproverId}`,
					is_group_assignemnt: assginedType === ASSOCIATED_TYPE.ROLE,
					business_entity_id: businessEntityId,
					base_url: uiClient.baseUrl,
					rel_url: this.createRelativeUrlForTask(otherInfo.approvalType, proposalDetail),
					additional_info: {
						proposal_id: proposalDetail.id,
						proposal_project_reference_number: proposalDetail.projectReferenceNumber,
						proposal_created_by: proposalDetail.submitterId,
						proposal_request_type: proposalDetail.afeRequestType.title,
						proposal_entity: proposalDetail.entityTitle,
						proposal_submitted_on: proposalDetail.createdOn,
						proposal_project_name: proposalDetail.name,
						approval_type: approvalType,
					},
					delegate_filter_param: {
						request_type_id: proposalDetail.afeRequestTypeId,
					},
				};

				const tasks = await this.taskApiClient.createTaskWithUseDelegation(payload);

				const delegateeUserIds = tasks
					.filter(task => task.type === 'delegate' && task.assigned_to?.delegate_to_username)
					.map(task => task.assigned_to.delegate_to_username);
				let delegateeUsersDetails: UserDetail[] = [];
				if (delegateeUserIds.length) {
					const usersAdDetails = await this.mSGraphApiClient.getUsersDetails(delegateeUserIds);
					delegateeUsersDetails = usersAdDetails.map(user => ({
						firstName: user.givenName,
						lastName: user.surname,
						email: user?.mail?.toLowerCase() || null,
						title: user.jobTitle || null,
						loginId:
							user.userType === AD_USER_TYPE.GUEST
								? user.mail.toLowerCase()
								: user.userPrincipalName.toLowerCase(),
					}));
				}
				for (const task of tasks) {
					let users: UserDetail[] = [];
					if (task.type === 'delegate') {
						const delegatee = delegateeUsersDetails.find(
							user => task.assigned_to?.delegate_to_username === user.loginId,
						);
						if (delegatee) {
							users = [delegatee];
						} else {
							continue;
						}
					} else {
						users = otherInfo.usersDetail;
					}
					const fullTaskLink = this.createFullUrlForTask(
						otherInfo.approvalType,
						proposalDetail,
						task.task_id,
					);
					await this.sendTaskAssignmentNotification(
						users,
						proposalDetail,
						fullTaskLink,
						isEmailApprover, // isMailApprovalTask
						task.task_id,
						'',
					);
				}
			}

			i++;
		}
	}

	/**
	 * Create task for the user.
	 * @param approver
	 * @param entityId
	 * @param afeProposalId
	 * @param currentContext
	 */
	private async createTask(
		afeProposal: AfeProposal,
		approver: AfeProposalApprover,
		entityId: number,
		currentContext: CurrentContext,
		isMailApprovalTask: boolean,
		approvalType: string,
		actionType?: TASK_ACTION,
	) {
		const { id, assignedTo, assginedType, assignedLevel, otherInfo } = approver;
		const existingTasks = await this.taskApiClient.getAllTasks(
			id,
			TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
		);

		

		if (existingTasks?.length) {
			await this.afeProposalApproverRepository.updateApproverStatus(
				id,
				APPROVER_STATUS.IN_PROGRESS,
				currentContext,
			);

			await this.changeAfeStatusToInprogress(afeProposal.id, currentContext, actionType);
			return;
		}
		

		const { uiClient } = this.configService.getAppConfig();
		/**
		 * In case of cost center the business entity id always be BU id and for other the associated level entity id.
		 */
		let businessEntityId = entityId;
		if (assginedType !== ASSOCIATED_TYPE.COST_CENTER && assginedType !== ASSOCIATED_TYPE.USER) {
			const associatedLevelEntity = await this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(
				entityId,
				assignedLevel,
			);
			businessEntityId = associatedLevelEntity.id;
		}
		

		const payload: CreateTask = {
			title: this.createTaskTitle(afeProposal.projectReferenceNumber, otherInfo.approvalType),
			assigned_to: assignedTo,
			entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
			entity_id: `${id}`,
			is_group_assignemnt: assginedType === ASSOCIATED_TYPE.ROLE,
			business_entity_id: businessEntityId,
			base_url: uiClient.baseUrl,
			rel_url: this.createRelativeUrlForTask(otherInfo.approvalType, afeProposal),
			additional_info: {
				proposal_id: afeProposal.id,
				proposal_project_reference_number: afeProposal.projectReferenceNumber,
				proposal_created_by: afeProposal.submitterId,
				proposal_request_type: afeProposal.afeRequestType.title,
				proposal_entity: afeProposal.entityTitle,
				proposal_submitted_on: afeProposal.createdOn,
				proposal_project_name: afeProposal.name,
				approval_type: approvalType,
			},
			delegate_filter_param: {
				request_type_id: afeProposal.afeRequestTypeId,
			},
		};
		

		await this.afeProposalApproverRepository.updateApproverStatus(
			id,
			APPROVER_STATUS.IN_PROGRESS,
			currentContext,
		);
		

		await this.changeAfeStatusToInprogress(afeProposal.id, currentContext, actionType);
		

		const tasks = await this.taskApiClient.createTaskWithUseDelegation(payload);
		

		const delegateeUserIds = tasks
			.filter(task => task.type === 'delegate' && task.assigned_to?.delegate_to_username)
			.map(task => task.assigned_to.delegate_to_username);

		

		let delegateeUsersDetails: UserDetail[] = [];
		if (delegateeUserIds.length) {
			const usersAdDetails = await this.mSGraphApiClient.getUsersDetails(delegateeUserIds);
			delegateeUsersDetails = usersAdDetails.map(user => ({
				firstName: user.givenName,
				lastName: user.surname,
				email: user?.mail?.toLowerCase() || null,
				title: user.jobTitle || null,
				loginId:
					user.userType === AD_USER_TYPE.GUEST
						? user.mail.toLowerCase()
						: user.userPrincipalName.toLowerCase(),
			}));
		}

		

		for (const task of tasks) {
			let users: UserDetail[] = [];
			

			if (task.type === 'delegate') {
				const delegatee = delegateeUsersDetails.find(
					user => task.assigned_to?.delegate_to_username === user.loginId,
				);
				if (delegatee) {
					users = [delegatee];
				} else {
					continue;
				}
			} else {
				users = otherInfo.usersDetail;
			}

			

			const fullTaskLink = this.createFullUrlForTask(
				otherInfo.approvalType,
				afeProposal,
				task.task_id,
			);

			

			await this.sendTaskAssignmentNotification(
				users,
				afeProposal,
				fullTaskLink,
				isMailApprovalTask,
				task.task_id,
				'',
			);
			

			await this.createTaskAssignmentNotificationsForNextApprover(
				{
					id: afeProposal.id,
					afeReferenceNumber: afeProposal.projectReferenceNumber,
					requestTypeId: afeProposal.afeRequestTypeId,
				},
				task.task_id,
				users.map(user => user.loginId.toLowerCase()),
				currentContext,
				actionType,
			);

			

		}
	}

	/**
	 * Create AFE task assignment notification for next approver.
	 * @param afeDetails
	 * @param taskId
	 * @param subscribers
	 * @param currentContext
	 */
	private async createTaskAssignmentNotificationsForNextApprover(
		afeDetails: { id: number; afeReferenceNumber: string; requestTypeId: number },
		taskId: number,
		subscribers: string[],
		currentContext: CurrentContext,
		actionType: TASK_ACTION | null = null,
	): Promise<void> {
		let url = `${stringPlaceholderReplacer(NOTIFICATION_URLS.AFE_TASK_URL, {
			afeId: `${afeDetails.id}`,
			taskId: `${taskId}`,
		})}`;

		if (actionType && actionType === TASK_ACTION.SEND_BACK) {
			url = `${stringPlaceholderReplacer(NOTIFICATION_URLS.AFE_SEND_BACK_URL, {
				afeRequestType: `${AFE_REQUEST_TYPE_ID_URL_MAPPING[afeDetails.requestTypeId]}`,
				afeId: `${afeDetails.id}`,
				taskId: `${taskId}`,
			})}`;
		}

		const expireAt = getNotificationExpiryDate();
		const type = NOTIFICATION_TYPE.INFO;
		const payload = {
			title: stringPlaceholderReplacer(NOTIFICATION_TITLES.AFE_TASK_ASSIGNMENT, {
				afeReferenceNumber: afeDetails.afeReferenceNumber,
			}),
			url: url,
			subscribers: subscribers,
			expireAt,
			type,
		};
		await this.notificationRepository.createNotification(payload, currentContext);
	}

	/**
	 * Create relative url.
	 * @param approvalType
	 * @param afeProposal
	 * @returns
	 */
	private createRelativeUrlForTask(approvalType: APPROVAL_TYPE, afeProposal: AfeProposal): string {
		switch (approvalType) {
			case APPROVAL_TYPE.RESUBMISSION:
				return `/afe/submit-afe/${AFE_REQUEST_TYPE_ID_URL_MAPPING[afeProposal.afeRequestTypeId]}/${afeProposal.id
					}?taskId={taskId}#resubmit`;
			case APPROVAL_TYPE.MORE_DETAIL:
			case APPROVAL_TYPE.APPROVAL:
				return `/mytask/task/${afeProposal.id}?taskId={taskId}`;
		}
	}

	/**
	 * Create full task url.
	 * @param approvalType
	 * @param afeProposal
	 * @param taskId
	 * @returns
	 */
	private createFullUrlForTask(
		approvalType: APPROVAL_TYPE,
		afeProposal: AfeProposal,
		taskId: number,
	): string {
		const config = this.configService.getAppConfig();
		switch (approvalType) {
			case APPROVAL_TYPE.RESUBMISSION:
				return `${config.uiClient.baseUrl}/afe/submit-afe/${AFE_REQUEST_TYPE_ID_URL_MAPPING[afeProposal.afeRequestTypeId]
					}/${afeProposal.id}?taskId=${taskId}#resubmit`;
			case APPROVAL_TYPE.MORE_DETAIL:
			case APPROVAL_TYPE.APPROVAL:
				return `${config.uiClient.baseUrl}/mytask/task/${afeProposal.id}?taskId=${taskId}`;
		}
	}

	/**
	 * Create task title
	 * @param projectReferenceNumber
	 * @param approvalType
	 * @returns
	 */
	private createTaskTitle(projectReferenceNumber: string, approvalType: APPROVAL_TYPE): string {
		if (approvalType === APPROVAL_TYPE.MORE_DETAIL) {
			return `${projectReferenceNumber} - More Detail Required`;
		} else if (approvalType === APPROVAL_TYPE.RESUBMISSION) {
			return `${projectReferenceNumber} - AFE Resubmission Required`;
		} else {
			return `${projectReferenceNumber} - Approval Required`;
		}
	}

	/**
	 * Send task assignment notification.
	 * @param email
	 * @param proposalId
	 * @param taskId
	 */
	private async sendTaskAssignmentNotification(
		usersDetail: UserDetail[],
		afeProposal: AfeProposal,
		taskLink: string,
		isMailApprovalTask: boolean,
		approvalTaskId: number,
		subjectPrefix: string = '',
	): Promise<void> {

		
		const receivers = usersDetail.map(user => user.email).filter(user => !!user);

		

		const { id: proposalId } = afeProposal;

		const config = this.configService.getAppConfig();
		const placeholdersValues = {
			taskLink: taskLink,
			afeDetailLink: `${config.uiClient.baseUrl}/afe/afe-detail/${proposalId}`,
		};
		

		if (receivers.length) {
			

			await this.sharedNotificationService.sendNotificationForAfeProposal(
				afeProposal.id,
				afeProposal.id,
				NOTIFICATION_ENTITY_TYPE.AFE_PROPOSAL_TASK_NOTIFICATION,
				{ to: receivers },
				isMailApprovalTask ? 'AFE.TASK.ASSIGNMENT.APPROVER' : 'AFE.TASK.ASSIGNMENT.OTHER',
				isMailApprovalTask,
				placeholdersValues,
				approvalTaskId,
				subjectPrefix,
			);

			

		}
	}

	/**
	 * Return all the pending tasks of an user.
	 * @param currentContext
	 * @returns
	 */
	public async getAllPendingUserTasks(
		currentContext: CurrentContext,
		assignedTo: string | null = null,
	): Promise<Record<string, any>> {
		let { username } = currentContext.user;

		if (assignedTo) {
			const hasAdminPermission = await this.adminApiClient.hasPermissionToUser(
				username,
				PERMISSIONS.AFE_ADMINISTRATION,
			);

			if (!hasAdminPermission) {
				throw new HttpException(
					`You don't have permission to search task for other users.`,
					HttpStatus.UNAUTHORIZED,
				);
			}

			username = assignedTo;
		}

		const data = await this.taskApiClient.getAllPendingUserTasks(username);
		return data.map(d => instanceToPlain(new TaskDetailResponseDto(d)));
	}

	/**
	 * Get task details by its id.
	 * @param id
	 * @returns
	 */
	public async getTaskDetailById(id: number): Promise<Record<string, any>> {
		const task = await this.taskApiClient.getTaskById(id);
		return instanceToPlain(new TaskDetailResponseDto(task));
	}

	/**
	 * Get the current in-progress task of the user.
	 * @param afeProposalId
	 * @param currentContext
	 * @param taskId
	 */
	public async getCurrentTaskOfUserByAfeId(
		afeProposalId: number,
		currentContext: CurrentContext,
		taskId?: number,
	): Promise<CurrentTaskOfUserResponseDto> {
		const { username } = currentContext.user;
		if (taskId) {
			const task = await this.taskApiClient.getTaskById(taskId);
			if (!task || task?.task_status !== 'Not Started') {
				throw new HttpException(`Task not found.`, HttpStatus.NOT_FOUND);
			}
			const { assigned_to, is_group_assignment, entity_id, business_entity_id, additional_info } =
				task;
			if (afeProposalId !== +additional_info?.proposal_id) {
				throw new HttpException(
					`Task doesn't belong to correct AFE Proposal'`,
					HttpStatus.BAD_REQUEST,
				);
			}
			if (is_group_assignment) {
				const isUserInGroup = await this.adminApiClient.hasUserRole(
					username,
					assigned_to,
					business_entity_id,
				);
				if (!isUserInGroup) {
					throw new HttpException(
						`User doesn't has permission to perform this action.`,
						HttpStatus.FORBIDDEN,
					);
				}
			} else if (assigned_to !== username) {
				throw new HttpException(
					`User doesn't has permission to perform this action.`,
					HttpStatus.FORBIDDEN,
				);
			}

			const step = await this.afeProposalApproverRepository.getApproverById(entity_id);
			if (step.actionStatus !== APPROVER_STATUS.IN_PROGRESS) {
				throw new HttpException(`Task is not in progress.`, HttpStatus.BAD_REQUEST);
			}
			return singleObjectToInstance(CurrentTaskOfUserResponseDto, {
				id: step.id,
				approvalType: step.otherInfo.approvalType,
			});
		} else {
			const approversList = await this.afeProposalApproverRepository.getApproversByProposalId(
				afeProposalId,
			);
			const currentUserSteps = await this.getCurrentTaskSteps(
				currentContext.user.username,
				approversList,
			);
			if (!currentUserSteps.length) {
				throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
			}

			for (let step of currentUserSteps) {
				const { id, otherInfo } = step;
				if (taskId) {
					const task = await this.taskApiClient.getTaskById(taskId);
					if (task.entity_id === id) {
						return singleObjectToInstance(CurrentTaskOfUserResponseDto, {
							id: id,
							approvalType: otherInfo.approvalType,
						});
					}
				}
			}
			throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
		}
	}

	/**
	 * Perform approval action by taskId.
	 * @param taskId
	 * @param userId
	 * @param action
	 * @returns
	 */
	public async taskApprovalByTaskId(taskId: string, userId: string, action: TASK_ACTION) {
		const task = await this.taskApiClient.getTaskById(+taskId);

		if (!task || task?.task_status !== 'Not Started') {
			throw new HttpException(`Task doesn't exist.`, HttpStatus.BAD_REQUEST);
		}
		const taskApprovalStep = await this.afeProposalApproverRepository.getApproverById(
			task.entity_id,
		);
		if (taskApprovalStep.actionStatus !== APPROVER_STATUS.IN_PROGRESS) {
			throw new HttpException(`Task is not in progress state.`, HttpStatus.CONFLICT);
		}

		const {
			givenName: given_name,
			surname: family_name,
			userPrincipalName: upn,
			displayName: name,
			userType,
			mail,
		} = await this.mSGraphApiClient.getUserDetails(userId);
		const username = userType == AD_USER_TYPE.GUEST ? mail.toLowerCase() : upn.toLowerCase();
		const currentContextUser: NonAuthTokenUser = {
			family_name,
			given_name,
			name,
			unique_name: username,
			username,
			upn,
		};

		let comments: string;
		switch (action) {
			case TASK_ACTION.APPROVE:
				comments = 'Approved by Email';
				break;
			case TASK_ACTION.REJECT:
				comments = 'Rejected by Email';
				break;
		}
		const approvers = await this.afeProposalApproverRepository.getApproversByProposalId(
			taskApprovalStep.afeProposalId,
		);
		const afeDetails = await this.afeProposalRepository.getAfeProposalById(
			taskApprovalStep.afeProposalId,
		);

		const { steps: latestWorkflowSteps, masterSettingWorkflows } =
			await this.getLatestWorkflowApproversList(taskApprovalStep.afeProposalId);

		//Update user details in approvers table with latest user details.
		const alreadyTraverseApproverIds = new Set();
		const updateUsersDetails = latestWorkflowSteps
			.map(step => {
				const approver = approvers.find(
					a =>
						step?.stepId &&
						a.workflowMasterStepsId === step?.stepId &&
						!alreadyTraverseApproverIds.has(a.id),
				);
				if (!approver) {
					return null;
				}
				alreadyTraverseApproverIds.add(approver.id);
				return {
					id: approver.id,
					user: step.approvers,
					assignedTo:
						(step.associateType === ASSOCIATED_TYPE.COST_CENTER ||
							step.associateType === ASSOCIATED_TYPE.USER) &&
							step?.approvers?.length
							? step.approvers[step.approvers.length - 1].loginId
							: null,
				};
			})
			.filter(step => step !== null);

		await this.databaseHelper.startTransaction(async () => {
			await this.afeProposalApproverRepository.updateUserDetailsByApproverIds(updateUsersDetails, {
				user: currentContextUser,
			});

			const updatedApprovers = await this.addNewWorkflowSteps(approvers, latestWorkflowSteps, {
				user: currentContextUser,
			});
			await this.updateAfeProposalLimitDeduction(
				taskApprovalStep.afeProposalId,
				masterSettingWorkflows,
				{ user: currentContextUser },
			);

			await this.performApprovalAction(
				action,
				afeDetails,
				taskApprovalStep,
				updatedApprovers,
				{ user: currentContextUser },
				task,
				comments,
			);
		});

		return { message: 'Action has been executed successfully.' };
	}

	public async sendReminder(approverId: number, currentContext: CurrentContext) {
		const { username } = currentContext.user;

		const existingTasks = await this.taskApiClient.getAllTasks(
			approverId,
			TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
		);

		if (!existingTasks?.length) {
			throw new HttpException(
				'The user has already approved the task or the task has not yet been assigned.',
				HttpStatus.NOT_ACCEPTABLE,
			);
		}

		const approvers = await this.afeProposalApproverRepository.getApproverById(approverId);

		if (!approvers) {
			throw new HttpException('Invalid Request!.', HttpStatus.BAD_REQUEST);
		}

		const proposalId = approvers.afeProposalId;
		const afeProposal = await this.afeProposalRepository.getAfeProposalById(proposalId);

		if (afeProposal.submitterId.toLowerCase() !== username.toLowerCase()) {
			throw new HttpException(
				'You do not have permission to send this AFE reminder!',
				HttpStatus.BAD_REQUEST,
			);
		}

		if (!afeProposal) {
			throw new HttpException('Invalid Request!.', HttpStatus.BAD_REQUEST);
		}

		for (const task of existingTasks) {
			let users: UserDetail[] = [];

			if (task.delegated_from_task_id) {
				if (task.assigned_to) {
					const assigninedUser = await this.mSGraphApiClient.getUsersDetails([task.assigned_to]);

					const assignedUserDetail = assigninedUser.map((assignee: any) => {
						return {
							firstName: assignee.givenName,
							lastName: assignee.surname,
							title: assignee.jobTitle,
							email: assignee.mail,
							loginId:
								assignee.userType == AD_USER_TYPE.GUEST
									? assignee.mail.toLowerCase()
									: assignee.userPrincipalName.toLowerCase(),
						};
					});

					users = assignedUserDetail;
				}
			} else {
				users = approvers.otherInfo.usersDetail;
			}

			const fullTaskLink = this.createFullUrlForTask(
				approvers.otherInfo.approvalType,
				afeProposal,
				task.id,
			);

			const isMailApprovalTask = approvers.otherInfo.approvalType !== APPROVAL_TYPE.MORE_DETAIL;

			await this.sendTaskAssignmentNotification(
				users,
				afeProposal,
				fullTaskLink,
				isMailApprovalTask,
				task.id,
				'Reminder - ',
			);
		}

		return {
			message: 'Reminder Sent Successfully!',
		};
	}
}
