"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowSharedBucketService = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const get_bucket_shared_limit_response_dto_1 = require("../dtos/response/get-bucket-shared-limit-response.dto");
const repositories_1 = require("../repositories");
let WorkflowSharedBucketService = class WorkflowSharedBucketService {
    constructor(workflowMasterStepRepository, workflowSharedBucketLimitRepository, historyApiClient) {
        this.workflowMasterStepRepository = workflowMasterStepRepository;
        this.workflowSharedBucketLimitRepository = workflowSharedBucketLimitRepository;
        this.historyApiClient = historyApiClient;
    }
    getSharedBucketLimitList(stepId) {
        return __awaiter(this, void 0, void 0, function* () {
            const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(stepId);
            if (stepDetail) {
                if (stepDetail.aggregateLimit <= 0) {
                    throw new exceptions_1.HttpException('Step don\'t have aggregate limit to share.', enums_1.HttpStatus.NOT_FOUND);
                }
                if (stepDetail.associateType !== associated_type_enum_1.ASSOCIATED_TYPE.ROLE) {
                    throw new exceptions_1.HttpException('Step is not allowed to manage bucket.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!stepDetail.workflowMasterSetting ||
                    (stepDetail.workflowMasterSetting &&
                        (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                const bucketList = yield this.workflowSharedBucketLimitRepository.getSharedBucketListByStepId(stepDetail.workflowMasterStepId);
                return (0, helpers_1.singleObjectToInstance)(get_bucket_shared_limit_response_dto_1.GetBucketSharedLimitWithStepResponseDTO, {
                    stepDetail: stepDetail,
                    bucketList: (0, helpers_1.multiObjectToInstance)(get_bucket_shared_limit_response_dto_1.GetBucketSharedLimitResponseDTO, bucketList)
                });
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    addNewBucketEntity(stepId, newSharedBucketLimitRequestDTO, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            if (newSharedBucketLimitRequestDTO.entityId === newSharedBucketLimitRequestDTO.bucketEntityId) {
                throw new exceptions_1.HttpException('Source & Shared Entity can\'t be same.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(stepId);
            if (stepDetail) {
                if (stepDetail.aggregateLimit <= 0) {
                    throw new exceptions_1.HttpException('Step don\'t have aggregate limit to share.', enums_1.HttpStatus.NOT_FOUND);
                }
                if (!stepDetail.workflowMasterSetting ||
                    (stepDetail.workflowMasterSetting &&
                        (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                const bucketList = yield this.workflowSharedBucketLimitRepository.getBucketDetailWithCondition({
                    workflowMasterStepId: stepDetail.workflowMasterStepId
                });
                if (bucketList && bucketList.length) {
                    const isAlreadyExist = bucketList.find((bucketDetail) => {
                        return (newSharedBucketLimitRequestDTO.entityId === bucketDetail.entityId);
                    });
                    if (isAlreadyExist) {
                        throw new exceptions_1.HttpException('Bucket limit already exist.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                    const ifSourceEntityIsSharing = bucketList.find((bucketDetail) => {
                        return (newSharedBucketLimitRequestDTO.bucketEntityId === bucketDetail.entityId);
                    });
                    if (ifSourceEntityIsSharing) {
                        throw new exceptions_1.HttpException(newSharedBucketLimitRequestDTO.bucketEntityCode + ' can\'t be added as source entity. As it\'s already added as a sharing entity.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                    const ifSharingIdIsBucket = bucketList.find((bucketDetail) => {
                        return (newSharedBucketLimitRequestDTO.entityId === bucketDetail.bucketEntityId);
                    });
                    if (ifSharingIdIsBucket) {
                        throw new exceptions_1.HttpException(newSharedBucketLimitRequestDTO.entityCode + ' can\'t be added as sharing entity. As it\'s already added as a source entity.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                const bucketDetail = yield this.workflowSharedBucketLimitRepository.addNewLimit(Object.assign(Object.assign({}, newSharedBucketLimitRequestDTO), { workflowMasterStepId: stepDetail.workflowMasterStepId, workflowMasterSettingId: (stepDetail.workflowMasterSetting.parentId ? stepDetail.workflowMasterSetting.parentId : stepDetail.workflowMasterSetting.id) }), currentContext);
                yield this.historyApiClient.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: stepDetail.workflowMasterStepId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_BUCKET_LIMIT_CREATED,
                    comments: newSharedBucketLimitRequestDTO.entityCode + ' will share the same bucket of ' + newSharedBucketLimitRequestDTO.bucketEntityCode,
                    additional_info: {
                        bucketDetail: Object.assign(Object.assign({}, newSharedBucketLimitRequestDTO), { workflowMasterStepId: stepDetail.workflowMasterStepId, workflowMasterSettingId: (stepDetail.workflowMasterSetting.parentId ? stepDetail.workflowMasterSetting.parentId : stepDetail.workflowMasterSetting.id) }),
                    }
                });
                return bucketDetail;
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    deleteBucketLimit(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const bucketDetail = yield this.workflowSharedBucketLimitRepository.getSharedBucketDetailById(id);
            if (bucketDetail) {
                const deleteResponse = yield this.workflowSharedBucketLimitRepository.deleteBucketLimit(id, currentContext);
                if (deleteResponse) {
                    yield this.historyApiClient.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: bucketDetail.workflowMasterStepId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_BUCKET_LIMIT_DELETED,
                        comments: 'Sharing entity ' + bucketDetail.entityCode + ' with source bucket entity ' + bucketDetail.bucketEntityCode + ' has been deleted'
                    });
                    return { message: 'Limit has been deleted successfully.' };
                }
                throw new exceptions_1.HttpException('Unable to delete the bucket limit.', enums_1.HttpStatus.BAD_REQUEST);
            }
            else {
                throw new exceptions_1.HttpException('Bucket limit is unavailable.', enums_1.HttpStatus.BAD_REQUEST);
            }
        });
    }
};
WorkflowSharedBucketService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.WorkflowMasterStepRepository,
        repositories_1.WorkflowSharedBucketLimitRepository,
        clients_1.HistoryApiClient])
], WorkflowSharedBucketService);
exports.WorkflowSharedBucketService = WorkflowSharedBucketService;
//# sourceMappingURL=workflow-shared-bucket.service.js.map