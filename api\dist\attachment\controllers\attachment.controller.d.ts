import { RequestContext } from 'src/shared/types';
import { AttachmentContentResponseDto } from '../dtos';
import { AttachmentService } from '../services/attachment.service';
export declare class AttachmentController {
    private readonly attachmentService;
    constructor(attachmentService: AttachmentService);
    getDraftsByIdAndActiveUser(fileId: string, taskId: number, request: RequestContext, res: any): Promise<void>;
    getProposalAttachmentMetaData(request: RequestContext, afeProposalId: number, taskId?: number): Promise<AttachmentContentResponseDto[]>;
    getAllApproversAttachmentMetaData(request: RequestContext, afeProposalId: number, taskId?: number): Promise<AttachmentContentResponseDto[]>;
    getApproverAttachmentMetaData(afeProposalId: number): Promise<AttachmentContentResponseDto[]>;
}
