import { ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { DEBOUNCE_TIME, PAGINATION_DEFAULT_LIMIT } from '@core/constants';
import { IPagination } from '@core/interfaces/api';
import { HistoryResponse } from '@core/interfaces/history-response';
import { UserModel } from '@core/models/basic/user';
import { ModalComponent, ModalConfig } from '@core/modules/partials';
import { ReportService } from '@core/services/api';
import { SpinnerService } from '@core/services/common/spinner.service';
import { TranslateService } from '@ngx-translate/core';
import { toNumber } from 'lodash';
import { debounceTime, finalize } from 'rxjs';
import Swal from 'sweetalert2';
import { CompanyCodeResponse, CostCenterResponse, CostCentersListResponse, CreateCostCenterRequest } from '../../core/models';
import { CompaniesService } from '../../core/services';

@Component({
  selector: 'app-cost-centers',
  templateUrl: './cost-centers.component.html',
  styleUrls: ['./cost-centers.component.scss']
})

export class CostCentersComponent implements OnInit, OnChanges {
  @Input('selectedCompanyCode') selectedCompanyCode: CompanyCodeResponse;

  public loading: boolean = true;
  public pagination: IPagination = { limit: PAGINATION_DEFAULT_LIMIT, offset: 0, page: 1 };
  public costCenters: CostCenterResponse[] = [];
  public totalRecords: number;
  public isEditMode: boolean = false;
  public isSubmitted: boolean = false;
  public editCostCenterId: number;

  public costCenterFormGroup: FormGroup;
  public costCenterEditorModalTitle: string = this.translateService.instant('MENU.ADD_COST_CENTER');
  public modalDismissButtonLabel: string = this.translateService.instant('FORM.BUTTON.SAVE_BUTTON');

  public sectionHead: UserModel | null = null;
  public departmentHead: UserModel | null = null;

  isMulti: boolean = false;
  bufferData: Uint8Array;
  hideForm: boolean = true;

  @ViewChild('costCenterEditorModal') private costCenterEditorModal: ModalComponent;

  historyModalConfig: ModalConfig = {
    modalTitle: this.translateService.instant('FORM.BUTTON.COST_CENTER_HISTORY_BUTTON'),
    hideCloseButton() {
      return true
    },
    hideDismissButton() {
      return true;
    },
    modalDialogConfig: { backdrop: 'static', size: 'lg', keyboard: false, centered: false }
  };

  companyHistory: HistoryResponse[] = [];
  @ViewChild('historyModal') private historyModalComponent: ModalComponent;

  public costCenterEditorModalConfig: ModalConfig = {
    modalTitle: this.costCenterEditorModalTitle,
    dismissButtonLabel: this.modalDismissButtonLabel,
    closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),
    onDismiss: () => {
      this.formReset();
      return true;
    },
    shouldClose: () => {
      this.formReset();
      return true;
    },
    shouldDismiss: () => {
      this.isSubmitted = true;
      this.cdr.detectChanges();

      if (this.isMulti) {
        this.uploadMultipleCostCenter();
        return false;
      }

      if (!this.costCenterFormGroup.valid) {
        return false;
      } else {
        this.cdr.detectChanges();
        if (this.isEditMode) {
          this.updateCostCenter();
        } else {
          this.createNewCostCenter();
        }
        return false;
      }
    },
    modalDialogConfig: { backdrop: 'static', size: 'lg', keyboard: false, centered: true }
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly spinnerService: SpinnerService,
    private readonly companiesService: CompaniesService,
    private readonly translateService: TranslateService,
    private readonly reportService: ReportService,
    private elementRef: ElementRef

  ) { }

  ngOnInit(): void {
    this.cdr.detectChanges();
    this.initForm();
  }

  ngOnChanges() {
    this.fetchCostCentersList();
  }

  toggleOpen() {
    // Manually trigger the click event
    const divElement: HTMLElement = this.elementRef.nativeElement.querySelector('.separator');
    divElement.click();
  }

  initForm() {
    this.costCenterFormGroup = new FormGroup({
      name: new FormControl('', [Validators.required]),
      code: new FormControl('', [Validators.required]),
      operating: new FormControl(true, [Validators.required]),
      departmentHead: new FormControl('', [Validators.required]),
      sectionHead: new FormArray([]),
    });

    // sections: new FormArray([this.sectionControls]),

    this.cdr.detectChanges();
  }

  sectionExist() {

    this.sections?.controls?.forEach((section, index) => {
      const sectionControl = section.get('title');

      if (sectionControl?.errors?.alreadyExist) {
        const errors = sectionControl.errors;
        delete errors.alreadyExist;
        sectionControl.setErrors((errors && Object.keys(errors).length > 0) ? errors : null);
        this.cdr.detectChanges();
      }

      const value = sectionControl?.value;
      if (value) {
        // Check if the same title exists in the section FormArray
        const isTitleDuplicate = this.sections.controls
          .filter((_, i) => i !== index) // Exclude the current section
          .some(section => section.get('title')?.value?.toLowerCase() === value.toLowerCase());

        if (isTitleDuplicate) {

          sectionControl.setErrors({
            alreadyExist: true
          });

          this.cdr.detectChanges();
        }
      }
    });
    this.cdr.detectChanges();
  }

  sectionCodeExist() {

    this.sections?.controls?.forEach((section, index) => {
      const sectionControl = section.get('code');

      if (sectionControl?.errors?.alreadyExist) {
        const errors = sectionControl.errors;
        delete errors.alreadyExist;
        sectionControl.setErrors((errors && Object.keys(errors).length > 0) ? errors : null);
        this.cdr.detectChanges();
      }

      const value = sectionControl?.value;
      if (value) {
        // Check if the same code exists in the section FormArray
        const isCodeDuplicate = this.sections.controls
          .filter((_, i) => i !== index) // Exclude the current section
          .some(section => section.get('code')?.value?.toLowerCase() === value.toLowerCase());

        if (isCodeDuplicate) {

          sectionControl.setErrors({
            alreadyExist: true
          });

          this.cdr.detectChanges();
        }
      }
    });
    this.cdr.detectChanges();
  }

  get sectionControls() {
    return new FormGroup({
      title: new FormControl('', [Validators.required]),
      code: new FormControl('', []),
      user: new FormControl('', [Validators.required]),
    })
  }

  getSectionControlsDefault(value: any) {
    return new FormGroup({
      title: new FormControl(value.title, [Validators.required]),
      code: new FormControl(value.code, []),
      user: new FormControl(value.user, [Validators.required]),
    })
  }

  removeSection(index: number) {
    this.sections.removeAt(index);
    this.cdr.detectChanges();
    this.sectionExist();
  }

  addNewSection() {
    this.sections.push(this.sectionControls);
    this.cdr.detectChanges();
  }

  private formReset() {
    this.costCenterFormGroup.reset();

    const sectionHeadArray = this.costCenterFormGroup.get('sectionHead') as FormArray;
    sectionHeadArray.clear();

    this.sectionHead = null;
    this.departmentHead = null;
    this.cdr.detectChanges();
    console.log('Reset')
    console.log(this.costCenterFormGroup);
  }

  get sections(): FormArray {
    return this.costCenterFormGroup?.controls["sectionHead"] as FormArray;
  }

  public isPatternError(FContorl: string): (boolean | undefined) {
    return (
      this.costCenterFormGroup.get(FContorl)?.hasError('pattern')
      && (this.costCenterFormGroup.get(FContorl)?.touched || this.isSubmitted)
    );
  }

  private fetchCostCentersList() {
    if (this.selectedCompanyCode) {
      this.loading = true;
      this.spinnerService.startSpinner();
      this.companiesService.getCostCentersByCompanyId(this.selectedCompanyCode.id, this.pagination)
        .pipe(finalize(() => {
          this.spinnerService.stopSpinner();
          this.loading = false;
          this.cdr.detectChanges();
        }))
        .subscribe({
          next: (response: CostCentersListResponse) => {
            this.costCenters = response.records;
            this.totalRecords = response.total;
          },
          error: () => {
            this.costCenters = [];
          }
        })
    }
  }

  public handlePageChange(event: number): void {
    this.spinnerService.startSpinner();
    this.loading = true;
    this.pagination.offset = +event - 1;
    this.pagination.page = event;
    this.fetchCostCentersList();
  }

  public isRequiredError(FContorl: string): (boolean | undefined) {
    return (
      this.costCenterFormGroup.get(FContorl)?.hasError('required')
      && (this.costCenterFormGroup.get(FContorl)?.touched || this.isSubmitted)
    );
  }

  public deleteCostCenter(costCenterId: number) {
    Swal.fire({
      title: this.translateService.instant('SWAL.CONFIRMATION'),
      text: this.translateService.instant('SWAL.COST_CENTER_DELETE_CONFIRM'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: this.translateService.instant('SWAL.DELETE_BUTTON'),
    }).then((result) => {
      if (result.isConfirmed) {
        this.spinnerService.startSpinner();
        this.companiesService.deleteCostCenterById(costCenterId)
          .pipe(finalize(() => {
            this.spinnerService.stopSpinner();
          })).subscribe({
            next: () => {
              Swal.fire(
                this.translateService.instant('SWAL.COST_CENTER_DELETE_TITLE'),
                this.translateService.instant('SWAL.COST_CENTER_DELETE_SUCCESS'),
                'success'
              )
              this.fetchCostCentersList();
            },
            error: (error) => {
              Swal.fire({
                icon: 'error',
                title: this.translateService.instant('SWAL.OOPS'),
                text: error.message
              })
            }
          })
      }
    })
  }

  /**
   * Open modal in edit or create new cost center mode.
   * @param isEditMode 
   * @param editCostCenterId 
   * @returns 
   */
  public async openCostCenterEditorModal(isEditMode: boolean = false, editCostCenterId?: number) {
    this.departmentHead = null;
    this.sectionHead = null;
    this.isSubmitted = false;
    this.isEditMode = isEditMode;
    this.isMulti = false;
    this.hideForm = true;

    this.cdr.detectChanges();
    this.costCenterEditorModalConfig.modalTitle = isEditMode ? this.translateService.instant('MENU.UPDATE_COST_CENTER') : this.translateService.instant('MENU.ADD_COST_CENTER');

    this.costCenterEditorModalConfig.dismissButtonLabel = isEditMode ? this.translateService.instant('FORM.BUTTON.UPDATE_BUTTON') : this.translateService.instant('FORM.BUTTON.ADD_BUTTON');

    if (isEditMode && editCostCenterId) {
      const currentCostCenter = this.costCenters.find(costCenter => costCenter.id === editCostCenterId);
      if (currentCostCenter) {
        const { name, code, departmentHead, operating, sectionHead = [] } = currentCostCenter;
        this.costCenterFormGroup.patchValue({ name, code, departmentHead, operating });

        console.log(sectionHead);

        if (sectionHead && sectionHead?.length) {
          sectionHead?.forEach((section: any) => {
            this.sections.push(this.getSectionControlsDefault(section));
            this.cdr.detectChanges();
          });
        }

        this.sectionExist();

        this.departmentHead = departmentHead;
      }
      this.editCostCenterId = editCostCenterId;
    } else {
      this.formReset();
    }

    this.hideForm = false;
    this.cdr.detectChanges();
    return await this.costCenterEditorModal.open();
  }

  public costCenterHeadAdded(userData: UserModel, controllerName: string) {
    if (userData) {
      this.costCenterFormGroup.patchValue({ [controllerName]: userData });
    } else {
      this.costCenterFormGroup.patchValue({ [controllerName]: '' });
    }
  }

  public costCenterSectionHeadAdded(userData: UserModel, index: number) {
    this.sections.at(index).patchValue({
      user: userData ? userData : ''
    });
    this.cdr.detectChanges();
  }

  getSectionHeadUser(index: number) {
    return this.sections.at(index).getRawValue()?.user;
  }

  private getCostCenterRequestPayload() {
    const requestPayload: CreateCostCenterRequest = {
      companyCodeId: +this.selectedCompanyCode.id,
      code: this.costCenterFormGroup.get('code')?.value,
      name: this.costCenterFormGroup.get('name')?.value,
      operating: (this.costCenterFormGroup.get('operating')?.value == 'true' || this.costCenterFormGroup.get('operating')?.value == true),
      departmentHead: this.costCenterFormGroup.get('departmentHead')?.value,
      sectionHead: this.costCenterFormGroup.get('sectionHead')?.value
    };
    return requestPayload;
  }


  /**
   * Create new cost center for the company.
   */
  private createNewCostCenter() {
    this.isSubmitted = true;
    this.cdr.detectChanges();

    this.sectionExist();

    console.log(this.costCenterFormGroup);


    if (this.costCenterFormGroup.valid) {
      this.spinnerService.startSpinner();
      this.companiesService.createCostCenter(this.getCostCenterRequestPayload())
        .pipe(finalize(() => {
          this.spinnerService.stopSpinner();
        }))
        .subscribe({
          next: () => {
            this.formReset();
            Swal.fire(
              this.translateService.instant('SWAL.SUCCESS'),
              this.translateService.instant('SWAL.ADD_COST_CENTER_CODE_SUCCESS'),
              'success'
            );
            this.costCenterEditorModal.close();
            this.fetchCostCentersList();
            this.isSubmitted = false;
            this.cdr.detectChanges();
          },
          error: (error) => {
            Swal.fire({
              icon: 'error',
              title: this.translateService.instant('SWAL.ADD_COST_CENTER_CODE_ERROR'),
              text: error.message,
            })
          }
        });
    }
  }

  /**
   * Update the cost center details.
   */
  private updateCostCenter() {
    this.isSubmitted = true;
    this.cdr.detectChanges();

    this.sectionExist();
    console.log(this.costCenterFormGroup);

    if (this.costCenterFormGroup?.valid && this.selectedCompanyCode?.id) {
      this.spinnerService.startSpinner();
      this.companiesService.updateCostCenter({ id: +this.editCostCenterId, ...this.getCostCenterRequestPayload() })
        .pipe(finalize(() => {
          this.spinnerService.stopSpinner();
        }))
        .subscribe({
          next: () => {
            Swal.fire(
              this.translateService.instant('SWAL.SUCCESS'),
              this.translateService.instant('SWAL.UPDATE_COST_CENTER_CODE_SUCCESS'),
              'success'
            );
            this.costCenterEditorModal.close();
            this.fetchCostCentersList();
            this.isSubmitted = false;
            this.cdr.detectChanges();
          },
          error: (error) => {
            Swal.fire({
              icon: 'error',
              title: this.translateService.instant('SWAL.UPDATE_COST_CENTER_CODE_ERROR'),
              text: error.message,
            })
          }
        });
    }
  }

  public exportToExcelSheet() {
    this.toggleOpen();
    this.reportService.downloadCostCenterExcelReport(toNumber(this.selectedCompanyCode.id));
  }

  public exportBlankFormat() {
    this.reportService.downloadCostCenterExcelReport(toNumber(0));
  }

  addBufferAttachment(bufferData: Uint8Array) {
    this.bufferData = bufferData;
    this.cdr.detectChanges();
  }

  changeAddOption(type: string) {
    this.isSubmitted = false;
    if (!this.isMulti) {
      this.initForm();
    }
    this.cdr.detectChanges();
  }

  uploadMultipleCostCenter() {

    if (this.selectedCompanyCode.id && this.bufferData) {
      this.spinnerService.startSpinner();

      this.companiesService.importCostCenter({
        companyCodeId: toNumber(this.selectedCompanyCode.id),
        bufferData: this.bufferData as Uint8Array
      }).subscribe({
        next: (response) => {
          console.log(response);
          this.costCenterEditorModal.close();
          Swal.fire(
            this.translateService.instant('SWAL.SUCCESS'),
            this.translateService.instant('SWAL.IMPORT_COST_CENTER_SUCCESS'),
            'success'
          );
          this.fetchCostCentersList();
        },
        error: (err) => {
          console.log(err);
          Swal.fire({
            icon: 'error',
            title: this.translateService.instant('SWAL.OOPS'),
            html: err.message
          })
          this.spinnerService.stopSpinner();
        }
      })
    }

  }

  openHistoryModel(costCenterId: number) {
    this.spinnerService.startSpinner();

    this.companiesService.getCostCenterHistory(costCenterId).subscribe({
      next: (response) => {
        this.companyHistory = response as HistoryResponse[];
        this.cdr.detectChanges();
        this.historyModalComponent.open();
        this.spinnerService.stopSpinner();

      },
      error: (err) => {
        console.log(err);
        this.companyHistory = [] as HistoryResponse[];
        this.cdr.detectChanges();
        this.historyModalComponent.open();
        this.spinnerService.stopSpinner();

        Swal.fire({
          icon: 'error',
          title: this.translateService.instant('SWAL.ERROR'),
          text: this.translateService.instant('SWAL.ERROR_HISTORY_FETCH')
        })
      }
    })

  }
}
