import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { UpdateApproversListRequestDto } from '../dtos';
import { AfeProposalApproverRepository, AfeProposalRepository } from '../repositories';
export declare class AfeProposalApproverService {
    private readonly afeProposalApproverRepository;
    private readonly afeProposalRepository;
    private readonly adminApiClient;
    private readonly databaseHelper;
    private readonly historyApiClient;
    constructor(afeProposalApproverRepository: AfeProposalApproverRepository, afeProposalRepository: AfeProposalRepository, adminApiClient: AdminApiClient, databaseHelper: DatabaseHelper, historyApiClient: HistoryApiClient);
    updateAfeProposalApprover(afeProposalId: number, updateApproversListRequestDto: UpdateApproversListRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
}
