{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/MyWorkspace/Projects/DpWorld/AFE_Revamp/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ReplaceUrlVariable } from '@core/constants/urls.constants';\nimport { BehaviorSubject, Subject, finalize, takeUntil } from 'rxjs';\nimport { LOCALSTORAGE_KEY } from '@core/constants';\nimport { PermissionEnum } from '@core/enums/Permission';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/services\";\nimport * as i2 from \"@core/services/common/spinner.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@core/services/common/localStorage.service\";\nimport * as i5 from \"@ngx-translate/core\";\nimport * as i6 from \"@core/services/common/permission.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ng-inline-svg-2\";\nimport * as i9 from \"../../core/modules/partials/ad-user-search/ad-user-search.component\";\nimport * as i10 from \"../../core/modules/partials/no-data/no-data.component\";\nimport * as i11 from \"../../core/modules/partials/skeleton-loader/list-skeleton-loader/list-skeleton-loader.component\";\nimport * as i12 from \"../../core/modules/partials/filter-elements/filter-badges/filter-badges.component\";\nimport * as i13 from \"../../core/modules/partials/modals/modal/modal.component\";\nimport * as i14 from \"./task-filter-modal/task-filter-modal.component\";\nconst _c0 = [\"filterModal\"];\n\nfunction MytaskComponent_ng_container_11_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 20)(3, \"div\", 21)(4, \"div\", 22);\n    i0.ɵɵelement(5, \"app-filter-badges\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"app-ad-user-search\", 25);\n    i0.ɵɵlistener(\"userSelected\", function MytaskComponent_ng_container_11_ng_container_3_Template_app_ad_user_search_userSelected_7_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onUserAdded($event));\n    });\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵpipe(9, \"lowercase\");\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"localStorageKey\", ctx_r5.filterLocalStorageKey);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(8, 4, \"FORM.PLACEHOLDER.SEARCH\"), \" \", i0.ɵɵpipeBind1(9, 6, i0.ɵɵpipeBind1(10, 8, \"LIST.ASSIGNED_TO\")), \"\");\n    i0.ɵɵproperty(\"isMultiSelect\", false);\n  }\n}\n\nfunction MytaskComponent_ng_container_11_div_12_ng_container_1_h5_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const task_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r12.title, \" \");\n  }\n}\n\nfunction MytaskComponent_ng_container_11_div_12_ng_container_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 41)(2, \"div\", 35)(3, \"div\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const task_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 2, \"LIST.TOTAL_AMOUNT\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(8, 4, task_r12 == null ? null : task_r12.additionalInfo == null ? null : task_r12.additionalInfo.totalAmount, \"USD\", \"symbol\", \"1.2-2\"), \" \");\n  }\n}\n\nfunction MytaskComponent_ng_container_11_div_12_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function MytaskComponent_ng_container_11_div_12_ng_container_1_Template_div_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const task_r12 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.nagivateToTaskAction(task_r12));\n    });\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"div\", 31)(5, \"div\", 32)(6, \"div\", 33);\n    i0.ɵɵelement(7, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 35);\n    i0.ɵɵtemplate(9, MytaskComponent_ng_container_11_div_12_ng_container_1_h5_9_Template, 2, 1, \"h5\", 36);\n    i0.ɵɵelementStart(10, \"div\", 37);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 38)(14, \"div\", 39)(15, \"div\", 40)(16, \"div\", 41)(17, \"div\", 35)(18, \"div\", 42);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 43);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(23, MytaskComponent_ng_container_11_div_12_ng_container_1_div_23_Template, 9, 9, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 45)(25, \"div\", 46)(26, \"div\", 41)(27, \"div\", 35)(28, \"div\", 47);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 48);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 46)(34, \"div\", 41)(35, \"div\", 35)(36, \"div\", 47);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 48);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const task_r12 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr064.svg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", task_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 10, task_r12.createdOn, \"MMM dd, yyyy ',' h:mm a\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(20, 13, \"LIST.PROJECT_NAME\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (task_r12 == null ? null : task_r12.additionalInfo == null ? null : task_r12.additionalInfo.proposal_project_name) || \"N/A\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r12 == null ? null : task_r12.additionalInfo == null ? null : task_r12.additionalInfo.totalAmount);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(30, 15, \"LIST.ASSIGNED_TO\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", task_r12.assignedTo || \"N/A\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(38, 17, \"FORM.LABEL.SUBMITTED_BY\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (task_r12 == null ? null : task_r12.additionalInfo == null ? null : task_r12.additionalInfo.proposal_created_by) || \"N/A\", \" \");\n  }\n}\n\nfunction MytaskComponent_ng_container_11_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, MytaskComponent_ng_container_11_div_12_ng_container_1_Template, 41, 19, \"ng-container\", 27);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.filteredTasks);\n  }\n}\n\nfunction MytaskComponent_ng_container_11_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 51);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"message\", i0.ɵɵpipeBind1(1, 1, \"ERROR.NO_TASK_AVAILABLE\"));\n  }\n}\n\nfunction MytaskComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtemplate(3, MytaskComponent_ng_container_11_ng_container_3_Template, 11, 10, \"ng-container\", 11);\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"div\", 15)(6, \"span\", 16);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"span\", 16);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(12, MytaskComponent_ng_container_11_div_12_Template, 2, 1, \"div\", 17);\n    i0.ɵɵtemplate(13, MytaskComponent_ng_container_11_ng_template_13_Template, 2, 3, \"ng-template\", null, 18, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r7 = i0.ɵɵreference(14);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.validateUserPermission(ctx_r0.permissionEnum.AFEAdministration));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.filteredTasks.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 5, \"COMMON.PENDING_TASKS\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredTasks == null ? null : ctx_r0.filteredTasks.length)(\"ngIfElse\", _r7);\n  }\n}\n\nfunction MytaskComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-list-skeleton-loader\", 52);\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"loaderCount\", 4);\n  }\n}\n\nfunction MytaskComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-task-filter-modal\", 53);\n    i0.ɵɵlistener(\"filterPayloadEvent\", function MytaskComponent_ng_container_16_Template_app_task_filter_modal_filterPayloadEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.getFilterPayload($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"localStorageKey\", ctx_r4.filterLocalStorageKey);\n  }\n}\n\nexport class MytaskComponent {\n  constructor(taskService, spinnerService, cdr, router, localStorageService, translateService, permissionService) {\n    this.taskService = taskService;\n    this.spinnerService = spinnerService;\n    this.cdr = cdr;\n    this.router = router;\n    this.localStorageService = localStorageService;\n    this.translateService = translateService;\n    this.permissionService = permissionService;\n    this.tasks = [];\n    this.filteredTasks = [];\n    this.loading = true;\n    this.filterLocalStorageKey = LOCALSTORAGE_KEY.TASK_LIST_FILTER;\n    this.filterModalTitle = this.translateService.instant('MENU.FILTER');\n    this.modalDismissButtonLabel = this.translateService.instant('FORM.BUTTON.APPLY');\n    this.filterChangeEvent = new BehaviorSubject(true);\n    this.isFilterModalReady = false;\n    this.permissionEnum = PermissionEnum;\n    this.filterModalConfig = {\n      modalTitle: this.filterModalTitle,\n      dismissButtonLabel: this.modalDismissButtonLabel,\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.RESET'),\n      onDismiss: () => {\n        this.isFilterModalReady = false;\n        return true;\n      },\n      shouldClose: () => {\n        this.isFilterModalReady = false;\n        this.filtersData = null;\n        this.localStorageService.set(this.filterLocalStorageKey, '');\n        this.isFilterModalReady = true;\n        this.cdr.detectChanges();\n        return false;\n      },\n      shouldDismiss: () => {\n        this.cdr.detectChanges();\n        this.localStorageService.set(this.filterLocalStorageKey, this.filtersData);\n        this.filterChangeEvent.next(true);\n        this.isFilterModalReady = false;\n        return true;\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: true\n      }\n    };\n    this.destroy$ = new Subject();\n  }\n\n  ngOnInit() {\n    this.fetchList();\n  }\n\n  fetchList(assignedTo = null) {\n    this.taskService.getUserPendingTasksList(assignedTo).pipe(finalize(() => {\n      this.spinnerService.stopSpinner();\n      this.loading = false;\n      this.cdr.detectChanges();\n    })).subscribe({\n      next: response => {\n        this.tasks = response;\n        this.localStorageService.watch(this.filterLocalStorageKey).pipe(takeUntil(this.destroy$)).subscribe({\n          next: val => {\n            this.storedFilterData = val;\n            this.filterTasks();\n          }\n        });\n      },\n      error: _ => {\n        this.tasks = [];\n      }\n    });\n  }\n\n  onUserAdded(value) {\n    if (value) {\n      this.spinnerService.startSpinner();\n      this.fetchList((value === null || value === void 0 ? void 0 : value.loginId) || null);\n    }\n  }\n\n  validateUserPermission(permission) {\n    return this.permissionService.checkPermissionByName(permission);\n  }\n\n  nagivateToTaskAction(task) {\n    const {\n      taskRelUrl\n    } = task;\n    this.router.navigateByUrl(ReplaceUrlVariable(`${taskRelUrl}`, {\n      taskId: task.id\n    }));\n  }\n\n  filterTasks() {\n    this.filteredTasks = this.tasks.filter(task => {\n      var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x;\n\n      return (!((_c = (_b = (_a = this.storedFilterData) === null || _a === void 0 ? void 0 : _a.obj) === null || _b === void 0 ? void 0 : _b.submittedBy) === null || _c === void 0 ? void 0 : _c.loginId) || ((_e = (_d = task.additionalInfo) === null || _d === void 0 ? void 0 : _d.proposal_created_by) === null || _e === void 0 ? void 0 : _e.toLowerCase()) === ((_j = (_h = (_g = (_f = this.storedFilterData) === null || _f === void 0 ? void 0 : _f.obj) === null || _g === void 0 ? void 0 : _g.submittedBy) === null || _h === void 0 ? void 0 : _h.loginId) === null || _j === void 0 ? void 0 : _j.toLowerCase())) && (!((_l = (_k = this.storedFilterData) === null || _k === void 0 ? void 0 : _k.obj) === null || _l === void 0 ? void 0 : _l.afeReferenceNumber) || ((_m = task.additionalInfo) === null || _m === void 0 ? void 0 : _m.proposalProjectReferenceNumber) === ((_p = (_o = this.storedFilterData) === null || _o === void 0 ? void 0 : _o.obj) === null || _p === void 0 ? void 0 : _p.afeReferenceNumber)) && (!((_r = (_q = this.storedFilterData) === null || _q === void 0 ? void 0 : _q.obj) === null || _r === void 0 ? void 0 : _r.fromAssignedDate) || (task === null || task === void 0 ? void 0 : task.createdOn) && this.createDateObjectWithOutTime(new Date(task.createdOn)).getTime() >= this.createDateObject((_t = (_s = this.storedFilterData) === null || _s === void 0 ? void 0 : _s.obj) === null || _t === void 0 ? void 0 : _t.fromAssignedDate).getTime()) && (!((_v = (_u = this.storedFilterData) === null || _u === void 0 ? void 0 : _u.obj) === null || _v === void 0 ? void 0 : _v.toAssignedDate) || task.createdOn && this.createDateObjectWithOutTime(new Date(task.createdOn)).getTime() <= this.createDateObject((_x = (_w = this.storedFilterData) === null || _w === void 0 ? void 0 : _w.obj) === null || _x === void 0 ? void 0 : _x.toAssignedDate).getTime());\n    });\n    this.cdr.detectChanges();\n  }\n\n  createDateObject(obj) {\n    const {\n      day,\n      month,\n      year\n    } = obj;\n    return new Date(year, month - 1, day, 0, 0, 0, 0);\n  }\n\n  createDateObjectWithOutTime(date) {\n    return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);\n  }\n\n  getFilterPayload(event) {\n    this.filtersData = event;\n  }\n\n  openFilterModal() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.isFilterModalReady = true;\n\n      _this.cdr.detectChanges();\n\n      return yield _this.filterModal.open();\n    })();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.unsubscribe();\n  }\n\n}\n\nMytaskComponent.ɵfac = function MytaskComponent_Factory(t) {\n  return new (t || MytaskComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.SpinnerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.LocalStorageService), i0.ɵɵdirectiveInject(i5.TranslateService), i0.ɵɵdirectiveInject(i6.PermissionService));\n};\n\nMytaskComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: MytaskComponent,\n  selectors: [[\"app-mytask\"]],\n  viewQuery: function MytaskComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterModal = _t.first);\n    }\n  },\n  decls: 17,\n  vars: 11,\n  consts: [[1, \"toolbar\"], [\"id\", \"kt_toolbar_container\", 1, \"d-flex\", \"flex-end\", \"flex-stack\", \"container-fluid\"], [1, \"page-title\", \"d-flex\", \"align-items-center\", \"flex-wrap\", \"me-3\"], [1, \"d-flex\", \"align-items-center\", \"text-dark\", \"fw-bolder\", \"my-1\", \"fs-3\", \"mr-2\", \"mb-0\", \"mt-0\"], [1, \"d-flex\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-primary\", \"textSize\", \"px-5\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-5\", \"svg-icon-gray-500\", \"me-1\", 3, \"inlineSVG\"], [4, \"ngIf\", \"ngIfElse\"], [\"loadingPage\", \"\"], [3, \"modalConfig\"], [\"filterModal\", \"\"], [4, \"ngIf\"], [\"id\", \"kt_post\", 1, \"post\", \"d-flex\", \"flex-column-fluid\"], [\"id\", \"kt_content_container\"], [1, \"d-flex\", \"justify-content-end\", \"mb-5\"], [1, \"badge\", \"badge-light-danger\", \"fs-6\", \"fw-bold\", \"px-4\", \"py-2\"], [1, \"ms-2\"], [\"class\", \"row g-6\", 4, \"ngIf\", \"ngIfElse\"], [\"noDataMessage\", \"\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"mb-6\"], [1, \"card-body\", \"py-4\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-8\"], [3, \"localStorageKey\"], [1, \"col-md-4\"], [3, \"placeholder\", \"isMultiSelect\", \"userSelected\"], [1, \"row\", \"g-6\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-6\", \"col-xl-4\"], [1, \"card\", \"border-0\", \"shadow-lg\", \"hover-elevate-up\", \"transition-all-300ms\", \"cursor-pointer\", \"h-100\", \"note-box\", \"overflow-hidden\", 3, \"click\"], [1, \"card-header\", \"border-0\", \"pt-5\", \"pb-3\"], [1, \"d-flex\", \"align-items-center\", \"w-100\"], [1, \"symbol\", \"symbol-40px\", \"me-3\"], [1, \"symbol-label\", \"bg-white\", \"bg-opacity-20\", \"backdrop-blur\"], [1, \"svg-icon\", \"svg-icon-2\", \"svg-icon-white\", 3, \"inlineSVG\"], [1, \"flex-grow-1\"], [\"class\", \"card-title fw-bold text-white mb-1\", 4, \"ngIf\"], [1, \"text-white-75\", \"fs-7\", \"fw-semibold\"], [1, \"card-body\", \"pt-3\", \"pb-5\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fw-bold\", \"text-white\", \"pb-1\", \"d-inline\", \"w-100\"], [1, \"d-inline-block\"], [\"class\", \"col-6\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-6\"], [1, \"form-label\", \"fw-bold\", \"text-white\", \"mb-1\"], [1, \"text-white\", \"fw-bold\", \"fs-7\", \"lh-sm\"], [1, \"card-title\", \"fw-bold\", \"text-white\", \"mb-1\"], [1, \"text-white\", \"fw-bold\", \"fs-7\"], [3, \"message\"], [3, \"loaderCount\"], [3, \"localStorageKey\", \"filterPayloadEvent\"]],\n  template: function MytaskComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n      i0.ɵɵtext(4);\n      i0.ɵɵpipe(5, \"translate\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"div\", 4)(7, \"button\", 5);\n      i0.ɵɵlistener(\"click\", function MytaskComponent_Template_button_click_7_listener() {\n        return ctx.openFilterModal();\n      });\n      i0.ɵɵelement(8, \"span\", 6);\n      i0.ɵɵtext(9);\n      i0.ɵɵpipe(10, \"translate\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(11, MytaskComponent_ng_container_11_Template, 15, 7, \"ng-container\", 7);\n      i0.ɵɵtemplate(12, MytaskComponent_ng_template_12_Template, 1, 1, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(14, \"app-modal\", 9, 10);\n      i0.ɵɵtemplate(16, MytaskComponent_ng_container_16_Template, 2, 1, \"ng-container\", 11);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(13);\n\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 7, \"MENU.MY_TASKS\"), \" \");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen031.svg\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 9, \"FORM.BUTTON.FILTER\"), \" \");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading)(\"ngIfElse\", _r1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"modalConfig\", ctx.filterModalConfig);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isFilterModalReady);\n    }\n  },\n  dependencies: [i7.NgForOf, i7.NgIf, i8.InlineSVGDirective, i9.AdUserSearchComponent, i10.NoDataComponent, i11.ListSkeletonLoaderComponent, i12.FilterBadgesComponent, i13.ModalComponent, i14.TaskFilterModalComponent, i7.LowerCasePipe, i7.CurrencyPipe, i7.DatePipe, i5.TranslatePipe],\n  styles: [\"p[_ngcontent-%COMP%] {\\n  margin-bottom: 0rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm15dGFzay5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLG1CQUFBO0FBQ0oiLCJmaWxlIjoibXl0YXNrLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsicCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAwcmVtO1xyXG59Il19 */\"]\n});", "map": {"version": 3, "mappings": ";AAQA,SAASA,kBAAT,QAAmC,gCAAnC;AAEA,SACEC,eADF,EAGEC,OAHF,EAIEC,QAJF,EAKEC,SALF,QAMO,MANP;AASA,SAASC,gBAAT,QAAiC,iBAAjC;AAKA,SAASC,cAAT,QAA+B,wBAA/B;;;;;;;;;;;;;;;;;;;;;;ICWMC;IAGEA,gCAA0C,CAA1C,EAA0C,KAA1C,EAA0C,EAA1C,EAA0C,CAA1C,EAA0C,KAA1C,EAA0C,EAA1C,EAA0C,CAA1C,EAA0C,KAA1C,EAA0C,EAA1C;IAIQA;IAEFA;IACAA,gCAAsB,CAAtB,EAAsB,oBAAtB,EAAsB,EAAtB;IAMIA;MAAAA;MAAA;MAAA,OAAgBA,0CAAhB;IAAmC,CAAnC;;;;IAEFA;IAKVA;;;;;IAhB6BA;IAAAA;IAKjBA;IAAAA;IAGAA;;;;;;IA0CEA;IAIEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAwCJA,gCAA6D,CAA7D,EAA6D,KAA7D,EAA6D,EAA7D,EAA6D,CAA7D,EAA6D,KAA7D,EAA6D,EAA7D,EAA6D,CAA7D,EAA6D,KAA7D,EAA6D,EAA7D;IAIQA;;IACFA;IACAA;IACEA;;IAIFA;;;;;IAPEA;IAAAA;IAGAA;IAAAA;;;;;;;;IAzElBA;IACEA,gCAA+B,CAA/B,EAA+B,KAA/B,EAA+B,EAA/B;IAGIA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAAT;IAAmC,CAAnC;IAGAA,gCAA4C,CAA5C,EAA4C,KAA5C,EAA4C,EAA5C,EAA4C,CAA5C,EAA4C,KAA5C,EAA4C,EAA5C,EAA4C,CAA5C,EAA4C,KAA5C,EAA4C,EAA5C;IAMQA;IAMFA;IAEFA;IACEA;IAMAA;IACEA;;IACFA;IAgBNA,iCAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC,EAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC,EAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC,EAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC,EAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC;IAQYA;;IACFA;IACAA;IACEA;IAGFA;IAMNA;IAeFA;IAGAA,iCAAiB,EAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,EAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,EAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,EAAjB,EAAiB,KAAjB,EAAiB,EAAjB;IAMUA;;IACFA;IACAA;IACEA;IACFA;IAMNA,iCAAmB,EAAnB,EAAmB,KAAnB,EAAmB,EAAnB,EAAmB,EAAnB,EAAmB,KAAnB,EAAmB,EAAnB,EAAmB,EAAnB,EAAmB,KAAnB,EAAmB,EAAnB;IAIQA;;IACFA;IACAA;IACEA;IAGFA;IAQhBA;;;;;IAxGgBA;IAAAA;IAUDA;IAAAA;IAKDA;IAAAA;IAyBIA;IAAAA;IAGAA;IAAAA;IASYA;IAAAA;IAwBZA;IAAAA;IAGAA;IAAAA;IAWAA;IAAAA;IAGAA;IAAAA;;;;;;IA5GpBA;IACEA;IAuHFA;;;;;IAvHiCA;IAAAA;;;;;;IA0H/BA;;;;;IACEA;;;;;;IAnKVA;IACEA,gCAAwD,CAAxD,EAAwD,KAAxD,EAAwD,EAAxD;IAIIA;IAyBAA,gCAA6C,CAA7C,EAA6C,KAA7C,EAA6C,EAA7C,EAA6C,CAA7C,EAA6C,MAA7C,EAA6C,EAA7C;IAEuBA;IAA0BA;IAC7CA;IACAA;IAAMA;;IAAwCA;IAKlDA;IA0HAA;IAKFA;IAEJA;;;;;;;IAlKSA;IAAAA;IA0BoBA;IAAAA;IAEbA;IAAAA;IAKYA;IAAAA,yFAA6B,UAA7B,EAA6BC,GAA7B;;;;;;IAmI1BD;;;;IAA0BA;;;;;;;;IAG1BA;IACEA;IACEA;MAAAA;MAAA;MAAA,OAAsBA,gDAAtB;IAA8C,CAA9C;IAGFA;IACFA;;;;;IAHIA;IAAAA;;;;AD9KN,OAAM,MAAOE,eAAP,CAAsB;EAuD1BC,YACmBC,WADnB,EAEUC,cAFV,EAGUC,GAHV,EAIUC,MAJV,EAKmBC,mBALnB,EAMmBC,gBANnB,EAOmBC,iBAPnB,EAOuD;IANpC;IACT;IACA;IACA;IACS;IACA;IACA;IA7DZ,aAA2B,EAA3B;IACA,qBAAmC,EAAnC;IACA,eAAmB,IAAnB;IACS,6BACdZ,gBAAgB,CAACa,gBADH;IAGT,wBACL,KAAKF,gBAAL,CAAsBG,OAAtB,CAA8B,aAA9B,CADK;IAEA,+BACL,KAAKH,gBAAL,CAAsBG,OAAtB,CAA8B,mBAA9B,CADK;IAIA,yBAAoB,IAAIlB,eAAJ,CAA6B,IAA7B,CAApB;IAIA,0BAA8B,KAA9B;IACP,sBAAiBK,cAAjB;IAEO,yBAAiC;MACtCc,UAAU,EAAE,KAAKC,gBADqB;MAEtCC,kBAAkB,EAAE,KAAKC,uBAFa;MAGtCC,gBAAgB,EAAE,KAAKR,gBAAL,CAAsBG,OAAtB,CAA8B,mBAA9B,CAHoB;MAItCM,SAAS,EAAE,MAAK;QACd,KAAKC,kBAAL,GAA0B,KAA1B;QACA,OAAO,IAAP;MACD,CAPqC;MAQtCC,WAAW,EAAE,MAAK;QAChB,KAAKD,kBAAL,GAA0B,KAA1B;QACA,KAAKE,WAAL,GAAmB,IAAnB;QACA,KAAKb,mBAAL,CAAyBc,GAAzB,CAA6B,KAAKC,qBAAlC,EAAyD,EAAzD;QACA,KAAKJ,kBAAL,GAA0B,IAA1B;QACA,KAAKb,GAAL,CAASkB,aAAT;QACA,OAAO,KAAP;MACD,CAfqC;MAgBtCC,aAAa,EAAE,MAAK;QAClB,KAAKnB,GAAL,CAASkB,aAAT;QACA,KAAKhB,mBAAL,CAAyBc,GAAzB,CACE,KAAKC,qBADP,EAEE,KAAKF,WAFP;QAIA,KAAKK,iBAAL,CAAuBC,IAAvB,CAA4B,IAA5B;QACA,KAAKR,kBAAL,GAA0B,KAA1B;QACA,OAAO,IAAP;MACD,CAzBqC;MA0BtCS,iBAAiB,EAAE;QACjBC,QAAQ,EAAE,QADO;QAEjBC,IAAI,EAAE,IAFW;QAGjBC,QAAQ,EAAE,KAHO;QAIjBC,QAAQ,EAAE;MAJO;IA1BmB,CAAjC;IAiCC,gBAA6B,IAAIrC,OAAJ,EAA7B;EAUJ;;EAEJsC,QAAQ;IACN,KAAKC,SAAL;EACD;;EAEDA,SAAS,CAACC,aAA4B,IAA7B,EAAiC;IACxC,KAAK/B,WAAL,CACGgC,uBADH,CAC2BD,UAD3B,EAEGE,IAFH,CAGIzC,QAAQ,CAAC,MAAK;MACZ,KAAKS,cAAL,CAAoBiC,WAApB;MACA,KAAKC,OAAL,GAAe,KAAf;MACA,KAAKjC,GAAL,CAASkB,aAAT;IACD,CAJO,CAHZ,EASGgB,SATH,CASa;MACTb,IAAI,EAAGc,QAAD,IAAgC;QACpC,KAAKC,KAAL,GAAaD,QAAb;QACA,KAAKjC,mBAAL,CACGmC,KADH,CACS,KAAKpB,qBADd,EAEGc,IAFH,CAEQxC,SAAS,CAAC,KAAK+C,QAAN,CAFjB,EAGGJ,SAHH,CAGa;UACTb,IAAI,EAAGkB,GAAD,IAAQ;YACZ,KAAKC,gBAAL,GAAwBD,GAAxB;YACA,KAAKE,WAAL;UACD;QAJQ,CAHb;MASD,CAZQ;MAaTC,KAAK,EAAGC,CAAD,IAAM;QACX,KAAKP,KAAL,GAAa,EAAb;MACD;IAfQ,CATb;EA0BD;;EAEDQ,WAAW,CAACC,KAAD,EAAiB;IAC1B,IAAGA,KAAH,EAAU;MACR,KAAK9C,cAAL,CAAoB+C,YAApB;MACA,KAAKlB,SAAL,CAAe,MAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEmB,OAAP,KAAkB,IAAjC;IACD;EACF;;EAEDC,sBAAsB,CAACC,UAAD,EAA2B;IAC/C,OAAO,KAAK7C,iBAAL,CAAuB8C,qBAAvB,CAA6CD,UAA7C,CAAP;EACD;;EAEDE,oBAAoB,CAACC,IAAD,EAAsB;IACxC,MAAM;MAAEC;IAAF,IAAiBD,IAAvB;IACA,KAAKnD,MAAL,CAAYqD,aAAZ,CACEnE,kBAAkB,CAAC,GAAGkE,UAAU,EAAd,EAAkB;MAAEE,MAAM,EAAEH,IAAI,CAACI;IAAf,CAAlB,CADpB;EAGD;;EAEOf,WAAW;IACjB,KAAKgB,aAAL,GAAqB,KAAKrB,KAAL,CAAWsB,MAAX,CAAmBN,IAAD,IAA0B;;;MAC/D,OACE,CAAC,EAAC,uBAAKZ,gBAAL,MAAqB,IAArB,IAAqBmB,aAArB,GAAqB,MAArB,GAAqBA,GAAEC,GAAvB,MAA0B,IAA1B,IAA0BC,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,WAA5B,MAAuC,IAAvC,IAAuCC,aAAvC,GAAuC,MAAvC,GAAuCA,GAAEhB,OAA1C,KACC,iBAAI,CAACiB,cAAL,MAAmB,IAAnB,IAAmBC,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,mBAArB,MAAwC,IAAxC,IAAwCC,aAAxC,GAAwC,MAAxC,GAAwCA,GAAEC,WAAF,EAAxC,OACE,6BAAK5B,gBAAL,MAAqB,IAArB,IAAqB6B,aAArB,GAAqB,MAArB,GAAqBA,GAAET,GAAvB,MAA0B,IAA1B,IAA0BU,aAA1B,GAA0B,MAA1B,GAA0BA,GAAER,WAA5B,MAAuC,IAAvC,IAAuCS,aAAvC,GAAuC,MAAvC,GAAuCA,GAAExB,OAAzC,MAAgD,IAAhD,IAAgDyB,aAAhD,GAAgD,MAAhD,GAAgDA,GAAEJ,WAAF,EADlD,CADF,MAGC,EAAC,iBAAK5B,gBAAL,MAAqB,IAArB,IAAqBiC,aAArB,GAAqB,MAArB,GAAqBA,GAAEb,GAAvB,MAA0B,IAA1B,IAA0Bc,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,kBAA7B,KACC,WAAI,CAACX,cAAL,MAAmB,IAAnB,IAAmBY,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,8BAArB,OACE,iBAAKrC,gBAAL,MAAqB,IAArB,IAAqBsC,aAArB,GAAqB,MAArB,GAAqBA,GAAElB,GAAvB,MAA0B,IAA1B,IAA0BmB,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEJ,kBAD9B,CAJF,MAMC,EAAC,iBAAKnC,gBAAL,MAAqB,IAArB,IAAqBwC,aAArB,GAAqB,MAArB,GAAqBA,GAAEpB,GAAvB,MAA0B,IAA1B,IAA0BqB,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,gBAA7B,KACE,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,SAAN,KACC,KAAKC,2BAAL,CACE,IAAIC,IAAJ,CAASjC,IAAI,CAAC+B,SAAd,CADF,EAEEG,OAFF,MAGE,KAAKC,gBAAL,CACE,iBAAK/C,gBAAL,MAAqB,IAArB,IAAqBgD,aAArB,GAAqB,MAArB,GAAqBA,GAAE5B,GAAvB,MAA0B,IAA1B,IAA0B6B,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEP,gBAD9B,EAEEI,OAFF,EAXN,MAcC,EAAC,iBAAK9C,gBAAL,MAAqB,IAArB,IAAqBkD,aAArB,GAAqB,MAArB,GAAqBA,GAAE9B,GAAvB,MAA0B,IAA1B,IAA0B+B,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,cAA7B,KACExC,IAAI,CAAC+B,SAAL,IACC,KAAKC,2BAAL,CACE,IAAIC,IAAJ,CAASjC,IAAI,CAAC+B,SAAd,CADF,EAEEG,OAFF,MAGE,KAAKC,gBAAL,CACE,iBAAK/C,gBAAL,MAAqB,IAArB,IAAqBqD,aAArB,GAAqB,MAArB,GAAqBA,GAAEjC,GAAvB,MAA0B,IAA1B,IAA0BkC,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEF,cAD9B,EAEEN,OAFF,EAnBN,CADF;IAwBD,CAzBoB,CAArB;IA0BA,KAAKtF,GAAL,CAASkB,aAAT;EACD;;EAEOqE,gBAAgB,CAAC3B,GAAD,EAAkD;IACxE,MAAM;MAAEmC,GAAF;MAAOC,KAAP;MAAcC;IAAd,IAAuBrC,GAA7B;IACA,OAAO,IAAIyB,IAAJ,CAASY,IAAT,EAAeD,KAAK,GAAG,CAAvB,EAA0BD,GAA1B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,CAAP;EACD;;EAEOX,2BAA2B,CAACc,IAAD,EAAW;IAC5C,OAAO,IAAIb,IAAJ,CACLa,IAAI,CAACC,WAAL,EADK,EAELD,IAAI,CAACE,QAAL,EAFK,EAGLF,IAAI,CAACG,OAAL,EAHK,EAIL,CAJK,EAKL,CALK,EAML,CANK,EAOL,CAPK,CAAP;EASD;;EAEMC,gBAAgB,CAACC,KAAD,EAAW;IAChC,KAAKxF,WAAL,GAAmBwF,KAAnB;EACD;;EAEKC,eAAe;IAAA;;IAAA;MACnB,KAAI,CAAC3F,kBAAL,GAA0B,IAA1B;;MACA,KAAI,CAACb,GAAL,CAASkB,aAAT;;MACA,aAAa,KAAI,CAACuF,WAAL,CAAiBC,IAAjB,EAAb;IAHmB;EAIpB;;EAEDC,WAAW;IACT,KAAKrE,QAAL,CAAcjB,IAAd,CAAmB,IAAnB;IACA,KAAKiB,QAAL,CAAcsE,WAAd;EACD;;AAhLyB;;;mBAAfhH,iBAAeF;AAAA;;;QAAfE;EAAeiH;EAAAC;IAAA;;;;;;;;;;;;;;;MChC5BpH,+BAAqB,CAArB,EAAqB,KAArB,EAAqB,CAArB,EAAqB,CAArB,EAAqB,KAArB,EAAqB,CAArB,EAAqB,CAArB,EAAqB,IAArB,EAAqB,CAArB;MASQA;;MACFA;MAGFA,+BAAoB,CAApB,EAAoB,QAApB,EAAoB,CAApB;MAEIA;QAAA,OAASqH,qBAAT;MAA0B,CAA1B;MAIArH;MAIAA;;MACFA;MAMNA;MAyKAA;MAGAA;MACEA;MAOFA;;;;;;MAzMQA;MAAAA;MAWEA;MAAAA;MAGFA;MAAAA;MAOOA;MAAAA,oCAAgB,UAAhB,EAAgBsH,GAAhB;MA4KStH;MAAAA;MACPA;MAAAA", "names": ["ReplaceUrlVariable", "BehaviorSubject", "Subject", "finalize", "takeUntil", "LOCALSTORAGE_KEY", "PermissionEnum", "i0", "_r7", "MytaskComponent", "constructor", "taskService", "spinnerService", "cdr", "router", "localStorageService", "translateService", "permissionService", "TASK_LIST_FILTER", "instant", "modalTitle", "filterModalTitle", "dismissButtonLabel", "modalDismissButtonLabel", "closeButtonLabel", "on<PERSON><PERSON><PERSON>", "isFilterModalReady", "shouldClose", "filtersData", "set", "filterLocalStorageKey", "detectChanges", "<PERSON><PERSON><PERSON><PERSON>", "filterChangeEvent", "next", "modalDialogConfig", "backdrop", "size", "keyboard", "centered", "ngOnInit", "fetchList", "assignedTo", "getUserPendingTasksList", "pipe", "stopSpinner", "loading", "subscribe", "response", "tasks", "watch", "destroy$", "val", "storedFilterData", "filterTasks", "error", "_", "onUserAdded", "value", "startSpinner", "loginId", "validateUserPermission", "permission", "checkPermissionByName", "nagivateToTaskAction", "task", "taskRelUrl", "navigateByUrl", "taskId", "id", "filteredTasks", "filter", "_a", "obj", "_b", "submittedBy", "_c", "additionalInfo", "_d", "proposal_created_by", "_e", "toLowerCase", "_f", "_g", "_h", "_j", "_k", "_l", "afeReferenceNumber", "_m", "proposalProjectReferenceNumber", "_o", "_p", "_q", "_r", "fromAssignedDate", "createdOn", "createDateObjectWithOutTime", "Date", "getTime", "createDateObject", "_s", "_t", "_u", "_v", "toAssignedDate", "_w", "_x", "day", "month", "year", "date", "getFullYear", "getMonth", "getDate", "getFilterPayload", "event", "openFilterModal", "filterModal", "open", "ngOnDestroy", "unsubscribe", "selectors", "viewQuery", "ctx", "_r1"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\modules\\mytask\\mytask.component.ts", "C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\modules\\mytask\\mytask.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectorR<PERSON>,\r\n  Component,\r\n  On<PERSON><PERSON>roy,\r\n  OnInit,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ReplaceUrlVariable } from '@core/constants/urls.constants';\r\nimport { SpinnerService } from '@core/services/common/spinner.service';\r\nimport {\r\n  BehaviorSubject,\r\n  Observable,\r\n  Subject,\r\n  finalize,\r\n  takeUntil,\r\n} from 'rxjs';\r\nimport { TaskDetailModel } from './core/models/task-detail.model';\r\nimport { TaskService } from './core/services';\r\nimport { LOCALSTORAGE_KEY } from '@core/constants';\r\nimport { LocalStorageService } from '@core/services/common/localStorage.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { ModalComponent, ModalConfig } from '@core/modules/partials';\r\nimport { UserModel } from '@core/models/basic/user';\r\nimport { PermissionEnum } from '@core/enums/Permission';\r\nimport { PermissionService } from '@core/services/common/permission.service';\r\n\r\n@Component({\r\n  selector: 'app-mytask',\r\n  templateUrl: './mytask.component.html',\r\n  styleUrls: ['./mytask.component.scss'],\r\n})\r\nexport class MytaskComponent implements OnInit, OnDestroy {\r\n  public tasks: TaskDetailModel[] = [];\r\n  public filteredTasks: TaskDetailModel[] = [];\r\n  public loading: boolean = true;\r\n  public readonly filterLocalStorageKey: string =\r\n    LOCALSTORAGE_KEY.TASK_LIST_FILTER;\r\n\r\n  public filterModalTitle: string =\r\n    this.translateService.instant('MENU.FILTER');\r\n  public modalDismissButtonLabel: string =\r\n    this.translateService.instant('FORM.BUTTON.APPLY');\r\n  public filtersData: any;\r\n  public storedFilterData: any | null;\r\n  public filterChangeEvent = new BehaviorSubject<boolean>(true);\r\n  public filterLocalStorage: Observable<any>;\r\n  @ViewChild('filterModal') private filterModal: ModalComponent;\r\n\r\n  public isFilterModalReady: boolean = false;\r\n  permissionEnum = PermissionEnum;\r\n  \r\n  public filterModalConfig: ModalConfig = {\r\n    modalTitle: this.filterModalTitle,\r\n    dismissButtonLabel: this.modalDismissButtonLabel,\r\n    closeButtonLabel: this.translateService.instant('FORM.BUTTON.RESET'),\r\n    onDismiss: () => {\r\n      this.isFilterModalReady = false;\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      this.isFilterModalReady = false;\r\n      this.filtersData = null;\r\n      this.localStorageService.set(this.filterLocalStorageKey, '');\r\n      this.isFilterModalReady = true;\r\n      this.cdr.detectChanges();\r\n      return false;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.cdr.detectChanges();\r\n      this.localStorageService.set(\r\n        this.filterLocalStorageKey,\r\n        this.filtersData\r\n      );\r\n      this.filterChangeEvent.next(true);\r\n      this.isFilterModalReady = false;\r\n      return true;\r\n    },\r\n    modalDialogConfig: {\r\n      backdrop: 'static',\r\n      size: 'lg',\r\n      keyboard: false,\r\n      centered: true,\r\n    },\r\n  };\r\n  private destroy$: Subject<boolean> = new Subject<boolean>();\r\n\r\n  constructor(\r\n    private readonly taskService: TaskService,\r\n    private spinnerService: SpinnerService,\r\n    private cdr: ChangeDetectorRef,\r\n    private router: Router,\r\n    private readonly localStorageService: LocalStorageService,\r\n    private readonly translateService: TranslateService,\r\n    private readonly permissionService: PermissionService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.fetchList();\r\n  }\r\n\r\n  fetchList(assignedTo: string | null = null) {\r\n    this.taskService\r\n      .getUserPendingTasksList(assignedTo)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.spinnerService.stopSpinner();\r\n          this.loading = false;\r\n          this.cdr.detectChanges();\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: TaskDetailModel[]) => {\r\n          this.tasks = response;\r\n          this.localStorageService\r\n            .watch(this.filterLocalStorageKey)\r\n            .pipe(takeUntil(this.destroy$))\r\n            .subscribe({\r\n              next: (val) => {\r\n                this.storedFilterData = val;\r\n                this.filterTasks();\r\n              },\r\n            });\r\n        },\r\n        error: (_) => {\r\n          this.tasks = [];\r\n        },\r\n      });\r\n  }\r\n\r\n  onUserAdded(value: UserModel) {\r\n    if(value) {\r\n      this.spinnerService.startSpinner();\r\n      this.fetchList(value?.loginId || null);\r\n    }\r\n  }\r\n\r\n  validateUserPermission(permission: PermissionEnum) {\r\n    return this.permissionService.checkPermissionByName(permission);\r\n  }\r\n\r\n  nagivateToTaskAction(task: TaskDetailModel) {\r\n    const { taskRelUrl } = task;\r\n    this.router.navigateByUrl(\r\n      ReplaceUrlVariable(`${taskRelUrl}`, { taskId: task.id })\r\n    );\r\n  }\r\n\r\n  private filterTasks() {\r\n    this.filteredTasks = this.tasks.filter((task: TaskDetailModel) => {\r\n      return (\r\n        (!this.storedFilterData?.obj?.submittedBy?.loginId ||\r\n          task.additionalInfo?.proposal_created_by?.toLowerCase() ===\r\n            this.storedFilterData?.obj?.submittedBy?.loginId?.toLowerCase()) &&\r\n        (!this.storedFilterData?.obj?.afeReferenceNumber ||\r\n          task.additionalInfo?.proposalProjectReferenceNumber ===\r\n            this.storedFilterData?.obj?.afeReferenceNumber) &&\r\n        (!this.storedFilterData?.obj?.fromAssignedDate ||\r\n          (task?.createdOn &&\r\n            this.createDateObjectWithOutTime(\r\n              new Date(task.createdOn)\r\n            ).getTime() >=\r\n              this.createDateObject(\r\n                this.storedFilterData?.obj?.fromAssignedDate\r\n              ).getTime())) &&\r\n        (!this.storedFilterData?.obj?.toAssignedDate ||\r\n          (task.createdOn &&\r\n            this.createDateObjectWithOutTime(\r\n              new Date(task.createdOn)\r\n            ).getTime() <=\r\n              this.createDateObject(\r\n                this.storedFilterData?.obj?.toAssignedDate\r\n              ).getTime()))\r\n      );\r\n    });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private createDateObject(obj: { day: number; month: number; year: number }) {\r\n    const { day, month, year } = obj;\r\n    return new Date(year, month - 1, day, 0, 0, 0, 0);\r\n  }\r\n\r\n  private createDateObjectWithOutTime(date: Date) {\r\n    return new Date(\r\n      date.getFullYear(),\r\n      date.getMonth(),\r\n      date.getDate(),\r\n      0,\r\n      0,\r\n      0,\r\n      0\r\n    );\r\n  }\r\n\r\n  public getFilterPayload(event: any) {\r\n    this.filtersData = event;\r\n  }\r\n\r\n  async openFilterModal() {\r\n    this.isFilterModalReady = true;\r\n    this.cdr.detectChanges();\r\n    return await this.filterModal.open();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next(true);\r\n    this.destroy$.unsubscribe();\r\n  }\r\n}\r\n", "<div class=\"toolbar\">\r\n  <div\r\n    id=\"kt_toolbar_container\"\r\n    class=\"d-flex flex-end flex-stack container-fluid\"\r\n  >\r\n    <div class=\"page-title d-flex align-items-center flex-wrap me-3\">\r\n      <h1\r\n        class=\"d-flex align-items-center text-dark fw-bolder my-1 fs-3 mr-2 mb-0 mt-0\"\r\n      >\r\n        {{ \"MENU.MY_TASKS\" | translate }}\r\n      </h1>\r\n    </div>\r\n\r\n    <div class=\"d-flex\">\r\n      <button\r\n        (click)=\"openFilterModal()\"\r\n        type=\"button\"\r\n        class=\"btn btn-sm btn-primary textSize px-5\"\r\n      >\r\n        <span\r\n          [inlineSVG]=\"'./assets/media/icons/duotune/general/gen031.svg'\"\r\n          class=\"svg-icon svg-icon-5 svg-icon-gray-500 me-1\"\r\n        ></span>\r\n        {{ \"FORM.BUTTON.FILTER\" | translate }}\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Main Content Container -->\r\n<ng-container *ngIf=\"!loading; else loadingPage\">\r\n  <div class=\"post d-flex flex-column-fluid\" id=\"kt_post\">\r\n    <div id=\"kt_content_container\">\r\n      <!-- Filter Section -->\r\n\r\n      <ng-container\r\n        *ngIf=\"validateUserPermission(permissionEnum.AFEAdministration)\"\r\n      >\r\n        <div class=\"card border-0 shadow-sm mb-6\">\r\n          <div class=\"card-body py-4\">\r\n            <div class=\"row align-items-center\">\r\n              <div class=\"col-md-8\">\r\n                <app-filter-badges [localStorageKey]=\"filterLocalStorageKey\">\r\n                </app-filter-badges>\r\n              </div>\r\n              <div class=\"col-md-4\">\r\n                <app-ad-user-search\r\n                  placeholder=\"{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{\r\n                    'LIST.ASSIGNED_TO' | translate | lowercase\r\n                  }}\"\r\n                  [isMultiSelect]=\"false\"\r\n                  (userSelected)=\"onUserAdded($event)\"\r\n                >\r\n                </app-ad-user-search>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <div class=\"d-flex justify-content-end mb-5\">\r\n        <div class=\"badge badge-light-danger fs-6 fw-bold px-4 py-2\">\r\n          <span class=\"ms-2\">{{ filteredTasks.length }}</span>\r\n          <span class=\"ms-2\"></span>\r\n          <span>{{ \"COMMON.PENDING_TASKS\" | translate }}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Tasks Grid -->\r\n      <div class=\"row g-6\" *ngIf=\"filteredTasks?.length; else noDataMessage\">\r\n        <ng-container *ngFor=\"let task of filteredTasks; let i = index\">\r\n          <div class=\"col-lg-6 col-xl-4\">\r\n            <div\r\n              class=\"card border-0 shadow-lg hover-elevate-up transition-all-300ms cursor-pointer h-100 note-box overflow-hidden\"\r\n              (click)=\"nagivateToTaskAction(task)\"\r\n            >\r\n              <!-- Card Header -->\r\n              <div class=\"card-header border-0 pt-5 pb-3\">\r\n                <div class=\"d-flex align-items-center w-100\">\r\n                  <div class=\"symbol symbol-40px me-3\">\r\n                    <div\r\n                      class=\"symbol-label bg-white bg-opacity-20 backdrop-blur\"\r\n                    >\r\n                      <span\r\n                        [inlineSVG]=\"\r\n                          './assets/media/icons/duotune/arrows/arr064.svg'\r\n                        \"\r\n                        class=\"svg-icon svg-icon-2 svg-icon-white\"\r\n                      ></span>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"flex-grow-1\">\r\n                    <h5\r\n                      class=\"card-title fw-bold text-white mb-1\"\r\n                      *ngIf=\"task.title\"\r\n                    >\r\n                      {{ task.title }}\r\n                    </h5>\r\n                    <div class=\"text-white-75 fs-7 fw-semibold\">\r\n                      {{ task.createdOn | date : \"MMM dd, yyyy ',' h:mm a\" }}\r\n                    </div>\r\n                  </div>\r\n                  <!-- <div class=\"ms-auto\">\r\n                    <div class=\" rounded-circle p-2\">\r\n                      <span\r\n                        [inlineSVG]=\"\r\n                          './assets/media/icons/duotune/arrows/arr064.svg'\r\n                        \"\r\n                        class=\"svg-icon svg-icon-4 svg-icon-white\"\r\n                      ></span>\r\n                    </div>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Card Body -->\r\n              <div class=\"card-body pt-3 pb-5\">\r\n                <!-- Row 1: Project Name & Total Amount -->\r\n                <div class=\"row mb-4\">\r\n                  <!-- Project Name -->\r\n                  <div class=\"col-12\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                      <div class=\"flex-grow-1\">\r\n                        <div class=\"fw-bold text-white pb-1 d-inline w-100\">\r\n                          {{ \"LIST.PROJECT_NAME\" | translate }}\r\n                        </div>\r\n                        <div class=\"d-inline-block\">\r\n                          {{\r\n                            task?.additionalInfo?.proposal_project_name || \"N/A\"\r\n                          }}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Total Amount -->\r\n                  <div class=\"col-6\" *ngIf=\"task?.additionalInfo?.totalAmount\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                      <div class=\"flex-grow-1\">\r\n                        <div class=\"form-label fw-bold text-white mb-1\">\r\n                          {{ \"LIST.TOTAL_AMOUNT\" | translate }}\r\n                        </div>\r\n                        <div class=\"text-white fw-bold fs-7\">\r\n                          {{\r\n                            task?.additionalInfo?.totalAmount\r\n                              | currency : \"USD\" : \"symbol\" : \"1.2-2\"\r\n                          }}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Row 2: Assigned To & Submitted By -->\r\n                <div class=\"row\">\r\n                  <!-- Assigned To -->\r\n                  <div class=\"col-6\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                      <div class=\"flex-grow-1\">\r\n                        <div class=\"form-label fw-bold text-white mb-1\">\r\n                          {{ \"LIST.ASSIGNED_TO\" | translate }}\r\n                        </div>\r\n                        <div class=\"text-white fw-bold fs-7 lh-sm\">\r\n                          {{ task.assignedTo || \"N/A\" }}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Submitted By -->\r\n                  <div class=\"col-6\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                      <div class=\"flex-grow-1\">\r\n                        <div class=\"form-label fw-bold text-white mb-1\">\r\n                          {{ \"FORM.LABEL.SUBMITTED_BY\" | translate }}\r\n                        </div>\r\n                        <div class=\"text-white fw-bold fs-7 lh-sm\">\r\n                          {{\r\n                            task?.additionalInfo?.proposal_created_by || \"N/A\"\r\n                          }}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n      <!-- No Data State -->\r\n      <ng-template #noDataMessage>\r\n        <app-no-data\r\n          [message]=\"'ERROR.NO_TASK_AVAILABLE' | translate\"\r\n        ></app-no-data>\r\n      </ng-template>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n<ng-template #loadingPage>\r\n  <app-list-skeleton-loader [loaderCount]=\"4\"></app-list-skeleton-loader>\r\n</ng-template>\r\n<app-modal #filterModal [modalConfig]=\"filterModalConfig\">\r\n  <ng-container *ngIf=\"isFilterModalReady\">\r\n    <app-task-filter-modal\r\n      (filterPayloadEvent)=\"getFilterPayload($event)\"\r\n      [localStorageKey]=\"filterLocalStorageKey\"\r\n    >\r\n    </app-task-filter-modal>\r\n  </ng-container>\r\n</app-modal>\r\n"]}, "metadata": {}, "sourceType": "module"}