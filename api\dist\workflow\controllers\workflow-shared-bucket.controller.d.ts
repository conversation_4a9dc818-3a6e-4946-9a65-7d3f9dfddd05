import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { NewSharedBucketLimitRequestDTO } from '../dtos/request/new-shared-bucket-limit-request.dto';
import { GetBucketSharedLimitResponseDTO, GetBucketSharedLimitWithStepResponseDTO } from '../dtos/response/get-bucket-shared-limit-response.dto';
import { WorkflowSharedBucketService } from '../services/workflow-shared-bucket.service';
export declare class WorkflowSharedBucketController {
    private workflowSharedBucketService;
    constructor(workflowSharedBucketService: WorkflowSharedBucketService);
    getSharedBucketLimitList(stepId: number): Promise<GetBucketSharedLimitWithStepResponseDTO>;
    addNewBucketEntity(stepId: number, newSharedBucketLimitRequestDTO: NewSharedBucketLimitRequestDTO, request: RequestContext): Promise<GetBucketSharedLimitResponseDTO>;
    deleteBucketEntity(id: number, request: RequestContext): Promise<MessageResponseDto>;
}
