{"version": 3, "file": "sftp.service.js", "sourceRoot": "", "sources": ["../../src/sftp-service/sftp.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,uCAAyB;AACzB,kDAAoC;AACpC,2CAA6B;AAC7B,wEAA0C;AAE1C,+DAAuI;AACvI,mCAAkC;AAClC,+CAA2D;AAC3D,wGAAkG;AAClG,0DAAqE;AACrE,mDAAmD;AACnD,2CAA6C;AAG7C,IAAa,WAAW,GAAxB,MAAa,WAAW;IAGpB,YACqB,UAAsB,EACtB,8BAA8D,EAC9D,qBAA4C,EAC5C,gCAAkE,EAClE,6BAA4D,EAC5D,qBAA4C,EAC5C,qBAA4C;QAN5C,eAAU,GAAV,UAAU,CAAY;QACtB,mCAA8B,GAA9B,8BAA8B,CAAgC;QAC9D,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,qCAAgC,GAAhC,gCAAgC,CAAkC;QAClE,kCAA6B,GAA7B,6BAA6B,CAA+B;QAC5D,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,0BAAqB,GAArB,qBAAqB,CAAuB;IAC7D,CAAC;IAEQ,GAAG;;YAEZ,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,8BAA8B,CAAC,2BAA2B,EAAE,CAAC;YAEjG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;gBAC1B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAClD,OAAO;aACV;YAED,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE;gBAEtC,IAAI;oBAEA,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;oBAEjF,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAEtC,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CACpF,QAAQ,CACX,CAAC;oBAEF,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;wBAClC,OAAO,CAAC,GAAG,CAAC,qCAAqC,GAAG,KAAK,CAAC,CAAC;wBAC3D,OAAO;qBACV;oBAED,OAAO,CAAC,GAAG,CAAC,2CAA2C,GAAG,KAAK,GAAG,iBAAiB,CAAC,CAAC;oBAErF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAExC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;oBACpE,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBAG9C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;oBAE3G,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,GAAG,mBAAmB,CAAC,EAAE,YAAY,CAAC,CAAC;oBAExG,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,IAAA,iBAAQ,EAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;oBAEzF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,gCAAgC,CAAC,cAAc,CAAC,CAAC;oBACtH,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,GAAG,uBAAuB,CAAC,EAAE,gBAAgB,CAAC,CAAC;oBAEhH,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,mCAAmC,CAAC,cAAc,CAAC,CAAC;oBAC1H,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,GAAG,2BAA2B,CAAC,EAAE,oBAAoB,CAAC,CAAC;oBAExH,IAAI,QAAQ,EAAE;wBACV,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;qBAC5D;oBAMD,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;oBACtC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;oBACzC,OAAO,CAAC,GAAG,CAAC,uCAAuC,GAAG,KAAK,GAAG,sBAAsB,CAAC,CAAA;oBAErF,IAAI,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;oBAC7B,IAAI,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;oBAE3B,IAAI,SAAS,KAAK,iBAAS,CAAC,OAAO,EAAE;wBACjC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;wBAC/C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;qBAC/B;yBAAM;wBACH,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;qBAChD;oBAED,MAAM,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,SAAS,EAAE,uBAAW,CAAC,CAAC;iBACrG;gBAAC,OAAO,KAAK,EAAE;oBACZ,OAAO,CAAC,KAAK,CAAC,mCAAmC,SAAS,CAAC,KAAK,IAAI,EAAE,KAAK,CAAC,CAAC;iBAChF;aACJ;QACL,CAAC;KAAA;IAEO,uBAAuB,CAAC,aAAa,EAAE,eAAe,GAAG,KAAK;QAClE,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,SAAS,QAAQ,CAAC,IAAI;YAClB,IAAI,IAAA,iBAAQ,EAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC1B,MAAM,CAAC,IAAI,iCACJ,IAAI,KACP,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAChD,CAAC;aACN;YAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;aACnD;QACL,CAAC;QAED,QAAQ,CAAC,aAAa,CAAC,CAAC;QAExB,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,oBAAoB,CAAC,YAAY;;QACrC,IAAI,CAAC,CAAA,MAAA,YAAY,CAAC,QAAQ,0CAAE,MAAM,CAAA,EAAE;YAChC,OAAO,CAAC,YAAY,CAAC,CAAC;SACzB;QAED,OAAO,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/E,CAAC;IAEa,sBAAsB,CAAC,QAAgB,EAAE,IAAW;;YAC9D,IAAI;gBACA,OAAO,CAAC,GAAG,CAAC,8BAA8B,GAAG,QAAQ,CAAC,CAAA;gBACtD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACnC,MAAM,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBAC1C,OAAO;yBACF,KAAK,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;yBAC9B,IAAI,CAAC,EAAE,CAAC;yBACR,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;yBACrB,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;aACN;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;gBAClE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAEa,YAAY,CAAC,SAAiB,EAAE,UAAsB,EAAE,SAAiB;;YACnF,IAAI;gBACA,MAAM,EAAE,UAAU,KAA0B,UAAU,EAA/B,gBAAgB,UAAK,UAAU,EAAhD,cAAmC,CAAa,CAAC;gBACvD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;gBACzD,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;gBAEhD,MAAM,SAAS,GAAG;oBACd,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,IAAI;oBACd,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,IAAI;iBAClB,CAAC;gBAEF,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBAExC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;oBACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAI5C,MAAM,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;oBACnC,MAAM,UAAU,GAAG,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC;oBAK1C,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;iBAC9D;gBAED,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;gBAC7D,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;aACtC;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;gBAC7C,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM,KAAK,CAAC;aACf;QAEL,CAAC;KAAA;IAEa,gBAAgB,CAAC,KAAa,EAAE,SAAiB,EAAE,OAAe;;YAC5E,IAAI;gBACA,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;gBACjD,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBAExC,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC5C,MAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oBACvC,OAAO;wBACH,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,QAAQ;qBACnB,CAAC;gBACN,CAAC,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG;oBACZ,SAAS,EAAE,CAAC;oBACZ,WAAW,EAAE,MAAM;oBACnB,OAAO,EAAE,KAAK;oBACd,iBAAiB,EAAE,KAAK;oBACxB,QAAQ,EAAE,OAAO;oBACjB,IAAI,EAAE,sBAAsB;oBAC5B,WAAW;iBACd,CAAC;gBAEF,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBAC3D,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;aACxC;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;gBACzD,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAEa,qBAAqB,CAAC,SAAiB;;YACjD,IAAI;gBACA,OAAO,CAAC,GAAG,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAA;gBAEtD,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtC,IAAI,UAAU,GAAG,EAAE,CAAC;gBAGpB,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;oBACzB,IAAI,IAAI,EAAE;wBACN,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC;wBACzB,IAAI;4BACA,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;yBACjD;wBAAC,OAAO,KAAK,EAAE;4BAEZ,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;gCACjD,MAAM,KAAK,CAAC;6BACf;yBACJ;qBACJ;iBACJ;gBACD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;aAC1C;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,qDAAqD,CAAC,CAAC;gBACrE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAEO,oBAAoB,CAAC,UAAkB;QAC3C,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAA;QAC1C,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAC7C,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAEpC,CAAC;IAEO,wBAAwB,CAAC,cAAsB;QACnD,MAAM,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC;aACzC,GAAG,CAAC,UAAU,CAAC,EAAE;YACd,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YACvD,OAAO;gBACH,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE;gBAC3C,IAAI,EAAE,QAAQ;aACjB,CAAC;QACN,CAAC,CAAC;aACD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;aACpD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC9B,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACvC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;gBACH,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;SACN;IACL,CAAC;CAEJ,CAAA;AA7QY,WAAW;IADvB,IAAA,mBAAU,GAAE;yDAKwB,0BAAU,oBAAV,0BAAU,gCACU,kEAA8B;QACvC,oCAAqB;QACV,+CAAgC;QACnC,4CAA6B;QACrC,+BAAqB;QACrB,gCAAqB;GAVxD,WAAW,CA6QvB;AA7QY,kCAAW"}