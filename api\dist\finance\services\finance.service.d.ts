import { EntitySetupRepository } from 'src/business-entity/repositories';
import { AdminApiClient } from 'src/shared/clients';
import { CURRENCY_TYPE } from 'src/shared/enums';
import { AllCostCenterResponseDto, AnalysisCodeResponseDto, CompanyCodeResponseDto, CostCenterFilterResponseDto, CostCenterResponseDto, CurrencyConversionResponseDto, NaturalAccountResponseDto } from '../dtos';
import { NaturalAccountNumberRepository, CostCenterRepository, CurrencyTypeRepository, CompanyCodeRepository, AnalysisCodeRepository } from '../repositories';
export declare class FinanceService {
    private readonly naturalAccountNumberRepository;
    private readonly costCenterRepository;
    private readonly currencyTypeRepository;
    private readonly entitySetupRepository;
    private readonly companyCodeRepository;
    private readonly analysisCodeRepository;
    private readonly adminApiClient;
    constructor(naturalAccountNumberRepository: NaturalAccountNumberRepository, costCenterRepository: CostCenterRepository, currencyTypeRepository: CurrencyTypeRepository, entitySetupRepository: EntitySetupRepository, companyCodeRepository: CompanyCodeRepository, analysisCodeRepository: AnalysisCodeRepository, adminApiClient: AdminApiClient);
    checkCompanyCodeExistsForBusinessEntity(entityId: number): Promise<boolean>;
    getCompanyDetailForBusinessEntity(entityId: number): Promise<CompanyCodeResponseDto>;
    getCompanyCodeListExcluding(excludedCode: string): Promise<import("../models").CompanyCode[]>;
    getNaturalAccountNumbersByRequestType(requestTypeId: number, entityId: number): Promise<NaturalAccountResponseDto[]>;
    getAnalysisCodesByRequestType(requestTypeId: number, entityId?: number): Promise<AnalysisCodeResponseDto[]>;
    getCostCentersWithCompanyCodeByEntity(entityId: number): Promise<CostCenterResponseDto[]>;
    getCurrencyConversionRateToPrimary(currencyType: CURRENCY_TYPE): Promise<CurrencyConversionResponseDto>;
    getCurrencyTypeForEntity(entityId: number): Promise<CurrencyConversionResponseDto>;
    getCostCenterByEntityIds(filter: {
        entityIds: number[];
    }): Promise<CostCenterFilterResponseDto[]>;
    getAllCostCenters(): Promise<AllCostCenterResponseDto[]>;
}
