"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowSharedChildLimitService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const get_child_shared_limit_response_dto_1 = require("../dtos/response/get-child-shared-limit-response.dto");
const repositories_1 = require("../repositories");
let WorkflowSharedChildLimitService = class WorkflowSharedChildLimitService {
    constructor(workflowMasterSettingRepository, workflowMasterStepRepository, adminApiClient, workflowSharedChildLimitRepository, workflowSharedBucketLimitRepository, sequlizeOperator, historyApiClient) {
        this.workflowMasterSettingRepository = workflowMasterSettingRepository;
        this.workflowMasterStepRepository = workflowMasterStepRepository;
        this.adminApiClient = adminApiClient;
        this.workflowSharedChildLimitRepository = workflowSharedChildLimitRepository;
        this.workflowSharedBucketLimitRepository = workflowSharedBucketLimitRepository;
        this.sequlizeOperator = sequlizeOperator;
        this.historyApiClient = historyApiClient;
    }
    addNewSharedLimit(newMasterStepRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(newMasterStepRequestDto.stepId);
            if (stepDetail) {
                const childStepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailWithSettingByCondition({
                    workflowMasterStepId: stepDetail.sharedLimitMasterStepChildId,
                    inherited: false
                });
                if (!childStepDetail) {
                    throw new exceptions_1.HttpException('Child workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
                }
                if (childStepDetail.singleLimit !== 0 || childStepDetail.aggregateLimit !== 0) {
                    throw new exceptions_1.HttpException('There is already a limit set on this child, so it can\'t be shared.', enums_1.HttpStatus.NOT_FOUND);
                }
                yield this.doBasicValidationCheck(stepDetail, newMasterStepRequestDto);
                const alreadyShared = yield this.workflowSharedChildLimitRepository.findChildSharedLimitByEntityAndWorkflowStep(stepDetail.workflowMasterStepId, childStepDetail.workflowMasterStepId, newMasterStepRequestDto.childEntityId);
                if (alreadyShared) {
                    throw new exceptions_1.HttpException('Limit has already been shared with the child.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                const childEntities = yield this.adminApiClient.getChildernListOfBusinessEntity(newMasterStepRequestDto.entityId);
                if (childEntities && childEntities.length) {
                    const isValidChild = childEntities.find((childEntityId) => {
                        return (childEntityId === newMasterStepRequestDto.childEntityId);
                    });
                    if (isValidChild) {
                        const { finalStepDetail, entitySharingSameBucket } = yield this.getActualStepDetail(stepDetail, newMasterStepRequestDto, false);
                        if (newMasterStepRequestDto.singleLimit &&
                            finalStepDetail.singleLimit !== -1 &&
                            (newMasterStepRequestDto.singleLimit > finalStepDetail.singleLimit)) {
                            throw new exceptions_1.HttpException('Child single limit can\'t be greater than parent single limit.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                        if (newMasterStepRequestDto.aggregateLimit) {
                            if (finalStepDetail.aggregateLimit === 0) {
                                throw new exceptions_1.HttpException('Parent don\'t have aggregate limit to share.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                            }
                            const totalSharedAggregateLimit = yield this.totalLimitSharedWithChild(finalStepDetail, entitySharingSameBucket);
                            if (finalStepDetail.aggregateLimit > 0) {
                                const totalSharableAggregateLimitRemaining = finalStepDetail.aggregateLimit - totalSharedAggregateLimit;
                                if (totalSharableAggregateLimitRemaining < newMasterStepRequestDto.aggregateLimit) {
                                }
                            }
                        }
                        if (!finalStepDetail.sharedLimitMasterStepChildId || !finalStepDetail.workflowMasterStepId) {
                            throw new exceptions_1.HttpException(stepDetail.title + ' is not allowed to share the limit to ' + childStepDetail.title + ' for selected parent entity.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                        const finalChildStepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailWithSettingByCondition({
                            workflowMasterStepId: finalStepDetail.sharedLimitMasterStepChildId,
                            inherited: false
                        });
                        const newShareLimitRequest = {
                            workflowMasterStepId: finalChildStepDetail.workflowMasterStepId,
                            workflowMasterParentStepId: finalStepDetail.workflowMasterStepId,
                            singleLimit: newMasterStepRequestDto.singleLimit,
                            aggregateLimit: newMasterStepRequestDto.aggregateLimit,
                            entityId: newMasterStepRequestDto.childEntityId,
                            entityCode: newMasterStepRequestDto.childEntityCode,
                            entityTitle: newMasterStepRequestDto.childEntityTitle
                        };
                        const childLimitDetail = yield this.workflowSharedChildLimitRepository.addNewSharedLimitStep(newShareLimitRequest, currentContext);
                        let historyRequest = [];
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: newShareLimitRequest.workflowMasterStepId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_ADDED,
                            comments: 'Limit added by parent step for ' + newShareLimitRequest.entityTitle + '(' + newShareLimitRequest.entityCode + ') entity.',
                            additional_info: {
                                limitDetail: newShareLimitRequest,
                            }
                        }, {
                            created_by: currentContext.user.username,
                            entity_id: newShareLimitRequest.workflowMasterParentStepId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_ADDED,
                            comments: 'Limit shared to ' + newShareLimitRequest.entityTitle + '(' + newShareLimitRequest.entityCode + ') entity for child step.',
                            additional_info: {
                                limitDetail: newShareLimitRequest,
                            }
                        });
                        yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                        return childLimitDetail;
                    }
                    else {
                        throw new exceptions_1.HttpException('Invalid Child Entity Selected.', enums_1.HttpStatus.NOT_FOUND);
                    }
                }
                else {
                    throw new exceptions_1.HttpException('Invalid Parent Entity Selected.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getChildSharedLimitList(parentStepId, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(parentStepId);
            if (stepDetail) {
                if (!stepDetail.canShareLimitToChild) {
                    throw new exceptions_1.HttpException('Step is not allowed to share the limit.', enums_1.HttpStatus.NOT_FOUND);
                }
                if (!stepDetail.workflowMasterSetting ||
                    (stepDetail.workflowMasterSetting &&
                        (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                const childEntities = yield this.adminApiClient.getChildernListOfBusinessEntity(entityId);
                if (childEntities && childEntities.length) {
                    const childLimitResponse = yield this.workflowSharedChildLimitRepository.findChildSharedLimitByParentStepId(stepDetail.workflowMasterStepId, childEntities);
                    return (0, helpers_1.multiObjectToInstance)(get_child_shared_limit_response_dto_1.GetChildSharedLimitResponseDTO, childLimitResponse);
                }
                else {
                    throw new exceptions_1.HttpException('Invalid Entity Selected.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    deleteChildSharedLimit(id, entityId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const sharedLimitDetail = yield this.workflowSharedChildLimitRepository.findChildSharedLimitByCondition({
                id
            });
            if (sharedLimitDetail) {
                if (sharedLimitDetail.entityId !== entityId) {
                    throw new exceptions_1.HttpException('Invalid entity id passed for this limit.', enums_1.HttpStatus.NOT_FOUND);
                }
                const deleteResponse = yield this.workflowSharedChildLimitRepository.deleteSharedLimitByCondition({
                    id
                }, currentContext);
                if (deleteResponse) {
                    let historyRequest = [];
                    historyRequest.push({
                        created_by: currentContext.user.username,
                        entity_id: sharedLimitDetail.workflowMasterStepId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_DELETED,
                        comments: 'Limit by parent step for ' + sharedLimitDetail.entityTitle + '(' + sharedLimitDetail.entityCode + ') entity has been deleted.',
                    }, {
                        created_by: currentContext.user.username,
                        entity_id: sharedLimitDetail.workflowMasterParentStepId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_DELETED,
                        comments: 'Limit shared to ' + sharedLimitDetail.entityTitle + '(' + sharedLimitDetail.entityCode + ') entity for child step has been deleted.',
                    });
                    yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                    return { message: 'Limit has been deleted successfully.' };
                }
                else {
                    throw new exceptions_1.HttpException('Unable to delete the shared limit.', enums_1.HttpStatus.BAD_REQUEST);
                }
            }
            else {
                throw new exceptions_1.HttpException('Limit is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    updateChildSharedLimit(id, updateSharedChildLimitRequestDTO, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!updateSharedChildLimitRequestDTO.singleLimit && !updateSharedChildLimitRequestDTO.aggregateLimit) {
                throw new exceptions_1.HttpException('Single or Aggregate limit is required to update.', enums_1.HttpStatus.NOT_FOUND);
            }
            const sharedLimitDetail = yield this.workflowSharedChildLimitRepository.findChildSharedLimitByCondition({
                id
            });
            if (sharedLimitDetail) {
                if (sharedLimitDetail.entityId !== updateSharedChildLimitRequestDTO.entityId) {
                    throw new exceptions_1.HttpException('Invalid entity id passed for this limit.', enums_1.HttpStatus.NOT_FOUND);
                }
                const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(updateSharedChildLimitRequestDTO.stepId);
                if (stepDetail) {
                    yield this.doBasicValidationCheck(stepDetail, updateSharedChildLimitRequestDTO);
                    if (stepDetail.workflowMasterStepId !== sharedLimitDetail.workflowMasterParentStepId) {
                        throw new exceptions_1.HttpException('Invalid step id passed.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                    let parentEntityIds = yield this.adminApiClient.getParentIdsOfEntity(updateSharedChildLimitRequestDTO.entityId);
                    parentEntityIds = parentEntityIds.reverse();
                    if (parentEntityIds && parentEntityIds.length > 1) {
                        const limitParentEntityId = parentEntityIds[1];
                        updateSharedChildLimitRequestDTO.entityId = limitParentEntityId;
                        const { finalStepDetail, entitySharingSameBucket } = yield this.getActualStepDetail(stepDetail, updateSharedChildLimitRequestDTO, false);
                        if (updateSharedChildLimitRequestDTO.singleLimit &&
                            finalStepDetail.singleLimit !== -1 &&
                            (updateSharedChildLimitRequestDTO.singleLimit > finalStepDetail.singleLimit)) {
                            throw new exceptions_1.HttpException('Child single limit can\'t be greater than parent single limit.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                        if (updateSharedChildLimitRequestDTO.aggregateLimit) {
                            const totalSharedAggregateLimit = yield this.totalLimitSharedWithChild(finalStepDetail, entitySharingSameBucket);
                            const totalSharableAggregateLimitRemaining = ((finalStepDetail.aggregateLimit - totalSharedAggregateLimit) + sharedLimitDetail.aggregateLimit);
                            if (totalSharableAggregateLimitRemaining < updateSharedChildLimitRequestDTO.aggregateLimit) {
                            }
                        }
                        let updateLimitPayload = {};
                        if (updateSharedChildLimitRequestDTO.singleLimit) {
                            updateLimitPayload = {
                                singleLimit: updateSharedChildLimitRequestDTO.singleLimit
                            };
                        }
                        if (updateSharedChildLimitRequestDTO.aggregateLimit) {
                            updateLimitPayload = Object.assign(Object.assign({}, updateLimitPayload), { aggregateLimit: updateSharedChildLimitRequestDTO.aggregateLimit });
                        }
                        yield this.workflowSharedChildLimitRepository.updateSharedLimit(id, updateLimitPayload, currentContext);
                        let historyRequest = [];
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: sharedLimitDetail.workflowMasterStepId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_UPDATED,
                            comments: 'Limit by parent step for ' + sharedLimitDetail.entityTitle + '(' + sharedLimitDetail.entityCode + ') entity has been updated.',
                            additional_info: {
                                limitBeforeUpdate: sharedLimitDetail,
                                newUpdates: updateLimitPayload
                            }
                        }, {
                            created_by: currentContext.user.username,
                            entity_id: sharedLimitDetail.workflowMasterParentStepId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_UPDATED,
                            comments: 'Limit shared to ' + sharedLimitDetail.entityTitle + '(' + sharedLimitDetail.entityCode + ') entity for child step has been updated.',
                            additional_info: {
                                limitBeforeUpdate: sharedLimitDetail,
                                newUpdates: updateLimitPayload
                            }
                        });
                        this.historyApiClient.addBulkRequestHistory(historyRequest);
                        return { message: 'Child limit has been updated successfully.' };
                    }
                    else {
                        throw new exceptions_1.HttpException('Entity id passed in request don\'t have any parent entity.', enums_1.HttpStatus.NOT_FOUND);
                    }
                }
                else {
                    throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                throw new exceptions_1.HttpException('Limit is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getLowestLevelStepDetailForEntityId(masterSettingDetail, stepId, currentEntityId, addPublishCondition) {
        return __awaiter(this, void 0, void 0, function* () {
            let locationIdParents = yield this.adminApiClient.getParentIdsOfEntity(currentEntityId);
            locationIdParents = locationIdParents.reverse();
            const masterWorkflowSettingOrCond = this.sequlizeOperator.orOperator({
                id: (masterSettingDetail.parentId ? masterSettingDetail.parentId : masterSettingDetail.id),
                parentId: (masterSettingDetail.parentId ? masterSettingDetail.parentId : masterSettingDetail.id)
            });
            let allWorkflowSettingMatchCondition = Object.assign(Object.assign({}, masterWorkflowSettingOrCond), { entityId: this.sequlizeOperator.inOperator(locationIdParents) });
            if (addPublishCondition) {
                allWorkflowSettingMatchCondition = Object.assign(Object.assign({}, masterWorkflowSettingOrCond), { published: true });
            }
            const allMatchingSettingData = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition(allWorkflowSettingMatchCondition);
            let matchingSettingDetail = null;
            for (const entityId of locationIdParents) {
                for (let i = 0; i < allMatchingSettingData.length; i++) {
                    if (allMatchingSettingData[i].entityId === entityId) {
                        matchingSettingDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailByCondition({
                            workflowMasterSettingId: allMatchingSettingData[i].id,
                            workflowMasterStepId: stepId
                        });
                        break;
                    }
                }
                if (matchingSettingDetail) {
                    break;
                }
            }
            return matchingSettingDetail;
        });
    }
    getAllChildOfParents(allParentEntityIds) {
        return __awaiter(this, void 0, void 0, function* () {
            let allChildsOfParents = [];
            for (let index = 0; index < allParentEntityIds.length; index++) {
                const childEntities = yield this.adminApiClient.getChildernListOfBusinessEntity(allParentEntityIds[index]);
                if (childEntities && childEntities.length) {
                    allChildsOfParents.push(...childEntities);
                }
            }
            return allChildsOfParents;
        });
    }
    getActualStepDetail(stepDetail, newMasterStepRequestDto, addPublishCondition = false) {
        return __awaiter(this, void 0, void 0, function* () {
            let bucketEntityId = yield this.workflowSharedBucketLimitRepository.getEntityIdWithBucketOrSharedEntity(newMasterStepRequestDto.entityId, stepDetail.workflowMasterSetting.parentId ? stepDetail.workflowMasterSetting.parentId : stepDetail.workflowMasterSetting.id, stepDetail.workflowMasterStepId);
            let entitySharingSameBucket = [];
            if (bucketEntityId) {
                if (newMasterStepRequestDto.aggregateLimit) {
                    const sharedBucketEntities = yield this.workflowSharedBucketLimitRepository.getAllBucketSharedEntityId(bucketEntityId, stepDetail.workflowMasterStepId);
                    if (sharedBucketEntities && sharedBucketEntities.length) {
                        sharedBucketEntities.forEach((sharedEntityId) => {
                            entitySharingSameBucket.push(sharedEntityId);
                        });
                    }
                    entitySharingSameBucket.push(bucketEntityId);
                }
            }
            else {
                entitySharingSameBucket.push(newMasterStepRequestDto.entityId);
                bucketEntityId = newMasterStepRequestDto.entityId;
            }
            const masterSettingDetail = stepDetail.workflowMasterSetting;
            let finalStepDetail = null;
            if (masterSettingDetail.entityId === bucketEntityId) {
                finalStepDetail = stepDetail;
            }
            if (!finalStepDetail) {
                finalStepDetail = yield this.getLowestLevelStepDetailForEntityId(stepDetail.workflowMasterSetting, stepDetail.workflowMasterStepId, bucketEntityId, addPublishCondition);
            }
            if (!finalStepDetail) {
                throw new exceptions_1.HttpException('Invalid Step.', enums_1.HttpStatus.NOT_FOUND);
            }
            return {
                finalStepDetail,
                entitySharingSameBucket
            };
        });
    }
    doBasicValidationCheck(stepDetail, newMasterStepRequestDto, isBalanceCheck = false) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!stepDetail.workflowMasterSetting ||
                (stepDetail.workflowMasterSetting &&
                    (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!isBalanceCheck) {
                if (!stepDetail.canShareLimitToChild) {
                    throw new exceptions_1.HttpException(stepDetail.title + ' can\'t share limit to child.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (stepDetail.singleLimit === 0 && newMasterStepRequestDto.singleLimit) {
                    throw new exceptions_1.HttpException(stepDetail.title + ' don\'t have single limit to share.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (stepDetail.aggregateLimit === 0 && newMasterStepRequestDto.aggregateLimit) {
                    throw new exceptions_1.HttpException(stepDetail.title + ' don\'t have aggregate limit to share.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
            }
        });
    }
    totalLimitSharedWithChild(finalStepDetail, entitySharingSameBucket) {
        return __awaiter(this, void 0, void 0, function* () {
            let allChildsOfParents = [];
            allChildsOfParents = yield this.getAllChildOfParents(entitySharingSameBucket);
            let totalSharedAggregateLimit = 0;
            if (allChildsOfParents.length) {
                const totalSharedAggregateLimitDetail = yield this.workflowSharedChildLimitRepository.getTotalAggregateLimitByParentStepAndChildEntities(finalStepDetail.workflowMasterStepId, allChildsOfParents);
                totalSharedAggregateLimit = (0, lodash_1.toNumber)((totalSharedAggregateLimitDetail && totalSharedAggregateLimitDetail.length > 0) ?
                    totalSharedAggregateLimitDetail[0].totalAggregateLimit :
                    0);
            }
            return totalSharedAggregateLimit;
        });
    }
};
WorkflowSharedChildLimitService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.WorkflowMasterSettingRepository,
        repositories_1.WorkflowMasterStepRepository,
        clients_1.AdminApiClient,
        repositories_1.WorkflowSharedChildLimitRepository,
        repositories_1.WorkflowSharedBucketLimitRepository,
        helpers_1.SequlizeOperator,
        clients_1.HistoryApiClient])
], WorkflowSharedChildLimitService);
exports.WorkflowSharedChildLimitService = WorkflowSharedChildLimitService;
//# sourceMappingURL=workflow-shared-child-limit.service.js.map