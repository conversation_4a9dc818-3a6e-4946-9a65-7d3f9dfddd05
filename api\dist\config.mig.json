{"database": {"host": "pgsqlhodev.postgres.database.azure.com", "port": 5432, "username": "afe_user@pgsqlhodev", "password": "AFE2020", "db": "afe", "dialect": "postgres", "schema": "mig_hosted", "enableSSL": true}, "azureAD": {"authority": "https://login.microsoftonline.com", "tennantId": "2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "1adb1c92-7181-447b-ae47-d5d5bbab1b65", "clientSecret": "****************************************", "version": "v2.0", "discovery": ".well-known/openid-configuration", "audience": "api://1adb1c92-7181-447b-ae47-d5d5bbab1b65", "graphApiUrl": "https://graph.microsoft.com", "graphApiVersion": "v1.0", "scope": ["General"]}, "swagger": {"user": "user", "password": "password"}, "microservices": {"adminApi": {"url": "http://adminapi.uat-admin.svc.cluster.local/api", "tennantId": "ebd93e46-23b9-45fb-93de-75dadf77efe0", "appId": "fd28a805-cc46-442b-830b-b02afe3041a9"}, "requestApi": {"url": "http://requestapi.uat-admin.svc.cluster.local/api", "tennantId": "ebd93e46-23b9-45fb-93de-75dadf77efe0", "appId": "fd28a805-cc46-442b-830b-b02afe3041a9"}, "notificationApi": {"url": "http://notification.uat-admin.svc.cluster.local/api", "tennantId": "ebd93e46-23b9-45fb-93de-75dadf77efe0", "appId": "fd28a805-cc46-442b-830b-b02afe3041a9"}, "fusionApi": {"url": "https://fapidev.dpworld.com/uaeho/epm", "Authorization": "Basic dWFlLmFwaWNvbnN1bWVyczpEcHdvcmxkQDIwMjE="}, "fusionUaeApi": {"url": "https://fapidev.dpworld.com/uaeho/epm", "Authorization": "Basic dWFlLmFwaWNvbnN1bWVyczpEcHdvcmxkQDIwMjE="}}, "uiClient": {"baseUrl": "https://hoappsuat.dpworld.com/MIGAFE", "task": {"relativeUrl": "/tasks/{{taskId}}"}}, "webClientConfig": {"apiBaseUrl": "https://hoappsuat.dpworld.com/MIGAFE/api/api", "msDetail": {"authority": "https://login.microsoftonline.com/2bd16c9b-7e21-4274-9c06-7919f7647bbb", "clientId": "e602d1a3-8381-4200-9e11-aedeeeac5b9e", "redirectUrl": "https://hoappsuat.dpworld.com/MIGAFE", "scope": ["api://1adb1c92-7181-447b-ae47-d5d5bbab1b65/General"], "graphUrl": "https://graph.microsoft.com/v1.0/users", "graphScope": ["User.Read.All"]}}, "businessEntityLevelForProjectReferenceNumber": "Region", "apiKey": "2771c580-8b85-435d-9393-9725007e1cdd", "oneAppApiKey": "04eeb7c8-76db-11ed-a1eb-0242ac120002", "logLevel": ["error", "log"], "thresholdDaysForReminder": 7}