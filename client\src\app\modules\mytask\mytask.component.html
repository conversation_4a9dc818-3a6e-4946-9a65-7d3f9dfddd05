<div class="toolbar">
  <div
    id="kt_toolbar_container"
    class="d-flex flex-end flex-stack container-fluid"
  >
    <div class="page-title d-flex align-items-center flex-wrap me-3">
      <h1
        class="d-flex align-items-center text-dark fw-bolder my-1 fs-3 mr-2 mb-0 mt-0"
      >
        {{ "MENU.MY_TASKS" | translate }}
      </h1>
    </div>

    <div class="d-flex">
      <button
        (click)="openFilterModal()"
        type="button"
        class="btn btn-sm btn-primary textSize px-5"
      >
        <span
          [inlineSVG]="'./assets/media/icons/duotune/general/gen031.svg'"
          class="svg-icon svg-icon-5 svg-icon-gray-500 me-1"
        ></span>
        {{ "FORM.BUTTON.FILTER" | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Main Content Container -->
<ng-container *ngIf="!loading; else loadingPage">
  <div class="post d-flex flex-column-fluid" id="kt_post">
    <div id="kt_content_container">
      <!-- Filter Section -->

      <ng-container
        *ngIf="validateUserPermission(permissionEnum.AFEAdministration)"
      >
        <div class="card border-0 shadow-sm mb-6">
          <div class="card-body py-4">
            <div class="row align-items-center">
              <div class="col-md-8">
                <app-filter-badges [localStorageKey]="filterLocalStorageKey">
                </app-filter-badges>
              </div>
              <div class="col-md-4">
                <app-ad-user-search
                  placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
                    'LIST.ASSIGNED_TO' | translate | lowercase
                  }}"
                  [isMultiSelect]="false"
                  (userSelected)="onUserAdded($event)"
                >
                </app-ad-user-search>
              </div>
            </div>
          </div>
        </div>
      </ng-container>

      <div class="d-flex justify-content-end mb-5">
        <div class="badge badge-light-danger fs-6 fw-bold px-4 py-2">
          <span class="ms-2">{{ filteredTasks.length }}</span>
          <span class="ms-2"></span>
          <span>{{ "COMMON.PENDING_TASKS" | translate }}</span>
        </div>
      </div>

      <!-- Tasks Grid -->
      <div class="row g-6" *ngIf="filteredTasks?.length; else noDataMessage">
        <ng-container *ngFor="let task of filteredTasks; let i = index">
          <div class="col-lg-6 col-xl-4">
            <div
              class="card border-0 shadow-lg hover-elevate-up transition-all-300ms cursor-pointer h-100 note-box overflow-hidden"
              (click)="nagivateToTaskAction(task)"
            >
              <!-- Card Header -->
              <div class="card-header border-0 pt-5 pb-3">
                <div class="d-flex align-items-center w-100">
                  <div class="symbol symbol-40px me-3">
                    <div
                      class="symbol-label bg-white bg-opacity-20 backdrop-blur"
                    >
                      <span
                        [inlineSVG]="
                          './assets/media/icons/duotune/arrows/arr064.svg'
                        "
                        class="svg-icon svg-icon-2 svg-icon-white"
                      ></span>
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5
                      class="card-title fw-bold text-white mb-1"
                      *ngIf="task.title"
                    >
                      {{ task.title }}
                    </h5>
                    <div class="text-white-75 fs-7 fw-semibold">
                      {{ task.createdOn | date : "MMM dd, yyyy ',' h:mm a" }}
                    </div>
                  </div>
                  <!-- <div class="ms-auto">
                    <div class=" rounded-circle p-2">
                      <span
                        [inlineSVG]="
                          './assets/media/icons/duotune/arrows/arr064.svg'
                        "
                        class="svg-icon svg-icon-4 svg-icon-white"
                      ></span>
                    </div>
                  </div> -->
                </div>
              </div>

              <!-- Card Body -->
              <div class="card-body pt-3 pb-5">
                <!-- Row 1: Project Name & Total Amount -->
                <div class="row">
                  <!-- Project Name -->
                  <div class="col-12">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="fw-bold pb-1 d-inline w-100">
                          {{ "LIST.PROJECT_NAME" | translate }} &nbsp;:
                        </div>
                        <div class="d-inline-block">
                          {{
                            task?.additionalInfo?.proposal_project_name || "N/A"
                          }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Total Amount -->
                  <div class="col-12" *ngIf="task?.additionalInfo?.totalAmount">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="fw-bold pb-1 d-inline w-100">
                          {{ "LIST.TOTAL_AMOUNT" | translate }} &nbsp;:
                        </div>
                        <div class="d-inline-block">
                          {{
                            task?.additionalInfo?.totalAmount
                              | currency : "USD" : "symbol" : "1.2-2"
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Row 2: Assigned To & Submitted By -->
                <div class="row">
                  <!-- Assigned To -->
                  <div class="col-12">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="fw-bold pb-1 d-inline w-100">
                          {{ "LIST.ASSIGNED_TO" | translate }} &nbsp;:
                        </div>
                        <div class="d-inline-block">
                          {{ task.assignedTo || "N/A" }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Submitted By -->
                  <div class="col-12">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="fw-bold pb-1 d-inline w-100">
                          {{ "FORM.LABEL.SUBMITTED_BY" | translate }} &nbsp;:
                        </div>
                        <div class="d-inline-block">
                          {{
                            task?.additionalInfo?.proposal_created_by || "N/A"
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
      <!-- No Data State -->
      <ng-template #noDataMessage>
        <app-no-data
          [message]="'ERROR.NO_TASK_AVAILABLE' | translate"
        ></app-no-data>
      </ng-template>
    </div>
  </div>
</ng-container>
<ng-template #loadingPage>
  <app-list-skeleton-loader [loaderCount]="4"></app-list-skeleton-loader>
</ng-template>
<app-modal #filterModal [modalConfig]="filterModalConfig">
  <ng-container *ngIf="isFilterModalReady">
    <app-task-filter-modal
      (filterPayloadEvent)="getFilterPayload($event)"
      [localStorageKey]="filterLocalStorageKey"
    >
    </app-task-filter-modal>
  </ng-container>
</app-modal>
