<!-- Header Section -->
<div class="toolbar bg-light">
  <div
    id="kt_toolbar_container"
    class="d-flex flex-end flex-stack container-fluid py-4"
  >
    <div class="page-title d-flex align-items-center flex-wrap me-3">
      <h1
        class="d-flex align-items-center text-dark fw-bolder my-1 fs-2 mr-2 mb-0 mt-0"
      >
        <span
          [inlineSVG]="'./assets/media/icons/duotune/general/gen025.svg'"
          class="svg-icon svg-icon-2 svg-icon-primary me-3"
        ></span>
        {{ "MENU.MY_TASKS" | translate }}
      </h1>
    </div>

    <div class="d-flex align-items-center gap-3">
      <!-- Task Count Badge -->
      <div class="badge badge-light-primary fs-7 fw-bold" *ngIf="filteredTasks?.length">
        {{ filteredTasks.length }} {{ "COMMON.TASKS" | translate }}
      </div>

      <!-- Filter Button -->
      <button
        type="button"
        class="btn btn-sm btn-flex btn-primary btn-active-primary fw-bold"
        (click)="openFilterModal()"
      >
        <span
          [inlineSVG]="'./assets/media/icons/duotune/general/gen031.svg'"
          class="svg-icon svg-icon-5 svg-icon-white me-2"
        ></span>
        {{ "FORM.BUTTON.FILTER" | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Main Content Container -->
<div class="post d-flex flex-column-fluid" id="kt_post">
  <div id="kt_content_container" class="container-fluid">

    <ng-container *ngIf="!loading; else loadingPage">
      <!-- Filter Section -->
      <div class="card border-0 shadow-sm mb-6">
        <div class="card-body py-4">
          <div class="row align-items-center">
            <div class="col-md-8">
              <app-filter-badges [localStorageKey]="filterLocalStorageKey">
              </app-filter-badges>
            </div>
            <div class="col-md-4" *ngIf="validateUserPermission(permissionEnum.AFEAdministration)">
              <app-ad-user-search
                placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
                  'LIST.ASSIGNED_TO' | translate | lowercase
                }}"
                [isMultiSelect]="false"
                (userSelected)="onUserAdded($event)"
              >
              </app-ad-user-search>
            </div>
          </div>
        </div>
      </div>

      <!-- Tasks Grid -->
      <div class="row g-6" *ngIf="filteredTasks?.length; else noDataMessage">
        <ng-container *ngFor="let task of filteredTasks; let i = index">
          <div class="col-lg-6 col-xl-4">
            <div
              class="card border-0 shadow-sm hover-elevate-up transition-all-300ms cursor-pointer h-100 note-box"
              (click)="nagivateToTaskAction(task)"
            >
              <!-- Card Header -->
              <div class="card-header border-0 pt-4 pb-2">
                <div class="d-flex align-items-center w-100">
                  <div class="symbol symbol-35px me-3">
                    <div class="symbol-label bg-light-primary">
                      <span
                        [inlineSVG]="'./assets/media/icons/duotune/general/gen025.svg'"
                        class="svg-icon svg-icon-3 svg-icon-primary"
                      ></span>
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h6 class="card-title fw-bold text-white mb-1" *ngIf="task.title">
                      {{ task.title }}
                    </h6>
                    <div class="text-white-50 fs-8">
                      {{ task.createdOn | date : "MMM dd, yyyy 'at' h:mm a" }}
                    </div>
                  </div>
                  <div class="ms-auto">
                    <span
                      [inlineSVG]="'./assets/media/icons/duotune/arrows/arr064.svg'"
                      class="svg-icon svg-icon-4 svg-icon-white"
                    ></span>
                  </div>
                </div>
              </div>

              <!-- Card Body -->
              <div class="card-body pt-2 pb-4">
                <!-- Project Name -->
                <div class="d-flex align-items-center mb-3">
                  <span
                    [inlineSVG]="'./assets/media/icons/duotune/general/gen014.svg'"
                    class="svg-icon svg-icon-5 svg-icon-white me-2"
                  ></span>
                  <div class="flex-grow-1">
                    <div class="text-white-75 fs-8 fw-bold">{{ "LIST.PROJECT_NAME" | translate }}</div>
                    <div class="text-white fw-bold fs-7">
                      {{ task?.additionalInfo?.proposal_project_name || 'N/A' }}
                    </div>
                  </div>
                </div>

                <!-- Total Amount -->
                <div class="d-flex align-items-center mb-3" *ngIf="task?.additionalInfo?.totalAmount">
                  <span
                    [inlineSVG]="'./assets/media/icons/duotune/finance/fin010.svg'"
                    class="svg-icon svg-icon-5 svg-icon-white me-2"
                  ></span>
                  <div class="flex-grow-1">
                    <div class="text-white-75 fs-8 fw-bold">{{ "LIST.TOTAL_AMOUNT" | translate }}</div>
                    <div class="text-white fw-bold fs-7">
                      {{
                        task?.additionalInfo?.totalAmount
                          | currency : "USD" : "symbol" : "1.2-2"
                      }}
                    </div>
                  </div>
                </div>

                <!-- Assigned To -->
                <div class="d-flex align-items-center mb-3">
                  <span
                    [inlineSVG]="'./assets/media/icons/duotune/communication/com006.svg'"
                    class="svg-icon svg-icon-5 svg-icon-white me-2"
                  ></span>
                  <div class="flex-grow-1">
                    <div class="text-white-75 fs-8 fw-bold">{{ "LIST.ASSIGNED_TO" | translate }}</div>
                    <div class="text-white fw-bold fs-7">{{ task.assignedTo || 'N/A' }}</div>
                  </div>
                </div>

                <!-- Submitted By -->
                <div class="d-flex align-items-center">
                  <span
                    [inlineSVG]="'./assets/media/icons/duotune/communication/com014.svg'"
                    class="svg-icon svg-icon-5 svg-icon-white me-2"
                  ></span>
                  <div class="flex-grow-1">
                    <div class="text-white-75 fs-8 fw-bold">{{ "FORM.LABEL.SUBMITTED_BY" | translate }}</div>
                    <div class="text-white fw-bold fs-7">
                      {{ task?.additionalInfo?.proposal_created_by || 'N/A' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
      <!-- No Data State -->
      <ng-template #noDataMessage>
        <div class="text-center py-15">
          <div class="mb-7">
            <span
              [inlineSVG]="'./assets/media/icons/duotune/general/gen025.svg'"
              class="svg-icon svg-icon-5x svg-icon-muted"
            ></span>
          </div>
          <h3 class="text-gray-800 fw-bold mb-3">{{ "ERROR.NO_TASK_AVAILABLE" | translate }}</h3>
          <p class="text-gray-600 fs-6 mb-0">{{ "COMMON.NO_TASKS_DESCRIPTION" | translate }}</p>
        </div>
      </ng-template>
    </ng-container>
  </div>
</div>
<ng-template #loadingPage>
  <app-list-skeleton-loader [loaderCount]="4"></app-list-skeleton-loader>
</ng-template>
<app-modal #filterModal [modalConfig]="filterModalConfig">
  <ng-container *ngIf="isFilterModalReady">
    <app-task-filter-modal
      (filterPayloadEvent)="getFilterPayload($event)"
      [localStorageKey]="filterLocalStorageKey"
    >
    </app-task-filter-modal>
  </ng-container>
</app-modal>
