<div class="toolbar">
  <div
    id="kt_toolbar_container"
    class="d-flex flex-end flex-stack container-fluid"
  >
    <div class="page-title d-flex align-items-center flex-wrap me-3">
      <h1
        class="d-flex align-items-center text-dark fw-bolder my-1 fs-2 mr-2 mb-0 mt-0"
      >
        {{ "MENU.MY_TASKS" | translate }}
      </h1>
    </div>

    <div class="d-flex">
      <div>
        <a
          class="btn btn-sm btn-flex btn-primary btn-active-primary fw-bold cursor-pointer"
          data-kt-menu-trigger="click"
          data-kt-menu-placement="bottom-end"
          data-kt-menu-flip="top-end"
          (click)="openFilterModal()"
        >
          <span
            [inlineSVG]="'./assets/media/icons/duotune/general/gen031.svg'"
            class="svg-icon svg-icon-5 svg-icon-gray-500 me-1"
          ></span>
          {{ "FORM.BUTTON.FILTER" | translate }}
        </a>
      </div>
    </div>
  </div>
</div>

<ng-container *ngIf="!loading; else loadingPage">
  <div *ngIf="true" class="align-middle">
    <app-filter-badges [localStorageKey]="filterLocalStorageKey">
    </app-filter-badges>
  </div>

  <div
    class="row"
    *ngIf="validateUserPermission(permissionEnum.AFEAdministration)"
  >
    <div class="col-md-3 ms-auto">
      <div class="col-lg-12 w-100">
        <app-ad-user-search
          placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
            'LIST.ASSIGNED_TO' | translate | lowercase
          }}"
          [isMultiSelect]="false"
          (userSelected)="onUserAdded($event)"
        >
        </app-ad-user-search>
      </div>
    </div>
  </div>

  <div class="" *ngIf="filteredTasks?.length; else noDataMessage">
    <div class="row">
      <ng-container *ngFor="let task of filteredTasks">
        <div class="col-md-6 mt-5" style="min-height: 100px">
          <div
            class="border p-4 h-100 note-box border"
            role="button"
            (click)="nagivateToTaskAction(task)"
          >
            <div class="d-flex">
              <img
                height="22px"
                src="./assets/media/svg/icons/dpw-icons/next-dark.png"
                alt="Next"
                *ngIf="task.title"
              />
              <h6
                class="px-2 pt-1 pb-1 fw-bold mb-0"
                style="color: #ffffff"
                *ngIf="task.title"
              >
                {{ task.title }}
              </h6>
            </div>
            <div class="ms-10">
              <span class="fw-bold pb-1 d-inline w-100"
                >{{ "LIST.PROJECT_NAME" | translate }}&nbsp;:
              </span>
              <span class="d-inline-block">
                {{ task?.additionalInfo?.proposal_project_name }}
              </span>
            </div>

            <div *ngIf="task?.additionalInfo?.totalAmount" class="ms-10">
              <span class="fw-bold pb-1 d-inline w-100"
                >{{ "LIST.TOTAL_AMOUNT" | translate }}&nbsp;:
              </span>
              <span class="d-inline-block">
                {{ task?.additionalInfo?.totalAmount }}
              </span>
            </div>

            <div class="ms-10">
              <span class="fw-bold pb-1 d-inline w-100"
                >{{ "LIST.ASSIGNED_TO" | translate }}&nbsp;:
              </span>
              <span class="d-inline-block">{{ task.assignedTo }}</span>
            </div>
            <p class="ms-10">
              <span class="pb-1 fw-bold"
                >{{ "LIST.CREATED_AT" | translate }}&nbsp;:
              </span>
              {{ task.createdOn | date : "medium" }}
            </p>
            <p class="pb-2 ms-10">
              <span class="fw-bold"
                >{{ "FORM.LABEL.SUBMITTED_BY" | translate }}&nbsp;:
              </span>
              {{ task?.additionalInfo?.proposal_created_by }}
            </p>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <ng-template #noDataMessage>
    <app-no-data
      [message]="'ERROR.NO_TASK_AVAILABLE' | translate"
    ></app-no-data>
  </ng-template>
</ng-container>
<ng-template #loadingPage>
  <app-list-skeleton-loader [loaderCount]="4"></app-list-skeleton-loader>
</ng-template>
<app-modal #filterModal [modalConfig]="filterModalConfig">
  <ng-container *ngIf="isFilterModalReady">
    <app-task-filter-modal
      (filterPayloadEvent)="getFilterPayload($event)"
      [localStorageKey]="filterLocalStorageKey"
    >
    </app-task-filter-modal>
  </ng-container>
</app-modal>
