<div class="toolbar">
  <div
    id="kt_toolbar_container"
    class="d-flex flex-end flex-stack container-fluid"
  >
    <div class="page-title d-flex align-items-center flex-wrap me-3">
      <h1
        class="d-flex align-items-center text-dark fw-bolder my-1 fs-2 mr-2 mb-0 mt-0"
      >
        {{ "MENU.MY_TASKS" | translate }}
      </h1>
    </div>

    <div class="d-flex">
      <!-- Filter Button -->
      <a
        class="btn btn-sm btn-flex btn-primary btn-active-primary fw-bold cursor-pointer"
        data-kt-menu-trigger="click"
        data-kt-menu-placement="bottom-end"
        data-kt-menu-flip="top-end"
        (click)="openFilterModal()"
      >
        <span
          [inlineSVG]="'./assets/media/icons/duotune/general/gen031.svg'"
          class="svg-icon svg-icon-5 svg-icon-gray-500 me-1"
        ></span>
        {{ "FORM.BUTTON.FILTER" | translate }}
      </a>
    </div>
  </div>
</div>

<!-- Main Content Container -->
<div class="post d-flex flex-column-fluid" id="kt_post">
  <div id="kt_content_container" class="container-fluid">
    <ng-container *ngIf="!loading; else loadingPage">
      <!-- Filter Section -->
      <div class="card border-0 shadow-sm mb-6">
        <div class="card-body py-4">
          <div class="row align-items-center">
            <div class="col-md-8">
              <app-filter-badges [localStorageKey]="filterLocalStorageKey">
              </app-filter-badges>
            </div>
            <div
              class="col-md-4"
              *ngIf="validateUserPermission(permissionEnum.AFEAdministration)"
            >
              <app-ad-user-search
                placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
                  'LIST.ASSIGNED_TO' | translate | lowercase
                }}"
                [isMultiSelect]="false"
                (userSelected)="onUserAdded($event)"
              >
              </app-ad-user-search>
            </div>
          </div>
        </div>
      </div>

      <div class="fw-bold fw-bold text-gray-600 d-flex justify-content-end mb-3">
        {{ "FORM.LABEL.TOTAL" | translate }}
        {{ "COMMON.PENDING_TASKS" | translate }} - {{ filteredTasks.length }}
      </div>

      <!-- Tasks Grid -->
      <div class="row g-6" *ngIf="filteredTasks?.length; else noDataMessage">
        <ng-container *ngFor="let task of filteredTasks; let i = index">
          <div class="col-lg-6 col-xl-4">
            <div
              class="card border-0 shadow-lg hover-elevate-up transition-all-300ms cursor-pointer h-100 note-box overflow-hidden"
              (click)="nagivateToTaskAction(task)"
            >
              <!-- Card Header -->
              <div class="card-header border-0 pt-5 pb-3">
                <div class="d-flex align-items-center w-100">
                  <div class="symbol symbol-40px me-3">
                    <div class="symbol-label bg-white bg-opacity-20 backdrop-blur">
                      <span
                        [inlineSVG]="'./assets/media/icons/duotune/general/gen043.svg'"
                        class="svg-icon svg-icon-2 svg-icon-white"
                      ></span>
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5
                      class="card-title fw-bold text-white mb-1"
                      *ngIf="task.title"
                    >
                      {{ task.title }}
                    </h5>
                    <div class="text-white-75 fs-7 fw-semibold">
                      {{ task.createdOn | date : "MMM dd, yyyy ',' h:mm a" }}
                    </div>
                  </div>
                  <!-- <div class="ms-auto">
                    <div class=" rounded-circle p-2">
                      <span
                        [inlineSVG]="
                          './assets/media/icons/duotune/arrows/arr064.svg'
                        "
                        class="svg-icon svg-icon-4 svg-icon-white"
                      ></span>
                    </div>
                  </div> -->
                </div>
              </div>

              <!-- Card Body -->
              <div class="card-body pt-3 pb-5">
                <!-- Row 1: Project Name & Total Amount -->
                <div class="row mb-4">
                  <!-- Project Name -->
                  <div class="col-6">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="form-label fw-bold text-white mb-1">
                          {{ "LIST.PROJECT_NAME" | translate }}
                        </div>
                        <div class="text-white fw-bold fs-7 lh-sm">
                          {{
                            task?.additionalInfo?.proposal_project_name || "N/A"
                          }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Total Amount -->
                  <div class="col-6" *ngIf="task?.additionalInfo?.totalAmount">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="form-label fw-bold text-white mb-1">
                          {{ "LIST.TOTAL_AMOUNT" | translate }}
                        </div>
                        <div class="text-white fw-bold fs-7">
                          {{
                            task?.additionalInfo?.totalAmount
                              | currency : "USD" : "symbol" : "1.2-2"
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Row 2: Assigned To & Submitted By -->
                <div class="row">
                  <!-- Assigned To -->
                  <div class="col-6">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="form-label fw-bold text-white mb-1">
                          {{ "LIST.ASSIGNED_TO" | translate }}
                        </div>
                        <div class="text-white fw-bold fs-7 lh-sm">
                          {{ task.assignedTo || "N/A" }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Submitted By -->
                  <div class="col-6">
                    <div class="d-flex align-items-center">
                      <div class="flex-grow-1">
                        <div class="form-label fw-bold text-white mb-1">
                          {{ "FORM.LABEL.SUBMITTED_BY" | translate }}
                        </div>
                        <div class="text-white fw-bold fs-7 lh-sm">
                          {{
                            task?.additionalInfo?.proposal_created_by || "N/A"
                          }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
      <!-- No Data State -->
      <ng-template #noDataMessage>
        <app-no-data
          [message]="'ERROR.NO_TASK_AVAILABLE' | translate"
        ></app-no-data>
      </ng-template>
    </ng-container>
  </div>
</div>
<ng-template #loadingPage>
  <app-list-skeleton-loader [loaderCount]="4"></app-list-skeleton-loader>
</ng-template>
<app-modal #filterModal [modalConfig]="filterModalConfig">
  <ng-container *ngIf="isFilterModalReady">
    <app-task-filter-modal
      (filterPayloadEvent)="getFilterPayload($event)"
      [localStorageKey]="filterLocalStorageKey"
    >
    </app-task-filter-modal>
  </ng-container>
</app-modal>
