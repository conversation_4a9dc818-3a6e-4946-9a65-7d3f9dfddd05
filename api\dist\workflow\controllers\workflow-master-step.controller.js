"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowMasterStepController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const enums_1 = require("../../shared/enums");
const dtos_2 = require("../dtos");
const new_master_step_request_dto_1 = require("../dtos/request/new-master-step-request.dto");
const update_master_step_request_dto_1 = require("../dtos/request/update-master-step-request.dto");
const get_aggregate_limit_balance_dto_1 = require("../dtos/response/get-aggregate-limit-balance.dto");
const get_child_shared_limit_response_dto_1 = require("../dtos/response/get-child-shared-limit-response.dto");
const get_exception_steps_response_dto_1 = require("../dtos/response/get-exception-steps-response.dto");
const get_history_response_dto_1 = require("../dtos/response/get-history-response.dto");
const get_role_based_steps_response_dto_1 = require("../dtos/response/get-role-based-steps-response.dto");
const services_1 = require("../services");
let WorkflowMasterStepController = class WorkflowMasterStepController {
    constructor(workflowMasterStepService) {
        this.workflowMasterStepService = workflowMasterStepService;
    }
    addNewWorkflowStep(request, newMasterStepRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterStepService.addNewWorkflowStep(newMasterStepRequestDto, request.currentContext);
        });
    }
    copyStepToOverridden(request, copyStepRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterStepService.copyStepToOverridden(copyStepRequestDto, request.currentContext);
        });
    }
    deleteStepFromOverridden(request, deleteStepRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterStepService.deleteStepFromOverridden(deleteStepRequestDto, request.currentContext);
        });
    }
    updateWorkflowStep(updateWorkflowStepRequestDto, request) {
        return this.workflowMasterStepService.updateWorkflowStep(updateWorkflowStepRequestDto, request.currentContext);
    }
    deleteWorkflowStep(stepId, request) {
        return this.workflowMasterStepService.deleteStep(stepId, request.currentContext);
    }
    deleteUnpublishedVersionWorkflowStep(stepId, request) {
        return this.workflowMasterStepService.deleteStep(stepId, request.currentContext, true);
    }
    childLimitShareStep(stepId, updateStepLimitShareRequestDTO, request) {
        return this.workflowMasterStepService.childLimitShareStep(stepId, updateStepLimitShareRequestDTO, request.currentContext);
    }
    changeApprovalSequence(stepId, stepMovementRequestDto, request) {
        return this.workflowMasterStepService.stepMovement(stepId, stepMovementRequestDto, request.currentContext);
    }
    getRoleBasedSteps(request, query) {
        const _a = Object.assign({}, query), { limit = 10, page = 1 } = _a, filter = __rest(_a, ["limit", "page"]);
        return this.workflowMasterStepService.getRoleBasedSteps(request.currentContext, limit, page, filter);
    }
    getStepDetail(stepId) {
        return this.workflowMasterStepService.getStepDetailWithId(stepId);
    }
    getStepBalance(stepId, request, entityId = null) {
        return this.workflowMasterStepService.getStepBalance(stepId, entityId, request.currentContext);
    }
    getExceptionSteps(stepId) {
        return this.workflowMasterStepService.getExceptionalOverriddenSteps(stepId);
    }
    getWorkflowStepHistory(stepId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterStepService.getWorkflowStepHistory(stepId);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add New Workflow Step.',
        type: dtos_2.GetMasterStepsResponseDTO,
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, new_master_step_request_dto_1.NewMasterStepRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "addNewWorkflowStep", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Copy step to requested overriddens.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/copy-step-to-overridden'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.CopyStepRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "copyStepToOverridden", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Delete step from requested overriddens.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/delete-step-from-overridden'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.DeleteStepRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "deleteStepFromOverridden", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update workflow step details.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Put)(''),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_master_step_request_dto_1.UpdateStepRequestDto, Object]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "updateWorkflowStep", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete workflow step.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)(':stepId'),
    __param(0, (0, common_1.Param)('stepId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "deleteWorkflowStep", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete unpublished version workflow step.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)(':stepId/unpublished-version'),
    __param(0, (0, common_1.Param)('stepId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "deleteUnpublishedVersionWorkflowStep", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update step limit sharing to child.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Put)('/:stepId/limit-share'),
    __param(0, (0, common_1.Param)('stepId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dtos_2.UpdateStepLimitShareRequestDTO, Object]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "childLimitShareStep", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Change step approval sequence number.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Put)('/:stepId/update-sequence'),
    __param(0, (0, common_1.Param)('stepId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_master_step_request_dto_1.StepMovementRequestDto, Object]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "changeApprovalSequence", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get step list based on user roles.',
        type: get_role_based_steps_response_dto_1.PaginatedGetRoleBasedStepsResponseDTO,
    }),
    (0, common_1.Get)('/role-based-steps'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "getRoleBasedSteps", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get step detail with step id.',
        type: get_child_shared_limit_response_dto_1.GetChildSharedLimitWithParentDetailDTO,
    }),
    (0, common_1.Get)('/:stepId'),
    __param(0, (0, common_1.Param)('stepId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "getStepDetail", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get step aggregate limit balance.',
        type: [get_aggregate_limit_balance_dto_1.GetAggregateLimitbalanceDTO],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        description: 'Parent Entity id to get list of all child entity with limit and deductions. Null in case of Group and Step other than Role.',
        required: false,
    }),
    (0, common_1.Get)('/:stepId/aggregate-balance'),
    __param(0, (0, common_1.Param)('stepId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, String]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "getStepBalance", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get exception overridden steps.',
        type: [get_exception_steps_response_dto_1.GetExceptionStepsResponseDTO],
    }),
    (0, common_1.Get)('/:stepId/exception-steps'),
    __param(0, (0, common_1.Param)('stepId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "getExceptionSteps", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get workflow step history by step id.',
        type: [get_history_response_dto_1.GetHistoryResponseDTO],
    }),
    (0, common_1.Get)('/:stepId/history'),
    __param(0, (0, common_1.Param)('stepId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterStepController.prototype, "getWorkflowStepHistory", null);
WorkflowMasterStepController = __decorate([
    (0, swagger_1.ApiTags)('Master Workflow Steps APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('workflow-step'),
    __metadata("design:paramtypes", [services_1.WorkflowMasterStepService])
], WorkflowMasterStepController);
exports.WorkflowMasterStepController = WorkflowMasterStepController;
//# sourceMappingURL=workflow-master-step.controller.js.map