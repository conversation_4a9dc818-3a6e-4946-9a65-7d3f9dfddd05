"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetBucketSharedLimitWithStepResponseDTO = exports.GetBucketSharedLimitResponseDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const get_master_steps_response_dto_1 = require("./get-master-steps-response.dto");
class GetBucketSharedLimitResponseDTO {
    constructor(partial = {}) {
        Object.assign(this, partial);
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetBucketSharedLimitResponseDTO.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetBucketSharedLimitResponseDTO.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetBucketSharedLimitResponseDTO.prototype, "entityCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetBucketSharedLimitResponseDTO.prototype, "bucketEntityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetBucketSharedLimitResponseDTO.prototype, "bucketEntityCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetBucketSharedLimitResponseDTO.prototype, "workflowMasterSettingId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetBucketSharedLimitResponseDTO.prototype, "workflowMasterStepId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetBucketSharedLimitResponseDTO.prototype, "createdOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetBucketSharedLimitResponseDTO.prototype, "updatedOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetBucketSharedLimitResponseDTO.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetBucketSharedLimitResponseDTO.prototype, "updatedBy", void 0);
exports.GetBucketSharedLimitResponseDTO = GetBucketSharedLimitResponseDTO;
class GetBucketSharedLimitWithStepResponseDTO {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: get_master_steps_response_dto_1.GetMasterStepsResponseDTO }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", get_master_steps_response_dto_1.GetMasterStepsResponseDTO)
], GetBucketSharedLimitWithStepResponseDTO.prototype, "stepDetail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [GetBucketSharedLimitResponseDTO] }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], GetBucketSharedLimitWithStepResponseDTO.prototype, "bucketList", void 0);
exports.GetBucketSharedLimitWithStepResponseDTO = GetBucketSharedLimitWithStepResponseDTO;
//# sourceMappingURL=get-bucket-shared-limit-response.dto.js.map