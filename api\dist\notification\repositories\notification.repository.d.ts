import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext, MutationParameters, NotificationPayload } from 'src/shared/types';
import { Notification } from '../models';
import { SequlizeOperator } from 'src/shared/helpers';
export declare class NotificationRepository extends BaseRepository<Notification> {
    private readonly sequlizeOperator;
    constructor(sequlizeOperator: SequlizeOperator);
    createNotification(payload: any, currentContext: CurrentContext): Promise<Notification | null>;
    bulkNotificationsInsert(records: NotificationPayload[], currentContext: CurrentContext): Promise<Notification[]>;
    addNotificationsViewerToViewedByList(notificationIds: number[], currentContext: CurrentContext): Promise<void>;
    getPaginatedNotificationListForUser(userId: string, limit?: number, page?: number): Promise<{
        rows: Notification[];
        count: number;
    } | null>;
    getAllMatchingSubscriber<PERSON><PERSON><PERSON><PERSON><PERSON>(userEmail: string): Promise<any>;
    updateNotificationByConditionWoUser(condition: any, data: any, params?: MutationParameters): Promise<number | null>;
}
