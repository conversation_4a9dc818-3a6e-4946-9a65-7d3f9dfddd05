"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectComponentService = void 0;
const common_1 = require("@nestjs/common");
const length_of_commitment_repository_1 = require("../../afe-config/repositories/length-of-commitment.repository");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const dtos_1 = require("../dtos");
const length_of_commitment_response_dto_1 = require("../dtos/response/length-of-commitment-response.dto");
const repositories_1 = require("../repositories");
let ProjectComponentService = class ProjectComponentService {
    constructor(entityService, projectComponentRepository, lengthOfCommitmentRepository) {
        this.entityService = entityService;
        this.projectComponentRepository = projectComponentRepository;
        this.lengthOfCommitmentRepository = lengthOfCommitmentRepository;
    }
    getProjectComponents(requestTypeId, locationId, childProject = true, budgetTypeId = null) {
        return __awaiter(this, void 0, void 0, function* () {
            let projectComponents = requestTypeId
                ? yield this.projectComponentRepository.getProjectComponentsByRequestId(requestTypeId, childProject, budgetTypeId)
                : yield this.projectComponentRepository.getAllProjectComponents();
            if (locationId) {
                projectComponents = yield this.entityService.filterItemsByEntityInclusionAndExclusion(locationId, projectComponents);
            }
            return (0, helpers_1.multiObjectToInstance)(dtos_1.ProjectComponentDto, projectComponents);
        });
    }
    getLengthOfCommitment() {
        return __awaiter(this, void 0, void 0, function* () {
            const listOfCommitment = yield this.lengthOfCommitmentRepository.getLengthOfCommitmentList();
            return (0, helpers_1.multiObjectToInstance)(length_of_commitment_response_dto_1.LengthOfCommitmentResponseDTO, listOfCommitment);
        });
    }
};
ProjectComponentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [services_1.EntityService,
        repositories_1.ProjectComponentRepository,
        length_of_commitment_repository_1.LengthOfCommitmentRepository])
], ProjectComponentService);
exports.ProjectComponentService = ProjectComponentService;
//# sourceMappingURL=project-component.service.js.map