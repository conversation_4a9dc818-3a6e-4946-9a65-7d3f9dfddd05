"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeProposalController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const passport_1 = require("@nestjs/passport");
const services_1 = require("../services");
const dtos_1 = require("../dtos");
const guards_1 = require("../../core/guards");
const decorators_1 = require("../../core/decorators");
const afe_proposal_approver_response_1 = require("../dtos/response/afe-proposal-approver-response");
const enums_1 = require("../../shared/enums");
const afe_listing_filter_request_dto_1 = require("../dtos/request/afe-listing-filter-request.dto");
const dtos_2 = require("../../shared/dtos");
let AfeProposalController = class AfeProposalController {
    constructor(afeProposalService, afeProposalReadService, afeProposalApproverService) {
        this.afeProposalService = afeProposalService;
        this.afeProposalReadService = afeProposalReadService;
        this.afeProposalApproverService = afeProposalApproverService;
    }
    createAfeProposal(request, submitAfeProposalRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeProposalService.submitAfeProposal(submitAfeProposalRequestDto, request.currentContext);
        });
    }
    getAfeProposalById(request, id, taskId) {
        return this.afeProposalReadService.getAfeProposalById(id, request.currentContext, taskId);
    }
    getAfeListing(request, limit = 10, page = 1, forApprovalHistory = false, filter) {
        return this.afeProposalReadService.getAfeListing(request.currentContext, limit, page, forApprovalHistory, filter);
    }
    getApproversListOfAfePoposal(afeProposalId, request, taskId) {
        return this.afeProposalReadService.getApproversListOfAfePoposal(afeProposalId, request.currentContext, taskId);
    }
    getAmountSplitsOfAfeProposal(afeProposalId, request, taskId) {
        return this.afeProposalReadService.getAmountSplitsOfAfeProposal(afeProposalId, request.currentContext, taskId);
    }
    getAfeProposalActionHistory(afeProposalId, request, taskId) {
        return this.afeProposalReadService.getAfeProposalActionHistory(afeProposalId, request.currentContext, taskId);
    }
    resubmitAfeProposal(request, resubmitAfeProposalRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeProposalService.resubmitAfeProposal(resubmitAfeProposalRequestDto, request.currentContext);
        });
    }
    searchAfeListByProjectRefernceNumber(request, projectReferenceNumber, afeRequestTypeIds, entityId) {
        return this.afeProposalReadService.searchAfeListByProjectReferenceNumber(projectReferenceNumber, JSON.parse(afeRequestTypeIds), request.currentContext, entityId);
    }
    getAllRelatedSupplementalAfes(request, afeProposalId, taskId) {
        return this.afeProposalReadService.getAllRelatedSupplementalAfes(afeProposalId, request.currentContext, taskId);
    }
    isAfeOrItsSupplementalInProgress(request, afeProposalId) {
        return this.afeProposalReadService.isAfeOrItsSupplementalInProgress(afeProposalId, request.currentContext);
    }
    getLatestVersionAfeInfoByParentAfeRefNumber(request, afeRefNumber) {
        return this.afeProposalReadService.getLatestVersionAfeInfoByParentAfeRefNumber(afeRefNumber, request.currentContext);
    }
    toggleAfeNotificationSubscription(request, afeProposalId, toggleValue) {
        return this.afeProposalService.toggleAfeNotificationSubscription(afeProposalId, toggleValue, request.currentContext);
    }
    updateProposalDetail(request, updateAfeDetailRequestDTO) {
        return this.afeProposalService.updateProposalDetail(updateAfeDetailRequestDTO, request.currentContext);
    }
    generateAfeProposalPdf(request, afeProposalId, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const pdf = yield this.afeProposalReadService.generateAfePdf(afeProposalId, request.currentContext);
            res.set({
                'Content-Disposition': 'attachment; filename="document.pdf"',
                'Content-Type': 'application/pdf',
            });
            res.send(pdf);
        });
    }
    updateApproversList(request, afeProposalId, updateApproversListRequestDto) {
        return this.afeProposalApproverService.updateAfeProposalApprover(afeProposalId, updateApproversListRequestDto, request.currentContext);
    }
    addNewReaders(request, afeProposalId, addNewReadersRequestDto) {
        return this.afeProposalService.addNewReader(afeProposalId, addNewReadersRequestDto, request.currentContext);
    }
    withdrawAfeProposal(request, withdrawAfeProposalRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeProposalService.withdrawAfeProposal(withdrawAfeProposalRequestDto, request.currentContext);
        });
    }
    updateParentAfeSupplementalsDeltaAmounts(parentId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeProposalService.updateParentAfeSupplementalsDeltaAmounts(parentId);
        });
    }
    sendBackAfeProposal(request, sendBackAfeProposalRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeProposalService.sendBackAfeProposal(sendBackAfeProposalRequestDto, request.currentContext);
        });
    }
    reopenAfeProposal(request, reopenAfeProposalRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeProposalService.reopenAfeProposal(reopenAfeProposalRequestDto, request.currentContext);
        });
    }
    revertSentBackAction(request, reopenAfeProposalRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeProposalService.revertSentBackAction(reopenAfeProposalRequestDto, request.currentContext);
        });
    }
    uploadEvidence(request, afeProposalId, evidences) {
        return this.afeProposalService.uploadEvidence(afeProposalId, evidences, request.currentContext);
    }
    updateApproverUser(request, afeProposalId, updateApproverUserRequestDTO) {
        return this.afeProposalService.updateApproverUser(afeProposalId, updateApproverUserRequestDTO, request.currentContext);
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_SUBMIT),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Create new afe proposal',
        type: dtos_1.CreateAfeProposalResposeDto,
    }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.SubmitAfeProposalRequestDto]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "createAfeProposal", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE details by its id.',
        type: dtos_1.AfeProposalResposeDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'taskId',
        type: Number,
        description: 'Taks id of the AFE if any.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "getAfeProposalById", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        type: Number,
        description: 'Number of records in response.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        type: Number,
        description: 'Page number of the paginated record.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get paginated AFE listing.',
        type: [dtos_1.PaginatedAfeProposalListingResponseDto],
    }),
    (0, common_1.Post)('list'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('forApprovalHistory')),
    __param(4, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, Boolean, afe_listing_filter_request_dto_1.AfeListingFilterRequestDto]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "getAfeListing", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE approvers list details by proposal id.',
        type: afe_proposal_approver_response_1.AfeProposalApproverResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'taskId',
        type: Number,
        description: 'Taks id of the AFE if any.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, common_1.Get)(':afeProposalId/approvers'),
    __param(0, (0, common_1.Param)('afeProposalId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Number]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "getApproversListOfAfePoposal", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE amount splits details by proposal id.',
        type: dtos_1.AfeProposalAmountSplitResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'taskId',
        type: Number,
        description: 'Taks id of the AFE if any.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, common_1.Get)(':afeProposalId/amount-splits'),
    __param(0, (0, common_1.Param)('afeProposalId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Number]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "getAmountSplitsOfAfeProposal", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE proposal action history.',
        type: dtos_1.AfeProposalActionHistoryResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'taskId',
        type: Number,
        description: 'Taks id of the AFE if any.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, common_1.Get)(':afeProposalId/history'),
    __param(0, (0, common_1.Param)('afeProposalId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Number]),
    __metadata("design:returntype", void 0)
], AfeProposalController.prototype, "getAfeProposalActionHistory", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_SUBMIT),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Resubmit the afe proposal and create the new version of it.',
        type: dtos_1.CreateAfeProposalResposeDto,
    }),
    (0, common_1.Post)('/resubmit'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ResubmitAfeProposalRequestDto]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "resubmitAfeProposal", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search AFE list by project reference number.',
        type: [dtos_1.SearchAfeByProjectRefernceNoResponse],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'afeRequestTypeIds',
        type: String,
        description: 'Afe request type ids and pass as a string like [6, 7]',
        required: true,
        allowEmptyValue: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'projectReferenceNumber',
        type: String,
        description: 'Project reference number search string.',
        required: true,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Business entity id.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, common_1.Get)('approved/search'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('projectReferenceNumber')),
    __param(2, (0, common_1.Query)('afeRequestTypeIds')),
    __param(3, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, Number]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "searchAfeListByProjectRefernceNumber", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all the related AFEs info (Parent afe and its supplementals).',
        type: [dtos_1.RelatedAfesInfoResponse],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'taskId',
        type: Number,
        description: 'Taks id of the AFE if any.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, common_1.Get)(':afeProposalId/supplementals'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "getAllRelatedSupplementalAfes", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Check if afe or its any supplemental in progress.',
        type: dtos_1.CheckAfeOrItsSupplementalInProgress,
    }),
    (0, common_1.Get)(':afeProposalId/check-inprogress'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "isAfeOrItsSupplementalInProgress", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get latest version of AFE info by parent afe reference number.',
        type: dtos_1.LatestAfeVersionInfoResponseDto,
    }),
    (0, common_1.Get)('supplemental/latest-version-info'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('afeRefNumber')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "getLatestVersionAfeInfoByParentAfeRefNumber", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiParam)({ name: 'toggleValue', type: Number, enum: enums_1.TOGGLE_ON_OFF }),
    (0, swagger_1.ApiParam)({ name: 'afeProposalId', type: Number }),
    (0, common_1.Post)(':afeProposalId/subscribe/:toggleValue'),
    (0, common_1.HttpCode)(204),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Param)('toggleValue')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, String]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "toggleAfeNotificationSubscription", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update proposal detail if user has general or finance edit permission.',
    }),
    (0, common_1.Put)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.UpdateAfeDetailRequestDTO]),
    __metadata("design:returntype", void 0)
], AfeProposalController.prototype, "updateProposalDetail", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Download AFE proposal detail pdf file.' }),
    (0, common_1.Post)(':afeProposalId/download-pdf'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "generateAfeProposalPdf", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update afe approvers list if user has afe submitter or Administration permission.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Put)(':afeProposalId/approvers'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_1.UpdateApproversListRequestDto]),
    __metadata("design:returntype", void 0)
], AfeProposalController.prototype, "updateApproversList", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Add new readers to AFE.',
        type: dtos_2.MessageResponseDto
    }),
    (0, common_1.Put)(':afeProposalId/add-new-readers'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_1.AddNewReadersRequestDto]),
    __metadata("design:returntype", void 0)
], AfeProposalController.prototype, "addNewReaders", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_SUBMIT),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Withdraw the afe proposal.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('/withdraw'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.WithdrawAfeProposalRequestDto]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "withdrawAfeProposal", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Post)('/update-delta-amount/:parentId'),
    __param(0, (0, common_1.Param)('parentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "updateParentAfeSupplementalsDeltaAmounts", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_SUBMIT),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Send back the afe proposal.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('/send-back'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.SendBackAfeProposalRequestDto]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "sendBackAfeProposal", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Reopen Rejected afe proposal.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('/re-open'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ReopenAfeProposalRequestDto]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "reopenAfeProposal", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Revert Sent Back Action.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('/revert-sent-back-action'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ReopenAfeProposalRequestDto]),
    __metadata("design:returntype", Promise)
], AfeProposalController.prototype, "revertSentBackAction", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Upload Evidence to AFE.',
        type: dtos_2.MessageResponseDto
    }),
    (0, common_1.Post)(':afeProposalId/upload-evidence'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_1.UploadEvidenceRequestDto]),
    __metadata("design:returntype", void 0)
], AfeProposalController.prototype, "uploadEvidence", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update custom in-Progress approver user.',
        type: dtos_2.MessageResponseDto
    }),
    (0, common_1.Post)(':afeProposalId/update-approver-user'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_1.UpdateApproverUserRequestDTO]),
    __metadata("design:returntype", void 0)
], AfeProposalController.prototype, "updateApproverUser", null);
AfeProposalController = __decorate([
    (0, swagger_1.ApiTags)('Afe Proposal APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('afe-proposals'),
    __metadata("design:paramtypes", [services_1.AfeProposalService,
        services_1.AfeProposalReadService,
        services_1.AfeProposalApproverService])
], AfeProposalController);
exports.AfeProposalController = AfeProposalController;
//# sourceMappingURL=afe-proposal.controller.js.map