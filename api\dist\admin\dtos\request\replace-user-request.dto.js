"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReplaceUserRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class ReplaceUserRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Login ID of the user whose ID will be replaced. Id is case sensitive so use both form of id (all small letter and original form) as well to update all possible places.',
        example: 'ALL SMALL LETTER - <EMAIL> & ORIGINAL FORM - <EMAIL>'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ReplaceUserRequestDto.prototype, "sourceLoginId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ReplaceUserRequestDto.prototype, "targetLoginId", void 0);
exports.ReplaceUserRequestDto = ReplaceUserRequestDto;
//# sourceMappingURL=replace-user-request.dto.js.map