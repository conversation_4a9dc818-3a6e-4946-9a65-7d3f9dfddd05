"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueueLog = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
let QueueLog = class QueueLog extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], QueueLog.prototype, "entityId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'action',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.QUEUE_LOG_ACTION)),
        allowNull: true,
    }),
    __metadata("design:type", String)
], QueueLog.prototype, "action", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'final_approval',
        type: sequelize_typescript_1.DataType.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], QueueLog.prototype, "finalApproval", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'data', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], QueueLog.prototype, "data", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'processed', type: sequelize_typescript_1.DataType.BOOLEAN, allowNull: false, defaultValue: false }),
    __metadata("design:type", Boolean)
], QueueLog.prototype, "processed", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'retry_count', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false, defaultValue: 0 }),
    __metadata("design:type", Number)
], QueueLog.prototype, "retryCount", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'errors', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], QueueLog.prototype, "errors", void 0);
QueueLog = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'queue_logs' })
], QueueLog);
exports.QueueLog = QueueLog;
//# sourceMappingURL=queue-log.model.js.map