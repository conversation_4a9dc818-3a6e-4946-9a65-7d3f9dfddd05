import { CURRENCY_TYPE } from 'src/shared/enums';
import { AllCostCenterResponseDto, AnalysisCodeResponseDto, CompanyCodeResponseDto, CostCenterFilterResponseDto, CostCenterResponseDto, CurrencyConversionResponseDto, NaturalAccountResponseDto } from '../dtos';
import { FinanceService } from '../services';
export declare class FinanceController {
    private readonly financeService;
    constructor(financeService: FinanceService);
    isCompanyExistForBusinessEntity(entityId: number): Promise<boolean>;
    getNaturalAccountNumbersByRequestType(requestTypeId: number, entityId: number): Promise<NaturalAccountResponseDto[]>;
    getAnalysisCodesByRequestType(requestTypeId: number, entityId: number): Promise<AnalysisCodeResponseDto[]>;
    getCostCentersWithCompanyCodeByEntity(entityId: number): Promise<CostCenterResponseDto[]>;
    getCurrencyConversionRateToPrimary(currencyType: CURRENCY_TYPE): Promise<CurrencyConversionResponseDto>;
    getCurrencyTypeForEntity(entityId: number): Promise<CurrencyConversionResponseDto>;
    getCompanyCodeList(excludedCode: string): Promise<CompanyCodeResponseDto[]>;
    getCostCenterByEntityIds(entityIds: {
        entityIds: number[];
    }): Promise<CostCenterFilterResponseDto[]>;
    getAllCostCenters(): Promise<AllCostCenterResponseDto[]>;
}
