import { Controller, Get, Param, Post, Body, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CURRENCY_TYPE } from 'src/shared/enums';
import {
	AllCostCenterResponseDto,
	AnalysisCodeResponseDto,
	CompanyCodeResponseDto,
	CostCenterFilterResponseDto,
	CostCenterResponseDto,
	CurrencyConversionResponseDto,
	NaturalAccountResponseDto,
} from '../dtos';
import { FinanceService } from '../services';

@ApiTags('Finance APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('finance')
export class FinanceController {
	constructor(private readonly financeService: FinanceService) { }

	@ApiResponse({
		status: 200,
		description: 'Check if company exists for the business entity or not.',
		type: Boolean,
	})
	@Get('entity/:entityId/company-codes/exists')
	public isCompanyExistForBusinessEntity(
		@Param('entityId') entityId: number
	) {
		return this.financeService.checkCompanyCodeExistsForBusinessEntity(entityId);
	}

	@ApiResponse({
		status: 200,
		description: 'Get company detail for the business entity.',
		type: CompanyCodeResponseDto,
	})
	@Get('entity/:entityId/company-detail')
	public getCompanyDetailForBusinessEntity(
		@Param('entityId') entityId: number
	) {
		return this.financeService.getCompanyDetailForBusinessEntity(entityId);
	}

	@ApiResponse({
		status: 200,
		description: 'Get natural account numbers by request type.',
		type: [NaturalAccountResponseDto],
	})
	@Get('request-type/:requestTypeId/natural-account-numbers')
	@ApiQuery({ name: 'entityId', required: true })
	public getNaturalAccountNumbersByRequestType(
		@Param('requestTypeId') requestTypeId: number,
		@Query('entityId') entityId: number
	) {
		return this.financeService.getNaturalAccountNumbersByRequestType(
			requestTypeId,
			entityId
		);
	}

	@ApiResponse({
		status: 200,
		description: 'Get analysis codes by request type.',
		type: [AnalysisCodeResponseDto],
	})
	@Get('request-type/:requestTypeId/analysis-codes')
	@ApiQuery({ name: 'entityId', required: true })
	public getAnalysisCodesByRequestType(
		@Param('requestTypeId') requestTypeId: number,
		@Query('entityId') entityId: number,
	) {
		return this.financeService.getAnalysisCodesByRequestType(
			requestTypeId,
			entityId
		);
	}

	@ApiResponse({
		status: 200,
		description: 'Get all the cost centers with its company code by entity id. ',
		type: [CostCenterResponseDto],
	})
	@Get('entity/:entityId/cost-centers')
	public getCostCentersWithCompanyCodeByEntity(@Param('entityId') entityId: number) {
		return this.financeService.getCostCentersWithCompanyCodeByEntity(entityId);
	}

	@ApiResponse({
		status: 200,
		description: 'Get given currency conversion rate to primary currency',
		type: CurrencyConversionResponseDto,
	})
	@Get('currency/:currencyType/conversion-rate')
	public getCurrencyConversionRateToPrimary(@Param('currencyType') currencyType: CURRENCY_TYPE) {
		return this.financeService.getCurrencyConversionRateToPrimary(currencyType);
	}

	@ApiResponse({
		status: 200,
		description: 'Get currency type for an entity.',
		type: CurrencyConversionResponseDto,
	})
	@Get('entity/:entityId/currency')
	public getCurrencyTypeForEntity(@Param('entityId') entityId: number) {
		return this.financeService.getCurrencyTypeForEntity(entityId);
	}

	@ApiResponse({
		status: 200,
		description: 'Return the active company code details for an business entity.',
		type: [CompanyCodeResponseDto],
	})
	@Get('get-company-code-excluding/:excludedCode')
	public getCompanyCodeList(
		@Param('excludedCode') excludedCode: string
	): Promise<CompanyCodeResponseDto[]> {
		return this.financeService.getCompanyCodeListExcluding(excludedCode);
	}

	@ApiResponse({
		status: 200,
		description: 'Get cost center of business entity',
		type: CostCenterFilterResponseDto,
	})
	@Post('cost-center-by-entity-ids')
	public async getCostCenterByEntityIds(
		@Body() entityIds: { entityIds: number[] }
	) {
		return this.financeService.getCostCenterByEntityIds(entityIds);
	}

	@ApiResponse({
		status: 200,
		description: 'Get all the cost centers. ',
		type: [AllCostCenterResponseDto],
	})
	@Get('get-all-cost-centers')
	public getAllCostCenters() {
		return this.financeService.getAllCostCenters();
	}
}
