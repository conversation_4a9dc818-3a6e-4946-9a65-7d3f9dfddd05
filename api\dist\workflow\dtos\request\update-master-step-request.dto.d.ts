import { StepPositionActionEnum } from 'src/workflow/types';
export declare class UpdateStepRequestDto {
    stepId: number;
    title: string;
    associateType: string;
    associateRole?: string;
    associateLevel?: string;
    associatedUser?: string;
    associatedColumn?: string;
    parallelIdentifier?: string;
    canWorkflowStart: boolean;
    isMandatory: boolean;
    includeBelowSteps: boolean;
    skipLimitRuleId: boolean;
    canRemovedAtChildLevel: boolean;
    canShareLimitToChild?: boolean;
    ruleId?: number;
    singleLimit?: number;
    aggregateLimit?: number;
    lengthOfCommitment?: number;
    unpublishedVersion?: boolean;
}
export declare class StepMovementRequestDto {
    position: StepPositionActionEnum;
    unpublishedVersion?: boolean;
}
