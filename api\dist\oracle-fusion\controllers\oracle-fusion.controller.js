"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OracleFusionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const oracle_fusion_service_1 = require("../services/oracle-fusion.service");
let OracleFusionController = class OracleFusionController {
    constructor(oracleFusionService) {
        this.oracleFusionService = oracleFusionService;
    }
};
OracleFusionController = __decorate([
    (0, swagger_1.ApiTags)('Oracle Fusion Integration API'),
    (0, common_1.Controller)('oracle-fusion'),
    __metadata("design:paramtypes", [oracle_fusion_service_1.OracleFusionService])
], OracleFusionController);
exports.OracleFusionController = OracleFusionController;
//# sourceMappingURL=oracle-fusion.controller.js.map