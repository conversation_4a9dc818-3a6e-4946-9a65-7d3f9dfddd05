"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataSharingSchedulers = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
let DataSharingSchedulers = class DataSharingSchedulers extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], DataSharingSchedulers.prototype, "title", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'frequency',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.FREQUENCY)),
        allowNull: false,
    }),
    __metadata("design:type", String)
], DataSharingSchedulers.prototype, "frequency", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'month_day', type: sequelize_typescript_1.DataType.INTEGER, allowNull: true }),
    __metadata("design:type", Number)
], DataSharingSchedulers.prototype, "monthDay", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'rule', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], DataSharingSchedulers.prototype, "rule", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entities', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Array)
], DataSharingSchedulers.prototype, "entities", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'recipients', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], DataSharingSchedulers.prototype, "recipients", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'last_run_at', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], DataSharingSchedulers.prototype, "lastRunAt", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'next_run_at', allowNull: true, type: sequelize_typescript_1.DataType.DATEONLY }),
    __metadata("design:type", Date)
], DataSharingSchedulers.prototype, "nextRunAt", void 0);
DataSharingSchedulers = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'data_sharing_schedulers' })
], DataSharingSchedulers);
exports.DataSharingSchedulers = DataSharingSchedulers;
//# sourceMappingURL=data_sharing_scheduler.model.js.map