import { RequestContext } from 'src/shared/types';
import { RequestTypeWiseAfeResponseDto } from '../dtos/response/request-type-wise-afe-response.dto';
import { StatusWiseAFEResponseDto } from '../dtos/response/status-wise-afe-response.dto';
import { DashboardService } from '../services';
export declare class DashboardController {
    private dashboardService;
    constructor(dashboardService: DashboardService);
    getStatusWiseCount(request: RequestContext, year?: number | null): Promise<StatusWiseAFEResponseDto>;
    getRequestTypeWiseCount(request: RequestContext, year: number, entityId?: number | null): Promise<RequestTypeWiseAfeResponseDto[]>;
}
