<html>

<head>
  <link rel="stylesheet" href="/node_modules/@fortawesome/fontawesome-free/css/all.min.css">
  <style>
    html {
      -webkit-print-color-adjust: exact;
    }

    @font-face {
      font-family: 'Pilat Demi';
      src: url({#PilateDemi#}) format('truetype');
    }

    @font-face {
      font-family: 'Pilat Light';
      src: url({#PilateLight#}) format('truetype');
    }

    @font-face {
      font-family: 'Pilat Heavy';
      src: url({#PilateHeavy#}) format('truetype');
    }

    * {
      margin: 0;
    }

    * {
      font-family: <PERSON><PERSON>, <PERSON>, sans-serif;
      box-sizing: border-box;
    }
    .account-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #dedede;
      padding: 5px 0;
    }

    .account-detail,
    .amount-detail {
      display: flex;
      gap: 5px;
      font-size: 14px;
      color: #3c3c3c;
    }

    .account-label {
      font-weight: 500;
      color: #575757;
    }

    h5.sub {
      margin: 0;
      padding: 10px 15px;
      font-weight: bold;
      font-family: <PERSON><PERSON>, Tahoma, sans-serif;
      border: 1px solid #bdbdbd;
      font-size: 16px;
      border-bottom: none;
    }

    .sub-header .table td {
      color: black;
      border: none;
    }

    .border-none {
      border: 0 !important;
    }

    .mt-5 {
      margin-top: 5px !important;
    }

    .mt-10 {
      margin-top: 10px !important;
    }

    .mt-15 {
      margin-top: 15px !important;
    }

    .mt-20 {
      margin-top: 20px !important;
    }

    .mt-40 {
      margin-top: 40px !important;
    }

    .bold {
      font-weight: bold !important;
      font-family: Pilat Wide Heavy, Tahoma, sans-serif;
    }

    .text-right {
      text-align: right !important;
    }

    .text-left {
      text-align: left;
    }

    .text-center {
      text-align: center;
    }

    .border-bottom {
      border-bottom: 1px solid #bdbdbd !important;
    }

    .border-left {
      border-left: 1px solid #bdbdbd !important;
    }

    .sub1 {
      color: #ffffff;
    }

    .table {
      border-collapse: collapse;
      width: 100%;
      border-spacing: 0px 5px;
    }

    .table th {
      border: 1px solid #bdbdbd;
      padding: 10px 14px;
      text-align: left;
      font-size: 13px;
      font-weight: 600;
      font-family: Pilat Wide Heavy, Tahoma, sans-serif;
    }

    .table td {
      border: 1px solid #bdbdbd;
      padding: 10px 10px;
      text-align: left;
      font-size: 13px;
      font-family: Pilat Demi, Tahoma, sans-serif;
    }

    .flex {
      display: flex;
      border: 1px solid #bdbdbd;
      margin-bottom: 7px;
      width: 99.87%;
    }

    .para {
      padding: 0px 15px;
      font-size: 15px;
      color: black;
    }

    .img-block {
      border: 1px solid #bdbdbd;
      border-top: none;
      padding: 10px;
    }

    .img-block img {
      width: 30%;
      border: 1px solid #bdbdbd;
      margin: 0px 10px;
    }

    table.border-top-none th,
    table.border-top-none td {
      border-top: 0px !important;
    }

    .border-top-none th {
      border: 1px solid #bdbdbd;
      padding: 10px 14px;
      text-align: left;
      font-size: 13px;
      border-top: 0px !important;
    }

    .border-top-none td {
      border: 1px solid #bdbdbd;
      padding: 10px 10px;
      text-align: left;
      font-size: 13px;
      border-top: 0px !important;
    }

    .header-color th {
      background-color: #bfbfbf
    }

    .header-color th:not(:last-child) {
      border-right: 1px solid #a2a2a2 !important;
    }

    .w-5 {
      width: 5% !important;
    }

    .w-10 {
      width: 10% !important;
    }

    .w-20 {
      width: 20% !important;
    }

    .w-15 {
      width: 15% !important;
    }

    .w-25 {
      width: 25% !important;
    }

    .w-35 {
      width: 35% !important;
    }

    .w-50 {
      width: 50% !important;
    }

    .w-75 {
      width: 75% !important;
    }

    .w-100 {
      width: 100% !important;
    }

    .w-40 {
      width: 40% !important;
    }

    .w-60 {
      width: 60% !important;
    }

    .h-100 {
      height: 100% !important;
    }

    .table1 {
      border-collapse: collapse;
      width: 100%;
      border-spacing: 0px 0px;
      margin-bottom: 10px;
    }


    .m-auto {
      margin: auto;
    }

    .table1 img {
      width: 200px;
    }

    .table1 h5 {
      font-size: 22px;
      margin-top: 10px;
    }

    .label {
      padding: 0;
      margin: 0;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      font-weight: 400;
      height: 20px;
      width: auto;
      font-size: .8rem;
    }

    .label-light-danger {
      color: #f64e60;
      background-color: #ffe2e5;
    }

    .label-light-primary {
      color: #3699ff;
      background-color: #e1f0ff;
    }

    .label-inline {
      height: auto !important;
      padding: 4px 7px !important;
      border-radius: .42rem;
    }

    .ml-3 {
      margin-left: .75rem !important;
    }

    .strip-label-btn {
      font-size: 12px;
    }

    .font-weight-bold {
      font-weight: 500 !important;
      font-family: Pilat Wide Heavy, Tahoma, sans-serif;
    }

    .label-orange {
      color: orange;
      background-color: #f9dcd0;
    }

    .label-light-success {
      color: #1bc5bd;
      background-color: #c9f7f5;
    }

    .label-danger {
      color: #fff;
      background-color: #f64e60;
    }

    .label-light-warning {
      color: #FFA800;
      background-color: #FFF4DE;
    }

    .approver-col p {
      margin: 0;
      font-size: 12px;
    }

    .approver-col h4 {
      margin: 0;
      font-size: 14px;
      font-weight: bold;
    }

    .project-amount-container {
      border: #dedede 1.2px solid;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .project-amount-section {
      width: 100%;
    }

    .project-title {
      font-size: 15px;
      font-weight: 600;
    }

    .project-amount {
      margin-top: 4px;
      font-size: 14px;
      color: #3c3c3c;
    }

    .expenditure-container {
      border: #dedede 1.2px solid;
      border-radius: 8px;
    }

    .details {
      padding-left: 12px;
    }

    .amount-split {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 20%;
    }

    .finance-sub-title {
      margin-top: 5px;
      font-weight: bold;
      font-size: 14px;
    }

    .split-type-label {
      font-size: 12px;
      font-weight: 500;
      color: #575757;
      margin-bottom: auto;
    }

    .amount-text {
      margin-top: 4px;
      font-size: 14px;
      color: #3c3c3c;
      margin-bottom: 12px;
    }

    .attachments-container {
      border: 1px solid #bdbdbd;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;
      gap: 10px;
    }

    .attachment {
      width: 30%;
      padding: 10px;
      margin-right: 10px;
      display: inline-block;
      vertical-align: top;
    }

    .attachment p {
      word-wrap: break-word;
      font-size: 12px;
      color: rgb(128, 128, 128);
      margin: 10px 0 0 0;
    }

    .attachment h4 {
      word-wrap: break-word;
      font-size: 14px;
      font-weight: bold;
      margin: 10px 0 0 0;
    }

    .attachment img {
      width: 60%;
    }

    .history-container {
      border: 1px solid #bdbdbd;
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .history-item {
      padding: 10px;
    }

    .history-title {
      margin: 0;
      font-weight: bold;
      font-size: 14px;
    }

    .history-author {
      margin: 0;
      padding-top: 5px;
      font-size: 12px;
    }

    .history-comment {
      margin: 0;
      padding-top: 5px;
      font-size: 12px;
      color: rgb(128, 128, 128);
    }

    .p-2 {
      padding: 2px !important;
    }

    .p-5 {
      padding: 5px !important;
    }

    .p-10 {
      padding: 10px !important;
    }

    .box-container {
      border: #dedede 1.2px solid;
      border-radius: 8px;
    }

    .account-number-section {
      margin-top: 5px;
      font-weight: bold;
      font-size: 14px;
    }

    .amount-sction {
      font-size: 12px;
      font-weight: 500;
      color: #575757;
      margin-bottom: auto;
    }

    .budget-type-unit {
      width: 100%;
      font-weight: 500;
      color: #575757;
    }

    .budget-type-unit h5 {
      font-size: 12px;
    }

    .budget-type-unit p {
      font-size: 14px;
    }

    .content-center {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }

    .content-item {
      margin-bottom: 10px; /* Adjust spacing between items as needed */
      text-align: center; /* Optionally center-align text */
    }

    .budget-type-section {
      display: flex;
      justify-content: space-between;
    }

    .budget-ref-detail {
      display: flex;
      justify-content: space-between;
    }

    .currency-amount {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;
    }

    .currency {
      width: 100%;
    }
  </style>
</head>

<body style="font-family: Pilat Demi, Tahoma, sans-serif;">
<div class="root">
  <div class="h-100 w-100 content-center">
    <div class="content-item" style="margin-bottom: 50px;">
      <img style="width: 300px;" src="{{base64DpWorldLogo}}" alt="DP World Logo">
    </div>
    <div class="content-item" style="font-size: 28px;">AFE Report</div>
    <div class="content-item" style="font-size: 18px;">Ref. No. {{ projectRefNum }}</div>
    <div class="content-item" style="font-size: 18px; width: 75%;">{{ projectName }}</div>
  </div>

  {{!-- <h5 class="sub" style="border: none !important;"></h5> --}}

  <h5 class="sub" style="color: white; background-color:#201864">
    Proposal Information
  </h5>
  <table class="table w-100">
    <tr>
      <th class="w-25">Project Ref. No.:</th>
      <td colspan="3">{{ projectRefNum }}</td>
    </tr>
    <tr>
      <th class="w-25">AFE Request Type:</th>
      <td colspan="3">{{ afeCategory }} {{ afeRequestType }}</td>
    </tr>
    <tr>
      <th class="w-25">Current Status:</th>
      <td colspan="3">{{ currentStatus }}</td>
    </tr>
    <tr>
      <th class="w-25">Created At:</th>
      <td colspan="3">{{ createAt }}</td>
    </tr>
    <tr>
      <th class="w-25">Business Entity:</th>
      <td colspan="3">{{ businessEntity }}</td>
    </tr>
    {{#if location }}
    <tr>
      <th class="w-25">Location:</th>
      <td colspan="3">{{ location }}</td>
    </tr>
    {{/if}}
    <tr>
      {{#if (or (eq afeRequestTypeId 1) (eq afeRequestTypeId 2))}}
      <th class="w-25">Project Name:</th>
      {{else}}
      <th class="w-25">Title:</th>
      {{/if}}
      <td colspan="3">{{ projectName }}</td>

    </tr>
    <tr>
      {{#if (or (eq afeRequestTypeId 1) (eq afeRequestTypeId 2))}}
      <th class="w-25">Project Justification</th>
      {{else}}
      <th class="w-25">Justification:</th>
      {{/if}}
      <td colspan="3">{{{ newlineToBr projectJustification }}}</td>
    </tr>
    <tr>
      {{#if (or (eq afeRequestTypeId 1) (eq afeRequestTypeId 2))}}
      <th class="w-25">Project Leader:</th>
      {{else}}
      <th class="w-25">Contact Person:</th>
      {{/if}}
      <td colspan="3">{{ projectLeaderName }}</td>
    </tr>
    {{#if projectLeaderContactNum }}
    <tr>
      {{#if (or (eq afeRequestTypeId 1) (eq afeRequestTypeId 2))}}
      <th class="w-25">Project Leader Contact:</th>
      {{else}}
      <th class="w-25">Contact Number:</th>
      {{/if}}
      <td colspan="3">{{ projectLeaderContactNum }}</td>
    </tr>
    {{/if}}
    {{#if (or (eq afeRequestTypeId 1) (eq afeRequestTypeId 2))}}
    <tr>
      <th class="w-25">Key Contacts:</th>
      <td colspan="3">{{ keyContacts }}</td>
    </tr>
    {{/if}}
    {{#if supplementalData.scopeChanges }}
    <tr>
      <th class="w-25">Scope Change:</th>
      <td colspan="3">{{ supplementalData.scopeChanges }}</td>
    </tr>
    {{/if}}
    {{#if supplementalData.valueChanges }}
    <tr>
      <th class="w-25">Value Change:</th>
      <td colspan="3">{{ supplementalData.valueChanges }}</td>
    </tr>
    {{/if}}
    {{#if supplementalData.scopeChangeReason }}
    <tr>
      <th class="w-25">Reason for change in scope:</th>
      <td colspan="3">{{ supplementalData.scopeChangeReason }}</td>
    </tr>
    {{/if}}
  </table>

  <!-- Project Details -->
  <h5 class="sub mt-20" style="color: white; background-color:#201864">
    Proposal Details
  </h5>
  <table class="table w-100">
    {{#if budgetType }}
    <tr>
      <th class="w-25">Budget Type:</th>
      <td colspan="3">{{ budgetType }}</td>
    </tr>
    {{/if}}
    {{#if yearOfCommitment }}
    <tr>
      <th class="w-25">Length of Commitment:</th>
      <td colspan="3">{{ yearOfCommitment }}</td>
    </tr>
    {{/if}}
    {{#if nature }}
    <tr>
      <th class="w-25">Nature type:</th>
      <td colspan="3">{{ nature }}</td>
    </tr>
    {{/if}}
    {{#if afeSubCategory }}
    <tr>
      <th class="w-25">Sub Type:</th>
      <td olspan="3">{{ afeSubCategory }}</td>
    </tr>
    {{/if}}
    {{#if type }}
    <tr>
      <th class="w-25">Type:</th>
      <td olspan="3">{{ type }}</td>
    </tr>
    {{/if}}
    {{#if budgetTypeJustification }}
    <tr>
      <th class="w-25">Why was this expense not included on the budget?</th>
      <td colspan="3">{{ budgetTypeJustification }}</td>
    </tr>
    {{/if}}
    {{#if (neq afeRequestTypeId 2)}}
    <tr>
      <th class="w-25">Has this proposal already been approved by the DPW Ltd Board of Directors/ExCom in Dubai?</th>
      <td colspan="3">{{ isApprovedByBoard }}</td>
    </tr>
    {{/if}}
    <tr>
      <th class="w-25">Submitted for Year:</th>
      <td>{{ submitYear }}</td>
    </tr>
  </table>

  <!-- Project Details -->
  {{#if splits.projectComponentSplit}}
  <h5 class="sub mt-20" style="color: white; background-color:#201864">
    Project Component Amount
  </h5>
  <div class="project-amount-container">
    {{#each splits.projectComponentSplit}}
    <div class="project-amount-section">
      <div class="project-title">
        {{ this.title }}
      </div>
      <div class="project-amount">
        {{ formattedCurrency this.amount this.currency }}
      </div>
    </div>
    {{/each}}
  </div>
  {{/if}}

  <!-- Finance Details -->
  <h5 style="page-break-after: always;"></h5>
  <h5 class="sub" style="color: white; background-color:#201864">
    Finance Details
  </h5>

  {{#if (or (eq afeRequestTypeId 6) (eq afeRequestTypeId 7))}}
  <div class="expenditure-container">
    <div class="details">
      <h3 class="finance-sub-title">Total Amount</h3>
      <div class="currency-amount">
        <div class="currency">
          <p class="split-type-label mt-5">Total Netbook Value in {{additionalCurrencyAmount.currency}}</p>
          <div class="amount-text">{{ formattedCurrency additionalCurrencyAmount.amount
            additionalCurrencyAmount.currency }}
          </div>
          {{#if (neq additionalCurrencyAmount.currency currencyType)}}
          <div class="currency">
            <p class="split-type-label mt-5">Total Netbook Value in {{currencyType}}</p>
            <div class="amount-text">{{ formattedCurrency totalAmount currencyType }}
            </div>
          </div>
          {{/if}}
        </div>
      </div>
      <div class="currency-amount">
        <div class="currency">
          <p class="split-type-label mt-5">Total Market Value in {{marketValueAdditionalCurrencyAmount.currency}}</p>
          <div class="amount-text">{{ formattedCurrency marketValueAdditionalCurrencyAmount.amount
            marketValueAdditionalCurrencyAmount.currency }}
          </div>
          {{#if (neq marketValueAdditionalCurrencyAmount.currency marketValueCurrency)}}
          <div class="currency">
            <p class="split-type-label mt-5">Total Netbook Value in {{marketValueCurrency}}</p>
            <div class="amount-text">{{ formattedCurrency marketValue marketValueCurrency }}
            </div>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>
  {{else}}
  <div class="expenditure-container">
    <div class="details mt-5">
      {{#if (or (eq afeRequestTypeId 1) (eq afeRequestTypeId 2))}}
        <h3 class="finance-sub-title">Total Expenditure Amount</h3>
      {{else}}
        <h3 class="finance-sub-title">Total Amount</h3>
      {{/if}}
      <div class="currency-amount">
        <div class="currency">
          <p class="split-type-label mt-5">Total Amount in {{additionalCurrencyAmount.currency}}</p>
          <div class="amount-text">{{ formattedCurrency additionalCurrencyAmount.amount
            additionalCurrencyAmount.currency }}
          </div>
          {{#if (neq additionalCurrencyAmount.currency currencyType)}}
          <div class="currency">
            <p class="split-type-label mt-5">Total Amount in {{currencyType}}</p>
            <div class="amount-text">{{ formattedCurrency totalAmount currencyType }}
            </div>
          </div>
          {{/if}}
        </div>
      </div>
    </div>
  </div>
  {{/if}}

  {{#if (and (eq budgetType 'Budgeted & Unbudgeted') splits.budgetTypeSplit)}}
  <div class="expenditure-container mt-5">
    <div class="details mt-5">
      <h3 class="finance-sub-title">Budget Type Amount Split</h3>
      <div class="amount-split mt-5">
        {{#each splits.budgetTypeSplit}}
        <div class="split-section">
          <p class="split-type-label">{{this.title}}</p>
          <div class="amount-text">{{formattedCurrency this.amount this.currency}}</div>
        </div>
        {{/each}}
      </div>
    </div>
  </div>
  {{/if}}

  {{#if splits.budgetBasedProjectSplit}}
  <div class="expenditure-container mt-5 p-10">
    <h4 class="mt-5">Budget Based Project Component Amount Split</h4>
    {{#each splits.budgetBasedProjectSplit}}
    <div class="mt-10">
      <h5>{{this.title}}</h5>
      <div class="budget-type-section mt-5">
        <div class="budget-type-unit">
          <h5>Budgeted ( Amount in {{this.currency}} )</h5>
          <p class="mt-5">{{formattedCurrency this.budgetedAmount this.currency}}</p>
        </div>
        <div class="budget-type-unit">
          <h5>Unbudgeted ( Amount in {{this.currency}} )</h5>
          <p>{{formattedCurrency this.unbudgetedAmount this.currency}}</p>
        </div>
      </div>
    </div>
    {{/each}}
  </div>
  {{/if}}

  {{#if splits.costCenterSplit}}
  <div class="expenditure-container mt-5 p-10">
    {{#if splits.costCenterSplit.0.title}}
    <h4 class="mt-5">Cost Center Amount Split</h4>
    {{/if}}
    {{#each splits.costCenterSplit}}
      {{#if this.title}}
      <div class="budget-type-unit mt-5">
        <h5>Cost Center Title</h5>
        <p>{{this.title}} - ({{this.code}})</p>
      </div>
      {{/if}}
      {{#if this.section}}
      <div class="budget-type-unit mt-5">
        <h5>Section</h5>
        <p>{{this.section}}</p>
      </div>
      {{/if}}
      {{#if this.analysisCode}}
      <div class="budget-type-unit mt-5">
        <h5>Analysis Code</h5>
        <p>{{this.analysisCode.title}} - ({{this.analysisCode.code}})</p>
      </div>
      {{/if}}
      {{!-- {{#if this.naturalAccount}}
      <div class="budget-type-unit mt-5">
        <h5>Natural Account</h5>
        <p>{{this.naturalAccount.title}} - ({{this.naturalAccount.number}})</p>
      </div>
      {{/if}} --}}
      {{#each this.budgetReferenceNumberSplit}}
      <div class="budget-ref-section mt-10">
        <h4 class="mt-5">Budget Reference Number Amount Split</h4>
        <div class="budget-ref-detail mt-5">
          <div class="budget-type-unit">
            <h5>Budget Reference Number</h5>
            {{#if this.number}}
            <p>{{this.number}}</p>
            {{else}}
            <p>N/A</p>
            {{/if}}
          </div>
          <div class="budget-type-unit">
            <p>Amount</p>
            <p>{{formattedCurrency this.amount this.currency}}</p>
          </div>
        </div>
      </div>
      {{/each}}
    {{/each}}
  </div>
  {{/if}}

  {{#if splits.naturalAccountNumberSplit}}
  <div class="expenditure-container mt-5">
    <div class="details">
      <h3 class="finance-sub-title">Natural Accounts</h3>
      <div class="budget-ref-detail mt-5">
        {{#if splits.naturalAccountNumberSplit}}
        {{#each splits.naturalAccountNumberSplit}}
        <div class="account-row">
          <div class="account-detail">
            <p class="split-type-label">Account Number</p>
            <div class="amount-text">{{this.title}} - ({{this.number}})</div>
          </div>
          <div class="account-detail">
            <p class="split-type-label">Amount</p>
            <p>{{formattedCurrency this.amount this.currency}}</p>
          </div>
        </div>
        {{/each}}
        {{/if}}

        {{!-- {{#if splits.analysisCodeSplit}}
        {{#each splits.analysisCodeSplit}}
        <div>
          <p class="split-type-label">Analysis Code</p>
          <div class="amount-text">{{this.title}} - ({{this.code}})</div>
        </div>
        {{/each}}
        {{/if}} --}}
      </div>
    </div>
  </div>
  {{/if}}

  {{#if splits.chartOfAccounts}}
  <div class="box-container mt-5 p-10">
    <div class="p-5">
      <h4 class="mt-5">Total Chargeable Amount</h4>
      {{#each splits.chartOfAccounts}}
      <div class="chart-acc-section">
        <div class="account-number-section">
          <h4>Chart of Account</h4>
          <p>
            {{this.segments.segment1}}-{{this.segments.segment2}}-{{this.segments.segment3}}-{{this.segments.segment4}}-{{this.segments.segment5}}-{{this.segments.segment6}}-{{this.segments.segment7}}-{{this.segments.segment8}}
          </p>
        </div>
        <div class="amount-sction mt-5">
          <h4>Total Chargeable Amount</h4>
          <p>{{formattedCurrency this.amount this.currency}}</p>
        </div>
      </div>
      {{/each}}
    </div>
  </div>
  {{/if}}

  <!-- Approvers list -->
  <h5 style="page-break-after: always;"></h5>
  <h5 class="sub" style="color: white; background-color:#201864">
    Approver Details
  </h5>
  <table class="table w-100">
    <tr>
      <th>Approver</th>
      <th>Status</th>
      <th>Action At</th>
      <th>Action By</th>
      <th>Comments</th>
    </tr>
    {{#each approversList}}
    <tr>
      <td class="approver-col">
        <h4>
        {{#if (neq this.actionStatus 'Discarded')}}
        {{ this.title }}
        {{/if}}
            {{#if this.userDetail}}
            <p>({{ this.userDetail }})</p>
            {{else}}
            <p>({{ this.approvers }})</p>
            {{/if}}
        </h4>
      </td>
      <td>
        {{ this.actionStatus }}
      </td>
      <td>
        {{ this.actionDate }}
      </td>
      <td>
        {{ this.actionBy }}
      </td>
      <td>
        {{ this.comment }}
      </td>
    </tr>
    {{/each}}
  </table>

  <!--- Reader Information -->
  <h5 class="sub mt-20" style="color: white; background-color:#201864">
    Additional Readers
  </h5>
  {{#if readers }}
  <table class="table w-100">
    <tr>
      <td>
        <div>
          {{ this.readers }}
        </div>
      </td>
    </tr>
  </table>
  {{/if}}

  <!--- Global Procurement Information -->
  {{#if globalProcurementQuesAns }}
  <h5 style="page-break-after: always;"></h5>
  <h5 class="sub" style="color: white; background-color:#201864">
    Global Procurement Information
  </h5>
  <table class="table w-100">
    {{#each globalProcurementQuesAns}}
    <tr>
      <td>
        <div>
          Q. {{ this.question }}
        </div>
        <div>
          -- {{ this.answer }}
        </div>
      </td>
    </tr>
    {{/each}}
  </table>
  {{/if}}

  <!--- Attachments Section -->
  {{#if attachments }}
  <h5 style="page-break-after: always;"></h5>
  <h5 class="sub" style="color: white; background-color:#201864">
    Supporting Documents
  </h5>
  <div class="attachments-container">
    {{#each attachments}}
    <div class="attachment">
      {{#if (contains this.attachment_content_type "image")}}
      <img src="{{this.base64}}" alt="attachment" />
      {{else if (eq this.attachment_content_type
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}}
      <img
        src="data:image/png;base64,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"
        alt="attachment" />
      {{else if (eq this.attachment_content_type "application/pdf")}}
      <img
        src="data:image/png;base64,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"
        alt="attachment" />
      {{else}}
      <img
        src="data:image/png;base64,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"
        alt="attachment" />
      {{/if}}
      <h4>{{this.attachment_name}}</h4>
      <p>{{this.description}}</p>
    </div>
    {{/each}}
  </div>
  {{/if}}


<!--- Approvers Attachments Section -->
  {{#if allApproversAttachments }}
  <h5 style="page-break-after: always;"></h5>
  <h5 class="sub" style="color: white; background-color:#201864">
    Approver Documents
  </h5>
  <div class="attachments-container">
    {{#each allApproversAttachments}}
    <div class="attachment">
      {{#if (contains this.attachment_content_type "image")}}
      <img src="{{this.base64}}" alt="attachment" />
      {{else if (eq this.attachment_content_type
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")}}
      <img
        src="data:image/png;base64,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"
        alt="attachment" />
      {{else if (eq this.attachment_content_type "application/pdf")}}
      <img
        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAYgAAAGICAYAAABbQ3cmAAAAAXNSR0IArs4c6QAAIABJREFUeF7tnQmUFdW1sHfd7nu7gUYGAQGZBwFB5kHj9PKyfHFIhMRfY0yMmkQyaJyQFQ3Gp0nwvRieeUkcX9Rl1DyVuKIxiVNMTGKeUVrEAaM4ICACMskk0N1w61/nIgYU6Kp9q/atqvudtVzJWn322ae+vemv71CnPGF8hMAzIh2KdXVHeb4/UkSG+CJDPN/vIp7XyRdp54kUwAYBawK17drJiMsuC5XW9/2mnO+fVDt9+u9DBTIZAiLiQWEHgbl1dYOKvv9FETleRMaKSA1sIJAkAhpBuP0jiSRVMV17qWpBzBapGVAofK7oed8U3/+YhzDT1b1VtlutIJBElTVKhJdblYIoiSGfP6soconneQMj5MlSEIiNQDmCQBKxlSXTC1edIObm8+OKnneDiEzIdGW5uMwRKFcQSCJzLRH7BVWNIB4XqW+bz//I87xzeCsp9r4iQQwEohAEkoihMBlesioE8XRd3UE5379HREZnuJZcWsYJRCUIJJHxRonw8jIviMb6+n/xi8X7PZEOEXJjKQiYE4hSEEjCvHypTJhpQTQWCpNF5C4RaZPK6rBpCOxCIGpBIAnaqzUCmRXEnELhs57IbO5naK0F+HlaCMQhCCSRlupXZp+ZFIR7W0mKxYdEpL4yWMkKgegJxCUIJBF9rbKyYuYE0VhXN0R8f46I7JeVInEdEHAE4hQEkqDH9kQgU4JwX2VtKBSeFJExlBsCWSMQtyCQRNY6pvzryZQgGuvqbhDf/3r5WFgBAskjYCEIJJG8uldyR5kRRGNt7eF+LvcEN8FVsp3IHScBK0EgiTirmK61MyGIx0Vq2xcKz/gio9KFn91CIDgBS0EgieB1yfLMTAjimXz+6/6O85UYEMgsAWtBIInMtlLgC0u9IJ4Ryfv5/ALxvP6Br5qJEEghgUoIAkmksFEi3HLqBdGYz39JPO8XETJhKQgkkkClBIEkEtkOJptKvSCeLhSeyolMMqFFEghUkEAlBYEkKlj4CqZOtSDcKa2e77/CN5cq2EGkNiNQaUEgCbNSJyZRqgXRWCj8QERmJIYmG4FAjASSIAgkEWOBE7h02gXxLHdNJ7Cr2FIsBJIiCCQRS3kTuWhqBfGkSOfaQmGVJ5JLJFk2BYGICSRJEEgi4uImdLnUCuKZQmGKL3JfQrmyLQhETiBpgkASkZc4cQumVhBzCoXLPZErE0eUDUEgJgJJFASSiKnYCVk2zYL4pSdyWkI4sg0IxE4gqYJAErGXvmIJ0iyIZzyRcRUjR2IIGBNIsiCQhHEzGKVLryDy+cWe5/Ux4kQaCFScQNIFgSQq3iKRbyC1gmgsFNaKSKfIibAgBBJKIA2CQBIJbR7ltlIriDmFQpMnUlBeN2EQSB2BtAgCSaSutfa64dQKorFQ8LNTBq4EAq0TSJMgkETr9UzDDASRhiqxRwiISNoEgSTS37YIIv015AqqhEAaBYEk0t2cCCLd9WP3VUQgrYJAEultUgSR3tqx8yojkGZBIIl0NiuCSGfd2HUVEki7IJBE+poWQaSvZuy4SglkQRBIIl3NiyDSVS92W8UEsiIIJJGeJkYQ6akVO61yAlkSBJJIRzMjiHTUiV1CIJX3QbRWNt/3m3K+f1Lt9Om/b20uP7cngCDsmZMRAioCWXsFsRMCklC1g0kQgjDBTBIIlE8gq4Lg7abyeyOuFRBEXGRZFwIRE8iyIJBExM0S0XIIIiKQLAOBuAlkXRBIIu4OCr8+ggjPjAgIVIRANQgCSVSktfaaFEEkqx7sBgJ7JVAtgkASyflHgCCSUwt2AoF9EqgmQSCJZPxjQBDJqAO7gECrBKpNEB9IwvP+X+20ab9rFRATIieAICJHyoIQiIdANQoCScTTS0FXRRBBSTEPAhUmUK2CQBKVazwEUTn2ZIZAKALVLAgkEapVIpuMICJDyUIQiJdAtQsCScTbX3taHUHYMycjBFQEEMQObKWzm/jgWtVDYYMQRFhizIdAhQggiH+CRxI2TYggbDiTBQJlE0AQuyNEEmW3VKsLIIhWETEBAskggCA+WgckEW9vIoh4+bI6BCIjgCD2jBJJRNZiH1kIQcTHlpUhECkBBLF3nEgi0lb7YDEEEQ9XVoVA5AQQxL6RIonIW04QRPRMWRECsRBAEK1jRRKtMwozA0GEocVcCFSQAIIIBh9JBOMUZBaCCEKJORBIAAEEEbwISCI4q33NRBDRcGQVCMROAEGEQ4wkwvHa02wEUT5DVoCACQEEER4zkgjPbNcIBFEeP6IhYEYAQehQIwkdNxeFIPTsiISAKQEEoceNJHTsEISOG1EQMCeAIMpDjiTC80MQ4ZkRAYGKEEAQ5WNHEuEYIohwvJgNgYoRQBDRoEcSwTkiiOCsmAmBihJAENHhRxLBWCKIYJyYBYGKE0AQ0ZYASbTOE0G0zogZEEgEAQQRfRmQxL6ZIojoe44VIRALAQQRC1aecb0PrAginp5jVQhETgBBRI70gwV5JbFntggivp5jZQhESgBBRIrzI4shiY/yRRDx9hyrQyAyAggiMpR7XQhJ7I4GQcTfc2SAQCQEEEQkGFtdBEn8ExGCaLVdmACBZBCobdtWRnz3u8nYTMZ34fv+Fi+Xm5K/6KJHM36p+7w8BFHN1efaU0UgV1cnI6+4IlV7TvNmeSXBaa5p7l/2XmUEvFxORs2cWWVXXdnLrXZJ8Aqisv1HdgiEIuAE4UTBsCPgi2z1PG9yNb7dhCDs+oxMECibwPAZMyTf0FD2OiwQjkC1vpJAEOH6hNkQqCiBoeefL/Xdu1d0D9WavBolgSCqtdu57lQSGPiVr0j7QYNSufcsbLra3m5CEFnoWq6hagj0njJF9p80qWquN4kXWk2vJBBEEjuQPUFgLwS6Hn64HPipT8GnwgSqRRIIosKNRnoIhCGw30EHyYCzzgoTwtyYCFTD200IIqbmYVkIxEGgdNzGjBkiXmr/6caBpWJrZv2VRGq7rLFQ8CvWFSSGQAUJDJs2Teq6dKngDki9K4EsSwJB0OsQSBmBPiedJJ3Hj0/ZrrO93ay+3YQgst23XF0GCXQaOVL6fv7zGbyydF9SFl9JIIh09yS7r0IC7tC+EZddJrna2iq8+mRfctYkgSCS3W/sDgJ7JND/jDOkw9Ch0EkggSy93YQgEthgbAkCrRHocPDB0v/001ubxs8rRCArryQQRIUaiLQQKIeAO9F12PTpUujYsZxliI2RQBYkgSBibBCWhkCcBLodeaT0PP74OFOwdpkE0v52E4IoswEIh0ClCOTy+dKriHz79pXaAnkDEHCSKEyb1ibA1MRNQRCJKwkbgkBwAvsfeqj0njw5eAAzK0IgP21aKn/XpnLTrsLcSV2RPidp0gh4ngyeOlXa9euXtJ2xn10IIAjjdkAQxsBJl1gCdV27ypDzzuO+iMRWSARBGBcHQRgDJ12iCXQeM0b6nHJKovdYzZtDEMbVRxDGwEmXeAIHnnCCdD3iiMTvsxo3iCCMq44gjIGTLvkEPE96nXiidDn00OTvtcp2iCCMC44gjIGTLh0EnCQ+/Wnpcthh6dhvlewSQRgXGkEYAyddqgh0HjtWek2eLLlCIVX7zupmEYRxZRGEMXDSpY5Afffu0nvKFGnXt2/q9p61DSMI44oiCGPgpEsnAc+TzqNGSfdjjpFC587pvIYM7BpBGBcRQRgDJ126CXhe6Xhwd+d1+4EDxaupSff1pGz3CMK4YAjCGDjpMkOgpq5O2g8aJA2DBkn9AQdIfdeuUtvQkJnrS+KFIAjjqiAIY+CkyzQBJ42a+npxT6vz8vlMX2slLm7Em2+m8lijVG7aFRhBVKLNyQkBCGgITGhuTuXv2lRuGkFoWpQYCECgUgQQhDF5XkEYAycdBCCgJoAg1Oh0gQhCx40oCEDAngCCMGaOIIyBkw4CEFATQBBqdLpABKHjRhQEIGBPAEEYM0cQxsBJBwEIqAkgCDU6XSCC0HEjCgIQsCeAIIyZIwhj4KSDAATUBBCEGp0uEEHouBEFAQjYE0AQxswRhDFw0kEAAmoCCEKNTheIIHTciIIABOwJIAhj5gjCGDjpIAABNQEEoUanC0QQOm5EQQAC9gQQhDFzBGEMnHQQgICaAIJQo9MFIggdN6IgAAF7AgjCmDmCMAZOOghAQE0AQajR6QIRhI4bURCAgD0BBGHMHEEYAycdBCCgJoAg1Oh0gQhCx40oCEDAngCCMGaOIIyBkw4CEFATQBBqdLpABKHjRhQEIGBPAEEYM0cQxsBJBwEIqAkgCDU6XSCC0HEjCgIQsCeAIIyZIwhj4KSDAATUBBCEGp0uEEHouBEFAQjYE0AQxswRhDFw0kEAAmoCCEKNTheIIHTciIIABOwJIAhj5gjCGDjpIAABNQEEoUanC0QQOm5EQQAC9gQQhDFzBGEMnHQQgICaAIJQo9MFIggdN6IgAAF7AgjCmDmCMAZOOghAQE0AQajR6QIRhI4bURCAgD0BBGHMHEEYAycdBCCgJoAg1Oh0gQhCx40oCEDAngCCMGaOIIyBkw4CEFATQBBqdLpABKHjRhQEIGBPAEEYM0cQxsBJBwEIqAkgCDU6XSCC0HEjCgIQsCeAIIyZIwhj4KSDAATUBBCEGp0uEEHouBEFAQjYE0AQxswRhDFw0kEAAmoCCEKNTheIIHTciIIABOwJIAhj5gjCGDjpIAABNQEEoUanC0QQOm5EQQAC9gQQhDFzBGEMnHQQgICaAIJQo9MFIggdN6IgAAF7AgjCmDmCMAZOOghAQE0AQajR6QIRhI4bURCAgD0BBGHMHEEYAycdBCCgJoAg1Oh0gQhCx40oCEDAngCCMGaOIIyBkw4CEFATQBBqdLpABKHjRhQEIGBPAEEYM0cQxsBJBwEIqAkgCDU6XSCC0HEjCgIQsCeAIIyZIwhj4KSDAATUBBCEGp0uEEHouBEFAQjYE0AQxswRhDFw0kEAAmoCCEKNTheIIHTciIIABOwJIAhj5gjCGDjpIAABNQEEoUanC0QQOm5EQQAC9gQQhDFzBGEMnHQQgICaAIJQo9MFIggdN6IgAAF7AgjCmDmCMAZOOghAQE0AQajR6QIRhI4bURCAgD0BBGHMHEEYAycdBCCgJoAg1Oh0gQhCx40oCEDAngCCMGaOIIyBkw4CEFATQBBqdLpABKHjRhQEIGBPAEEYM0cQxsBJBwEIqAkgCDU6XSCC0HEjCgIQsCeAIIyZIwhj4KSDAATUBBCEGp0uEEHouBEFAQjYE0AQxswRhDFw0kEAAmoCCEKNTheIIHTciIIABOwJIAhj5gjCGDjpIAABNQEEoUanC0QQOm5EQQAC9gQQhDFzBGEMnHQQgICaAIJQo9MFIggdN6IgAAF7AgjCmDmCMAZOOghAQE0AQajR6QIRhI4bURCAgD0BBGHMHEEYAycdBCCgJoAg1Oh0gQhCx40oCAQlkKuvl9pu3aTQs6fk2rYV8TypaWiQ7Rs3il8syrZVq6Rl5crS/zL2TQBBGHcIgjAGTrpME/AKBWk49FBpf/TR0jBpktQPGSKFPn0CXbPf1CRbX3tNti5YIFteeknea2yUTXPmyPZ16wLFV8MkBGFcZQRhDJx0oQn0/uEPJdfQEDpu7ezZsvEvfwkdpwloO2aMdJs6VTpOmSK1nTtrlthjjN/cLPO6dpXi1q2RrZnmhRCEcfUQhDFw0oUmMHrpUqnt2jV03JILL5SV118fOi5MQJuDD5beV18t+x1zTJiwwHM3P/us/OOwwwLPz/pEBGFcYQRhDJx0oQkkUhCeJz0uvVR6fuc74uXzoa8paMDKG26QJRdcEHR65uchCOMSIwhj4KQLTSBpgvDq6mTgnXdKxxNPDH0tYQMWnnGGrL377rBhmZ2PIIxLiyCMgZMuNIEkCcK9Whg4e7Z0PP740NehCXhxyBBpWrRIE5rJGARhXFYEYQycdKEJJEkQvWbOlO4XXxz6GjQB7quvz/furQnNbAyCMC4tgjAGTrrQBJIiiIbDDpOhf/qTSC4X+ho0Ae/+5jfyximnaEIzG4MgjEuLIIyBky40gUQIwvNk6J//XLrHwWosvfRSWXHNNVbpUpEHQRiXCUEYAyddaAJJEIS78W3Io4+G3ns5Aa98/OOy6ckny1kic7EIwrikCMIYOOlCE0iCIAbccYd0Nny7x29p2XGD3JYtoXllOQBBGFcXQRgDJ11oApUWhPta65hly1R3c4e+2PcD3DEbLx9xhDY8s3EIwri0CMIYOOlCE6i0IPb7xCfkoAcfDL3vXQOaFi6UdQ88IJufe05a3nlHtm/YUDq4L9eundT16yf1gwdL27FjxR3Z4Q73W3nddbLkoovKypnFYARhXFUEYQycdKEJVFoQPWfMkJ6XXx563y7AHcC3ZNo0WXXLLSLFYqtr5Nq0KR305ySyed68VudX2wQEYVxxBGEMnHShCVRaEO7GuE6TJ4fetwtwrwLcqwFGNAQQRDQcA6+CIAKjYmKFCFRaEMOfe07aDBsW+urdq4AXBgwQf9u20LEE7JkAgjDuDARhDJx0oQlUWhBj3nlHajp2DL3vVTffLIvPOSd0HAF7J4AgjLsDQRgDJ11oApUUhPvAeOz69aH37AIWn3eerLrpJlUsQbyCSEQPIIhElIFN7INAJQVRu//+MnrZMlV9XpsyRdY/9JAqliAEkYgeQBCJKAObSKgg3HOkR775pqo+C445Rjb+9a+qWIIQRCJ6AEEkogxsIqGCqOvbVw559VVVfV4+6ih57+mnVbEEIYhE9ACCSEQZ2ASCoAcCEOBD6gCQopyCIKKkyVpxEKjkZxC8goijovo1EYSenSoSQaiwEWRIAEEYwk54KgRhXCAEET/wfLdu0nDEEdJm+HCpGzhQ6vr0EXekQum79Z4n29evl+LWrdL81lvS9PrrsmX+/NKHm+6JYmkZ7kC7hkmTSucJ1btrHDhQajt1Kp035H5WfO892b5xo2x97TXZ+uqrsunvfxd3IJ07iqK1gSBaI1Q9P0cQxrWudkG4X2Q1HTqEor5txQppbuWrj4U+faTL6adLp5NPVt2FK75fEsWa//1fWXPnnYmUhbtHoNNnPiP7f+ELJQE66YUZxc2bZf0jj8jae+6RdQ8+uFdZxC2IfI8e4v7b0yj06CGDfv3rMJf1wdxFZ58tm+fPDxy75fnnxd++PfD8apyIIIyrXu2CcP/4O55wQijqK370I1l62WV7jGk3bpz0uPTSHWtG9GhK92wAd9jb8pkzEyGKXEODHHDeedL9vPOkplOnUOz2NtkdS7Hq5z+XlddfL9vWrNltWtyCOPCKK0o1q/SY161b6dUkY+8EEIRxdyCIaAThjmzudfXV6kPdgpS9uGmTLJk+XVbfemuQ6bHM6XzqqdJn1iyp7do1lvXdL8gVs2bJip/85INXFAgiFtSpXBRBGJcNQZQnCK+2VrpffLH0uOSS0G+xaEu99t57ZdFXv2r6tLGa9u2l7/XXmz1VbctLL4l7i+a9uXMFQWg7JXtxCMK4pghCL4j6QYOk/623SrtJk4yrJqUPsV8/6aTSg2fiHvkDDpDBDzwgbUePjjvVbuu7D7AXn3uu9LrqKtUrliUXXlh6y6q1wVtMrRFKzs8RhHEtEIROEBuffFIG3HZb6A+4oyyvk8SrJ5wgfnNzlMvutpb78Hbo449LXf/+seVodWH3oB3F5zkIolWyqZuAIIxLhiDCC6LpjTd2/MJU/NKKuryrb7tNFn3ta1EvW1rPva005I9/lLajRsWyftyLIoi4CduvjyCMmSOI8IIwLlGr6RaefrqsnT271XlhJwy6917p+OlPhw1LzHwEkZhSRLYRBBEZymALIYj0C2Lb6tUyf/Ro2bZqVbCiB5jV9eyzpe+11waYmdwpCCK5tdHuDEFoySnjEET6BeFK7x5M4x5QE8Vw5w8NnzdPcu3aRbFcxdZAEBVDH1tiBBEb2j0vjCCyIQh3M938Qw6RJuWzC3btjgF33imdTz7ZuBOjT4cgomda6RURhHEFEET8gnA3uG1duHDHHcK+L+6IivohQ8Q9rSzKsfLGG2XJ+eeXtWS78eNl2N/+VjojKsrR/Pbb0rRwoTiRuXtH3PlU7pgTL5+PMs1uayGI2NBWbGEEYYweQcQgCN+XjU88Ie/ed59seOyx0gF1TgwfHm2GDZPOn/ucdP3a16S2c+eyK+/uQn6+f//SwXjaMfCee6TTlCna8N3i3KuZd372M1l3//3iBPHh4eTghNTh2GNL5zkVeveOJO/ORYIKwrHf23lchV69ZMhjj6n29cbnPiebn38+cGzT4sUi7iu9jL0SQBDGzYEgohOE++t49S9+ISuuuUbcV2GDjtouXaTvz34mnT772aAhe523aOrU0h40wx0weMgrr4hXU6MJ/yDGcVj2/e+XOLj/H2S4nB1PPFEOvPLK0qurKEZQQewrF8+DiKIS0a2BIKJjGWglBBGBIIpFccdfLLvyStn6+uuBuH/0z2lPel99dekQvHKGOxX19c98RrXEgd/7nvT49rdVsTuD3JHer3/2s+pnMbu3n7p85SvS87LLSm9DlTMQRDn0khmLIIzrgiDKE8TWBQvkza9+Vd6bM6f8ynmeDLz77rLe4ilu2SLP9ewp7ijtsGPESy+JOz5EO9wd3QuOO042uc8wyhzu85n+t9wiHY47Tr0SglCjS2wggjAuDYLQC2LNXXfJ4m98I9JD89wvxhHz55f1mcRrkyfL+ocfDtVJbUaMkOFz54aK+fDkJRdcICtvuKGsNXYL9rzSQYjubSfN214IIrpSJGUlBGFcCQShE8T6Rx+V1048cY8fPpdbQvdsAneAnHYsv+oqefvKK0OFd58+XXr94AehYnadvPEvf5EFn/xkLDziPs11XxfNZxDqloglEEHEgnXviyIInSDcq4c3zzwzlmrV7LefjHz1VfXDeNw3p9whfmGGO621g/sFrxm+L/+YNCnUN3bCpEEQYWhley6CMK4vgkieIFwL9P3pT0tff9UMd7+F+xwi8MjlZMzy5Tueka0Y7rGhpVdTMQ0EERPYFC6LIIyLhiCSKQj317z7q147nCA+/OjOva3lvlY64oUXtKlk4RlnyNq771bHtxaIIFojVD0/RxDGtUYQyRSEV1cno99+u3Tktma8fOSRgb9Z5e4/GPSrX2nSlL4t9Vzv3uLuFo9rIIi4yKZvXQRhXDMEkUxBuDYY8uij0v7oo1UdEeavevdNoV4zZ6ryvHv//eLuGI5zIIg46aZrbQRhXC8EkVxBlPM5xFvTp8s7P/1poG7qd8MN0uXLXw4098OT3v7ud2X51VerYoMGIYigpLI/D0EY1xhBJFcQB5x7rvT+r/9SdcTyH/5Q3r788kCxg++/X31Dmvtw2n1IHedAEHHSTdfaCMK4XggiuYLo8G//JoN/+1tVR6y6+WZZfM45gWLd6a3tJkwINPfDk14YMGCPB/GpFttLEIKIkma610IQxvVDEMkVRLuJE2XYE0+oOsJ9q8h9DhFkjHztNXEH9YUd7iC+uQ0NYcNCz0cQoZFlNgBBGJcWQSRXEG0OPrj0ZDfNePfXv5Y3Pv/5QKHu21LuRNmwI/T9FmETvD8fQSjBZTAMQRgXFUEkVxDur3r3171mrPv970unqgYZY1avVn2dtmnRInkxoqO597VPBBGkitUxB0EY1xlBJFcQ7q9699e9ZoS5u3nchg3i7rsIO7a8+KK8NH582LDQ8xFEaGSZDUAQxqVFEMkVhHua2cgQDx7atXXC3J8wbvNm1Wmpm597rnQGU9wDQcRNOD3rIwjjWiGI5AqizfDhMvzZZ1UdseaXv5Q3A97bMHb9+tJzssMO9yjV+SNGhA0LPR9BhEaW2QAEYVxaBJFcQTQcdpgM/fOfVR2x6uc/l8XnnhsodsyqVeJOkA07mpctkxf69w8bFno+ggiNLLMBCMK4tAgiuYLocOyxMvg3v1F1hLuL2t1NHWRofwFvX79e5pX5WNA498cDg4LQTdccBGFcLwSRXEEccP75pedUa0aYIzDUjxr1fXm2c2fV403DXJNWYAgiDOV0zEUQxnVCEMkVhHsm8/5f/KKqIxZ+6Uuy9p57AsUOffxxafjYxwLN/fCkf0yYIJvLOCo8SFIEEYRSdcxBEMZ1RhDJFcTBc+ZI21GjVB3xytFHy6anngoU6476dkd+a8bCL3xB1t57ryY0cAyCCIwq8xMRhHGJEUQyBVHbubOMWrJEvHxe1RHP9+snLcuXB4rt85OfSLevfz3Q3A9PWjZzpiz73vdUsUGDEERQUtmfhyCMa4wgkimILmedJf1uvFHVDds3bJB5BxwgUiwGii/n1Fj3KsW9WolzIIg46aZrbQRhXC8EkUxBHPTww7Lfxz+u6oYNjz0mr55wQuDYcr4t5ST0XJ8+sm3VqsD5wk5EEGGJZXc+gjCuLYJIniDqBw2S4S+8oLq72bVP2Ld9yjnzyeVbdPbZsvr222PrXAQRG9rULYwgjEuGIJIniIH33COdpkxRd8Krn/qUbPjDH0LFj1q0SPI9eoSK2Tm5dCaTe56E76viWwtCEK0Rqp6fIwjjWiMInSDeve8+eePUUyOvVsOhh+64e9rzVGv7TU3yXK9e4j6HCDMGzp4tnSZPDhOy21x3cqw7QTbq4R5kNOSxx1RHgXAfRNTVqPx6CMK4BghCJwhXppXXXSdvXXKJ+M3NkVQt19AgB//f/0n90KHq9cIc871rkm7f/Kb0+fGP1Xndq4h/fOxjkbFwG3Ef1Pf57/9WycHFIwh1ORMbiCCMS4Mg9IJwpXqvsbH05LYm5amrO8vtvs7q/orvePzxZXWAO6DPHdQXdtT16yeHLFgQNmy3+U6YSy66qKw1XHBd377Se9Ys9b0ZOzeAIMouReIWQBDGJUEQ5QnClcu9gnCH4y276irZtnp16Aq6g/IG3H67dDjuuNCxuwZaLbpPAAANj0lEQVSU3l7q3VvcGUmaMXzuXGlT5umsy6+6St5290UoPo+o7dpVenz729Jt6lTV8yk+fM0IQtMFyY5BEMb1QRDlC2Jnydwv5neuvVZW33yzuJNOg4wOxx9femvH/QVf7nDfJHLfKNKO7hdeKL3+8z+14R/ErX/4YVlywQXS9OabgdaqP+gg6XLmmdJ16lTVk+32lgRBBMKfqkkIwrhcCCI6Qewsnb9tm2x68klZ/+ijsvnZZ2XrggWlv+qLTU1S06GD1A8ZIu0PP1w6n3JK2X+xf9AuxaLMHzNGtr7yirqD3BPsRi1cGMlf747B+gcflHW//a28N2+eNC9dWmLgrj/ftau0GTZM3AfQ+x1zjLQdPVq9530FIohYsFZ0UQRhjB9BRC8I4xKW0q174AF5/eSTy07d73/+R7qccUbZ6yRhAQSRhCpEuwcEES3PVldDEOkXhPtr/eXDDxf3CNByh3ura8SLL4pXKJS7VMXjEUTFSxD5BhBE5Ej3vSCCSL8gVvz4x7L0kksi65xyDu+LbBMRLIQgIoCYsCUQhHFBEES6BdG0eLG8NHasFDdtiqxz3Leqhs+bJ4VevSJbsxILIYhKUI83J4KIl+9HVkcQ6RWE+1rrgmOPLX0gHvXo8MlP7njcqfKO7qj3o1kPQWioJTsGQRjXB0GkVBC+X7pBL+hT4zRt1XPGDOl5+eWa0Ehj3Gcrufr60HeYI4hIy5CIxRCEcRkQhE4QLStWiLuxy6upMa7YjnRLZ8yQFbNmxZvb86T/rbfK/qedFm+efay+5o47ZPG3viWDH3hA2h91VKh9IIhQuFIxGUEYlwlB6ASx5q67SkdaDLzjDqnp1Mmsau4bS29NmyYrlQ8TCrtRJ8C+114rXb785bChZc1390y4X/A7jw0Z8oc/IIiyiGYjGEEY1xFB6AXx5plnSqF3b+l3002y3yc+EXvltq1ZI2+cdppsdKe9Wg7Pk+4XXywH/vu/qx+BGma7a3/1K3lr+vTdHpmKIMIQzO5cBGFcWwRRniBK5fI82f/UU6XnlVeWDpqLfBSLsvq222Tp5ZfH+uS21vbdbtIk6XfdddLmkENam6r6uTv4cOl3viMb//rXj8QjCBXSzAUhCOOSIogIBPF+zdzNZV1OP126feMbkfwSLR1X8dBDsvw//kPemzvXuDP2ki6XK12je0XhzlAqexSLJSGsuOYaWf/II3tdbtB990n7ww4Llc59TrPqlltCxXx4snuFOLyxUbWGe+xrYuqmuoLkBSEI45ogiOgEsWvp2o0bJx0nTxb3dVF3QqpXWxuossWtW0t3RK/73e/EfUDrPgxP5PA8aX/kkaXzpPb713+VuoEDA2/TfT1309NPy4Y//lHW3nWXuHs5GBAIQgBBBKEU4RwEEY8gdi1Rrl07aTtypNQNGCDu+c/uRjT3nzsSe/u6dbJt7drSf1vmz5fNzz8vfktLhBW2WarQs6fUDxsm9YMHS75nT3HX7L6aKtu3y/aNG0sH9bnTXbe+8YZsffllKW7ZYrMxsmSKAIIwLieCiF8QxiUlHQQySwBBGJcWQSAI45YjHQTUBBCEGp0uEEEgCF3nEAUBewIIwpg5gkAQxi1HOgioCSAINTpdIIJAELrOIQoC9gQQhDFzBIEgjFuOdBBQE0AQanS6QASBIHSdQxQE7AkgCGPmCAJBGLcc6SCgJoAg1Oh0gQgCQeg6hygI2BNAEMbMEQSCMG450kFATQBBqNHpAhEEgtB1DlEQsCeAIIyZIwgEYdxypIOAmgCCUKPTBSIIBKHrHKIgYE8AQRgzRxAIwrjlSAcBNQEEoUanC0QQCELXOURBwJ4AgjBmjiAQhHHLkQ4CagIIQo1OF4ggEISuc4iCgD0BBGHMHEEgCOOWIx0E1AQQhBqdLhBBIAhd5xAFAXsCCMKYebULovesWdL+8MNDU1//yCPy9hVXhI4jAAIQ0BNAEHp2qshqF4QKGkEQgEBFCCAIY+wIwhg46SAAATUBBKFGpwtEEDpuREEAAvYEEIQxcwRhDJx0EICAmgCCUKPTBSIIHTeiIAABewIIwpg5gjAGTjoIQEBNAEGo0ekCEYSOG1EQgIA9AQRhzBxBGAMnHQQgoCaAINTodIEIQseNKAhAwJ4AgjBmjiCMgZMOAhBQE0AQanS6QASh40YUBCBgTwBBGDNHEMbASQcBCKgJIAg1Ol0ggtBxIwoCELAngCCMmSMIY+CkgwAE1AQQhBqdLhBB6LgRBQEI2BNAEMbMEYQxcNJBAAJqAghCjU4XiCB03IiCAATsCSAIY+YIwhg46SAAATUBBKFGpwtEEDpuREEAAvYEEIQxcwRhDJx0EICAmgCCUKPTBSIIHTeiIAABewIIwpg5gjAGTjoIQEBNAEGo0ekCEYSOG1EQgIA9AQRhzBxBGAMnHQQgoCaAINTodIEIQseNKAhAwJ4AgjBmjiCMgZMOAhBQE0AQanS6QASh40YUBCBgTwBBGDNHEMbASQcBCKgJIAg1Ol0ggtBxIwoCELAngCCMmSMIY+CkgwAE1AQQhBqdLhBB6LgRBQEI2BNAEMbMEYQxcNJBAAJqAghCjU4XiCB03IiCAATsCSAIY+YIwhg46SAAATUBBKFGpwtEEDpuREEAAvYEEIQxcwRhDJx0EICAmgCCUKPTBSIIHTeiIAABewIIwpg5gjAGTjoIQEBNAEGo0ekCEYSOG1EQgIA9AQRhzBxBGAMnHQQgoCaAINTodIEIQseNKAhAwJ4AgjBmjiCMgZMOAhBQE0AQanS6QASh40YUBCBgTwBBGDNHEMbASQcBCKgJIAg1Ol0ggtBxIwoCELAngCCMmSMIY+CkgwAE1AQQhBqdLhBB6LgRBQEI2BNAEMbMEYQxcNJBAAJqAghCjU4XiCB03IiCAATsCSAIY+YIwhg46SAAATUBBKFGpwtEEDpuREEAAvYEEIQxcwRhDJx0EICAmgCCUKPTBSIIHTeiIAABewIIwpg5gjAGTjoIQEBNAEGo0ekCEYSOG1EQgIA9AQRhzBxBGAMnHQQgoCaAINTodIEIQseNKAhAwJ4AgjBmjiCMgZMOAhBQE0AQanS6QASh40YUBCBgTwBBGDNHEMbASQcBCKgJIAg1Ol3gnEKhyRMp6KKJggAEIGBHAEHYsS5laiwU1ohIZ+O0pIMABCAQmgCCCI2svICn8/lFOc/rW94qREMAAhCIl4AvUpzY3FwTb5Z4VvfiWTb+VRsLhUYRGR9/JjJAAAIQ0BPwfX/lxJaWA/QrVC4ytYKYUyj80hM5rXLoyAwBCECgdQK+5z0xsanpqNZnJm9GmgVxuSdyZfKQsiMIQAAC/yTge97NE5uazk4jk9QKorFQmCwi96cROnuGAASqh4DneeeNb2r6WRqvOLWCmCfScVuhsFpEUvnhTxqbhT1DAAIqAodMaG6er4qscFBqBeG4zSkU5noiYyvMkPQQgAAE9kjAfUA9oaWluyfipxFR2gXxfU/ksjSCZ88QgED2CXgit41vbj4rrVeaakE8VVc3OOf7CzyRVF9HWpuHfUMAAvsmkMvlPjFu69Y/pZVT6n+xPl0oPJUTmZTWArBvCEAgmwS8YvHthdu29T1FZHtarzD1gmjM508Xz7s9rQVg3xCAQGYJXDahuXlmmq8u9YKYLVLTN59/Jed5g9JcCPYOAQhkh4Avsj7f3NxvjMi6NF9V6gXh4M/J56d6nndTmgvB3iEAgewQ8EV+MLG5+btpv6JMCMK9iuhfKDwjIqPTXhD2DwEIpJuAL7I819w8bLzI+nRfSYa+/dOYz0/0Pe/vnkgu7UVh/xCAQHoJeCKnjG9u/lV6r+CfO8/EK4idl9OYz18rnndOFgrDNUAAAikkUCw+MGHbNncMUCZGpgTxmkjdu4XCk9xdnYne5CIgkC4Cvv9WsaVlzCQR9zCzTIxMCcJV5P2b5xo9kQ6ZqBAXAQEIJJ6AL9JU4/v/Mq6l5anEbzbEBjMnCHftc2prj/ZyuYdFpD4EC6ZCAAIQCE3APTFORD4/sbl5dujghAdkUhCO+dOFwmdyIu6DIk57TXgTsj0IpJqA5507oanpulRfw142n1lBuOt9/5kRd4lImywWj2uCAAQqSmC75/vfGt/SckNFdxFj8kwL4v23m46SXO43nkjHGDmyNAQgUF0EtvoiX5jY3PzrLF925gXhivdsfX3fbcXi3Z7IoVkuJtcGAQjET8Dz/UUicur4lpan489W2QxVIQiHuPQV2Hz+avG8c7mZrrJNR3YIpJWAJ/LLbc3N3zxUZENaryHMvqtGEDuhPJPPj93ueddzRHiYNmEuBKqbQNH3X8/lcudNaGp6qJpIVJ0gXHHd2U398vkv5UQu9T1vcDUVnGuFAARCECgWl/o1NbM6NTXdOFikKURkJqZWpSB2Vu79Q/5OKXreNz3fP5wn02Wip7kICJRNwBd5Vnz/pk4tLb+oRjHsBFjVgti1ixrr6wf4xeIXfZHjcyLjRKS27C5jAQhAIBUE3M1unsgLvoi7wfbOic3NL6Vi4zFvEkHsAfDfRNrX19UdVfT9kSIyxBcZmvP9ruJ5HXyRBk+kLua6sDwEIBAxAV+kxRPZJL7/rnjeWl/k1ZzIK9tF5ktz81+zdIZSVOj+P5VWtJdKwQeqAAAAAElFTkSuQmCC"
        alt="attachment" />
      {{else}}
      <img
        src="data:image/png;base64,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"
        alt="attachment" />
      {{/if}}
      <h4>{{this.attachment_name}}</h4>
      <p>{{this.description}}</p>
    </div>
    {{/each}}
  </div>
  {{/if}}

  <!--- History Section -->
  <h5 style="page-break-after: always;"></h5>
  <h5 class="sub" style="color: white; background-color:#201864">
    Approval History
  </h5>
  <div class="history-container">
    {{#each history}}
    <div class="history-item">
      <h3 class="history-title">&#9675; {{formattedDate this.actionDate}}</h3>
      <p class="history-author">{{this.actionPerformed}} - by {{this.createdBy}}</p>
      <p class="history-comment">{{this.actionComments}}</p>
    </div>
    {{/each}}
  </div>
</div>
</b6ody>

</html>