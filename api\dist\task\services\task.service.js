"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskService = void 0;
const common_1 = require("@nestjs/common");
const parallel_identifier_repository_1 = require("../../afe-config/repositories/parallel-identifier.repository");
const repositories_1 = require("../../afe-proposal/repositories");
const config_service_1 = require("../../config/config.service");
const services_1 = require("../../finance/services");
const repositories_2 = require("../../notification/repositories");
const repositories_3 = require("../../queue/repositories");
const clients_1 = require("../../shared/clients");
const constants_1 = require("../../shared/constants");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const mappings_1 = require("../../shared/mappings");
const services_2 = require("../../shared/services");
const services_3 = require("../../workflow/services");
const dtos_1 = require("../dtos");
const task_action_with_email_template_mapping_1 = require("../mappings/task-action-with-email-template.mapping");
let TaskService = class TaskService {
    constructor(taskApiClient, afeProposalRepository, afeProposalApproverRepository, afeProposalAmountSplitRepository, workflowService, configService, adminApiClient, afeProposalLimitDeductionRepository, databaseHelper, sharedAttachmentService, historyApiClient, notificationRepository, mSGraphApiClient, queueLogRepository, sharedNotificationService, financeAdminService, parallelIdentifierRepository) {
        this.taskApiClient = taskApiClient;
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.afeProposalAmountSplitRepository = afeProposalAmountSplitRepository;
        this.workflowService = workflowService;
        this.configService = configService;
        this.adminApiClient = adminApiClient;
        this.afeProposalLimitDeductionRepository = afeProposalLimitDeductionRepository;
        this.databaseHelper = databaseHelper;
        this.sharedAttachmentService = sharedAttachmentService;
        this.historyApiClient = historyApiClient;
        this.notificationRepository = notificationRepository;
        this.mSGraphApiClient = mSGraphApiClient;
        this.queueLogRepository = queueLogRepository;
        this.sharedNotificationService = sharedNotificationService;
        this.financeAdminService = financeAdminService;
        this.parallelIdentifierRepository = parallelIdentifierRepository;
    }
    performActionOnAfeAproposalTask(actionType, performActionOnAfeProposalRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const { id, idType, attachments, comments, delegatee, assignedToAfeSubmitter, taskId } = performActionOnAfeProposalRequestDto;
            let afeProposalId;
            let task;
            if (idType === enums_1.APPROVAL_ACTION_ID_TYPE.TASK) {
                task = yield this.taskApiClient.getTaskById(id);
                if (!task || task.task_status !== 'Not Started') {
                    throw new exceptions_1.HttpException(`Task doesn't exist with this id.`, enums_1.HttpStatus.BAD_REQUEST);
                }
                afeProposalId = task.additional_info.proposal_id;
            }
            else {
                afeProposalId = id;
            }
            if (!delegatee &&
                (actionType === enums_1.TASK_ACTION.DELEGATE || actionType === enums_1.TASK_ACTION.REASSIGNE)) {
                throw new exceptions_1.HttpException(`Delegatee details are missing.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (actionType === enums_1.TASK_ACTION.MORE_DETAIL && !delegatee && !assignedToAfeSubmitter) {
                throw new exceptions_1.HttpException(`Either delegatee or afe submitter is required.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const approvers = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
            const { steps: latestWorkflowSteps, masterSettingWorkflows } = yield this.getLatestWorkflowApproversList(afeProposalId);
            const afeDetails = yield this.afeProposalRepository.getAfeProposalById(afeProposalId);
            const alreadyTraverseApproverIds = new Set();
            const updateUsersDetails = latestWorkflowSteps
                .map(step => {
                var _a;
                const approver = approvers.find(a => (step === null || step === void 0 ? void 0 : step.stepId) &&
                    a.workflowMasterStepsId === (step === null || step === void 0 ? void 0 : step.stepId) &&
                    !alreadyTraverseApproverIds.has(a.id));
                if (!approver) {
                    return null;
                }
                alreadyTraverseApproverIds.add(approver.id);
                return {
                    id: approver.id,
                    user: step.approvers,
                    assignedTo: (step.associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER ||
                        step.associateType === associated_type_enum_1.ASSOCIATED_TYPE.USER) &&
                        ((_a = step === null || step === void 0 ? void 0 : step.approvers) === null || _a === void 0 ? void 0 : _a.length)
                        ? step.approvers[step.approvers.length - 1].loginId
                        : null,
                };
            })
                .filter(step => step !== null);
            yield this.afeProposalApproverRepository.updateUserDetailsByApproverIds(updateUsersDetails, currentContext);
            if (attachments === null || attachments === void 0 ? void 0 : attachments.length) {
                this.sharedAttachmentService.validateAttachment(attachments);
            }
            yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const updatedApprovers = yield this.addNewWorkflowSteps(approvers, latestWorkflowSteps, currentContext);
                yield this.updateAfeProposalLimitDeduction(afeProposalId, masterSettingWorkflows, currentContext);
                let userCurrentStep = null;
                if (taskId) {
                    const { assigned_to, is_group_assignment, entity_id, business_entity_id } = task;
                    if (is_group_assignment) {
                        const isUserInGroup = yield this.adminApiClient.hasUserRole(user.username, assigned_to, business_entity_id);
                        if (!isUserInGroup) {
                            throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                        }
                    }
                    else if ((assigned_to === null || assigned_to === void 0 ? void 0 : assigned_to.toLowerCase()) !== user.username.toLowerCase()) {
                        throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                    }
                    userCurrentStep = updatedApprovers.find(approver => approver.id === entity_id);
                    if (!userCurrentStep || (userCurrentStep === null || userCurrentStep === void 0 ? void 0 : userCurrentStep.actionStatus) !== enums_1.APPROVER_STATUS.IN_PROGRESS) {
                        throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                    }
                }
                else {
                    const currentSteps = yield this.getCurrentTaskSteps(user.username, updatedApprovers);
                    if (!currentSteps.length) {
                        throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                    }
                    userCurrentStep = currentSteps[currentSteps.length - 1];
                }
                const { otherInfo } = userCurrentStep;
                if ((actionType === enums_1.TASK_ACTION.MORE_DETAIL_SUBMITTED &&
                    (otherInfo === null || otherInfo === void 0 ? void 0 : otherInfo.approvalType) !== enums_1.APPROVAL_TYPE.MORE_DETAIL) ||
                    (actionType === enums_1.TASK_ACTION.DISCARD &&
                        (otherInfo === null || otherInfo === void 0 ? void 0 : otherInfo.approvalType) !== enums_1.APPROVAL_TYPE.RESUBMISSION)) {
                    throw new exceptions_1.HttpException(`Can't perform this action on this task.`, enums_1.HttpStatus.BAD_REQUEST);
                }
                yield this.performApprovalAction(actionType, afeDetails, userCurrentStep, updatedApprovers, currentContext, task, comments, delegatee, assignedToAfeSubmitter);
                if (attachments === null || attachments === void 0 ? void 0 : attachments.length) {
                    yield this.sharedAttachmentService.addBulkAttachment(attachments, userCurrentStep.id, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_ACTION, constants_1.ATTACHMENT_REL_PATH.AFE_ACTION, user.username, afeProposalId);
                }
            }));
            return { message: 'Action has been executed successfully.' };
        });
    }
    createStepUniqueKey(associatedLevel, associateRole, associateType, associatedColumn) {
        return `associatedLevel:${associatedLevel || ''}#associateRole:${associateRole || ''}#associateType:${associateType || ''}#associatedColumn:${associatedColumn || ''}`;
    }
    addNewWorkflowSteps(approvers, latestWorkflowSteps, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            let { assignedLevel, associatedColumn, assignedTo, assginedType, afeProposalId, approvalSequence: lastApproverSequence, } = approvers[approvers.length - 1];
            const lastStepkey = this.createStepUniqueKey(assignedLevel, assignedTo, assginedType, associatedColumn);
            let newStepsIndex = null;
            for (let i = 0; i < latestWorkflowSteps.length; i++) {
                const { associateLevel, associatedColumn, associateRole, associateType } = latestWorkflowSteps[i];
                const stepKey = this.createStepUniqueKey(associateLevel, associateRole, associateType, associatedColumn);
                if (lastStepkey === stepKey) {
                    newStepsIndex = i + 1;
                }
            }
            const currentStepsSet = new Set();
            for (let i = 0; i < approvers.length; i++) {
                const { assignedLevel, associatedColumn, assignedTo, assginedType } = approvers[i];
                const key = this.createStepUniqueKey(assignedLevel, assignedTo, assginedType, associatedColumn);
                currentStepsSet.add(key);
            }
            const newApprovers = [];
            if (newStepsIndex) {
                for (let i = newStepsIndex; i < latestWorkflowSteps.length; i++) {
                    const { associateLevel, associatedColumn, associateRole, associateType, approvers, parallelIdentifier, title, stepId, associatedCostCenterId, section, associatedLevelEntityId, } = latestWorkflowSteps[i];
                    const stepKey = this.createStepUniqueKey(associateLevel, associateRole, associateType, associatedColumn);
                    if (!currentStepsSet.has(stepKey)) {
                        lastApproverSequence += 1;
                        const approver = {
                            assignedTo: associateRole || approvers[approvers.length - 1].loginId,
                            title: title,
                            afeProposalId: afeProposalId,
                            userDetail: null,
                            otherInfo: Object.assign({ usersDetail: approvers, approvalType: enums_1.APPROVAL_TYPE.APPROVAL }, (associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER && section && { section: section })),
                            assignedLevel: associateLevel,
                            assginedType: associateType || enums_1.ASSIGNED_TYPE.USER,
                            associatedColumn: associatedColumn,
                            parallelIdentifier: parallelIdentifier,
                            approvalSequence: lastApproverSequence,
                            workflowMasterStepsId: stepId,
                            associatedCostCenterId: associatedCostCenterId || null,
                            assignedEntityId: associatedLevelEntityId,
                        };
                        newApprovers.push(approver);
                    }
                }
            }
            if (newApprovers.length) {
                yield this.afeProposalApproverRepository.bulkApproversInsert(newApprovers, currentContext);
            }
            return this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
        });
    }
    getCurrentTaskSteps(userId, approvers) {
        return __awaiter(this, void 0, void 0, function* () {
            const userInprogressSteps = [];
            const currentWorkflowSteps = this.getCurrentInprogressWorkflowSteps(approvers);
            for (const step of currentWorkflowSteps) {
                const { assignedTo, assginedType, assignedEntityId } = step;
                if ((assginedType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER || assginedType === associated_type_enum_1.ASSOCIATED_TYPE.USER) &&
                    assignedTo.toLowerCase() === userId) {
                    userInprogressSteps.push(step);
                }
                else if (assginedType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE) {
                    const hasUserRole = yield this.adminApiClient.hasUserRole(userId, assignedTo, assignedEntityId);
                    if (hasUserRole) {
                        userInprogressSteps.push(step);
                    }
                }
                else {
                    continue;
                }
            }
            return userInprogressSteps;
        });
    }
    createNotificationOnAfeApproval(taskAction, afeDetails, currentContext, comments, assignedToAfeSubmitter, delegateeDetails) {
        return __awaiter(this, void 0, void 0, function* () {
            const url = `${(0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_URLS.AFE_DETAILS_URL, {
                afeId: `${afeDetails.id}`,
            })}`;
            const expireAt = (0, helpers_1.getNotificationExpiryDate)();
            const description = comments ? `With comments: ${comments}` : null;
            const commonPayload = { expireAt, url, description };
            const { user } = currentContext;
            const { name: approverName, username: approverId } = user;
            const { data, projectReferenceNumber: afeReferenceNumber, submitterId, subscribers, } = afeDetails;
            let submitterDetails = data.submitterDetails;
            let submitterName = `${submitterDetails.firstName} ${submitterDetails.lastName}`;
            let notifications = [];
            switch (taskAction) {
                case enums_1.TASK_ACTION.APPROVE:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.SUCCESS, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.APPROVE.APPROVER, { afeReferenceNumber }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.SUCCESS, subscribers: [submitterId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.APPROVE.SUBMITTER, { afeReferenceNumber, approverName }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.SUCCESS, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.APPROVE.SUBSCRIBER, { afeReferenceNumber, approverName }) }),
                    ];
                    break;
                case enums_1.TASK_ACTION.DELEGATE:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.APPROVER, {
                                afeReferenceNumber,
                                delegateeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: [submitterId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.SUBMITTER, {
                                afeReferenceNumber,
                                approverName,
                                delegateeName: assignedToAfeSubmitter
                                    ? 'you'
                                    : `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.SUBSCRIBER, {
                                afeReferenceNumber,
                                approverName,
                                delegateeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: [delegateeDetails.loginId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DELEGATE.DELEGATEE, { afeReferenceNumber, approverName }) }),
                    ];
                    break;
                case enums_1.TASK_ACTION.REJECT:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.REJECT, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REJECT.APPROVER, { afeReferenceNumber }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.REJECT, subscribers: [submitterId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REJECT.SUBMITTER, { afeReferenceNumber, approverName }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.REJECT, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REJECT.SUBSCRIBER, { afeReferenceNumber, approverName }) }),
                    ];
                    break;
                case enums_1.TASK_ACTION.MORE_DETAIL:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.APPROVER, {
                                afeReferenceNumber,
                                delegateeName: assignedToAfeSubmitter
                                    ? submitterName
                                    : `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: [submitterId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.SUBMITTER, {
                                afeReferenceNumber,
                                approverName,
                                delegateeName: assignedToAfeSubmitter
                                    ? 'you'
                                    : `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.SUBSCRIBER, {
                                afeReferenceNumber,
                                approverName,
                                delegateeName: assignedToAfeSubmitter
                                    ? submitterName
                                    : `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                    ];
                    if (!assignedToAfeSubmitter) {
                        notifications.push(Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.INFO, subscribers: [delegateeDetails.loginId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL.DELEGATEE, { afeReferenceNumber, approverName }) }));
                    }
                    break;
                case enums_1.TASK_ACTION.MORE_DETAIL_SUBMITTED:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.SUCCESS, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL_SUBMITTED.APPROVER, { afeReferenceNumber }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.SUCCESS, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL_SUBMITTED.SUBSCRIBER, { afeReferenceNumber, approverName }) }),
                    ];
                    if (submitterId !== approverId) {
                        notifications.push(Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.SUCCESS, subscribers: [submitterId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.MORE_DETAIL_SUBMITTED.SUBMITTER, { afeReferenceNumber, approverName }) }));
                    }
                    break;
                case enums_1.TASK_ACTION.SEND_BACK:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.WARNING, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.SEND_BACK.APPROVER, { afeReferenceNumber, submitterName: submitterName }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.WARNING, subscribers: [submitterId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.SEND_BACK.SUBMITTER, { afeReferenceNumber, approverName }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.WARNING, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.SEND_BACK.SUBSCRIBER, { afeReferenceNumber, approverName, submitterName: submitterName }) }),
                    ];
                    break;
                case enums_1.TASK_ACTION.DISCARD:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.REJECT, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DISCARD.APPROVER, { afeReferenceNumber }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.REJECT, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.DISCARD.SUBSCRIBER, { afeReferenceNumber, approverName }) }),
                    ];
                    break;
                case enums_1.TASK_ACTION.REASSIGNE:
                    notifications = [
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.WARNING, subscribers: [approverId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.APPROVER, {
                                afeReferenceNumber,
                                assigneeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.WARNING, subscribers: [submitterId], title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.SUBMITTER, {
                                afeReferenceNumber,
                                assigneeName: delegateeDetails.loginId === submitterId
                                    ? 'you'
                                    : `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                                approverName,
                            }) }),
                        Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.WARNING, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.SUBSCRIBER, {
                                afeReferenceNumber,
                                approverName,
                                assigneeName: `${delegateeDetails.firstName} ${delegateeDetails.lastName}`,
                            }) }),
                    ];
                    if (delegateeDetails.loginId !== submitterId) {
                        notifications.push(Object.assign(Object.assign({}, commonPayload), { type: enums_1.NOTIFICATION_TYPE.WARNING, subscribers: subscribers, title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_APPROVAL_ACTION.REASSIGNE.DELEGATEE, { afeReferenceNumber, approverName }) }));
                    }
                    break;
            }
            yield this.notificationRepository.bulkNotificationsInsert(notifications, currentContext);
        });
    }
    updateAfeProposalLimitDeduction(afeProposalId, newMasterSettingWorkflows, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const currentDeductionEntries = yield this.afeProposalLimitDeductionRepository.getCurrentLimitDeductionsForAfeProposal(afeProposalId);
            const cancelEntriesIds = currentDeductionEntries
                .filter(entry => {
                const index = newMasterSettingWorkflows.findIndex(e => e.deductionStepId === entry.workflowMasterStepId &&
                    e.workflowMasterSettingId === entry.workflowMasterSettingId);
                return index === -1;
            })
                .map(e => e.id);
            const afeProposal = yield this.afeProposalRepository.getAfeProposalById(afeProposalId);
            const limitDeductionAfeData = {
                afeReferenceNumber: afeProposal.projectReferenceNumber,
                projectName: afeProposal.name,
                requestTypeId: afeProposal.afeRequestTypeId,
                isSupplemental: afeProposal.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL,
                submitterId: afeProposal.submitterId,
                budgetType: afeProposal.budgetType || null,
                year: afeProposal.workflowYear,
                isApprovedByBoard: afeProposal.isApprovedByBoard,
                parentAfeId: afeProposal.parentAfeId || null,
            };
            const newdeductionEntries = newMasterSettingWorkflows
                .filter(entry => {
                const index = currentDeductionEntries.findIndex(e => e.workflowMasterStepId === entry.deductionStepId &&
                    e.workflowMasterSettingId === entry.workflowMasterSettingId);
                return index === -1;
            })
                .map(e => ({
                afeProposalId: afeProposalId,
                entityId: e.associatedEntityId,
                entityTitle: e.associatedEntityTitle,
                entityCode: e.associatedEntityCode,
                workflowMasterSettingId: e.workflowMasterSettingId,
                workflowMasterStepId: e.deductionStepId,
                costCenterId: e.costCenterId,
                amount: e.amount,
                data: limitDeductionAfeData,
                status: enums_1.AFE_LIMIT_DEDUCATION_STATUS.IN_PROGRESS,
            }));
            if (cancelEntriesIds.length) {
                yield this.afeProposalLimitDeductionRepository.changeStatusByIds(cancelEntriesIds, enums_1.AFE_LIMIT_DEDUCATION_STATUS.CANCELLED, currentContext);
            }
            if (newdeductionEntries.length) {
                yield this.afeProposalLimitDeductionRepository.bulkInsertAfeProposalLimitDeductions(newdeductionEntries, currentContext);
                const updatedDeductions = yield this.afeProposalLimitDeductionRepository.getCurrentLimitDeductionsForAfeProposal(afeProposalId);
                for (const deduction of updatedDeductions) {
                    const workflowDeduction = newMasterSettingWorkflows.find(workflow => workflow.deductionStepId === deduction.workflowMasterStepId);
                    if (workflowDeduction === null || workflowDeduction === void 0 ? void 0 : workflowDeduction.parentDeductionStepId) {
                        const parentDeduction = updatedDeductions.find(d => d.workflowMasterStepId === workflowDeduction.parentDeductionStepId &&
                            d.amount === workflowDeduction.amount);
                        if (parentDeduction) {
                            yield this.afeProposalLimitDeductionRepository.updateParentDeductionId(deduction.id, parentDeduction.id, currentContext);
                        }
                    }
                }
            }
        });
    }
    getCurrentInprogressWorkflowSteps(approvers) {
        const inProgressSteps = [];
        for (const approver of approvers) {
            if (approver.actionStatus === enums_1.APPROVER_STATUS.IN_PROGRESS) {
                inProgressSteps.push(approver);
            }
        }
        return inProgressSteps;
    }
    getLatestWorkflowApproversList(afeProposalId) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const afeProposal = yield this.afeProposalRepository.getAfeProposalById(afeProposalId);
            const { afeRequestTypeId, budgetType, isApprovedByBoard, entityId, yearOfCommitment: lengthOfCommitment, totalAmount, workflowYear, data, parentAfeId, category, submitterId, } = afeProposal;
            const computeApproversListPayload = {
                requestTypeId: afeRequestTypeId,
                budgetType: budgetType,
                entityId: entityId,
                totalAmount: totalAmount,
                isApprovedByBoard: isApprovedByBoard,
                lengthOfCommitment,
                year: workflowYear,
                afeSubType: data.subType || null,
                afeType: data.type || null,
                parentAfeId: parentAfeId || null,
                isSupplemental: category === enums_1.AFE_CATEGORY.SUPPLEMENTAL,
                projectLeaderId: ((_b = (_a = data === null || data === void 0 ? void 0 : data.projectDetails) === null || _a === void 0 ? void 0 : _a.projectLeader) === null || _b === void 0 ? void 0 : _b.loginId) || null,
            };
            const projectComponentSplit = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(afeProposalId, enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT);
            if (projectComponentSplit === null || projectComponentSplit === void 0 ? void 0 : projectComponentSplit.length) {
                computeApproversListPayload.projectComponentSplits = projectComponentSplit.map(split => ({
                    id: split.objectId,
                    title: split.objectTitle,
                    amount: split.amount,
                    currency: split.currency,
                }));
            }
            const budgetTypeSplit = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(afeProposalId, enums_1.AMOUNT_SPLIT.BUDGET_TYPE_SPLIT);
            if (budgetTypeSplit === null || budgetTypeSplit === void 0 ? void 0 : budgetTypeSplit.length) {
                computeApproversListPayload.budgetTypeSplits = budgetTypeSplit.map(split => ({
                    id: split.objectId,
                    title: split.objectTitle,
                    amount: split.amount,
                    currency: split.currency,
                }));
            }
            const costCenterSplit = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(afeProposalId, enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT);
            if (costCenterSplit === null || costCenterSplit === void 0 ? void 0 : costCenterSplit.length) {
                computeApproversListPayload.costCenters = costCenterSplit
                    .map(split => {
                    var _a;
                    return ({
                        id: split.objectId,
                        code: split.objectTitle,
                        amount: split.amount,
                        section: ((_a = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _a === void 0 ? void 0 : _a.section) || null,
                    });
                })
                    .filter(split => split.id !== 0);
            }
            const projectComponentSplitByBudgetType = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(afeProposalId, enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT);
            if ((projectComponentSplitByBudgetType === null || projectComponentSplitByBudgetType === void 0 ? void 0 : projectComponentSplitByBudgetType.length) && budgetType === enums_1.BUDGET_TYPE.MIXED) {
                computeApproversListPayload.budgetBasedProjectSplit = (0, helpers_1.serializeBudgetBasedProjectAmountSplits)(projectComponentSplitByBudgetType, false);
            }
            return this.workflowService.computeAfeApproversList(computeApproversListPayload, submitterId);
        });
    }
    performApprovalAction(actionType, afeDetails, currentApprover, approvers, currentContext, task, comments, delegatee, assignedToAfeSubmitter) {
        var _a, _b, _c;
        return __awaiter(this, void 0, void 0, function* () {
            const { id, afeProposalId, approvalSequence } = currentApprover;
            let actionPerformed;
            let canCallNextTask = true;
            let isNotALastStep = true;
            let originalApproverDetail = null;
            const { original_owner: originalApproverId, delegated_task_type: delegateTaskType } = task;
            if (originalApproverId && delegateTaskType === 'representative') {
                originalApproverDetail = yield this.getUserDetails(originalApproverId);
            }
            let delegateeDetails = null;
            if (delegatee) {
                delegateeDetails = yield this.getUserDetails(delegatee.loginId);
            }
            switch (actionType) {
                case enums_1.TASK_ACTION.APPROVE:
                case enums_1.TASK_ACTION.AUTO_APPROVE:
                    const approvalStatus = enums_1.TASK_ACTION.AUTO_APPROVE === actionType
                        ? enums_1.APPROVER_STATUS.AUTO_APPROVED
                        : enums_1.APPROVER_STATUS.APPROVED;
                    yield this.afeProposalApproverRepository.updateApproverStatusOnAction(id, approvalStatus, currentContext, originalApproverDetail, comments);
                    isNotALastStep =
                        yield this.afeProposalApproverRepository.isAnyNotInitiatedAndInprogressApproverExist(afeProposalId);
                    if (!isNotALastStep) {
                        yield this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(afeProposalId, enums_1.AFE_LIMIT_DEDUCATION_STATUS.APPROVED, currentContext);
                        const companyDetail = yield this.financeAdminService.getEntityActiveCompanyCodeDetails(afeDetails.entityId, false);
                        const isFusionIntegrationEnabled = (_a = companyDetail === null || companyDetail === void 0 ? void 0 : companyDetail.fusionIntegrationForRequestTypeIds) === null || _a === void 0 ? void 0 : _a.includes(afeDetails.afeRequestTypeId);
                        yield this.afeProposalRepository.changeStatus(afeProposalId, enums_1.AFE_PROPOSAL_STATUS.APPROVED, constants_1.AFE_USER_STATUS.APPROVED, currentContext, isFusionIntegrationEnabled);
                        canCallNextTask = false;
                    }
                    actionPerformed =
                        enums_1.TASK_ACTION.AUTO_APPROVE === actionType
                            ? enums_1.HISTORY_ACTION_TYPE.AUTO_APPROVED
                            : enums_1.HISTORY_ACTION_TYPE.APPROVED;
                    break;
                case enums_1.TASK_ACTION.REJECT:
                    yield this.afeProposalApproverRepository.updateApproverStatusOnAction(id, enums_1.APPROVER_STATUS.REJECTED, currentContext, originalApproverDetail, comments);
                    yield this.afeProposalRepository.changeStatus(afeProposalId, enums_1.AFE_PROPOSAL_STATUS.REJECTED, constants_1.AFE_USER_STATUS.REJECTED, currentContext);
                    yield this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(afeProposalId, enums_1.AFE_LIMIT_DEDUCATION_STATUS.REJECTED, currentContext);
                    canCallNextTask = false;
                    actionPerformed = enums_1.HISTORY_ACTION_TYPE.REJECTED;
                    this.cancelInprogressTasks(afeProposalId, currentContext);
                    break;
                case enums_1.TASK_ACTION.DELEGATE:
                    yield this.afeProposalApproverRepository.updateApproverStatusOnAction(id, enums_1.APPROVER_STATUS.DELEGATED, currentContext, originalApproverDetail, comments);
                    yield this.afeProposalApproverRepository.incrementSequenceNumber(afeProposalId, approvalSequence, 1);
                    const { loginId: delegateeLoginId } = delegatee;
                    const delegateApprovalStep = {
                        assignedTo: delegateeLoginId.toLowerCase(),
                        title: (delegateeDetails === null || delegateeDetails === void 0 ? void 0 : delegateeDetails.title) || delegateeLoginId.toLowerCase(),
                        afeProposalId: afeProposalId,
                        userDetail: null,
                        otherInfo: { usersDetail: [delegateeDetails], approvalType: enums_1.APPROVAL_TYPE.APPROVAL },
                        assignedLevel: null,
                        assginedType: enums_1.ASSIGNED_TYPE.USER,
                        associatedColumn: null,
                        parallelIdentifier: currentApprover.parallelIdentifier,
                        approvalSequence: approvalSequence + 1,
                        workflowMasterStepsId: null,
                        assignedEntityId: null,
                    };
                    yield this.afeProposalApproverRepository.createAfeProposalApprover(delegateApprovalStep, currentContext);
                    actionPerformed = enums_1.HISTORY_ACTION_TYPE.DELEGATED;
                    break;
                case enums_1.TASK_ACTION.MORE_DETAIL:
                case enums_1.TASK_ACTION.REASSIGNE:
                    const status = actionType === enums_1.TASK_ACTION.MORE_DETAIL
                        ? enums_1.APPROVER_STATUS.MORE_DETAIL
                        : enums_1.APPROVER_STATUS.REASSIGNED;
                    yield this.afeProposalApproverRepository.updateApproverStatusOnAction(id, status, currentContext, originalApproverDetail, comments);
                    yield this.afeProposalApproverRepository.incrementSequenceNumber(afeProposalId, approvalSequence, 2);
                    let assigneeDetails = [delegateeDetails];
                    let assigneeLoginId;
                    let title;
                    if (assignedToAfeSubmitter) {
                        assigneeLoginId = afeDetails.submitterId;
                        assigneeDetails = [afeDetails.data.submitterDetails];
                        title = ((_c = (_b = afeDetails.data) === null || _b === void 0 ? void 0 : _b.submitterDetails) === null || _c === void 0 ? void 0 : _c.title) || afeDetails.submitterId.toLowerCase();
                    }
                    else {
                        assigneeLoginId = delegatee.loginId;
                        title = (delegateeDetails === null || delegateeDetails === void 0 ? void 0 : delegateeDetails.title) || delegatee.loginId.toLowerCase();
                    }
                    const assigneeApprovalStep = {
                        assignedTo: assigneeLoginId.toLowerCase(),
                        title: title,
                        afeProposalId: afeProposalId,
                        userDetail: null,
                        otherInfo: {
                            usersDetail: assigneeDetails,
                            approvalType: actionType === enums_1.TASK_ACTION.MORE_DETAIL
                                ? enums_1.APPROVAL_TYPE.MORE_DETAIL
                                : enums_1.APPROVAL_TYPE.APPROVAL,
                        },
                        assignedLevel: null,
                        assginedType: enums_1.ASSIGNED_TYPE.USER,
                        associatedColumn: null,
                        parallelIdentifier: currentApprover.parallelIdentifier,
                        approvalSequence: currentApprover.approvalSequence + 1,
                        workflowMasterStepsId: null,
                        assignedEntityId: null,
                    };
                    const assignee = yield this.afeProposalApproverRepository.createAfeProposalApprover(assigneeApprovalStep, currentContext);
                    const asssignerApprovalStep = {
                        assignedTo: currentApprover.assignedTo,
                        title: currentApprover.title,
                        afeProposalId: afeProposalId,
                        userDetail: null,
                        otherInfo: currentApprover.otherInfo,
                        assignedLevel: currentApprover.assignedLevel,
                        assginedType: currentApprover.assginedType,
                        associatedColumn: currentApprover.associatedColumn,
                        parallelIdentifier: null,
                        approvalSequence: currentApprover.approvalSequence + 2,
                        workflowMasterStepsId: currentApprover.workflowMasterStepsId,
                        assignedEntityId: currentApprover.assignedEntityId,
                    };
                    const assigner = yield this.afeProposalApproverRepository.createAfeProposalApprover(asssignerApprovalStep, currentContext);
                    yield this.afeProposalApproverRepository.updateOriginalApproverId(assignee.id, assigner.id, currentContext);
                    actionPerformed =
                        actionType === enums_1.TASK_ACTION.MORE_DETAIL
                            ? enums_1.HISTORY_ACTION_TYPE.MORE_DETAIL
                            : enums_1.HISTORY_ACTION_TYPE.REASSIGNED;
                    break;
                case enums_1.TASK_ACTION.MORE_DETAIL_SUBMITTED:
                    yield this.afeProposalApproverRepository.updateApproverStatusOnAction(id, enums_1.APPROVER_STATUS.MORE_DETAIL_SUBMITTED, currentContext, originalApproverDetail, comments);
                    actionPerformed = enums_1.HISTORY_ACTION_TYPE.MORE_DETAIL_SUBMITTED;
                    break;
                case enums_1.TASK_ACTION.SEND_BACK:
                    yield this.afeProposalRepository.changeStatus(afeProposalId, enums_1.AFE_PROPOSAL_STATUS.SENT_BACK, constants_1.AFE_USER_STATUS.SENT_BACK, currentContext);
                    yield this.afeProposalApproverRepository.updateApproverStatusOnAction(id, enums_1.APPROVER_STATUS.SEND_BACK, currentContext, originalApproverDetail, comments);
                    yield this.cancelInprogressTasks(afeProposalId, currentContext);
                    const afeCreator = yield this.afeProposalRepository.getAfeProposalById(afeProposalId);
                    const { submitterId: afeCreatorId } = afeCreator;
                    const afeCreatorApprover = {
                        assignedTo: afeCreatorId.toLowerCase(),
                        title: afeCreatorId,
                        afeProposalId: afeProposalId,
                        userDetail: null,
                        otherInfo: {
                            usersDetail: [{ loginId: afeCreatorId }],
                            approvalType: enums_1.APPROVAL_TYPE.RESUBMISSION,
                        },
                        assignedLevel: null,
                        assginedType: enums_1.ASSIGNED_TYPE.USER,
                        associatedColumn: null,
                        parallelIdentifier: null,
                        approvalSequence: currentApprover.approvalSequence,
                        workflowMasterStepsId: null,
                        assignedEntityId: null,
                    };
                    yield this.afeProposalApproverRepository.createAfeProposalApprover(afeCreatorApprover, currentContext);
                    actionPerformed = enums_1.HISTORY_ACTION_TYPE.SENT_BACK;
                    break;
                case enums_1.TASK_ACTION.DISCARD:
                    yield this.afeProposalApproverRepository.updateApproverStatusOnAction(id, enums_1.APPROVER_STATUS.DISCARDED, currentContext, originalApproverDetail, comments);
                    actionPerformed = enums_1.HISTORY_ACTION_TYPE.DISCARDED;
                    yield this.afeProposalRepository.changeStatus(afeProposalId, enums_1.AFE_PROPOSAL_STATUS.CANCELLED, constants_1.AFE_USER_STATUS.CANCELLED, currentContext);
                    yield this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(afeProposalId, enums_1.AFE_LIMIT_DEDUCATION_STATUS.CANCELLED, currentContext);
                    canCallNextTask = false;
                    break;
                default:
                    throw new exceptions_1.HttpException('Invalid action type.', enums_1.HttpStatus.BAD_REQUEST);
            }
            const isMailApprovalTask = actionType !== enums_1.TASK_ACTION.MORE_DETAIL;
            if (canCallNextTask) {
                yield this.createNextTasks(afeProposalId, actionType, currentContext, isMailApprovalTask, originalApproverId, task, false);
            }
            if (actionType !== enums_1.TASK_ACTION.AUTO_APPROVE) {
                isNotALastStep =
                    yield this.afeProposalApproverRepository.isAnyNotInitiatedAndInprogressApproverExist(afeProposalId);
                yield this.createNotificationOnAfeApproval(actionType, afeDetails, currentContext, comments, assignedToAfeSubmitter, delegatee);
                yield this.afeProposalRepository.addContextUserToSubscriberList(afeDetails.id, currentContext);
                yield this.createQueueLogOnApprovalAction(actionType, afeDetails, isNotALastStep, approvers, currentContext);
                const submitterDetails = yield this.mSGraphApiClient.getUserDetails(afeDetails.submitterId);
                const config = this.configService.getAppConfig();
                const placeholdersValues = Object.assign({ afeDetailLink: `${config.uiClient.baseUrl}/afe/afe-detail/${afeDetails.id}`, approvers: `${currentContext.user.name}`, approver: `${currentApprover.title}`, comments: comments || '-' }, (delegatee && { delegatee: `${delegatee.firstName} ${delegatee.lastName}` }));
                if (submitterDetails === null || submitterDetails === void 0 ? void 0 : submitterDetails.mail) {
                    yield this.sharedNotificationService.sendNotificationForAfeProposal(afeDetails.id, currentApprover.id, enums_1.NOTIFICATION_ENTITY_TYPE.AFE_TASK_APPROVAL_NOTIFICATION, { to: [submitterDetails.mail] }, task_action_with_email_template_mapping_1.TASK_ACTION_WITH_EMAIL_TEMPLATE[actionType], false, placeholdersValues);
                }
                if (!isNotALastStep && (submitterDetails === null || submitterDetails === void 0 ? void 0 : submitterDetails.mail)) {
                    yield this.sharedNotificationService.sendNotificationForAfeProposal(afeDetails.id, currentApprover.id, enums_1.NOTIFICATION_ENTITY_TYPE.AFE_APPROVAL_NOTIFICATION, { to: [submitterDetails.mail] }, 'AFE.TASK.APPROVAL.FINAL_APPROVAL', false, placeholdersValues);
                }
                yield this.createApprovalHistorty(afeProposalId, actionPerformed, comments, currentContext, originalApproverDetail);
            }
            yield this.taskApiClient.completeAllTasks({
                entity_id: id,
                entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                outcome: actionType,
            });
        });
    }
    cancelInprogressTasks(afeProposalId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const approvers = yield this.afeProposalApproverRepository.getInProgressApproversListByProposalId(afeProposalId);
            if (approvers === null || approvers === void 0 ? void 0 : approvers.length) {
                let approverIds = [];
                for (let i = 0; i < approvers.length; i++) {
                    approverIds.push(approvers[i].id);
                    yield this.taskApiClient.cancelAllTasks({
                        entity_id: approvers[i].id,
                        entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                        comments: 'Cancelled',
                        created_by: user.username.toLowerCase(),
                    });
                }
                yield this.afeProposalApproverRepository.updateStatusOnActionByIds(approverIds, enums_1.APPROVER_STATUS.DISCARDED, currentContext, 'Cancelled');
            }
        });
    }
    createApprovalHistorty(afeProposalId, actionPerformed, comments, currentContext, originalApproverDetail) {
        return __awaiter(this, void 0, void 0, function* () {
            const addHistoryPayload = {
                created_by: currentContext.user.username,
                entity_id: afeProposalId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                action_performed: actionPerformed,
                comments: comments,
            };
            if (originalApproverDetail) {
                yield Promise.all([
                    this.historyApiClient.addRequestHistory(Object.assign(Object.assign({}, addHistoryPayload), { created_by: originalApproverDetail.loginId })),
                    this.historyApiClient.addRequestHistory(Object.assign(Object.assign({}, addHistoryPayload), { created_by: currentContext.user.username, additional_info: { hidden: true } })),
                ]);
            }
            else {
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
            }
        });
    }
    changeAfeStatusToInprogress(afeProposalId, currentContext, actionType) {
        return __awaiter(this, void 0, void 0, function* () {
            const approvers = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
            const inProgressStep = approvers.find(approver => approver.actionStatus === enums_1.APPROVER_STATUS.IN_PROGRESS);
            if (inProgressStep) {
                const parallelIdentifiers = yield this.parallelIdentifierRepository.getAllParallelIdentifiers();
                let afeUserStatus = inProgressStep.title;
                if (inProgressStep === null || inProgressStep === void 0 ? void 0 : inProgressStep.parallelIdentifier) {
                    const parallelIdentifier = parallelIdentifiers.find(parallelIdentifier => parallelIdentifier.identifier === inProgressStep.parallelIdentifier);
                    afeUserStatus = (parallelIdentifier === null || parallelIdentifier === void 0 ? void 0 : parallelIdentifier.title) || afeUserStatus;
                }
                yield this.afeProposalRepository.changeStatus(afeProposalId, enums_1.TASK_ACTION.SEND_BACK === actionType
                    ? enums_1.AFE_PROPOSAL_STATUS.SENT_BACK
                    : enums_1.AFE_PROPOSAL_STATUS.IN_PROGRESS, `Pending with ${afeUserStatus}`, currentContext);
            }
        });
    }
    getUserDetails(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { givenName: firstName, surname: lastName, userPrincipalName: upn, userType, mail: email, jobTitle: title, } = yield this.mSGraphApiClient.getUserDetails(userId);
            const loginId = userType == enums_1.AD_USER_TYPE.GUEST ? email.toLowerCase() : upn.toLowerCase();
            return { firstName, lastName, loginId, email, title };
        });
    }
    createQueueLogOnApprovalAction(actionType, afeDetails, isNotALastStep, approvers, currentContext) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { id: proposalId, entityId, budgetType, totalAmount, afeRequestTypeId, data, } = afeDetails;
            const splits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndTypes(proposalId, [enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT, enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT]);
            const entityParents = yield this.adminApiClient.getParentsOfEntity(entityId);
            const logPayload = {
                entityId: entityId,
                action: mappings_1.TASK_ACTION_WITH_QUEUE_LOG_ACTION_MAPPING[actionType],
                finalApproval: !isNotALastStep || actionType === enums_1.TASK_ACTION.REJECT,
                data: {
                    proposalId: proposalId,
                    budgetType: budgetType,
                    totalAmount: totalAmount,
                    requestTypeId: afeRequestTypeId,
                    afeType: data.type,
                    afeSubType: data.subType,
                    projectComponentId: ((_a = splits === null || splits === void 0 ? void 0 : splits.filter(p => p.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT)) === null || _a === void 0 ? void 0 : _a.map(p => p.objectId)) || null,
                    costCenterId: ((_b = splits === null || splits === void 0 ? void 0 : splits.filter(c => c.type === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT)) === null || _b === void 0 ? void 0 : _b.map(c => c.objectId)) ||
                        null,
                    approversLevel: [...new Set(approvers.map(step => step.assignedLevel))].filter(a => !!a),
                    businessUnitHierarchy: entityParents.map(entity => ({
                        id: entity.id,
                        code: entity.code,
                        level: entity.entity_type,
                    })),
                    status: !isNotALastStep
                        ? enums_1.QUEUE_LOG_ACTION.FINAL_APPROVAL
                        : mappings_1.TASK_ACTION_WITH_QUEUE_LOG_ACTION_MAPPING[actionType],
                },
            };
            yield this.queueLogRepository.createQueueLogEntry(logPayload, currentContext);
        });
    }
    createNextTasks(afeProposalId, actionType, currentContext, isMailApprovalTask = true, taskOriginalApproverId, task, isFirstTimeCall = true) {
        return __awaiter(this, void 0, void 0, function* () {
            const approvers = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
            const afeProposal = yield this.afeProposalRepository.getAfeProposalById(afeProposalId);
            let currentParallelIdentifier = null;
            let i = 0;
            let isAnyParallelApproverInProgress = false;
            while (i < approvers.length) {
                const { id: currentApproverId, parallelIdentifier, actionStatus, originalApproverId, otherInfo, } = approvers[i];
                if (actionStatus === enums_1.APPROVER_STATUS.IN_PROGRESS && !parallelIdentifier) {
                    break;
                }
                if (actionStatus === enums_1.APPROVER_STATUS.IN_PROGRESS && parallelIdentifier) {
                    isAnyParallelApproverInProgress = true;
                }
                if (actionStatus === enums_1.APPROVER_STATUS.IN_PROGRESS &&
                    parallelIdentifier &&
                    (currentParallelIdentifier === null || currentParallelIdentifier === parallelIdentifier)) {
                    currentParallelIdentifier = parallelIdentifier;
                    if ((actionType === enums_1.TASK_ACTION.APPROVE || actionType === enums_1.TASK_ACTION.AUTO_APPROVE) &&
                        otherInfo.usersDetail.some(user => user.loginId.toLowerCase() ===
                            ((taskOriginalApproverId === null || taskOriginalApproverId === void 0 ? void 0 : taskOriginalApproverId.toLowerCase()) ||
                                currentContext.user.username.toLowerCase()) && !isFirstTimeCall)) {
                        yield this.performApprovalAction(enums_1.TASK_ACTION.AUTO_APPROVE, afeProposal, approvers[i], approvers, currentContext, task, 'Auto Approved');
                    }
                    i += 1;
                    continue;
                }
                if (actionStatus === enums_1.APPROVER_STATUS.NOT_INITIATED &&
                    (currentParallelIdentifier === null || currentParallelIdentifier === parallelIdentifier)) {
                    const dependentApprover = approvers.find(a => a.originalApproverId === currentApproverId);
                    if ((dependentApprover === null || dependentApprover === void 0 ? void 0 : dependentApprover.actionStatus) === enums_1.APPROVER_STATUS.IN_PROGRESS) {
                        i += 1;
                        continue;
                    }
                    if (!parallelIdentifier && isAnyParallelApproverInProgress) {
                        break;
                    }
                    if (parallelIdentifier !== null) {
                        if ((actionType === enums_1.TASK_ACTION.APPROVE || actionType === enums_1.TASK_ACTION.AUTO_APPROVE) &&
                            otherInfo.usersDetail.some(user => user.loginId.toLowerCase() ===
                                ((taskOriginalApproverId === null || taskOriginalApproverId === void 0 ? void 0 : taskOriginalApproverId.toLowerCase()) ||
                                    currentContext.user.username.toLowerCase()) && !isFirstTimeCall)) {
                            yield this.performApprovalAction(enums_1.TASK_ACTION.AUTO_APPROVE, afeProposal, approvers[i], approvers, currentContext, task, 'Auto Approved');
                        }
                        else {
                            yield this.createTask(afeProposal, approvers[i], afeProposal.entityId, currentContext, isMailApprovalTask, mappings_1.TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING[actionType], actionType);
                        }
                        i =
                            originalApproverId &&
                                i + 1 < approvers.length &&
                                approvers[i + 1].id === originalApproverId
                                ? i + 2
                                : i + 1;
                        currentParallelIdentifier = parallelIdentifier;
                        continue;
                    }
                    if ((actionType === enums_1.TASK_ACTION.APPROVE || actionType === enums_1.TASK_ACTION.AUTO_APPROVE) &&
                        otherInfo.usersDetail.some(user => user.loginId.toLowerCase() ===
                            ((taskOriginalApproverId === null || taskOriginalApproverId === void 0 ? void 0 : taskOriginalApproverId.toLowerCase()) ||
                                currentContext.user.username.toLowerCase()) && !isFirstTimeCall)) {
                        yield this.performApprovalAction(enums_1.TASK_ACTION.AUTO_APPROVE, afeProposal, approvers[i], approvers, currentContext, task, 'Auto Approved');
                    }
                    else {
                        yield this.createTask(afeProposal, approvers[i], afeProposal.entityId, currentContext, isMailApprovalTask, mappings_1.TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING[actionType], actionType);
                    }
                    break;
                }
                i += 1;
            }
        });
    }
    recreateTask(afeProposalId, proposalDetail, approvalType = mappings_1.TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING.APPROVE, isEmailApprover = false) {
        return __awaiter(this, void 0, void 0, function* () {
            const approvers = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
            let i = 0;
            while (i < approvers.length) {
                const { id: currentApproverId, assignedTo, assginedType, assignedLevel, actionStatus, otherInfo, } = approvers[i];
                if (actionStatus === enums_1.APPROVER_STATUS.IN_PROGRESS) {
                    const { uiClient } = this.configService.getAppConfig();
                    let businessEntityId = proposalDetail.entityId;
                    if (assginedType !== associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER && assginedType !== associated_type_enum_1.ASSOCIATED_TYPE.USER) {
                        const associatedLevelEntity = yield this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(proposalDetail.entityId, assignedLevel);
                        businessEntityId = associatedLevelEntity.id;
                    }
                    const payload = {
                        title: this.createTaskTitle(proposalDetail.projectReferenceNumber, otherInfo.approvalType),
                        assigned_to: assignedTo,
                        entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                        entity_id: `${currentApproverId}`,
                        is_group_assignemnt: assginedType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE,
                        business_entity_id: businessEntityId,
                        base_url: uiClient.baseUrl,
                        rel_url: this.createRelativeUrlForTask(otherInfo.approvalType, proposalDetail),
                        additional_info: {
                            proposal_id: proposalDetail.id,
                            proposal_project_reference_number: proposalDetail.projectReferenceNumber,
                            proposal_created_by: proposalDetail.submitterId,
                            proposal_request_type: proposalDetail.afeRequestType.title,
                            proposal_entity: proposalDetail.entityTitle,
                            proposal_submitted_on: proposalDetail.createdOn,
                            proposal_project_name: proposalDetail.name,
                            approval_type: approvalType,
                            totalAmount: proposalDetail.totalAmount
                        },
                        delegate_filter_param: {
                            request_type_id: proposalDetail.afeRequestTypeId,
                        },
                    };
                    const tasks = yield this.taskApiClient.createTaskWithUseDelegation(payload);
                    const delegateeUserIds = tasks
                        .filter(task => { var _a; return task.type === 'delegate' && ((_a = task.assigned_to) === null || _a === void 0 ? void 0 : _a.delegate_to_username); })
                        .map(task => task.assigned_to.delegate_to_username);
                    let delegateeUsersDetails = [];
                    if (delegateeUserIds.length) {
                        const usersAdDetails = yield this.mSGraphApiClient.getUsersDetails(delegateeUserIds);
                        delegateeUsersDetails = usersAdDetails.map(user => {
                            var _a;
                            return ({
                                firstName: user.givenName,
                                lastName: user.surname,
                                email: ((_a = user === null || user === void 0 ? void 0 : user.mail) === null || _a === void 0 ? void 0 : _a.toLowerCase()) || null,
                                title: user.jobTitle || null,
                                loginId: user.userType === enums_1.AD_USER_TYPE.GUEST
                                    ? user.mail.toLowerCase()
                                    : user.userPrincipalName.toLowerCase(),
                            });
                        });
                    }
                    for (const task of tasks) {
                        let users = [];
                        if (task.type === 'delegate') {
                            const delegatee = delegateeUsersDetails.find(user => { var _a; return ((_a = task.assigned_to) === null || _a === void 0 ? void 0 : _a.delegate_to_username) === user.loginId; });
                            if (delegatee) {
                                users = [delegatee];
                            }
                            else {
                                continue;
                            }
                        }
                        else {
                            users = otherInfo.usersDetail;
                        }
                        const fullTaskLink = this.createFullUrlForTask(otherInfo.approvalType, proposalDetail, task.task_id);
                        yield this.sendTaskAssignmentNotification(users, proposalDetail, fullTaskLink, isEmailApprover, task.task_id, '');
                    }
                }
                i++;
            }
        });
    }
    createTask(afeProposal, approver, entityId, currentContext, isMailApprovalTask, approvalType, actionType) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, assignedTo, assginedType, assignedLevel, otherInfo } = approver;
            const existingTasks = yield this.taskApiClient.getAllTasks(id, enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK);
            if (existingTasks === null || existingTasks === void 0 ? void 0 : existingTasks.length) {
                yield this.afeProposalApproverRepository.updateApproverStatus(id, enums_1.APPROVER_STATUS.IN_PROGRESS, currentContext);
                yield this.changeAfeStatusToInprogress(afeProposal.id, currentContext, actionType);
                return;
            }
            const { uiClient } = this.configService.getAppConfig();
            let businessEntityId = entityId;
            if (assginedType !== associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER && assginedType !== associated_type_enum_1.ASSOCIATED_TYPE.USER) {
                const associatedLevelEntity = yield this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(entityId, assignedLevel);
                businessEntityId = associatedLevelEntity.id;
            }
            const payload = {
                title: this.createTaskTitle(afeProposal.projectReferenceNumber, otherInfo.approvalType),
                assigned_to: assignedTo,
                entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                entity_id: `${id}`,
                is_group_assignemnt: assginedType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE,
                business_entity_id: businessEntityId,
                base_url: uiClient.baseUrl,
                rel_url: this.createRelativeUrlForTask(otherInfo.approvalType, afeProposal),
                additional_info: {
                    proposal_id: afeProposal.id,
                    proposal_project_reference_number: afeProposal.projectReferenceNumber,
                    proposal_created_by: afeProposal.submitterId,
                    proposal_request_type: afeProposal.afeRequestType.title,
                    proposal_entity: afeProposal.entityTitle,
                    proposal_submitted_on: afeProposal.createdOn,
                    proposal_project_name: afeProposal.name,
                    approval_type: approvalType,
                    totalAmount: afeProposal.totalAmount
                },
                delegate_filter_param: {
                    request_type_id: afeProposal.afeRequestTypeId,
                },
            };
            yield this.afeProposalApproverRepository.updateApproverStatus(id, enums_1.APPROVER_STATUS.IN_PROGRESS, currentContext);
            yield this.changeAfeStatusToInprogress(afeProposal.id, currentContext, actionType);
            const tasks = yield this.taskApiClient.createTaskWithUseDelegation(payload);
            const delegateeUserIds = tasks
                .filter(task => { var _a; return task.type === 'delegate' && ((_a = task.assigned_to) === null || _a === void 0 ? void 0 : _a.delegate_to_username); })
                .map(task => task.assigned_to.delegate_to_username);
            let delegateeUsersDetails = [];
            if (delegateeUserIds.length) {
                const usersAdDetails = yield this.mSGraphApiClient.getUsersDetails(delegateeUserIds);
                delegateeUsersDetails = usersAdDetails.map(user => {
                    var _a;
                    return ({
                        firstName: user.givenName,
                        lastName: user.surname,
                        email: ((_a = user === null || user === void 0 ? void 0 : user.mail) === null || _a === void 0 ? void 0 : _a.toLowerCase()) || null,
                        title: user.jobTitle || null,
                        loginId: user.userType === enums_1.AD_USER_TYPE.GUEST
                            ? user.mail.toLowerCase()
                            : user.userPrincipalName.toLowerCase(),
                    });
                });
            }
            for (const task of tasks) {
                let users = [];
                if (task.type === 'delegate') {
                    const delegatee = delegateeUsersDetails.find(user => { var _a; return ((_a = task.assigned_to) === null || _a === void 0 ? void 0 : _a.delegate_to_username) === user.loginId; });
                    if (delegatee) {
                        users = [delegatee];
                    }
                    else {
                        continue;
                    }
                }
                else {
                    users = otherInfo.usersDetail;
                }
                const fullTaskLink = this.createFullUrlForTask(otherInfo.approvalType, afeProposal, task.task_id);
                yield this.sendTaskAssignmentNotification(users, afeProposal, fullTaskLink, isMailApprovalTask, task.task_id, '');
                yield this.createTaskAssignmentNotificationsForNextApprover({
                    id: afeProposal.id,
                    afeReferenceNumber: afeProposal.projectReferenceNumber,
                    requestTypeId: afeProposal.afeRequestTypeId,
                }, task.task_id, users.map(user => user.loginId.toLowerCase()), currentContext, actionType);
            }
        });
    }
    createTaskAssignmentNotificationsForNextApprover(afeDetails, taskId, subscribers, currentContext, actionType = null) {
        return __awaiter(this, void 0, void 0, function* () {
            let url = `${(0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_URLS.AFE_TASK_URL, {
                afeId: `${afeDetails.id}`,
                taskId: `${taskId}`,
            })}`;
            if (actionType && actionType === enums_1.TASK_ACTION.SEND_BACK) {
                url = `${(0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_URLS.AFE_SEND_BACK_URL, {
                    afeRequestType: `${mappings_1.AFE_REQUEST_TYPE_ID_URL_MAPPING[afeDetails.requestTypeId]}`,
                    afeId: `${afeDetails.id}`,
                    taskId: `${taskId}`,
                })}`;
            }
            const expireAt = (0, helpers_1.getNotificationExpiryDate)();
            const type = enums_1.NOTIFICATION_TYPE.INFO;
            const payload = {
                title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_TASK_ASSIGNMENT, {
                    afeReferenceNumber: afeDetails.afeReferenceNumber,
                }),
                url: url,
                subscribers: subscribers,
                expireAt,
                type,
            };
            yield this.notificationRepository.createNotification(payload, currentContext);
        });
    }
    createRelativeUrlForTask(approvalType, afeProposal) {
        switch (approvalType) {
            case enums_1.APPROVAL_TYPE.RESUBMISSION:
                return `/afe/submit-afe/${mappings_1.AFE_REQUEST_TYPE_ID_URL_MAPPING[afeProposal.afeRequestTypeId]}/${afeProposal.id}?taskId={taskId}#resubmit`;
            case enums_1.APPROVAL_TYPE.MORE_DETAIL:
            case enums_1.APPROVAL_TYPE.APPROVAL:
                return `/mytask/task/${afeProposal.id}?taskId={taskId}`;
        }
    }
    createFullUrlForTask(approvalType, afeProposal, taskId) {
        const config = this.configService.getAppConfig();
        switch (approvalType) {
            case enums_1.APPROVAL_TYPE.RESUBMISSION:
                return `${config.uiClient.baseUrl}/afe/submit-afe/${mappings_1.AFE_REQUEST_TYPE_ID_URL_MAPPING[afeProposal.afeRequestTypeId]}/${afeProposal.id}?taskId=${taskId}#resubmit`;
            case enums_1.APPROVAL_TYPE.MORE_DETAIL:
            case enums_1.APPROVAL_TYPE.APPROVAL:
                return `${config.uiClient.baseUrl}/mytask/task/${afeProposal.id}?taskId=${taskId}`;
        }
    }
    createTaskTitle(projectReferenceNumber, approvalType) {
        if (approvalType === enums_1.APPROVAL_TYPE.MORE_DETAIL) {
            return `${projectReferenceNumber} - More Detail Required`;
        }
        else if (approvalType === enums_1.APPROVAL_TYPE.RESUBMISSION) {
            return `${projectReferenceNumber} - AFE Resubmission Required`;
        }
        else {
            return `${projectReferenceNumber} - Approval Required`;
        }
    }
    sendTaskAssignmentNotification(usersDetail, afeProposal, taskLink, isMailApprovalTask, approvalTaskId, subjectPrefix = '') {
        return __awaiter(this, void 0, void 0, function* () {
            const receivers = usersDetail.map(user => user.email).filter(user => !!user);
            const { id: proposalId } = afeProposal;
            const config = this.configService.getAppConfig();
            const placeholdersValues = {
                taskLink: taskLink,
                afeDetailLink: `${config.uiClient.baseUrl}/afe/afe-detail/${proposalId}`,
            };
            if (receivers.length) {
                yield this.sharedNotificationService.sendNotificationForAfeProposal(afeProposal.id, afeProposal.id, enums_1.NOTIFICATION_ENTITY_TYPE.AFE_PROPOSAL_TASK_NOTIFICATION, { to: receivers }, isMailApprovalTask ? 'AFE.TASK.ASSIGNMENT.APPROVER' : 'AFE.TASK.ASSIGNMENT.OTHER', isMailApprovalTask, placeholdersValues, approvalTaskId, subjectPrefix);
            }
        });
    }
    getAllPendingUserTasks(currentContext, assignedTo = null) {
        return __awaiter(this, void 0, void 0, function* () {
            let { username } = currentContext.user;
            if (assignedTo) {
                const hasAdminPermission = yield this.adminApiClient.hasPermissionToUser(username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
                if (!hasAdminPermission) {
                    throw new exceptions_1.HttpException(`You don't have permission to search task for other users.`, enums_1.HttpStatus.UNAUTHORIZED);
                }
                username = assignedTo;
            }
            const data = yield this.taskApiClient.getAllPendingUserTasks(username);
            return data.map(d => (0, helpers_1.instanceToPlain)(new dtos_1.TaskDetailResponseDto(d)));
        });
    }
    getTaskDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const task = yield this.taskApiClient.getTaskById(id);
            return (0, helpers_1.instanceToPlain)(new dtos_1.TaskDetailResponseDto(task));
        });
    }
    getCurrentTaskOfUserByAfeId(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username } = currentContext.user;
            if (taskId) {
                const task = yield this.taskApiClient.getTaskById(taskId);
                if (!task || (task === null || task === void 0 ? void 0 : task.task_status) !== 'Not Started') {
                    throw new exceptions_1.HttpException(`Task not found.`, enums_1.HttpStatus.NOT_FOUND);
                }
                const { assigned_to, is_group_assignment, entity_id, business_entity_id, additional_info } = task;
                if (afeProposalId !== +(additional_info === null || additional_info === void 0 ? void 0 : additional_info.proposal_id)) {
                    throw new exceptions_1.HttpException(`Task doesn't belong to correct AFE Proposal'`, enums_1.HttpStatus.BAD_REQUEST);
                }
                if (is_group_assignment) {
                    const isUserInGroup = yield this.adminApiClient.hasUserRole(username, assigned_to, business_entity_id);
                    if (!isUserInGroup) {
                        throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                    }
                }
                else if (assigned_to !== username) {
                    throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                }
                const step = yield this.afeProposalApproverRepository.getApproverById(entity_id);
                if (step.actionStatus !== enums_1.APPROVER_STATUS.IN_PROGRESS) {
                    throw new exceptions_1.HttpException(`Task is not in progress.`, enums_1.HttpStatus.BAD_REQUEST);
                }
                return (0, helpers_1.singleObjectToInstance)(dtos_1.CurrentTaskOfUserResponseDto, {
                    id: step.id,
                    approvalType: step.otherInfo.approvalType,
                });
            }
            else {
                const approversList = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
                const currentUserSteps = yield this.getCurrentTaskSteps(currentContext.user.username, approversList);
                if (!currentUserSteps.length) {
                    throw new exceptions_1.HttpException('Forbidden', enums_1.HttpStatus.FORBIDDEN);
                }
                for (let step of currentUserSteps) {
                    const { id, otherInfo } = step;
                    if (taskId) {
                        const task = yield this.taskApiClient.getTaskById(taskId);
                        if (task.entity_id === id) {
                            return (0, helpers_1.singleObjectToInstance)(dtos_1.CurrentTaskOfUserResponseDto, {
                                id: id,
                                approvalType: otherInfo.approvalType,
                            });
                        }
                    }
                }
                throw new exceptions_1.HttpException('Forbidden', enums_1.HttpStatus.FORBIDDEN);
            }
        });
    }
    taskApprovalByTaskId(taskId, userId, action) {
        return __awaiter(this, void 0, void 0, function* () {
            const task = yield this.taskApiClient.getTaskById(+taskId);
            if (!task || (task === null || task === void 0 ? void 0 : task.task_status) !== 'Not Started') {
                throw new exceptions_1.HttpException(`Task doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const taskApprovalStep = yield this.afeProposalApproverRepository.getApproverById(task.entity_id);
            if (taskApprovalStep.actionStatus !== enums_1.APPROVER_STATUS.IN_PROGRESS) {
                throw new exceptions_1.HttpException(`Task is not in progress state.`, enums_1.HttpStatus.CONFLICT);
            }
            const { givenName: given_name, surname: family_name, userPrincipalName: upn, displayName: name, userType, mail, } = yield this.mSGraphApiClient.getUserDetails(userId);
            const username = userType == enums_1.AD_USER_TYPE.GUEST ? mail.toLowerCase() : upn.toLowerCase();
            const currentContextUser = {
                family_name,
                given_name,
                name,
                unique_name: username,
                username,
                upn,
            };
            let comments;
            switch (action) {
                case enums_1.TASK_ACTION.APPROVE:
                    comments = 'Approved by Email';
                    break;
                case enums_1.TASK_ACTION.REJECT:
                    comments = 'Rejected by Email';
                    break;
            }
            const approvers = yield this.afeProposalApproverRepository.getApproversByProposalId(taskApprovalStep.afeProposalId);
            const afeDetails = yield this.afeProposalRepository.getAfeProposalById(taskApprovalStep.afeProposalId);
            const { steps: latestWorkflowSteps, masterSettingWorkflows } = yield this.getLatestWorkflowApproversList(taskApprovalStep.afeProposalId);
            const alreadyTraverseApproverIds = new Set();
            const updateUsersDetails = latestWorkflowSteps
                .map(step => {
                var _a;
                const approver = approvers.find(a => (step === null || step === void 0 ? void 0 : step.stepId) &&
                    a.workflowMasterStepsId === (step === null || step === void 0 ? void 0 : step.stepId) &&
                    !alreadyTraverseApproverIds.has(a.id));
                if (!approver) {
                    return null;
                }
                alreadyTraverseApproverIds.add(approver.id);
                return {
                    id: approver.id,
                    user: step.approvers,
                    assignedTo: (step.associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER ||
                        step.associateType === associated_type_enum_1.ASSOCIATED_TYPE.USER) &&
                        ((_a = step === null || step === void 0 ? void 0 : step.approvers) === null || _a === void 0 ? void 0 : _a.length)
                        ? step.approvers[step.approvers.length - 1].loginId
                        : null,
                };
            })
                .filter(step => step !== null);
            yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.afeProposalApproverRepository.updateUserDetailsByApproverIds(updateUsersDetails, {
                    user: currentContextUser,
                });
                const updatedApprovers = yield this.addNewWorkflowSteps(approvers, latestWorkflowSteps, {
                    user: currentContextUser,
                });
                yield this.updateAfeProposalLimitDeduction(taskApprovalStep.afeProposalId, masterSettingWorkflows, { user: currentContextUser });
                yield this.performApprovalAction(action, afeDetails, taskApprovalStep, updatedApprovers, { user: currentContextUser }, task, comments);
            }));
            return { message: 'Action has been executed successfully.' };
        });
    }
    sendReminder(approverId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username } = currentContext.user;
            const existingTasks = yield this.taskApiClient.getAllTasks(approverId, enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK);
            if (!(existingTasks === null || existingTasks === void 0 ? void 0 : existingTasks.length)) {
                throw new exceptions_1.HttpException('The user has already approved the task or the task has not yet been assigned.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            const approvers = yield this.afeProposalApproverRepository.getApproverById(approverId);
            if (!approvers) {
                throw new exceptions_1.HttpException('Invalid Request!.', enums_1.HttpStatus.BAD_REQUEST);
            }
            const proposalId = approvers.afeProposalId;
            const afeProposal = yield this.afeProposalRepository.getAfeProposalById(proposalId);
            if (afeProposal.submitterId.toLowerCase() !== username.toLowerCase()) {
                throw new exceptions_1.HttpException('You do not have permission to send this AFE reminder!', enums_1.HttpStatus.BAD_REQUEST);
            }
            if (!afeProposal) {
                throw new exceptions_1.HttpException('Invalid Request!.', enums_1.HttpStatus.BAD_REQUEST);
            }
            for (const task of existingTasks) {
                let users = [];
                if (task.delegated_from_task_id) {
                    if (task.assigned_to) {
                        const assigninedUser = yield this.mSGraphApiClient.getUsersDetails([task.assigned_to]);
                        const assignedUserDetail = assigninedUser.map((assignee) => {
                            return {
                                firstName: assignee.givenName,
                                lastName: assignee.surname,
                                title: assignee.jobTitle,
                                email: assignee.mail,
                                loginId: assignee.userType == enums_1.AD_USER_TYPE.GUEST
                                    ? assignee.mail.toLowerCase()
                                    : assignee.userPrincipalName.toLowerCase(),
                            };
                        });
                        users = assignedUserDetail;
                    }
                }
                else {
                    users = approvers.otherInfo.usersDetail;
                }
                const fullTaskLink = this.createFullUrlForTask(approvers.otherInfo.approvalType, afeProposal, task.id);
                const isMailApprovalTask = approvers.otherInfo.approvalType !== enums_1.APPROVAL_TYPE.MORE_DETAIL;
                yield this.sendTaskAssignmentNotification(users, afeProposal, fullTaskLink, isMailApprovalTask, task.id, 'Reminder - ');
            }
            return {
                message: 'Reminder Sent Successfully!',
            };
        });
    }
};
TaskService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.TaskApiClient,
        repositories_1.AfeProposalRepository,
        repositories_1.AfeProposalApproverRepository,
        repositories_1.AfeProposalAmountSplitRepository,
        services_3.WorkflowService,
        config_service_1.ConfigService,
        clients_1.AdminApiClient,
        repositories_1.AfeProposalLimitDeductionRepository,
        helpers_1.DatabaseHelper,
        services_2.SharedAttachmentService,
        clients_1.HistoryApiClient,
        repositories_2.NotificationRepository,
        clients_1.MSGraphApiClient,
        repositories_3.QueueLogRepository,
        services_2.SharedNotificationService,
        services_1.FinanceAdminService,
        parallel_identifier_repository_1.ParallelIdentifierRepository])
], TaskService);
exports.TaskService = TaskService;
//# sourceMappingURL=task.service.js.map