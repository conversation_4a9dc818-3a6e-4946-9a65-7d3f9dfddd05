<ng-container *ngIf="formReady; else loadingPage">
  <div class="w-100 py-5 bg-body rounded" [ngClass]="{'px-5': !isDryRun}">
    <div *ngIf="
        !isDryRun &&
        currencyDetail &&
        !currencyDetail.primary &&
        projectComponentList.length
      " class="d-flex justify-content-end w-100 px-0 pb-2">
      <span class="fw-bold">{{ "COMMON.EXCHANGE_RATE" | translate }} &nbsp;</span>
      <span class="ml-1 badge label-light-primary px-5 fs-8 fw-bold">
        1 USD - {{ currencyDetail.conversionRate }}
        {{ currencyDetail.currency }}
      </span>
    </div>

    <div class="fv-row" *ngIf="totalProjectComponentLeft()">
      <div class="col-12 border rounded p-5 mb-5">
        <div class="pb-1 pb-lg-1 border-bottom mb-5">
          <h3 class="fw-bolder d-flex align-items-center" translate="AFE_MODULE.SECTION_TITLE.PROJECT_COMPONENT"></h3>
        </div>

        <label class="form-label required fw-bold pb-1">
          {{ "FORM.PLACEHOLDER.SELECT" | translate }}
          {{ "FORM.LABEL.PROJECT_COMPONENT" | translate }}
        </label>
        <select [(ngModel)]="projectComponentSelected" name="projectComponent"
          class="form-select form-select-lg form-select-solid" (change)="onAddProjectComponent()">
          <option value="">
            {{ "FORM.PLACEHOLDER.SELECT" | translate }}
            {{ "FORM.LABEL.PROJECT_COMPONENT" | translate }}
          </option>
          <ng-container *ngFor="let projectComponent of projectComponentList">
            <option *ngIf="projectComponent.active" [value]="+projectComponent.id">
              {{ projectComponent.title }}
            </option>
          </ng-container>
        </select>

        <ng-container *ngIf="isRequiredError()">
          <app-input-error-message errorMessage=" {{
              'FORM.VALIDATION.MIN_PROJECT_COMPONENT' | translate
            }}">
          </app-input-error-message>
        </ng-container>
      </div>
    </div>

    <div *ngIf="projectComponentList.length" [formGroup]="form">
      <div *ngIf="projectComponentSplit.controls.length" formArrayName="projectComponentSplit">
        <div class="col-12 border rounded p-5">
          <div class="pb-1 pb-lg-1 border-bottom mb-5">
            <h3 class="fw-bolder d-flex align-items-center"
              translate="AFE_MODULE.SECTION_TITLE.PROJECT_COMPONENT_AMOUNT"></h3>
          </div>

          <div *ngFor="
              let projectComponent of projectComponentSplit.controls;
              let i = index
            " [formGroupName]="i">
            <div class="fv-row mt-7">
              <div class="d-flex justify-content-between mb-1">
                <label class="form-label fw-bold">
                  {{ projectComponent.value.title }}
                  ({{ "COMMON.AMOUNT" | translate }}
                  {{ "COMMON.IN" | translate }} {{ currencyDetail.currency }})
                </label>
              </div>
              <div class="d-flex justify-content-between">
                <div class="w-100">
                  <app-input-amount type="currency" formControlName="amount" placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                    'COMMON.AMOUNT' | translate
                  }} {{ 'COMMON.IN' | translate }} {{
                    currencyDetail.currency
                  }}"></app-input-amount>
                </div>

                <div class="align-self-center px-2">
                  <a class="text-right cursor-pointer " (click)="
                      changeProjectComponentStatus(projectComponent.value, i)
                    ">
                    <img width="32px" src="./assets/media/svg/icons/dpw-icons/cross2.png" alt="Next" />
                  </a>
                </div>
              </div>
              <ng-container *ngIf="
                  projectComponent.get('amount')?.hasError('min') &&
                  (isSubmitted || projectComponent.get('amount')?.touched)
                ">
                <app-input-error-message errorMessage="{{ 'FORM.VALIDATION.MIN_AMOUNT' | translate }}">
                </app-input-error-message>
              </ng-container>

              <ng-container *ngIf="
              projectComponent.get('amount')?.hasError('max') && 
                  (isSubmitted || projectComponent.get('amount')?.touched)
                ">
                <app-input-error-message errorMessage="{{ 'FORM.VALIDATION.MAX_AMOUNT' | translate }}">
                </app-input-error-message>
              </ng-container>

              <ng-container *ngIf="
                  projectComponent.get('amount')?.hasError('required') &&
                  (isSubmitted || projectComponent.get('amount')?.touched)
                ">
                <app-input-error-message errorMessage="{{
                    'FORM.VALIDATION.REQUIRED_FIELD' | translate
                  }}">
                </app-input-error-message>
              </ng-container>
            </div>
          </div>
        </div>

        <ng-container *ngIf="parentProjectComponentInfo?.length">
          <!-- <div class="separator separator-dashed separator-border-1 my-3"></div> -->
          <div class="col-12 border rounded p-5 mb-5 mt-5">
            <div class="pb-1 pb-lg-1 border-bottom">
              <h3 class="fw-bolder d-flex align-items-center" translate="AFE_MODULE.SECTION_TITLE.AFE_APPROVED_VALUE">
              </h3>
            </div>
            <div class="fv-row mb-2" *ngIf="parentProjectComponentInfo?.length">
              <div class="row mt-5">
                <div class="col-lg-6 col-sm-6 mb-2 mb-lg-2" *ngFor="let projectComponent of parentProjectComponentInfo;let i = index">
                  <label class="text-gray-600 fw-bold">
                    {{ projectComponent.title }}
                    ({{ "COMMON.AMOUNT" | translate }}
                    {{ "COMMON.IN" | translate }} {{ projectComponent.currency }})
                  </label>
                  <div class="h5 text-gray-800">
                    {{
                    projectComponent.amount
                      | currency: projectComponent.currency:"symbol":"1.2-2"
                  }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #loadingPage>
  <app-form-skeleton-loader></app-form-skeleton-loader>
</ng-template>
