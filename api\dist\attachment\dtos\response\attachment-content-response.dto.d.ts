/// <reference types="node" />
export declare class BufferData {
    type: string;
    data: Buffer;
}
export declare class AttachmentContentResponseDto {
    id: number;
    attachment_name?: string;
    entity_id: number;
    meta_data_1?: string;
    meta_data_2?: string;
    meta_data_3?: string;
    description?: string;
    additional_info?: object;
    file_id?: string;
    contents: BufferData;
    attachment_content_type: string;
    created_by: string;
    created_on: Date;
}
