import { BaseModel } from 'src/shared/models';
import { AnalysisCode } from './analysis-code.model';
import { CostCenter } from './cost-center.model';
import { NaturalAccountNumber } from './natural-account-number.model';
export declare class CompanyCode extends BaseModel<CompanyCode> {
    code: string;
    name: string;
    entityId: number;
    entityCode: string;
    entityType: string;
    fusionIntegrationForRequestTypeIds: number[];
    enableMultiNaturalAccount: boolean;
    costCenters: CostCenter[];
    naturalAccountNumbers: NaturalAccountNumber[];
    analysisCodes: AnalysisCode[];
}
