"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSharedChildLimitRequestDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdateSharedChildLimitRequestDTO {
}
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Child level entity id to whom limit is shared.'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], UpdateSharedChildLimitRequestDTO.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent level step id who shared the limit.'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], UpdateSharedChildLimitRequestDTO.prototype, "stepId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Both single and aggregate limit should not be blank.'
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateSharedChildLimitRequestDTO.prototype, "singleLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Both single and aggregate limit should not be blank.'
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateSharedChildLimitRequestDTO.prototype, "aggregateLimit", void 0);
exports.UpdateSharedChildLimitRequestDTO = UpdateSharedChildLimitRequestDTO;
//# sourceMappingURL=update-shared-child-limit-request.dto.js.map