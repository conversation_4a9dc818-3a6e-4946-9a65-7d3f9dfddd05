import { BaseRepository } from 'src/shared/repositories';
import { ProjectComponent } from '../models';
export declare class ProjectComponentRepository extends BaseRepository<ProjectComponent> {
    constructor();
    getProjectComponentsByRequestId(afeRequestId: number, childProject?: boolean, budgetTypeId?: number): Promise<ProjectComponent[] | null>;
    getProjectComponentsByIds(ids: number[]): Promise<ProjectComponent[] | null>;
    getAllProjectComponents(): Promise<ProjectComponent[] | null>;
}
