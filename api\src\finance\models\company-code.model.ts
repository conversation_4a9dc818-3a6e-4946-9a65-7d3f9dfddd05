import { Column, DataType, HasMany, Table } from 'sequelize-typescript';
import { BaseModel } from 'src/shared/models';
import { AnalysisCode } from './analysis-code.model';
import { CostCenter } from './cost-center.model';
import { NaturalAccountNumber } from './natural-account-number.model';

@Table({ tableName: 'company_codes' })
export class CompanyCode extends BaseModel<CompanyCode> {
	@Column({ type: DataType.STRING, allowNull: false })
	public code: string;

	@Column({ type: DataType.STRING, allowNull: false })
	public name: string;

	@Column({ field: 'entity_id', type: DataType.INTEGER, allowNull: false })
	public entityId: number;

	@Column({ field: 'entity_code', type: DataType.STRING, allowNull: false })
	public entityCode: string;

	@Column({ field: 'entity_type', type: DataType.STRING, allowNull: false })
	public entityType: string;

	@Column({ field: 'fusion_for_request_type_ids', type: DataType.JSONB, allowNull: true })
	public fusionIntegrationForRequestTypeIds: number[];

	@Column({ field: 'allow_multiple_natual_accounts', type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
	public enableMultiNaturalAccount: boolean;

	@HasMany(() => CostCenter)
	public costCenters: CostCenter[];

	@HasMany(() => NaturalAccountNumber)
	public naturalAccountNumbers: NaturalAccountNumber[];

	@HasMany(() => AnalysisCode)
	public analysisCodes: AnalysisCode[];


}
