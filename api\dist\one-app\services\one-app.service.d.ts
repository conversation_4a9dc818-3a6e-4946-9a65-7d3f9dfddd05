import { AfeProposalAmountSplitRepository, AfeProposalApproverRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { ConfigService } from 'src/config/config.service';
import { CompanyCodeRepository, CostCenterRepository } from 'src/finance/repositories';
import { AdminApiClient, AttachmentApiClient, MSGraphApiClient, TaskApiClient } from 'src/shared/clients';
import { BUDGET_TYPE_TITLE, TASK_ACTION } from 'src/shared/enums';
import { AfeProposalValidator } from 'src/shared/validators';
import { TaskService } from 'src/task/services';
import { GetAfeAttachmentRequestDto, GetAfeDetailRequestDto, GetAfeDetailResponseDTO, GetAFEHistoryResponseDTO, GetApproversForMoreDetailsResponseDTO, GetAttachmentDTO, MaximoRequestDto, TaskListResponseDTO, UserEmailRequestDto } from '../dtos';
export declare class OneAppService {
    private readonly afeProposalRepository;
    private readonly afeProposalAmountSplitRepository;
    private readonly taskApiClient;
    private readonly mSGraphApiClient;
    private readonly attachmentService;
    private readonly configService;
    private readonly adminApiClient;
    private readonly afeProposalValidator;
    private readonly costCenterRepository;
    private readonly afeProposalApproverRepository;
    private readonly taskService;
    private readonly companyCodeRepository;
    constructor(afeProposalRepository: AfeProposalRepository, afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository, taskApiClient: TaskApiClient, mSGraphApiClient: MSGraphApiClient, attachmentService: AttachmentApiClient, configService: ConfigService, adminApiClient: AdminApiClient, afeProposalValidator: AfeProposalValidator, costCenterRepository: CostCenterRepository, afeProposalApproverRepository: AfeProposalApproverRepository, taskService: TaskService, companyCodeRepository: CompanyCodeRepository);
    getAfeTasks(userDetail: UserEmailRequestDto): Promise<TaskListResponseDTO[]>;
    getAfeDetail(afeDetailRequest: GetAfeDetailRequestDto): Promise<GetAfeDetailResponseDTO>;
    getAfeAttachment(getAfeAttachmentRequestDto: GetAfeAttachmentRequestDto): Promise<GetAttachmentDTO>;
    getAfeHistory(getAfeHistoryDto: GetAfeDetailRequestDto): Promise<GetAFEHistoryResponseDTO[]>;
    private getUserEmail;
    getTaskApprovalActions(afeId: number, taskId: number): Promise<string[]>;
    getApproversForMoreDetailTaskAction(afeId: number): Promise<GetApproversForMoreDetailsResponseDTO[]>;
    taskApprovalByTaskId(afeId: number, taskId: number, userId: string, action: TASK_ACTION, comments: string, assigneeId?: string): Promise<string>;
    getUserSubmittedAFEProposal(userId: string, pageSize: number, activePage: number): Promise<GetAfeDetailResponseDTO[]>;
    getUserActionedAFEProposals(userId: string, pageSize: number, activePage: number, type: 'APPROVED' | 'REJECTED' | 'ALL'): Promise<{
        ID: number;
        AFEModule: string;
        AFEType: any;
        BusinessUnit: string;
        BudgetType: BUDGET_TYPE_TITLE;
        ProjectName: string;
        ProjectReferenceNumber: string;
        TotalAmount: string;
        BudgetReferenceNumber: string;
        SubmissionDate: Date;
        ActionDateTime: any;
        AFEStatus: string;
        ApproverRole: any;
        Action: any;
        Comments: any;
        TotalCount: any;
    }[]>;
    private transformAfeProposalsList;
    getAFECompleteDetail(requestData: MaximoRequestDto): Promise<any>;
}
