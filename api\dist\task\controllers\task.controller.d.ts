import { MessageResponseDto } from 'src/shared/dtos';
import { TASK_ACTION } from 'src/shared/enums';
import { RequestContext } from 'src/shared/types';
import { CurrentTaskOfUserResponseDto, PerformActionOnAfeProposalRequestDto } from '../dtos';
import { TaskService } from '../services';
export declare class TaskController {
    private readonly taskService;
    constructor(taskService: TaskService);
    performActionOnAfeAproposalTask(actionType: TASK_ACTION, performActionOnAfeProposalRequestDto: PerformActionOnAfeProposalRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    getAllPendingUserTasks(request: RequestContext, assignedTo?: string): Promise<Record<string, any>>;
    getTaskDetailById(id: number): Promise<Record<string, any>>;
    getCurrentTaskOfUserByAfeId(id: number, request: RequestContext, taskId: number): Promise<CurrentTaskOfUserResponseDto>;
    sendReminder(approverId: number, request: RequestContext): Promise<{
        message: string;
    }>;
}
