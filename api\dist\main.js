"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const express_1 = require("express");
const swagger_1 = require("@nestjs/swagger");
const helmet_1 = __importDefault(require("helmet"));
const express_basic_auth_1 = __importDefault(require("express-basic-auth"));
const app_module_1 = require("./app.module");
const enums_1 = require("./shared/enums");
const config_service_1 = require("./config/config.service");
const pg = __importStar(require("pg"));
function bootstrap() {
    return __awaiter(this, void 0, void 0, function* () {
        const app = yield core_1.NestFactory.create(app_module_1.AppModule);
        const config = app.get(config_service_1.ConfigService).getAppConfig();
        const globalPrefix = 'api';
        app.setGlobalPrefix(globalPrefix);
        app.use((0, express_1.json)({ limit: '200mb' }));
        app.enableCors();
        app.use((0, helmet_1.default)({ crossOriginResourcePolicy: false }));
        app.use(helmet_1.default.noSniff());
        app.use(helmet_1.default.hidePoweredBy());
        app.use(helmet_1.default.contentSecurityPolicy());
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            transform: true,
        }));
        if (process.env.NODE_ENV !== enums_1.ENV.PROD) {
            app.use(['/doc', '/doc-json'], (0, express_basic_auth_1.default)({
                challenge: true,
                users: {
                    [config.swagger.user]: config.swagger.password,
                },
            }));
            const options = new swagger_1.DocumentBuilder()
                .addBearerAuth()
                .setTitle('AFE')
                .setDescription('The AFE service APIs')
                .setVersion('1.0')
                .addTag('afe')
                .build();
            const document = swagger_1.SwaggerModule.createDocument(app, options);
            const swaggerOptions = { swaggerOptions: { persistAuthorization: true } };
            swagger_1.SwaggerModule.setup('doc', app, document, swaggerOptions);
        }
        const port = process.env.PORT || 5100;
        const server = yield app.listen(port, () => {
            common_1.Logger.log(`Listening at http://localhost:${port}/${globalPrefix}`);
        });
        server.setTimeout(600000);
        pg.defaults.parseInputDatesAsUTC = true;
        pg.types.setTypeParser(pg.types.builtins.TIMESTAMP, (stringValue) => new Date(`${stringValue}Z`));
        if (module.hot) {
            module.hot.accept();
            module.hot.dispose(() => app.close());
        }
    });
}
bootstrap();
//# sourceMappingURL=main.js.map