import { Response } from 'express';
import { RequestContext } from 'src/shared/types';
import { AddCostCenterRequestDto, AnalysisCodeResponseDto, CompanyCodeResponseDto, CostCenterResponseDto, CreateAnalysisCodeRequestDto, CreateCompanyCodeRequestDto, CreateNaturalAccountNumberRequestDto, ImportDataRequestDto, NaturalAccountResponseDto, ToggleActiveStateCompanyCodeRequestDto, UpdateAnalysisCodeRequestDto, UpdateCompanyCodeRequestDto, UpdateCostCenterRequestDto, UpdateFusionIntegrationRequestDto, UpdateNaturalAccountNumberRequestDto } from '../dtos';
import { MessageResponseDto } from 'src/shared/dtos';
import { FinanceAdminService } from '../services/finance-admin.service';
import { Pagination } from 'src/core/pagination';
import { ReportService } from 'src/report/services';
import { UploadEvidenceRequestDto } from 'src/afe-proposal/dtos';
export declare class FinanceAdminController {
    private readonly financeAdminService;
    private readonly reportService;
    constructor(financeAdminService: FinanceAdminService, reportService: ReportService);
    createCompanyCode(request: RequestContext, createCompanyCodeRequestDto: CreateCompanyCodeRequestDto): Promise<CompanyCodeResponseDto>;
    updateCompanyCodeDetail(updateCompanyCodeRequestDto: UpdateCompanyCodeRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    deleteCompanyCodeById(id: number, request: RequestContext): Promise<MessageResponseDto>;
    getCompanyCodesListByEntityId(entityId: number, limit?: number, page?: number): Promise<Pagination<CompanyCodeResponseDto>>;
    getEntityActiveCompanyCodeDetails(entityId: number): Promise<CompanyCodeResponseDto>;
    addCostCenter(request: RequestContext, addCostCenterRequestDto: AddCostCenterRequestDto): Promise<CostCenterResponseDto>;
    getCostCentersByCompanyCodeId(companyId: number, limit?: number, page?: number): Promise<Pagination<CostCenterResponseDto>>;
    createAnalysisCode(request: RequestContext, createCompanyCodeRequestDto: CreateAnalysisCodeRequestDto): Promise<AnalysisCodeResponseDto>;
    getAnalsysisCodesByCompanyCodeId(companyId: number, limit?: number, page?: number): Promise<Pagination<AnalysisCodeResponseDto>>;
    createNatualAccountNumber(request: RequestContext, createNaturalAccountNumberRequestDto: CreateNaturalAccountNumberRequestDto): Promise<NaturalAccountResponseDto>;
    getNaturalAccountNumbersByCompanyCodeId(companyId: number, limit?: number, page?: number): Promise<Pagination<NaturalAccountResponseDto>>;
    updateCostCenterDetail(updateCostCenterRequestDto: UpdateCostCenterRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    updateFusionIntegration(updateFusionIntegrationRequestDto: UpdateFusionIntegrationRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    updateAnalysisCodeDetail(updateAnalysisCodeRequestDto: UpdateAnalysisCodeRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    updateNaturalAccountNumber(updateNaturalAccountNumberRequestDto: UpdateNaturalAccountNumberRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    deleteCostCenterById(id: number, request: RequestContext): Promise<MessageResponseDto>;
    deleteAnalysisCodeById(id: number, request: RequestContext): Promise<MessageResponseDto>;
    deleteNaturalAccountNumberById(id: number, request: RequestContext): Promise<MessageResponseDto>;
    toggleActiveStateOfCompany(request: RequestContext, toggleActiveStateCompanyCodeRequestDto: ToggleActiveStateCompanyCodeRequestDto): Promise<MessageResponseDto>;
    deactivateCompanyCode(companyId: number, request: RequestContext): Promise<MessageResponseDto>;
    downloadAnalysisCode(res: Response, companyId: number): Promise<Response<any, Record<string, any>>>;
    downloadCostCenter(res: Response, companyId: number): Promise<Response<any, Record<string, any>>>;
    downloadNaturalAccount(res: Response, companyId: number): Promise<Response<any, Record<string, any>>>;
    importAnalysisCode(request: RequestContext, importDataRequestDto: ImportDataRequestDto): Promise<MessageResponseDto>;
    importNaturalAccount(request: RequestContext, importDataRequestDto: ImportDataRequestDto): Promise<MessageResponseDto>;
    importCostCenter(request: RequestContext, importDataRequestDto: ImportDataRequestDto): Promise<MessageResponseDto>;
    getCompanyHistory(entityId: number): Promise<Record<string, any>>;
    getCostCenterHistory(costCenterId: number): Promise<Record<string, any>>;
    uploadEvidence(companyId: number, request: RequestContext, evidences: UploadEvidenceRequestDto): Promise<{
        message: string;
    }>;
    getEvidences(companyId: number): Promise<import("../../attachment/dtos").AttachmentContentResponseDto[]>;
}
