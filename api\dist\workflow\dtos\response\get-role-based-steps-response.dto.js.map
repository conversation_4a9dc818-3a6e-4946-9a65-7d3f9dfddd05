{"version": 3, "file": "get-role-based-steps-response.dto.js", "sourceRoot": "", "sources": ["../../../../src/workflow/dtos/response/get-role-based-steps-response.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,yDAA2C;AAC3C,iDAAkE;AAClE,qFAAwE;AAExE,MAAa,qBAAqB;CAIjC;AADA;IAFI,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAChC,IAAA,0BAAM,GAAE;;oDACY;AAHtB,sDAIC;AAED,MAAa,2BAA2B;CAqCvC;AAlCA;IAFI,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAChC,IAAA,0BAAM,GAAE;;kEACoB;AAI7B;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;+DACsB;AAI/B;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;uEACyB;AAIlC;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;6DACe;AAIxB;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;+DACiB;AAI1B;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;gEACkB;AAI3B;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;+DACiB;AAI1B;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;8BACgB,qBAAqB;qEAAC;AAI/C;IAFI,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAChC,IAAA,0BAAM,GAAE;;yDACW;AAnCrB,kEAqCC;AAED,MAAa,yBAAyB;CA0DrC;AAtDA;IAFI,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAChC,IAAA,0BAAM,GAAE;;qDACS;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;uEAC2B;AAIpC;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;wDACY;AAIrB;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;0EAC8B;AAIvC;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;iEACqB;AAI9B;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;gEACoB;AAI7B;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;;gEAC6B;AAItC;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;;mEACkC;AAI3C;IAFC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC7B,IAAA,0BAAM,GAAE;;iEACqB;AAI9B;IAFI,IAAA,qBAAW,GAAE;IAChB,IAAA,0BAAM,GAAE;8BACS,IAAI;4DAAC;AAIvB;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;8BACS,IAAI;4DAAC;AAIvB;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;;4DACgB;AAIzB;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;;4DACgB;AAIzB;IAFI,IAAA,qBAAW,GAAE;IAChB,IAAA,0BAAM,GAAE;8BACqB,2BAA2B;wEAAC;AAxD3D,8DA0DC;AAGD,MAAa,qCAAqC;CAYjD;AATA;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;;wEACS;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,yBAAyB,EAAE,CAAC;IAC/D,IAAA,0BAAM,GAAE;;sEAC4B;AAIrC;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAM,GAAE;;oEACK;AAXf,sFAYC"}