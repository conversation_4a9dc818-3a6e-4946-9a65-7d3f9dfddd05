import { AdminApiClient, RequestApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { SharedDelegationService } from 'src/shared/services';
import { CurrentContext } from 'src/shared/types';
import { AddRepresentativeRequestDto } from '../dtos';
import { UpdateRepresentativeRequestDto } from '../dtos/request/update-representative-request.dto';
export declare class RepresentativeService {
    private readonly requestApiClient;
    private readonly adminApiClient;
    private readonly sharedDelegationService;
    constructor(requestApiClient: RequestApiClient, adminApiClient: AdminApiClient, sharedDelegationService: SharedDelegationService);
    addRepresentative(addRepresentativeRequestDto: AddRepresentativeRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateUpcomingRepresentative(representativePayload: UpdateRepresentativeRequestDto, currentContext: CurrentContext): Promise<any>;
    getRepresentativesOfUser(currentContext: CurrentContext, filterQuery?: any): Promise<any>;
    deleteRepresentativeById(id: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    ifDelegationAlreadyAdded(username: string, fromDate: Date, toDate: Date, editDelegationId?: number): Promise<boolean>;
}
