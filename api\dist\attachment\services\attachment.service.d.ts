import { AfeProposalApproverRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { CurrentContext } from 'src/shared/types';
import { AfeProposalValidator } from 'src/shared/validators';
import { AttachmentContentResponseDto } from '../dtos';
import { DraftAfeRepository } from 'src/afe-draft/repositories';
export declare class AttachmentService {
    private readonly attachmentApiClient;
    private readonly afeProposalRepository;
    private readonly afeProposalValidator;
    private readonly draftAfeRepository;
    private readonly afeProposalApproverRepository;
    constructor(attachmentApiClient: AttachmentApiClient, afeProposalRepository: AfeProposalRepository, afeProposalValidator: AfeProposalValidator, draftAfeRepository: DraftAfeRepository, afeProposalApproverRepository: AfeProposalApproverRepository);
    getContentByFileId(fileId: string, currentContext: CurrentContext, taskId?: number): Promise<AttachmentContentResponseDto>;
    getProposalAttachmentMetaData(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<AttachmentContentResponseDto[]>;
    getAllApproversAttachmentMetaData(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<AttachmentContentResponseDto[]>;
    getApproverAttachmentMetaData(approverId: number): Promise<AttachmentContentResponseDto[]>;
}
