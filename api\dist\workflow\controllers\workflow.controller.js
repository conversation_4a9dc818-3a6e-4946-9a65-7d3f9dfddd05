"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../dtos");
const workflow_service_1 = require("../services/workflow.service");
const decorators_1 = require("../../core/decorators");
let WorkflowController = class WorkflowController {
    constructor(workflowService) {
        this.workflowService = workflowService;
    }
    getAfeApproversList(computeAfeApproversListDto, request) {
        const { currentContext } = request;
        const { user } = currentContext;
        return this.workflowService.getAfeApproversList(computeAfeApproversListDto, user.username);
    }
};
__decorate([
    (0, decorators_1.Permissions)('AFE.Submit'),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all list of approvers',
        type: dtos_1.WorkflowResponseDto,
    }),
    (0, common_1.Post)('/approvers'),
    (0, common_1.HttpCode)(200),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.ComputeAfeApproversListDto, Object]),
    __metadata("design:returntype", void 0)
], WorkflowController.prototype, "getAfeApproversList", null);
WorkflowController = __decorate([
    (0, swagger_1.ApiTags)('Workflow APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('workflows'),
    __metadata("design:paramtypes", [workflow_service_1.WorkflowService])
], WorkflowController);
exports.WorkflowController = WorkflowController;
//# sourceMappingURL=workflow.controller.js.map