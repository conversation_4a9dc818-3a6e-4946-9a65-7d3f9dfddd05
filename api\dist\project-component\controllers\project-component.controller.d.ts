import { LengthOfCommitmentResponseDTO } from '../dtos/response/length-of-commitment-response.dto';
import { ProjectComponentDto } from '../dtos/response/project-component-response.dto';
import { ProjectComponentService } from '../services';
export declare class ProjectComponentController {
    private readonly projectComponentService;
    constructor(projectComponentService: ProjectComponentService);
    getProjectComponents(requestTypeId?: number, locationId?: number, budgetTypeId?: number, childProject?: boolean): Promise<ProjectComponentDto[]>;
    getLengthOfCommitment(): Promise<LengthOfCommitmentResponseDTO[]>;
}
