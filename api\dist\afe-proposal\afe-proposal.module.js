"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeProposalModule = void 0;
const common_1 = require("@nestjs/common");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const repositories_1 = require("./repositories");
const repositories_2 = require("../workflow/repositories");
const helpers_1 = require("../shared/helpers");
const clients_1 = require("../shared/clients");
const draft_afe_repository_1 = require("../afe-draft/repositories/draft-afe-repository");
const services_2 = require("../workflow/services");
const repositories_3 = require("../finance/repositories");
const history_api_client_1 = require("../shared/clients/history-api.client");
const repositories_4 = require("../afe-config/repositories");
const repositories_5 = require("../project-component/repositories");
const condition_creator_service_1 = require("../shared/services/condition-creator.service");
const services_3 = require("../shared/services");
const services_4 = require("../task/services");
const services_5 = require("../finance/services");
const repositories_6 = require("../business-entity/repositories");
const repositories_7 = require("../finance/repositories");
const repositories_8 = require("../settings/repositories");
const repositories_9 = require("../notification/repositories");
const services_6 = require("../settings/services");
const validators_1 = require("../shared/validators");
const repositories_10 = require("../queue/repositories");
const parallel_identifier_repository_1 = require("../afe-config/repositories/parallel-identifier.repository");
const pdf_generator_service_1 = require("../pdf-generator/pdf-generator.service");
const length_of_commitment_repository_1 = require("../afe-config/repositories/length-of-commitment.repository");
const services_7 = require("../business-entity/services");
const attachment_service_1 = require("../attachment/services/attachment.service");
const repositories = [
    draft_afe_repository_1.DraftAfeRepository,
    repositories_1.AfeProposalRepository,
    repositories_2.WorkflowMasterSettingRepository,
    repositories_2.WorkflowMasterStepRepository,
    repositories_2.WorkflowSharedChildLimitRepository,
    repositories_1.AfeProposalLimitDeductionRepository,
    repositories_2.WorkflowSharedBucketLimitRepository,
    repositories_3.CostCenterRepository,
    repositories_1.AfeProposalAmountSplitRepository,
    repositories_1.AfeProposalApproverRepository,
    repositories_3.CurrencyTypeRepository,
    repositories_3.AnalysisCodeRepository,
    repositories_3.NaturalAccountNumberRepository,
    repositories_4.AfeBudgetTypeRepository,
    repositories_5.ProjectComponentRepository,
    repositories_6.EntitySetupRepository,
    repositories_7.CompanyCodeRepository,
    repositories_8.SettingsRepository,
    repositories_9.NotificationRepository,
    repositories_10.QueueLogRepository,
    parallel_identifier_repository_1.ParallelIdentifierRepository,
    length_of_commitment_repository_1.LengthOfCommitmentRepository,
    repositories_1.UserProjectComponentMappingRepository,
    repositories_1.UserCostCenterMappingRepository,
    repositories_1.LocationRepository
];
let AfeProposalModule = class AfeProposalModule {
};
AfeProposalModule = __decorate([
    (0, common_1.Module)({
        providers: [
            services_4.TaskService,
            services_1.AfeProposalService,
            services_1.AfeProposalReadService,
            services_3.ExcelSheetService,
            services_2.WorkflowService,
            helpers_1.DatabaseHelper,
            clients_1.AdminApiClient,
            clients_1.TaskApiClient,
            clients_1.NotificationApiClient,
            clients_1.AttachmentApiClient,
            clients_1.RequestApiClient,
            history_api_client_1.HistoryApiClient,
            condition_creator_service_1.ConditionCreatorService,
            services_3.SharedPermissionService,
            services_5.FinanceAdminService,
            services_3.SharedAttachmentService,
            services_3.CurrencyService,
            services_5.FinanceService,
            clients_1.MSGraphApiClient,
            helpers_1.SequlizeOperator,
            ...repositories,
            services_6.SettingsService,
            validators_1.AfeProposalValidator,
            services_3.SharedNotificationService,
            pdf_generator_service_1.PdfGeneratorService,
            services_1.AfeProposalApproverService,
            services_7.BusinessEntityService,
            attachment_service_1.AttachmentService
        ],
        controllers: [controllers_1.AfeProposalController],
    })
], AfeProposalModule);
exports.AfeProposalModule = AfeProposalModule;
//# sourceMappingURL=afe-proposal.module.js.map