{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/MyWorkspace/Projects/DpWorld/AFE_Revamp/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';\nimport { toNumber } from 'lodash';\nimport { finalize } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/common/spinner.service\";\nimport * as i2 from \"../../core/services\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@core/data/request-type\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../../../core/modules/partials/input-error-message/input-error-message.component\";\nimport * as i8 from \"../../../../core/modules/partials/upload-attachment/upload-attachment.component\";\nimport * as i9 from \"../../../../core/modules/partials/empty-state/empty-state.component\";\nimport * as i10 from \"../../../../core/modules/partials/history-logs/history-logs.component\";\nimport * as i11 from \"../../../../core/modules/partials/modals/modal/modal.component\";\nimport * as i12 from \"@core/directives/toggle-profile-menu.directive\";\nconst _c0 = [\"uploadEvidence\"];\nconst _c1 = [\"companyCodeEditorModal\"];\nconst _c2 = [\"historyModal\"];\nconst _c3 = [\"fusionIntegrationModal\"];\n\nfunction CompanyCodeComponent_div_0_div_53_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const requestType_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", requestType_r16, \" \");\n  }\n}\n\nfunction CompanyCodeComponent_div_0_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CompanyCodeComponent_div_0_div_53_ng_container_1_Template, 3, 1, \"ng-container\", 60);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.fusionIntegrationEnableFor);\n  }\n}\n\nfunction CompanyCodeComponent_div_0_ng_template_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2, \" Disabled \");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction CompanyCodeComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"h3\", 30)(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 32)(8, \"div\", 33)(9, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function CompanyCodeComponent_div_0_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.openEvidenceModel());\n    });\n    i0.ɵɵelement(10, \"span\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 36);\n    i0.ɵɵelement(12, \"img\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 38)(14, \"div\", 33);\n    i0.ɵɵelement(15, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"div\", 40);\n    i0.ɵɵelementStart(17, \"div\", 41)(18, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function CompanyCodeComponent_div_0_Template_a_click_18_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.deactivateCompanyCode());\n    });\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 33)(24, \"a\", 43);\n    i0.ɵɵlistener(\"click\", function CompanyCodeComponent_div_0_Template_a_click_24_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.openHistoryModel());\n    });\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(27, \"div\", 44)(28, \"div\", 5)(29, \"div\", 45);\n    i0.ɵɵelement(30, \"label\", 46);\n    i0.ɵɵelementStart(31, \"div\", 8)(32, \"div\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 45);\n    i0.ɵɵelement(35, \"label\", 47);\n    i0.ɵɵelementStart(36, \"div\", 8)(37, \"div\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 45);\n    i0.ɵɵelement(40, \"label\", 48);\n    i0.ɵɵelementStart(41, \"div\", 8)(42, \"div\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 45);\n    i0.ɵɵelement(45, \"label\", 49);\n    i0.ɵɵelementStart(46, \"div\", 8)(47, \"div\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 50);\n    i0.ɵɵelement(50, \"label\", 51);\n    i0.ɵɵelementStart(51, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CompanyCodeComponent_div_0_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.openFusionIntegrationModal());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 8);\n    i0.ɵɵtemplate(53, CompanyCodeComponent_div_0_div_53_Template, 2, 1, \"div\", 53);\n    i0.ɵɵtemplate(54, CompanyCodeComponent_div_0_ng_template_54_Template, 3, 0, \"ng-template\", null, 54, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 50)(57, \"div\", 55);\n    i0.ɵɵelement(58, \"label\", 51);\n    i0.ɵɵelementStart(59, \"div\", 56);\n    i0.ɵɵtext(60);\n    i0.ɵɵpipe(61, \"translate\");\n    i0.ɵɵelementStart(62, \"div\", 57)(63, \"input\", 58);\n    i0.ɵɵlistener(\"click\", function CompanyCodeComponent_div_0_Template_input_click_63_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.updateMultiNaturalAccount());\n    })(\"ngModelChange\", function CompanyCodeComponent_div_0_Template_input_ngModelChange_63_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.enableMultiNatualAccount = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(64, \"label\", 59);\n    i0.ɵɵelementEnd()()()()()();\n  }\n\n  if (rf & 2) {\n    const _r13 = i0.ɵɵreference(55);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 15, \"FORM.LABEL.COMPANY\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"transform\", \"-30px, 50.5px, 0px\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(19, 17, \"FORM.BUTTON.DEACTIVATE\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(21, 19, \"FORM.BUTTON.DEACTIVATE\"), \" \", i0.ɵɵpipeBind1(22, 21, \"FORM.LABEL.COMPANY\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(25, 23, \"FORM.BUTTON.HISTORY_BUTTON\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(26, 25, \"FORM.BUTTON.HISTORY_BUTTON\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedCompanyCode.code, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedCompanyCode.name, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedEntity.entityName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedCompanyCode.entityCode, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.fusionIntegrationEnableFor.length)(\"ngIfElse\", _r13);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(61, 27, \"FORM.LABEL.SINGLE_NATURAL_ACCOUNT\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.enableMultiNatualAccount);\n  }\n}\n\nconst _c4 = function (a0) {\n  return {\n    entityName: a0\n  };\n};\n\nfunction CompanyCodeComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-empty-state\", 63);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementStart(2, \"div\", 64)(3, \"div\", 65)(4, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function CompanyCodeComponent_ng_template_1_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.openHistoryModel());\n    });\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"span\", 67)(8, \"a\", 68);\n    i0.ɵɵlistener(\"click\", function CompanyCodeComponent_ng_template_1_Template_a_click_8_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.openCompanyCodeEditorModal());\n    });\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"span\", 69);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"message\", i0.ɵɵpipeBind2(1, 5, \"EMPTY_STATE.EMPTY_COMPANY_CODE_LIST\", i0.ɵɵpureFunction1(14, _c4, ctx_r2.selectedEntity.entityName)))(\"showImage\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(5, 8, \"FORM.BUTTON.HISTORY_BUTTON\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(6, 10, \"FORM.BUTTON.HISTORY_BUTTON\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(10, 12, \"FORM.LABEL.SETUP_COMPANY\"), \" \");\n  }\n}\n\nfunction CompanyCodeComponent_ng_container_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 70);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nconst _c5 = function (a0) {\n  return {\n    name: a0,\n    length: 4\n  };\n};\n\nfunction CompanyCodeComponent_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 70);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind2(2, 1, \"FORM.VALIDATION.LENGTH_ERROR\", i0.ɵɵpureFunction1(6, _c5, i0.ɵɵpipeBind1(3, 4, \"FORM.LABEL.FUSSION_NUMBER\"))));\n  }\n}\n\nfunction CompanyCodeComponent_ng_container_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 70);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nfunction CompanyCodeComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"input\", 73);\n    i0.ɵɵelementStart(3, \"label\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const i_r28 = ctx.index;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControlName\", i_r28);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.fusionIntegrationColumns[i_r28].col, \" \");\n  }\n}\n\nfunction CompanyCodeComponent_div_58_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 70);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nfunction CompanyCodeComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 5)(2, \"div\", 74)(3, \"app-upload-attachment\", 75);\n    i0.ɵɵlistener(\"attachmentDelete\", function CompanyCodeComponent_div_58_Template_app_upload_attachment_attachmentDelete_3_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.onAttachmentDelete($event));\n    })(\"attachmentEdited\", function CompanyCodeComponent_div_58_Template_app_upload_attachment_attachmentEdited_3_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onAttachmentEdited($event));\n    });\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CompanyCodeComponent_div_58_ng_container_6_Template, 3, 3, \"ng-container\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"isBoxShadow\", true)(\"labelHeader\", i0.ɵɵpipeBind1(4, 10, \"AFE_MODULE.SECTION_TITLE.SUPPORTING_DOCUMENTS\"))(\"labelSubHeader\", i0.ɵɵpipeBind1(5, 12, \"COMMON.ATTACHMENTS\"))(\"required\", false)(\"isShowHeader\", true)(\"isDescriptionRequired\", false)(\"isAdd\", true)(\"attachments\", ctx_r11.evidenceDocuments)(\"isViewOnly\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.evidenceError);\n  }\n}\n\nexport class CompanyCodeComponent {\n  constructor(cdr, spinnerService, companiesService, translateService, requestTypeData, fb) {\n    this.cdr = cdr;\n    this.spinnerService = spinnerService;\n    this.companiesService = companiesService;\n    this.translateService = translateService;\n    this.requestTypeData = requestTypeData;\n    this.fb = fb;\n    this.onCompanyChange = new EventEmitter();\n    this.isEditMode = false;\n    this.isSubmitted = false;\n    this.companyEditorModalTitle = this.translateService.instant('MENU.ADD_COMPANY');\n    this.modalDismissButtonLabel = this.translateService.instant('FORM.BUTTON.SAVE_BUTTON');\n    this.fusionEnableModalTitle = this.translateService.instant('MENU.CHOOSE_REQUEST_TYPES_FOR_FUSION_INTEGRATION');\n    this.evidenceError = false;\n    this.evidenceFormReady = false;\n    this.evidenceDocuments = [];\n    this.deletedEvidenceDocuments = [];\n    this.enableMultiNatualAccount = false;\n    this.historyModalConfig = {\n      modalTitle: this.translateService.instant('FORM.BUTTON.COMPANY_HISTORY_BUTTON'),\n\n      hideCloseButton() {\n        return true;\n      },\n\n      hideDismissButton() {\n        return true;\n      },\n\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: false\n      }\n    };\n    this.companyHistory = [];\n    this.companyCodeEditorModalConfig = {\n      modalTitle: this.companyEditorModalTitle,\n      dismissButtonLabel: this.modalDismissButtonLabel,\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),\n      onDismiss: () => {\n        this.companyCodeFormGroup.reset();\n        return true;\n      },\n      shouldClose: () => {\n        this.companyCodeFormGroup.reset();\n        return true;\n      },\n      shouldDismiss: () => {\n        this.isSubmitted = true;\n\n        if (!this.companyCodeFormGroup.valid) {\n          return false;\n        }\n\n        this.addNewCompany();\n        return false;\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'm',\n        keyboard: false,\n        centered: true\n      }\n    };\n    this.fusionIntegrationModalConfig = {\n      modalTitle: this.fusionEnableModalTitle,\n      dismissButtonLabel: this.modalDismissButtonLabel,\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),\n      onDismiss: () => {\n        return true;\n      },\n      shouldClose: () => {\n        return true;\n      },\n      shouldDismiss: () => {\n        this.updateFussionIntegration();\n        return false;\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: true\n      }\n    };\n    this.fusionIntegrationColumns = [];\n    this.fusionIntegrationEnableFor = [];\n    this.uploadEvidenceConfig = {\n      modalTitle: this.translateService.instant('FORM.BUTTON.UPLOAD_EVIDENCE_BUTTON'),\n      dismissButtonLabel: this.translateService.instant('FORM.BUTTON.UPLOAD_BUTTON'),\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),\n      onDismiss: () => {\n        this.evidenceFormReady = false;\n        this.evidenceDocuments = [];\n        this.deletedEvidenceDocuments = [];\n        this.cdr.detectChanges();\n        return true;\n      },\n      shouldClose: () => {\n        this.evidenceFormReady = false;\n        this.evidenceDocuments = [];\n        this.deletedEvidenceDocuments = [];\n        this.cdr.detectChanges();\n        return true;\n      },\n      shouldDismiss: () => {\n        this.uploadNewEvidence();\n        return false;\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: false\n      }\n    };\n    this.fusionIntegrationColumns = this.requestTypeData.data.map(d => ({\n      id: d.id,\n      col: d.title\n    })); // Create a FormControl for each available colunm, initialize them as unchecked, and put them in an array\n\n    const formControls = this.fusionIntegrationColumns.map(col => {\n      var _a, _b;\n\n      return new FormControl((_b = (_a = this.selectedCompanyCode) === null || _a === void 0 ? void 0 : _a.fusionIntegrationForRequestTypeIds) === null || _b === void 0 ? void 0 : _b.includes(col.id));\n    }); // Create a FormControl for the select/unselect all checkbox\n\n    const selectAllControl = new FormControl();\n    this.fusionIntegrationFormGroup = this.fb.group({\n      requestTypes: new FormArray(formControls),\n      selectAll: selectAllControl\n    });\n  }\n\n  ngOnInit() {\n    var _a, _b, _c, _d;\n\n    this.companyCodeFormGroup = new FormGroup({\n      companyCode: new FormControl('', Validators.required),\n      companyName: new FormControl(((_a = this.selectedEntity) === null || _a === void 0 ? void 0 : _a.entityName) || '', Validators.required)\n    }); // Subscribe to changes on the selectAll checkbox\n\n    (_c = (_b = this.fusionIntegrationFormGroup) === null || _b === void 0 ? void 0 : _b.get('selectAll')) === null || _c === void 0 ? void 0 : _c.valueChanges.subscribe(bool => {\n      var _a, _b;\n\n      (_b = (_a = this.fusionIntegrationFormGroup) === null || _a === void 0 ? void 0 : _a.get('requestTypes')) === null || _b === void 0 ? void 0 : _b.patchValue(Array(this.fusionIntegrationColumns.length).fill(bool), {\n        emitEvent: false\n      });\n    }); // Subscribe to changes on the colunm name checkboxes\n\n    (_d = this.fusionIntegrationFormGroup.get('requestTypes')) === null || _d === void 0 ? void 0 : _d.valueChanges.subscribe(val => {\n      var _a, _b;\n\n      const allSelected = val.every(bool => bool);\n\n      if (((_a = this.fusionIntegrationFormGroup.get('selectAll')) === null || _a === void 0 ? void 0 : _a.value) !== allSelected) {\n        (_b = this.fusionIntegrationFormGroup.get('selectAll')) === null || _b === void 0 ? void 0 : _b.patchValue(allSelected, {\n          emitEvent: false\n        });\n      }\n    });\n    this.setFusionIntegrationEnabledFor();\n  }\n\n  ngOnChanges() {}\n\n  setFusionIntegrationEnabledFor() {\n    var _a, _b, _c;\n\n    this.fusionIntegrationEnableFor = ((_c = (_b = (_a = this.selectedCompanyCode) === null || _a === void 0 ? void 0 : _a.fusionIntegrationForRequestTypeIds) === null || _b === void 0 ? void 0 : _b.map(id => {\n      var _a;\n\n      return (_a = this.requestTypeData.data.find(d => d.id === id)) === null || _a === void 0 ? void 0 : _a.title;\n    })) === null || _c === void 0 ? void 0 : _c.filter(title => !!title)) || [];\n  }\n\n  getRequestTypesControls() {\n    return this.fusionIntegrationFormGroup.get('requestTypes').controls;\n  }\n\n  isAnyRequestTypeSelected() {\n    return this.getRequestTypesControls().some(control => control.value);\n  }\n\n  updateMultiNaturalAccount() {\n    this.enableMultiNatualAccount = !this.enableMultiNatualAccount;\n    this.cdr.detectChanges();\n  }\n\n  isAllRequestTypeSelected() {\n    return this.getRequestTypesControls().every(control => control.value);\n  }\n\n  isRequiredError(FContorl) {\n    var _a, _b;\n\n    return ((_a = this.companyCodeFormGroup.get(FContorl)) === null || _a === void 0 ? void 0 : _a.hasError('required')) && (((_b = this.companyCodeFormGroup.get(FContorl)) === null || _b === void 0 ? void 0 : _b.touched) || this.isSubmitted);\n  }\n\n  isPatternError(FContorl) {\n    var _a, _b;\n\n    return ((_a = this.companyCodeFormGroup.get(FContorl)) === null || _a === void 0 ? void 0 : _a.hasError('pattern')) && (((_b = this.companyCodeFormGroup.get(FContorl)) === null || _b === void 0 ? void 0 : _b.touched) || this.isSubmitted);\n  }\n  /**\r\n   * Get the company code form controller reference.\r\n   */\n\n\n  get companyCodeFormController() {\n    return this.companyCodeFormGroup.get('companyCode');\n  }\n  /**\r\n   * Open company code editor.\r\n   * @returns\r\n   */\n\n\n  openCompanyCodeEditorModal() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.isSubmitted = false;\n      return yield _this.companyCodeEditorComponent.open();\n    })();\n  }\n  /**\r\n   * Add new company in the business unit.\r\n   */\n\n\n  addNewCompany() {\n    var _a, _b, _c;\n\n    this.isSubmitted = false;\n\n    if ((_a = this.companyCodeFormGroup) === null || _a === void 0 ? void 0 : _a.valid) {\n      const requestPayload = {\n        code: (_b = this.companyCodeFormGroup.get('companyCode')) === null || _b === void 0 ? void 0 : _b.value,\n        name: (_c = this.companyCodeFormGroup.get('companyName')) === null || _c === void 0 ? void 0 : _c.value,\n        entityId: this.selectedEntity.entityId\n      };\n      this.spinnerService.startSpinner();\n      this.companiesService.createCompanyCode(requestPayload).pipe(finalize(() => {\n        this.spinnerService.stopSpinner();\n      })).subscribe({\n        next: () => {\n          Swal.fire(this.translateService.instant('SWAL.SUCCESS'), this.translateService.instant('SWAL.ADD_NEW_COMPANY_CODE_SUCCESS'), 'success');\n          this.onCompanyChange.emit(true);\n          this.companyCodeFormGroup.reset();\n          this.companyCodeEditorComponent.close();\n          this.isSubmitted = true;\n        },\n        error: error => {\n          Swal.fire({\n            icon: 'error',\n            title: this.translateService.instant('SWAL.ADD_NEW_COMPANY_CODE_ERROR'),\n            text: error.message\n          });\n        }\n      });\n    }\n  }\n  /**\r\n   * Deactivate current active company code for the business unit entity.\r\n   */\n\n\n  deactivateCompanyCode() {\n    Swal.fire({\n      title: this.translateService.instant('SWAL.CONFIRMATION'),\n      text: this.translateService.instant('SWAL.COMPANY_CODE_INACTIVE_CONFIRM'),\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#3085d6',\n      cancelButtonColor: '#d33',\n      confirmButtonText: this.translateService.instant('SWAL.DEACTIVATE_BUTTON')\n    }).then(response => {\n      var _a;\n\n      if (response.isConfirmed && this.selectedCompanyCode) {\n        this.spinnerService.startSpinner();\n        this.companiesService.deactivateCompanyCode((_a = this.selectedCompanyCode) === null || _a === void 0 ? void 0 : _a.id).pipe(finalize(() => {\n          this.spinnerService.stopSpinner();\n        })).subscribe({\n          next: () => {\n            Swal.fire(this.translateService.instant('SWAL.SUCCESS'), this.translateService.instant('SWAL.MARK_COMPANY_CODE_INACTIVE'), 'success');\n            this.onCompanyChange.emit(true);\n          },\n          error: error => {\n            Swal.fire({\n              icon: 'error',\n              title: this.translateService.instant('SWAL.DEACTIVATE_COMPANY_CODE_ERROR'),\n              text: error.message\n            });\n          }\n        });\n      }\n\n      this.cdr.detectChanges();\n    });\n  }\n\n  openHistoryModel() {\n    this.spinnerService.startSpinner();\n    this.companiesService.getCompanyHistory(this.selectedEntity.entityId).subscribe({\n      next: response => {\n        this.companyHistory = response;\n        this.cdr.detectChanges();\n        this.historyModalComponent.open();\n        this.spinnerService.stopSpinner();\n      },\n      error: _ => {\n        this.companyHistory = [];\n        this.cdr.detectChanges();\n        this.historyModalComponent.open();\n        this.spinnerService.stopSpinner();\n        Swal.fire({\n          icon: 'error',\n          title: this.translateService.instant('SWAL.ERROR'),\n          text: this.translateService.instant('SWAL.ERROR_HISTORY_FETCH')\n        });\n      }\n    });\n  }\n  /**\r\n   * Open fusion integration update modal.\r\n   */\n\n\n  openFusionIntegrationModal() {\n    var _a, _b, _c;\n\n    const selectedValues = this.fusionIntegrationColumns.map(value => {\n      var _a, _b;\n\n      return (_b = (_a = this.selectedCompanyCode) === null || _a === void 0 ? void 0 : _a.fusionIntegrationForRequestTypeIds) === null || _b === void 0 ? void 0 : _b.includes(value.id);\n    });\n    const allValuesSelected = selectedValues.every(value => value);\n    (_b = (_a = this.fusionIntegrationFormGroup) === null || _a === void 0 ? void 0 : _a.get('requestTypes')) === null || _b === void 0 ? void 0 : _b.patchValue(selectedValues, {\n      emitEvent: false\n    });\n    (_c = this.fusionIntegrationFormGroup.get('selectAll')) === null || _c === void 0 ? void 0 : _c.patchValue(allValuesSelected, {\n      emitEvent: false\n    });\n    this.fusionIntegrationModal.open();\n  }\n  /**\r\n   * Update the fusion integration enablement for the AFE request types\r\n   */\n\n\n  updateFussionIntegration() {\n    const selectedRequestTypes = this.fusionIntegrationFormGroup.value.requestTypes.map((checked, index) => checked ? this.fusionIntegrationColumns[index].id : null).filter(value => !!value);\n\n    if (this.selectedCompanyCode) {\n      this.spinnerService.startSpinner();\n      this.companiesService.updateFusionIntegration({\n        requestTypeIds: selectedRequestTypes,\n        id: toNumber(this.selectedCompanyCode.id)\n      }).subscribe({\n        next: _ => {\n          if (this.selectedCompanyCode) {\n            this.selectedCompanyCode.fusionIntegrationForRequestTypeIds = selectedRequestTypes;\n          }\n\n          this.setFusionIntegrationEnabledFor();\n          this.cdr.detectChanges();\n          Swal.fire(this.translateService.instant('SWAL.SUCCESS'), this.translateService.instant('SWAL.FUSION_INTEGRATION_UPDATE_SUCCESS'), 'success');\n          this.spinnerService.stopSpinner();\n        },\n        error: err => {\n          Swal.fire({\n            icon: 'error',\n            title: this.translateService.instant('SWAL.FUSION_INTEGRATION_UPDATE_ERROR'),\n            text: err.message\n          });\n          this.spinnerService.stopSpinner();\n        }\n      });\n    }\n  }\n\n  openEvidenceModel() {\n    this.evidenceFormReady = false;\n    this.evidenceError = false;\n    this.evidenceDocuments = [];\n    this.deletedEvidenceDocuments = [];\n    this.cdr.detectChanges();\n    this.uploadEvidenceModelComponent.open();\n    setTimeout(() => {\n      this.evidenceFormReady = true;\n      this.cdr.detectChanges();\n    }, 100);\n  }\n\n  onAttachmentEdited(attachments) {\n    this.evidenceDocuments = attachments;\n    this.cdr.detectChanges();\n  }\n\n  onAttachmentDelete(attachment) {\n    if (!attachment.IsNew) {\n      this.deletedEvidenceDocuments.push(attachment);\n      this.cdr.detectChanges();\n    }\n\n    console.log(this.evidenceDocuments);\n    this.evidenceDocuments = this.evidenceDocuments.filter(x => x !== attachment);\n    console.log(this.evidenceDocuments);\n    this.cdr.detectChanges();\n  }\n\n  uploadNewEvidence() {\n    var _a, _b;\n\n    this.evidenceError = false;\n    console.log(this.evidenceDocuments);\n\n    if (!((_a = this.evidenceDocuments) === null || _a === void 0 ? void 0 : _a.length)) {\n      this.evidenceError = true;\n      this.cdr.detectChanges();\n      return;\n    }\n\n    if ((_b = this === null || this === void 0 ? void 0 : this.selectedCompanyCode) === null || _b === void 0 ? void 0 : _b.id) {\n      this.spinnerService.startSpinner();\n      this.companiesService.uploadEvidence(this.selectedCompanyCode.id, this.evidenceDocuments).subscribe({\n        next: response => {\n          Swal.fire(this.translateService.instant('SWAL.SUCCESS'), this.translateService.instant('SWAL.UPLOAD_EVIDENCE_SUCCESS'), 'success');\n          this.evidenceDocuments = [];\n          this.deletedEvidenceDocuments = [];\n          this.evidenceFormReady = false;\n          this.cdr.detectChanges();\n          this.uploadEvidenceModelComponent.close();\n          this.spinnerService.stopSpinner();\n        },\n        error: error => {\n          Swal.fire({\n            icon: 'error',\n            title: this.translateService.instant('SWAL.ERROR'),\n            text: error.message\n          });\n          this.spinnerService.stopSpinner();\n        }\n      });\n    }\n  }\n\n}\n\nCompanyCodeComponent.ɵfac = function CompanyCodeComponent_Factory(t) {\n  return new (t || CompanyCodeComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.SpinnerService), i0.ɵɵdirectiveInject(i2.CompaniesService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.RequestTypeData), i0.ɵɵdirectiveInject(i5.FormBuilder));\n};\n\nCompanyCodeComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CompanyCodeComponent,\n  selectors: [[\"app-company-code\"]],\n  viewQuery: function CompanyCodeComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(_c2, 5);\n      i0.ɵɵviewQuery(_c3, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.uploadEvidenceModelComponent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.companyCodeEditorComponent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.historyModalComponent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fusionIntegrationModal = _t.first);\n    }\n  },\n  inputs: {\n    selectedEntity: \"selectedEntity\",\n    selectedCompanyCode: \"selectedCompanyCode\"\n  },\n  outputs: {\n    onCompanyChange: \"onCompanyChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 59,\n  vars: 47,\n  consts: [[\"class\", \"card mb-5 mb-xl-8\", 4, \"ngIf\", \"ngIfElse\"], [\"noDataMessage\", \"\"], [3, \"modalConfig\"], [\"companyCodeEditorModal\", \"\"], [1, \"w-100\", \"px-5\", \"bg-body\", \"rounded\", 3, \"formGroup\"], [1, \"row\"], [1, \"col-lg-6\", \"col-md-6\", \"col-6\", \"mb-6\", \"mb-lg-5\"], [\"translate\", \"LIST.ENTITY_NAME\", 1, \"fw-bold\", \"text-muted\"], [1, \"h4\", \"text-gray-800\"], [\"translate\", \"LIST.ENTITY_CODE\", 1, \"fw-bold\", \"text-muted\"], [1, \"separator\", \"my-2\"], [1, \"col-md-12\", \"mb-5\"], [1, \"form-label\", \"required\"], [\"name\", \"companyCode\", \"formControlName\", \"companyCode\", \"pattern\", \"[a-zA-Z0-9]{4}\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", 3, \"placeholder\"], [4, \"ngIf\"], [\"name\", \"companyName\", \"formControlName\", \"companyName\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", 3, \"placeholder\"], [\"historyModal\", \"\"], [1, \"w-100\", \"px-1\", \"bg-body\", \"rounded\", \"py-1\"], [3, \"title\", \"historyLogs\"], [\"fusionIntegrationModal\", \"\"], [1, \"w-100\", \"p-5\", \"bg-body\", \"rounded\", 3, \"formGroup\"], [1, \"row\", \"p-1\", \"p-lg-3\", \"p-md-3\", \"p-sm-3\"], [1, \"col-12\", \"col-md-12\", \"p-0\", \"m-0\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"value\", \"\", \"formControlName\", \"selectAll\", 1, \"form-check-input\"], [1, \"form-label\", \"fw-bold\"], [\"class\", \"col-6 p-0 m-0\", \"formArrayName\", \"requestTypes\", 4, \"ngFor\", \"ngForOf\"], [\"uploadEvidence\", \"\"], [1, \"card\", \"mb-5\", \"mb-xl-8\"], [1, \"card-header\", \"border-0\", \"pt-5\"], [1, \"card-title\", \"align-items-start\", \"flex-column\"], [1, \"card-label\", \"fw-bolder\", \"fs-3\", \"mb-1\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"menu-item\", \"px-3\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"fw-bold\", \"btnRounded\", \"px-6\", \"py-2\", \"mx-1\", 3, \"click\"], [\"translate\", \"FORM.BUTTON.UPLOAD_EVIDENCE_BUTTON\"], [\"appToggleProfileMenu\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-color-primary\", \"btn-active-light-primary\", 3, \"transform\"], [\"width\", \"45px\", \"src\", \"./assets/media/svg/icons/dpw-icons/menu.png\", \"alt\", \"next\"], [1, \"fs-6\", \"fw-bold\", \"menu\", \"menu-column\", \"menu-gray-600\", \"menu-rounded\", \"menu-state-bg\", \"menu-state-primary\", \"menu-sub\", \"menu-sub-dropdown\", \"py-4\", \"w-275px\"], [\"translate\", \"MENU.QUICK_ACTION\", 1, \"menu-content\", \"fs-6\", \"text-dark\", \"fw-bolder\", \"px-3\", \"py-4\"], [1, \"separator\", \"mb-3\", \"opacity-75\"], [1, \"menu-item\", \"px-3\", \"mb-2\"], [1, \"menu-link\", \"px-3\", \"cursor-pointer\", 3, \"title\", \"click\"], [1, \"menu-link\", \"px-3\", \"cursor-pointer\", 3, \"title\", \"translate\", \"click\"], [1, \"card-body\", \"py-3\"], [1, \"col-lg-3\", \"col-sm-6\", \"mb-4\", \"mb-lg-5\"], [\"translate\", \"FORM.LABEL.COMPANY_FUSSION_NUMBER\", 1, \"fw-bold\", \"text-muted\"], [\"translate\", \"FORM.LABEL.COMPANY_NAME\", 1, \"fw-bold\", \"text-muted\"], [\"translate\", \"FORM.LABEL.ENTITY_NAME\", 1, \"fw-bold\", \"text-muted\"], [\"translate\", \"FORM.LABEL.ENTITY_CODE\", 1, \"fw-bold\", \"text-muted\"], [1, \"col-lg-6\", \"col-sm-6\", \"mb-4\", \"mb-lg-5\"], [\"translate\", \"FORM.LABEL.FUSION_INTEGRATION_ENABLED_FOR\", 1, \"fw-bold\", \"text-muted\"], [\"type\", \"button\", \"translate\", \"FORM.BUTTON.EDIT_BUTTON\", 1, \"btn\", \"btn-sm\", \"mb-2\", \"editBtn\", \"btnRounded\", \"fw-bold\", \"ps-4\", \"pe-4\", \"py-1\", \"mx-2\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"disable\", \"\"], [1, \"col-lg-6\", \"col-md-6\", \"col-sm-12\"], [1, \"d-flex\", \"justify-content-end\", \"fw-bold\"], [1, \"form-switch\", \"form-check\", \"ms-2\"], [\"type\", \"checkbox\", \"id\", \"site_state\", 1, \"form-check-input\", 3, \"ngModel\", \"click\", \"ngModelChange\"], [\"for\", \"site_state\", \"translate\", \"FORM.LABEL.MULTIPLE_NATURAL_ACCOUNTS\", 1, \"form-check-label\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"badge-success\", \"badge-space\"], [1, \"badge\", \"badge-danger\"], [3, \"message\", \"showImage\"], [1, \"history\", \"d-flex\", \"justify-content-end\"], [1, \"outline-btn-light\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"outline-btn-light\", \"mx-2\", 3, \"title\", \"translate\", \"click\"], [1, \"action\", \"position-relative\", \"d-inline-block\", \"text-danger\"], [1, \"text-danger\", \"opacity-75-hover\", \"cursor-pointer\", 3, \"click\"], [1, \"position-absolute\", \"opacity-15\", \"bottom-0\", \"start-0\", \"border-4\", \"border-danger\", \"border-bottom\", \"w-100\"], [3, \"errorMessage\"], [\"formArrayName\", \"requestTypes\", 1, \"col-6\", \"p-0\", \"m-0\"], [1, \"form-check\", \"mt-3\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"formControlName\"], [1, \"fv-row\", \"mb-5\", \"mb-lg-7\"], [3, \"isBoxShadow\", \"labelHeader\", \"labelSubHeader\", \"required\", \"isShowHeader\", \"isDescriptionRequired\", \"isAdd\", \"attachments\", \"isViewOnly\", \"attachmentDelete\", \"attachmentEdited\"]],\n  template: function CompanyCodeComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CompanyCodeComponent_div_0_Template, 65, 29, \"div\", 0);\n      i0.ɵɵtemplate(1, CompanyCodeComponent_ng_template_1_Template, 12, 16, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(3, \"app-modal\", 2, 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6);\n      i0.ɵɵelement(8, \"label\", 7);\n      i0.ɵɵelementStart(9, \"div\", 8);\n      i0.ɵɵtext(10);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(11, \"div\", 6);\n      i0.ɵɵelement(12, \"label\", 9);\n      i0.ɵɵelementStart(13, \"div\", 8);\n      i0.ɵɵtext(14);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelement(15, \"div\", 10);\n      i0.ɵɵelementStart(16, \"div\", 5)(17, \"div\", 11)(18, \"label\", 12);\n      i0.ɵɵtext(19);\n      i0.ɵɵpipe(20, \"translate\");\n      i0.ɵɵpipe(21, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(22, \"input\", 13);\n      i0.ɵɵpipe(23, \"translate\");\n      i0.ɵɵpipe(24, \"lowercase\");\n      i0.ɵɵpipe(25, \"translate\");\n      i0.ɵɵtemplate(26, CompanyCodeComponent_ng_container_26_Template, 3, 3, \"ng-container\", 14);\n      i0.ɵɵtemplate(27, CompanyCodeComponent_ng_container_27_Template, 4, 8, \"ng-container\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"div\", 11)(29, \"label\", 12);\n      i0.ɵɵtext(30);\n      i0.ɵɵpipe(31, \"translate\");\n      i0.ɵɵpipe(32, \"translate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(33, \"input\", 15);\n      i0.ɵɵpipe(34, \"translate\");\n      i0.ɵɵpipe(35, \"lowercase\");\n      i0.ɵɵpipe(36, \"translate\");\n      i0.ɵɵtemplate(37, CompanyCodeComponent_ng_container_37_Template, 3, 3, \"ng-container\", 14);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(38, \"app-modal\", 2, 16)(40, \"div\", 17);\n      i0.ɵɵelement(41, \"app-history-logs\", 18);\n      i0.ɵɵpipe(42, \"translate\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(43, \"app-modal\", 2, 19);\n      i0.ɵɵelementContainerStart(45);\n      i0.ɵɵelementStart(46, \"div\", 20)(47, \"div\", 21)(48, \"div\", 22)(49, \"div\", 23);\n      i0.ɵɵelement(50, \"input\", 24);\n      i0.ɵɵelementStart(51, \"label\", 25);\n      i0.ɵɵtext(52, \" Select/Deselect all \");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(53, \"div\", 21);\n      i0.ɵɵtemplate(54, CompanyCodeComponent_div_54_Template, 5, 2, \"div\", 26);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementContainerEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementContainerStart(55);\n      i0.ɵɵelementStart(56, \"app-modal\", 2, 27);\n      i0.ɵɵtemplate(58, CompanyCodeComponent_div_58_Template, 7, 14, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementContainerEnd();\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedCompanyCode)(\"ngIfElse\", _r1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"modalConfig\", ctx.companyCodeEditorModalConfig);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.companyCodeFormGroup);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate1(\" \", ctx.selectedEntity.entityName, \" \");\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" \", ctx.selectedEntity.entityCode, \" \");\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(20, 25, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(21, 27, \"FORM.LABEL.COMPANY_FUSSION_NUMBER\"), \" \");\n      i0.ɵɵadvance(3);\n      i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(23, 29, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(24, 31, i0.ɵɵpipeBind1(25, 33, \"FORM.LABEL.COMPANY_FUSSION_NUMBER\")), \"\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.isRequiredError(\"companyCode\"));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isPatternError(\"companyCode\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(31, 35, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(32, 37, \"FORM.LABEL.COMPANY_NAME\"), \" \");\n      i0.ɵɵadvance(3);\n      i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(34, 39, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(35, 41, i0.ɵɵpipeBind1(36, 43, \"FORM.LABEL.COMPANY_NAME\")), \"\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngIf\", ctx.isRequiredError(\"companyName\"));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"modalConfig\", ctx.historyModalConfig);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(42, 45, \"FORM.BUTTON.COMPANY_HISTORY_BUTTON\"))(\"historyLogs\", ctx.companyHistory);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"modalConfig\", ctx.fusionIntegrationModalConfig);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"formGroup\", ctx.fusionIntegrationFormGroup);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.getRequestTypesControls());\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"modalConfig\", ctx.uploadEvidenceConfig);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.evidenceFormReady);\n    }\n  },\n  dependencies: [i6.NgForOf, i6.NgIf, i7.InputErrorMessageComponent, i8.UploadAttachmentComponent, i9.EmptyStateComponent, i10.HistoryLogsComponent, i3.TranslateDirective, i11.ModalComponent, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.PatternValidator, i5.NgModel, i5.FormGroupDirective, i5.FormControlName, i5.FormArrayName, i12.ToggleProfileMenuDirective, i6.LowerCasePipe, i3.TranslatePipe],\n  styles: [\".badge-space[_ngcontent-%COMP%] {\\n  margin-right: 0.4rem;\\n  margin-top: 0.4rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNvbXBhbnktY29kZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLG9CQUFBO0VBQ0Esa0JBQUE7QUFDSiIsImZpbGUiOiJjb21wYW55LWNvZGUuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuYmFkZ2Utc3BhY2Uge1xyXG4gICAgbWFyZ2luLXJpZ2h0OiAwLjRyZW07XHJcbiAgICBtYXJnaW4tdG9wOiAwLjRyZW07XHJcbn0iXX0= */\"]\n});", "map": {"version": 3, "mappings": ";AAAA,SAGEA,YAHF,QASO,eATP;AAUA,SACEC,SADF,EAGEC,WAHF,EAIEC,SAJF,EAKEC,UALF,QAMO,gBANP;AAWA,SAASC,QAAT,QAAyB,QAAzB;AACA,SAASC,QAAT,QAAyB,MAAzB;AACA,OAAOC,IAAP,MAAiB,aAAjB;;;;;;;;;;;;;;;;;;;;;IC0GYC;IAGEA;IACEA;IACFA;IACFA;;;;;IAFIA;IAAAA;;;;;;IALNA;IACEA;IAOFA;;;;;IAN4BA;IAAAA;;;;;;IAQ1BA,4BAAK,CAAL,EAAK,MAAL,EAAK,EAAL;IACoCA;IAASA;;;;;;;;IA3IzDA,gCAA+E,CAA/E,EAA+E,KAA/E,EAA+E,EAA/E,EAA+E,CAA/E,EAA+E,IAA/E,EAA+E,EAA/E,EAA+E,CAA/E,EAA+E,MAA/E,EAA+E,EAA/E;IAImDA;;IAE3CA;IAGJA,+BAAiB,CAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,CAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,CAAjB,EAAiB,QAAjB,EAAiB,EAAjB;IAIQA;MAAAA;MAAA;MAAA,OAASA,2CAAT;IAA4B,CAA5B;IAGAA;IACFA;IAEFA;IAMEA;IAKFA;IACAA,kCAEC,EAFD,EAEC,KAFD,EAEC,EAFD;IAIIA;IAIFA;IAEAA;IAEAA,iCAAiC,EAAjC,EAAiC,GAAjC,EAAiC,EAAjC;IAGIA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAAgC,CAAhC;;IAGAA;;;IAEFA;IAGFA,iCAA4B,EAA5B,EAA4B,GAA5B,EAA4B,EAA5B;IAGIA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;;;IAIFA;IAQVA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,CAA5B,EAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B;IAGMA;IAIAA,gCAA8B,EAA9B,EAA8B,KAA9B;IAEIA;IACFA;IAGJA;IACEA;IAIAA,gCAA8B,EAA9B,EAA8B,KAA9B;IAEIA;IACFA;IAGJA;IACEA;IAIAA,gCAA8B,EAA9B,EAA8B,KAA9B;IAEIA;IACFA;IAGJA;IACEA;IAIAA,gCAA8B,EAA9B,EAA8B,KAA9B;IAEIA;IACFA;IAIJA;IACEA;IAIAA;IACEA;MAAAA;MAAA;MAAA,OAASA,oDAAT;IAAqC,CAArC;IAIDA;IACDA;IACEA;IASAA;IAKFA;IAGFA,iCAA4C,EAA5C,EAA4C,KAA5C,EAA4C,EAA5C;IAGIA;IAKAA;IACEA;;IACAA,iCAAyC,EAAzC,EAAyC,OAAzC,EAAyC,EAAzC;IAEIA;MAAAA;MAAA;MAAA,OAASA,mDAAT;IAAoC,CAApC,EAAqC,eAArC,EAAqC;MAAAA;MAAA;MAAA;IAAA,CAArC;IADFA;IAQFA;IAKFA;;;;;;;IArKyCA;IAAAA;IAiBzCA;IAAAA;IAwBIA;IAAAA;IAIAA;IAAAA;IAOAA;IAAAA;IAGAA;IAmBFA;IAAAA;IAWAA;IAAAA;IAWAA;IAAAA;IAWAA;IAAAA;IAiBIA;IAAAA,gEAAyC,UAAzC,EAAyCC,IAAzC;IA0BJD;IAAAA;IAIIA;IAAAA;;;;;;;;;;;;;;IAmBdA;;IAOEA,gCAAgD,CAAhD,EAAgD,KAAhD,EAAgD,EAAhD,EAAgD,CAAhD,EAAgD,QAAhD,EAAgD,EAAhD;IAKMA;MAAAA;MAAA;MAAA,OAASA,0CAAT;IAA2B,CAA3B;;;IAGDA;IAILA,iCAAkE,CAAlE,EAAkE,GAAlE,EAAkE,EAAlE;IAEIA;MAAAA;MAAA;MAAA,OAASA,oDAAT;IAAqC,CAArC;IAGAA;;IACFA;IACAA;IAIFA;;;;;IA7BAA,qJAGC,WAHD,EAGC,IAHD;IAUMA;IAAAA;IAGAA;IAUFA;IAAAA;;;;;;IA0CAA;IACEA;;IAIFA;;;;IAHIA;IAAAA;;;;;;;;;;;;;IAIJA;IACEA;;;IAQFA;;;;IAPIA;IAAAA;;;;;;IAuBJA;IACEA;;IAIFA;;;;IAHIA;IAAAA;;;;;;IAuCJA,gCAIC,CAJD,EAIC,KAJD,EAIC,EAJD;IAMIA;IAKAA;IACEA;IACFA;;;;;;IAJEA;IAAAA;IAGAA;IAAAA;;;;;;IA8BJA;IACEA;;IAIFA;;;;IAHIA;IAAAA;;;;;;;;IArBVA,4BAA+B,CAA/B,EAA+B,KAA/B,EAA+B,CAA/B,EAA+B,CAA/B,EAA+B,KAA/B,EAA+B,EAA/B,EAA+B,CAA/B,EAA+B,uBAA/B,EAA+B,EAA/B;IAcQA;MAAAA;MAAA;MAAA,OAAoBA,kDAApB;IAA8C,CAA9C,EAA+C,kBAA/C,EAA+C;MAAAA;MAAA;MAAA,OAC3BA,kDAD2B;IACD,CAD9C;;;IAIFA;IACAA;IAMFA;;;;;IArBIA;IAAAA,mCAAoB,aAApB,EAAoBA,sEAApB,EAAoB,gBAApB,EAAoBA,2CAApB,EAAoB,UAApB,EAAoB,KAApB,EAAoB,cAApB,EAAoB,IAApB,EAAoB,uBAApB,EAAoB,KAApB,EAAoB,OAApB,EAAoB,IAApB,EAAoB,aAApB,EAAoBE,yBAApB,EAAoB,YAApB,EAAoB,KAApB;IAeaF;IAAAA;;;;ADnUzB,OAAM,MAAOG,oBAAP,CAA2B;EAmH/BC,YACmBC,GADnB,EAEmBC,cAFnB,EAGmBC,gBAHnB,EAImBC,gBAJnB,EAKmBC,eALnB,EAMmBC,EANnB,EAMkC;IALf;IACA;IACA;IACA;IACA;IACA;IAjHT,uBAAkB,IAAIlB,YAAJ,EAAlB;IAEH,kBAAsB,KAAtB;IAEA,mBAAuB,KAAvB;IACA,+BACL,KAAKgB,gBAAL,CAAsBG,OAAtB,CAA8B,kBAA9B,CADK;IAEA,+BAAkC,KAAKH,gBAAL,CAAsBG,OAAtB,CACvC,yBADuC,CAAlC;IAIA,8BAAiC,KAAKH,gBAAL,CAAsBG,OAAtB,CACtC,kDADsC,CAAjC;IAIP,qBAAyB,KAAzB;IACA,yBAA6B,KAA7B;IACA,yBAAkC,EAAlC;IACA,gCAAyC,EAAzC;IAEA,gCAAoC,KAApC;IAOA,0BAAkC;MAChCC,UAAU,EAAE,KAAKJ,gBAAL,CAAsBG,OAAtB,CACV,oCADU,CADoB;;MAIhCE,eAAe;QACb,OAAO,IAAP;MACD,CAN+B;;MAOhCC,iBAAiB;QACf,OAAO,IAAP;MACD,CAT+B;;MAUhCC,iBAAiB,EAAE;QACjBC,QAAQ,EAAE,QADO;QAEjBC,IAAI,EAAE,IAFW;QAGjBC,QAAQ,EAAE,KAHO;QAIjBC,QAAQ,EAAE;MAJO;IAVa,CAAlC;IAkBA,sBAAoC,EAApC;IAGO,oCAA4C;MACjDP,UAAU,EAAE,KAAKQ,uBADgC;MAEjDC,kBAAkB,EAAE,KAAKC,uBAFwB;MAGjDC,gBAAgB,EAAE,KAAKf,gBAAL,CAAsBG,OAAtB,CAChB,2BADgB,CAH+B;MAMjDa,SAAS,EAAE,MAAK;QACd,KAAKC,oBAAL,CAA0BC,KAA1B;QACA,OAAO,IAAP;MACD,CATgD;MAUjDC,WAAW,EAAE,MAAK;QAChB,KAAKF,oBAAL,CAA0BC,KAA1B;QACA,OAAO,IAAP;MACD,CAbgD;MAcjDE,aAAa,EAAE,MAAK;QAClB,KAAKC,WAAL,GAAmB,IAAnB;;QACA,IAAI,CAAC,KAAKJ,oBAAL,CAA0BK,KAA/B,EAAsC;UACpC,OAAO,KAAP;QACD;;QACD,KAAKC,aAAL;QACA,OAAO,KAAP;MACD,CArBgD;MAsBjDhB,iBAAiB,EAAE;QACjBC,QAAQ,EAAE,QADO;QAEjBC,IAAI,EAAE,GAFW;QAGjBC,QAAQ,EAAE,KAHO;QAIjBC,QAAQ,EAAE;MAJO;IAtB8B,CAA5C;IA8BA,oCAA4C;MACjDP,UAAU,EAAE,KAAKoB,sBADgC;MAEjDX,kBAAkB,EAAE,KAAKC,uBAFwB;MAGjDC,gBAAgB,EAAE,KAAKf,gBAAL,CAAsBG,OAAtB,CAChB,2BADgB,CAH+B;MAMjDa,SAAS,EAAE,MAAK;QACd,OAAO,IAAP;MACD,CARgD;MASjDG,WAAW,EAAE,MAAK;QAChB,OAAO,IAAP;MACD,CAXgD;MAYjDC,aAAa,EAAE,MAAK;QAClB,KAAKK,wBAAL;QACA,OAAO,KAAP;MACD,CAfgD;MAgBjDlB,iBAAiB,EAAE;QACjBC,QAAQ,EAAE,QADO;QAEjBC,IAAI,EAAE,IAFW;QAGjBC,QAAQ,EAAE,KAHO;QAIjBC,QAAQ,EAAE;MAJO;IAhB8B,CAA5C;IA0BA,gCAA0D,EAA1D;IACA,kCAAqD,EAArD;IAkVA,4BAAoC;MACzCP,UAAU,EAAE,KAAKJ,gBAAL,CAAsBG,OAAtB,CAA8B,oCAA9B,CAD6B;MAEzCU,kBAAkB,EAAE,KAAKb,gBAAL,CAAsBG,OAAtB,CAA8B,2BAA9B,CAFqB;MAGzCY,gBAAgB,EAAE,KAAKf,gBAAL,CAAsBG,OAAtB,CAChB,2BADgB,CAHuB;MAMzCa,SAAS,EAAE,MAAK;QACd,KAAKU,iBAAL,GAAyB,KAAzB;QACA,KAAKC,iBAAL,GAAyB,EAAzB;QACA,KAAKC,wBAAL,GAAgC,EAAhC;QACA,KAAK/B,GAAL,CAASgC,aAAT;QACA,OAAO,IAAP;MACD,CAZwC;MAazCV,WAAW,EAAE,MAAK;QAChB,KAAKO,iBAAL,GAAyB,KAAzB;QACA,KAAKC,iBAAL,GAAyB,EAAzB;QACA,KAAKC,wBAAL,GAAgC,EAAhC;QACA,KAAK/B,GAAL,CAASgC,aAAT;QACA,OAAO,IAAP;MACD,CAnBwC;MAoBzCT,aAAa,EAAE,MAAK;QAClB,KAAKU,iBAAL;QACA,OAAO,KAAP;MACD,CAvBwC;MAwBzCvB,iBAAiB,EAAE;QACjBC,QAAQ,EAAE,QADO;QAEjBC,IAAI,EAAE,IAFW;QAGjBC,QAAQ,EAAE,KAHO;QAIjBC,QAAQ,EAAE;MAJO;IAxBsB,CAApC;IAxUL,KAAKoB,wBAAL,GAAgC,KAAK9B,eAAL,CAAqB+B,IAArB,CAA0BC,GAA1B,CAA+BC,CAAD,KAAQ;MACpEC,EAAE,EAAED,CAAC,CAACC,EAD8D;MAEpEC,GAAG,EAAEF,CAAC,CAACG;IAF6D,CAAR,CAA9B,CAAhC,CAFgC,CAMhC;;IACA,MAAMC,YAAY,GAAG,KAAKP,wBAAL,CAA8BE,GAA9B,CAClBG,GAAD,IAAQ;;;MACN,WAAIlD,WAAJ,CACE,iBAAKqD,mBAAL,MAAwB,IAAxB,IAAwBC,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEC,kCAA1B,MAA4D,IAA5D,IAA4DC,aAA5D,GAA4D,MAA5D,GAA4DA,GAAEC,QAAF,CAC1DP,GAAG,CAACD,EADsD,CAD9D;IAIC,CANgB,CAArB,CAPgC,CAgBhC;;IACA,MAAMS,gBAAgB,GAAG,IAAI1D,WAAJ,EAAzB;IACA,KAAK2D,0BAAL,GAAkC,KAAK3C,EAAL,CAAQ4C,KAAR,CAAc;MAC9CC,YAAY,EAAE,IAAI9D,SAAJ,CAAcqD,YAAd,CADgC;MAE9CU,SAAS,EAAEJ;IAFmC,CAAd,CAAlC;EAID;;EAEDK,QAAQ;;;IACN,KAAKhC,oBAAL,GAA4B,IAAI9B,SAAJ,CAAc;MACxC+D,WAAW,EAAE,IAAIhE,WAAJ,CAAgB,EAAhB,EAAoBE,UAAU,CAAC+D,QAA/B,CAD2B;MAExCC,WAAW,EAAE,IAAIlE,WAAJ,CACX,YAAKmE,cAAL,MAAmB,IAAnB,IAAmBb,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEc,UAArB,KAAmC,EADxB,EAEXlE,UAAU,CAAC+D,QAFA;IAF2B,CAAd,CAA5B,CADM,CASN;;IACA,iBAAKN,0BAAL,MAA+B,IAA/B,IAA+BH,aAA/B,GAA+B,MAA/B,GAA+BA,GAC3Ba,GAD2B,CACvB,WADuB,CAA/B,MACoB,IADpB,IACoBC,aADpB,GACoB,MADpB,GACoBA,GAChBC,YADgB,CACHC,SADG,CACQC,IAAD,IAAS;;;MAChC,iBAAKd,0BAAL,MAA+B,IAA/B,IAA+BL,aAA/B,GAA+B,MAA/B,GAA+BA,GAC3Be,GAD2B,CACvB,cADuB,CAA/B,MACuB,IADvB,IACuBb,aADvB,GACuB,MADvB,GACuBA,GACnBkB,UADmB,CACRC,KAAK,CAAC,KAAK9B,wBAAL,CAA8B+B,MAA/B,CAAL,CAA4CC,IAA5C,CAAiDJ,IAAjD,CADQ,EACgD;QACnEK,SAAS,EAAE;MADwD,CADhD,CADvB;IAKD,CAPiB,CADpB,CAVM,CAoBN;;IACA,WAAKnB,0BAAL,CACGU,GADH,CACO,cADP,OACsB,IADtB,IACsBU,aADtB,GACsB,MADtB,GACsBA,GAClBR,YADkB,CACLC,SADK,CACMQ,GAAD,IAAQ;;;MAC/B,MAAMC,WAAW,GAAGD,GAAG,CAACE,KAAJ,CAAWT,IAAD,IAAeA,IAAzB,CAApB;;MACA,IACE,YAAKd,0BAAL,CAAgCU,GAAhC,CAAoC,WAApC,OAAgD,IAAhD,IAAgDf,aAAhD,GAAgD,MAAhD,GAAgDA,GAAE6B,KAAlD,MACAF,WAFF,EAGE;QACA,WAAKtB,0BAAL,CACGU,GADH,CACO,WADP,OACmB,IADnB,IACmBb,aADnB,GACmB,MADnB,GACmBA,GACfkB,UADe,CACJO,WADI,EACS;UAAEH,SAAS,EAAE;QAAb,CADT,CADnB;MAGD;IACF,CAXmB,CADtB;IAcA,KAAKM,8BAAL;EACD;;EAEDC,WAAW,IAAY;;EAEfD,8BAA8B;;;IACpC,KAAKE,0BAAL,GACE,wBAAKjC,mBAAL,MAAwB,IAAxB,IAAwBC,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEC,kCAA1B,MAA4D,IAA5D,IAA4DC,aAA5D,GAA4D,MAA5D,GAA4DA,GACxDT,GADwD,CACnDE,EAAD,IAAO;MAAA;;MAAC,kBAAKlC,eAAL,CAAqB+B,IAArB,CAA0ByC,IAA1B,CAAgCvC,CAAD,IAAOA,CAAC,CAACC,EAAF,KAASA,EAA/C,OAAkD,IAAlD,IAAkDK,aAAlD,GAAkD,MAAlD,GAAkDA,GAAEH,KAApD;IAAyD,CADb,CAA5D,MAC0E,IAD1E,IAC0EmB,aAD1E,GAC0E,MAD1E,GAC0EA,GACtEkB,MADsE,CAC9DrC,KAAD,IAAW,CAAC,CAACA,KADkD,CAD1E,KAEkC,EAHpC;EAID;;EAEMsC,uBAAuB;IAC5B,OAAQ,KAAK9B,0BAAL,CAAgCU,GAAhC,CAAoC,cAApC,EACLqB,QADH;EAED;;EAEMC,wBAAwB;IAC7B,OAAO,KAAKF,uBAAL,GAA+BG,IAA/B,CAAqCC,OAAD,IAAkBA,OAAO,CAACV,KAA9D,CAAP;EACD;;EAEMW,yBAAyB;IAC9B,KAAKC,wBAAL,GAAgC,CAAC,KAAKA,wBAAtC;IACA,KAAKpF,GAAL,CAASgC,aAAT;EACD;;EAEMqD,wBAAwB;IAC7B,OAAO,KAAKP,uBAAL,GAA+BP,KAA/B,CACJW,OAAD,IAAkBA,OAAO,CAACV,KADrB,CAAP;EAGD;;EAEMc,eAAe,CAACC,QAAD,EAAiB;;;IACrC,OACE,YAAKnE,oBAAL,CAA0BsC,GAA1B,CAA8B6B,QAA9B,OAAuC,IAAvC,IAAuC5C,aAAvC,GAAuC,MAAvC,GAAuCA,GAAE6C,QAAF,CAAW,UAAX,CAAvC,MACC,YAAKpE,oBAAL,CAA0BsC,GAA1B,CAA8B6B,QAA9B,OAAuC,IAAvC,IAAuC1C,aAAvC,GAAuC,MAAvC,GAAuCA,GAAE4C,OAAzC,KAAoD,KAAKjE,WAD1D,CADF;EAID;;EAEMkE,cAAc,CAACH,QAAD,EAAiB;;;IACpC,OACE,YAAKnE,oBAAL,CAA0BsC,GAA1B,CAA8B6B,QAA9B,OAAuC,IAAvC,IAAuC5C,aAAvC,GAAuC,MAAvC,GAAuCA,GAAE6C,QAAF,CAAW,SAAX,CAAvC,MACC,YAAKpE,oBAAL,CAA0BsC,GAA1B,CAA8B6B,QAA9B,OAAuC,IAAvC,IAAuC1C,aAAvC,GAAuC,MAAvC,GAAuCA,GAAE4C,OAAzC,KAAoD,KAAKjE,WAD1D,CADF;EAID;EAED;;;;;EAG6B,IAAzBmE,yBAAyB;IAC3B,OAAO,KAAKvE,oBAAL,CAA0BsC,GAA1B,CAA8B,aAA9B,CAAP;EACD;EAED;;;;;;EAIakC,0BAA0B;IAAA;;IAAA;MACrC,KAAI,CAACpE,WAAL,GAAmB,KAAnB;MACA,aAAa,KAAI,CAACqE,0BAAL,CAAgCC,IAAhC,EAAb;IAFqC;EAGtC;EAED;;;;;EAGQpE,aAAa;;;IACnB,KAAKF,WAAL,GAAmB,KAAnB;;IACA,IAAI,WAAKJ,oBAAL,MAAyB,IAAzB,IAAyBuB,aAAzB,GAAyB,MAAzB,GAAyBA,GAAElB,KAA/B,EAAsC;MACpC,MAAMsE,cAAc,GAAG;QACrBC,IAAI,EAAE,WAAK5E,oBAAL,CAA0BsC,GAA1B,CAA8B,aAA9B,OAA4C,IAA5C,IAA4Cb,aAA5C,GAA4C,MAA5C,GAA4CA,GAAE2B,KAD/B;QAErByB,IAAI,EAAE,WAAK7E,oBAAL,CAA0BsC,GAA1B,CAA8B,aAA9B,OAA4C,IAA5C,IAA4CC,aAA5C,GAA4C,MAA5C,GAA4CA,GAAEa,KAF/B;QAGrB0B,QAAQ,EAAE,KAAK1C,cAAL,CAAoB0C;MAHT,CAAvB;MAKA,KAAKjG,cAAL,CAAoBkG,YAApB;MACA,KAAKjG,gBAAL,CACGkG,iBADH,CACqBL,cADrB,EAEGM,IAFH,CAGI5G,QAAQ,CAAC,MAAK;QACZ,KAAKQ,cAAL,CAAoBqG,WAApB;MACD,CAFO,CAHZ,EAOGzC,SAPH,CAOa;QACT0C,IAAI,EAAE,MAAK;UACT7G,IAAI,CAAC8G,IAAL,CACE,KAAKrG,gBAAL,CAAsBG,OAAtB,CAA8B,cAA9B,CADF,EAEE,KAAKH,gBAAL,CAAsBG,OAAtB,CACE,mCADF,CAFF,EAKE,SALF;UAOA,KAAKmG,eAAL,CAAqBC,IAArB,CAA0B,IAA1B;UACA,KAAKtF,oBAAL,CAA0BC,KAA1B;UACA,KAAKwE,0BAAL,CAAgCc,KAAhC;UACA,KAAKnF,WAAL,GAAmB,IAAnB;QACD,CAbQ;QAcToF,KAAK,EAAGA,KAAD,IAAU;UACflH,IAAI,CAAC8G,IAAL,CAAU;YACRK,IAAI,EAAE,OADE;YAERrE,KAAK,EAAE,KAAKrC,gBAAL,CAAsBG,OAAtB,CACL,iCADK,CAFC;YAKRwG,IAAI,EAAEF,KAAK,CAACG;UALJ,CAAV;QAOD;MAtBQ,CAPb;IA+BD;EACF;EAED;;;;;EAGOC,qBAAqB;IAC1BtH,IAAI,CAAC8G,IAAL,CAAU;MACRhE,KAAK,EAAE,KAAKrC,gBAAL,CAAsBG,OAAtB,CAA8B,mBAA9B,CADC;MAERwG,IAAI,EAAE,KAAK3G,gBAAL,CAAsBG,OAAtB,CAA8B,oCAA9B,CAFE;MAGRuG,IAAI,EAAE,SAHE;MAIRI,gBAAgB,EAAE,IAJV;MAKRC,kBAAkB,EAAE,SALZ;MAMRC,iBAAiB,EAAE,MANX;MAORC,iBAAiB,EAAE,KAAKjH,gBAAL,CAAsBG,OAAtB,CACjB,wBADiB;IAPX,CAAV,EAUG+G,IAVH,CAUSC,QAAD,IAAa;;;MACnB,IAAIA,QAAQ,CAACC,WAAT,IAAwB,KAAK7E,mBAAjC,EAAsD;QACpD,KAAKzC,cAAL,CAAoBkG,YAApB;QACA,KAAKjG,gBAAL,CACG8G,qBADH,CACyB,WAAKtE,mBAAL,MAAwB,IAAxB,IAAwBC,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEL,EADnD,EAEG+D,IAFH,CAGI5G,QAAQ,CAAC,MAAK;UACZ,KAAKQ,cAAL,CAAoBqG,WAApB;QACD,CAFO,CAHZ,EAOGzC,SAPH,CAOa;UACT0C,IAAI,EAAE,MAAK;YACT7G,IAAI,CAAC8G,IAAL,CACE,KAAKrG,gBAAL,CAAsBG,OAAtB,CAA8B,cAA9B,CADF,EAEE,KAAKH,gBAAL,CAAsBG,OAAtB,CACE,iCADF,CAFF,EAKE,SALF;YAOA,KAAKmG,eAAL,CAAqBC,IAArB,CAA0B,IAA1B;UACD,CAVQ;UAWTE,KAAK,EAAGA,KAAD,IAAU;YACflH,IAAI,CAAC8G,IAAL,CAAU;cACRK,IAAI,EAAE,OADE;cAERrE,KAAK,EAAE,KAAKrC,gBAAL,CAAsBG,OAAtB,CACL,oCADK,CAFC;cAKRwG,IAAI,EAAEF,KAAK,CAACG;YALJ,CAAV;UAOD;QAnBQ,CAPb;MA4BD;;MACD,KAAK/G,GAAL,CAASgC,aAAT;IACD,CA3CD;EA4CD;;EAEDwF,gBAAgB;IACd,KAAKvH,cAAL,CAAoBkG,YAApB;IAEA,KAAKjG,gBAAL,CACGuH,iBADH,CACqB,KAAKjE,cAAL,CAAoB0C,QADzC,EAEGrC,SAFH,CAEa;MACT0C,IAAI,EAAGe,QAAD,IAAa;QACjB,KAAKI,cAAL,GAAsBJ,QAAtB;QACA,KAAKtH,GAAL,CAASgC,aAAT;QACA,KAAK2F,qBAAL,CAA2B7B,IAA3B;QACA,KAAK7F,cAAL,CAAoBqG,WAApB;MACD,CANQ;MAOTM,KAAK,EAAGgB,CAAD,IAAM;QACX,KAAKF,cAAL,GAAsB,EAAtB;QACA,KAAK1H,GAAL,CAASgC,aAAT;QACA,KAAK2F,qBAAL,CAA2B7B,IAA3B;QACA,KAAK7F,cAAL,CAAoBqG,WAApB;QAEA5G,IAAI,CAAC8G,IAAL,CAAU;UACRK,IAAI,EAAE,OADE;UAERrE,KAAK,EAAE,KAAKrC,gBAAL,CAAsBG,OAAtB,CAA8B,YAA9B,CAFC;UAGRwG,IAAI,EAAE,KAAK3G,gBAAL,CAAsBG,OAAtB,CAA8B,0BAA9B;QAHE,CAAV;MAKD;IAlBQ,CAFb;EAsBD;EAED;;;;;EAGOuH,0BAA0B;;;IAC/B,MAAMC,cAAc,GAAG,KAAK5F,wBAAL,CAA8BE,GAA9B,CAAmCoC,KAAD,IAAU;;;MACjE,OAAO,iBAAK9B,mBAAL,MAAwB,IAAxB,IAAwBC,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEC,kCAA1B,MAA4D,IAA5D,IAA4DC,aAA5D,GAA4D,MAA5D,GAA4DA,GAAEC,QAAF,CACjE0B,KAAK,CAAClC,EAD2D,CAAnE;IAGD,CAJsB,CAAvB;IAMA,MAAMyF,iBAAiB,GAAGD,cAAc,CAACvD,KAAf,CAAsBC,KAAD,IAAWA,KAAhC,CAA1B;IAEA,iBAAKxB,0BAAL,MAA+B,IAA/B,IAA+BL,aAA/B,GAA+B,MAA/B,GAA+BA,GAC3Be,GAD2B,CACvB,cADuB,CAA/B,MACuB,IADvB,IACuBb,aADvB,GACuB,MADvB,GACuBA,GACnBkB,UADmB,CACR+D,cADQ,EACQ;MAAE3D,SAAS,EAAE;IAAb,CADR,CADvB;IAIA,WAAKnB,0BAAL,CACGU,GADH,CACO,WADP,OACmB,IADnB,IACmBC,aADnB,GACmB,MADnB,GACmBA,GACfI,UADe,CACJgE,iBADI,EACe;MAAE5D,SAAS,EAAE;IAAb,CADf,CADnB;IAIA,KAAK6D,sBAAL,CAA4BlC,IAA5B;EACD;EAED;;;;;EAGOlE,wBAAwB;IAC7B,MAAMqG,oBAAoB,GACxB,KAAKjF,0BAAL,CAAgCwB,KAAhC,CAAsCtB,YAAtC,CACGd,GADH,CACO,CAAC8F,OAAD,EAAeC,KAAf,KACHD,OAAO,GAAG,KAAKhG,wBAAL,CAA8BiG,KAA9B,EAAqC7F,EAAxC,GAA6C,IAFxD,EAIGuC,MAJH,CAIWL,KAAD,IAAgB,CAAC,CAACA,KAJ5B,CADF;;IAOA,IAAI,KAAK9B,mBAAT,EAA8B;MAC5B,KAAKzC,cAAL,CAAoBkG,YAApB;MACA,KAAKjG,gBAAL,CACGkI,uBADH,CAC2B;QACvBC,cAAc,EAAEJ,oBADO;QAEvB3F,EAAE,EAAE9C,QAAQ,CAAC,KAAKkD,mBAAL,CAAyBJ,EAA1B;MAFW,CAD3B,EAKGuB,SALH,CAKa;QACT0C,IAAI,EAAGqB,CAAD,IAAM;UACV,IAAI,KAAKlF,mBAAT,EAA8B;YAC5B,KAAKA,mBAAL,CAAyBE,kCAAzB,GACEqF,oBADF;UAED;;UACD,KAAKxD,8BAAL;UACA,KAAKzE,GAAL,CAASgC,aAAT;UACAtC,IAAI,CAAC8G,IAAL,CACE,KAAKrG,gBAAL,CAAsBG,OAAtB,CAA8B,cAA9B,CADF,EAEE,KAAKH,gBAAL,CAAsBG,OAAtB,CACE,wCADF,CAFF,EAKE,SALF;UAOA,KAAKL,cAAL,CAAoBqG,WAApB;QACD,CAhBQ;QAiBTM,KAAK,EAAG0B,GAAD,IAAQ;UACb5I,IAAI,CAAC8G,IAAL,CAAU;YACRK,IAAI,EAAE,OADE;YAERrE,KAAK,EAAE,KAAKrC,gBAAL,CAAsBG,OAAtB,CACL,sCADK,CAFC;YAKRwG,IAAI,EAAEwB,GAAG,CAACvB;UALF,CAAV;UAOA,KAAK9G,cAAL,CAAoBqG,WAApB;QACD;MA1BQ,CALb;IAiCD;EACF;;EAEDiC,iBAAiB;IACf,KAAK1G,iBAAL,GAAyB,KAAzB;IACA,KAAK2G,aAAL,GAAqB,KAArB;IACA,KAAK1G,iBAAL,GAAyB,EAAzB;IACA,KAAKC,wBAAL,GAAgC,EAAhC;IACA,KAAK/B,GAAL,CAASgC,aAAT;IACA,KAAKyG,4BAAL,CAAkC3C,IAAlC;IAEA4C,UAAU,CAAC,MAAK;MACd,KAAK7G,iBAAL,GAAyB,IAAzB;MACA,KAAK7B,GAAL,CAASgC,aAAT;IACD,CAHS,EAGP,GAHO,CAAV;EAID;;EAkCD2G,kBAAkB,CAACC,WAAD,EAA0B;IAC1C,KAAK9G,iBAAL,GAAyB8G,WAAzB;IACA,KAAK5I,GAAL,CAASgC,aAAT;EACD;;EAED6G,kBAAkB,CAACC,UAAD,EAAuB;IACvC,IAAI,CAACA,UAAU,CAACC,KAAhB,EAAuB;MACrB,KAAKhH,wBAAL,CAA8BiH,IAA9B,CAAmCF,UAAnC;MACA,KAAK9I,GAAL,CAASgC,aAAT;IACD;;IAEDiH,OAAO,CAACC,GAAR,CAAY,KAAKpH,iBAAjB;IACA,KAAKA,iBAAL,GAAyB,KAAKA,iBAAL,CAAuB+C,MAAvB,CAA+BsE,CAAD,IAAYA,CAAC,KAAKL,UAAhD,CAAzB;IACAG,OAAO,CAACC,GAAR,CAAY,KAAKpH,iBAAjB;IACA,KAAK9B,GAAL,CAASgC,aAAT;EACD;;EAEDC,iBAAiB;;;IACf,KAAKuG,aAAL,GAAqB,KAArB;IAEAS,OAAO,CAACC,GAAR,CAAY,KAAKpH,iBAAjB;;IAEA,IAAI,EAAC,WAAKA,iBAAL,MAAsB,IAAtB,IAAsBa,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEsB,MAAzB,CAAJ,EAAqC;MACnC,KAAKuE,aAAL,GAAqB,IAArB;MACA,KAAKxI,GAAL,CAASgC,aAAT;MACA;IACD;;IAED,IAAI,eAAI,IAAJ,aAAI,MAAJ,GAAI,MAAJ,QAAMU,mBAAN,MAAyB,IAAzB,IAAyBG,aAAzB,GAAyB,MAAzB,GAAyBA,GAAEP,EAA/B,EAAmC;MACjC,KAAKrC,cAAL,CAAoBkG,YAApB;MAEA,KAAKjG,gBAAL,CAAsBkJ,cAAtB,CAAqC,KAAK1G,mBAAL,CAAyBJ,EAA9D,EAAkE,KAAKR,iBAAvE,EAA0F+B,SAA1F,CAAoG;QAClG0C,IAAI,EAAGe,QAAD,IAAkB;UACtB5H,IAAI,CAAC8G,IAAL,CACE,KAAKrG,gBAAL,CAAsBG,OAAtB,CAA8B,cAA9B,CADF,EAEE,KAAKH,gBAAL,CAAsBG,OAAtB,CAA8B,8BAA9B,CAFF,EAGE,SAHF;UAKA,KAAKwB,iBAAL,GAAyB,EAAzB;UACA,KAAKC,wBAAL,GAAgC,EAAhC;UACA,KAAKF,iBAAL,GAAyB,KAAzB;UACA,KAAK7B,GAAL,CAASgC,aAAT;UACA,KAAKyG,4BAAL,CAAkC9B,KAAlC;UACA,KAAK1G,cAAL,CAAoBqG,WAApB;QACD,CAbiG;QAclGM,KAAK,EAAGA,KAAD,IAAe;UACpBlH,IAAI,CAAC8G,IAAL,CAAU;YACRK,IAAI,EAAE,OADE;YAERrE,KAAK,EAAE,KAAKrC,gBAAL,CAAsBG,OAAtB,CAA8B,YAA9B,CAFC;YAGRwG,IAAI,EAAEF,KAAK,CAACG;UAHJ,CAAV;UAKA,KAAK9G,cAAL,CAAoBqG,WAApB;QACD;MArBiG,CAApG;IAuBD;EAEF;;AA3hB8B;;;mBAApBxG,sBAAoBH;AAAA;;;QAApBG;EAAoBuJ;EAAAC;IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MClCjC3J;MAgLAA;MAmCAA,wCAAgF,CAAhF,EAAgF,KAAhF,EAAgF,CAAhF,EAAgF,CAAhF,EAAgF,KAAhF,EAAgF,CAAhF,EAAgF,CAAhF,EAAgF,KAAhF,EAAgF,CAAhF;MAIQA;MACAA;MACEA;MACFA;MAEFA;MACEA;MACAA;MACEA;MACFA;MAGJA;MACAA,gCAAiB,EAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,EAAjB,EAAiB,OAAjB,EAAiB,EAAjB;MAGMA;;;MAEFA;MACAA;;;;MASAA;MAMAA;MAUFA;MAEAA,iCAA4B,EAA5B,EAA4B,OAA5B,EAA4B,EAA5B;MAEIA;;;MAEFA;MACAA;;;;MAQAA;MAMFA;MAKNA,0CAA4D,EAA5D,EAA4D,KAA5D,EAA4D,EAA5D;MAEIA;;MAKFA;MAGFA;MACEA;MACEA,iCAGC,EAHD,EAGC,KAHD,EAGC,EAHD,EAGC,EAHD,EAGC,KAHD,EAGC,EAHD,EAGC,EAHD,EAGC,KAHD,EAGC,EAHD;MAOQA;MAMAA;MAAmCA;MAAoBA;MAI7DA;MACEA;MAgBFA;MAEJA;MACFA;MAEAA;MACEA;MACEA;MA4BFA;MACFA;;;;;;MA/WMA,+CAA2B,UAA3B,EAA2B4J,GAA3B;MAmN6B5J;MAAAA;MACOA;MAAAA;MAKhCA;MAAAA;MAMAA;MAAAA;MAQAA;MAAAA;MAKAA;MAAAA;MAOaA;MAAAA;MAMAA;MAAAA;MAcbA;MAAAA;MAKAA;MAAAA;MAMaA;MAAAA;MAWEA;MAAAA;MAGnBA;MAAAA,qFAA0D,aAA1D,EAA0D6J,kBAA1D;MAO6B7J;MAAAA;MAI7BA;MAAAA;MAmBoBA;MAAAA;MAmBGA;MAAAA;MACnBA;MAAAA", "names": ["EventEmitter", "FormArray", "FormControl", "FormGroup", "Validators", "toNumber", "finalize", "<PERSON><PERSON>", "i0", "_r13", "ctx_r11", "CompanyCodeComponent", "constructor", "cdr", "spinnerService", "companiesService", "translateService", "requestTypeData", "fb", "instant", "modalTitle", "hideClose<PERSON><PERSON>on", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modalDialogConfig", "backdrop", "size", "keyboard", "centered", "companyEditorModalTitle", "dismissButtonLabel", "modalDismissButtonLabel", "closeButtonLabel", "on<PERSON><PERSON><PERSON>", "companyCodeFormGroup", "reset", "shouldClose", "<PERSON><PERSON><PERSON><PERSON>", "isSubmitted", "valid", "addNewCompany", "fusionEnableModalTitle", "updateFussionIntegration", "evidenceFormReady", "evidenceDocuments", "deletedEvidenceDocuments", "detectChanges", "uploadNewEvidence", "fusionIntegrationColumns", "data", "map", "d", "id", "col", "title", "formControls", "selectedCompanyCode", "_a", "fusionIntegrationForRequestTypeIds", "_b", "includes", "selectAllControl", "fusionIntegrationFormGroup", "group", "requestTypes", "selectAll", "ngOnInit", "companyCode", "required", "companyName", "selected<PERSON><PERSON><PERSON>", "entityName", "get", "_c", "valueChanges", "subscribe", "bool", "patchValue", "Array", "length", "fill", "emitEvent", "_d", "val", "allSelected", "every", "value", "setFusionIntegrationEnabledFor", "ngOnChanges", "fusionIntegrationEnableFor", "find", "filter", "getRequestTypesControls", "controls", "isAnyRequestTypeSelected", "some", "control", "updateMultiNaturalAccount", "enableMultiNatualAccount", "isAllRequestTypeSelected", "isRequiredError", "FContorl", "<PERSON><PERSON><PERSON><PERSON>", "touched", "isPatternError", "companyCodeFormController", "openCompanyCodeEditorModal", "companyCodeEditorComponent", "open", "requestPayload", "code", "name", "entityId", "startSpinner", "createCompanyCode", "pipe", "stopSpinner", "next", "fire", "onCompanyChange", "emit", "close", "error", "icon", "text", "message", "deactivateCompanyCode", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "then", "response", "isConfirmed", "openHistoryModel", "getCompanyHistory", "companyHistory", "historyModalComponent", "_", "openFusionIntegrationModal", "<PERSON><PERSON><PERSON><PERSON>", "allValuesSelected", "fusionIntegrationModal", "selectedRequestTypes", "checked", "index", "updateFusionIntegration", "requestTypeIds", "err", "openEvidenceModel", "evidenceError", "uploadEvidenceModelComponent", "setTimeout", "onAttachmentEdited", "attachments", "onAttachmentDelete", "attachment", "IsNew", "push", "console", "log", "x", "uploadEvidence", "selectors", "viewQuery", "_r1", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\modules\\admin\\companies\\company-code\\company-code.component.ts", "C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\modules\\admin\\companies\\company-code\\company-code.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectorRef,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnChanges,\r\n  OnInit,\r\n  Output,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport {\r\n  FormArray,\r\n  FormBuilder,\r\n  FormControl,\r\n  FormGroup,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { HistoryResponse } from '@core/interfaces/history-response';\r\nimport { ModalComponent, ModalConfig } from '@core/modules/partials';\r\nimport { SpinnerService } from '@core/services/common/spinner.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { toNumber } from 'lodash';\r\nimport { finalize } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\nimport { CompanyCodeResponse } from '../../core/models';\r\nimport { CompaniesService } from '../../core/services';\r\nimport { RequestTypeData } from '@core/data/request-type';\r\nimport { Attachment } from '@core/models';\r\n\r\n@Component({\r\n  selector: 'app-company-code',\r\n  templateUrl: './company-code.component.html',\r\n  styleUrls: ['./company-code.component.scss'],\r\n})\r\nexport class CompanyCodeComponent implements OnChanges, OnInit {\r\n  @Input('selectedEntity') selectedEntity: {\r\n    entityId: number;\r\n    entityName: string;\r\n    entityCode: string;\r\n  };\r\n  @Input('selectedCompanyCode') selectedCompanyCode: CompanyCodeResponse | null;\r\n\r\n  @Output() onCompanyChange = new EventEmitter<boolean>();\r\n\r\n  public isEditMode: boolean = false;\r\n  public companyCodeFormGroup: FormGroup;\r\n  public isSubmitted: boolean = false;\r\n  public companyEditorModalTitle: string =\r\n    this.translateService.instant('MENU.ADD_COMPANY');\r\n  public modalDismissButtonLabel: string = this.translateService.instant(\r\n    'FORM.BUTTON.SAVE_BUTTON'\r\n  );\r\n\r\n  public fusionEnableModalTitle: string = this.translateService.instant(\r\n    'MENU.CHOOSE_REQUEST_TYPES_FOR_FUSION_INTEGRATION'\r\n  );\r\n\r\n  evidenceError: boolean = false;\r\n  evidenceFormReady: boolean = false;\r\n  evidenceDocuments: Attachment[] = [];\r\n  deletedEvidenceDocuments: Attachment[] = [];\r\n\r\n  enableMultiNatualAccount: boolean = false;\r\n\r\n  @ViewChild('uploadEvidence') private uploadEvidenceModelComponent: ModalComponent;\r\n\r\n  @ViewChild('companyCodeEditorModal')\r\n  private companyCodeEditorComponent: ModalComponent;\r\n\r\n  historyModalConfig: ModalConfig = {\r\n    modalTitle: this.translateService.instant(\r\n      'FORM.BUTTON.COMPANY_HISTORY_BUTTON'\r\n    ),\r\n    hideCloseButton() {\r\n      return true;\r\n    },\r\n    hideDismissButton() {\r\n      return true;\r\n    },\r\n    modalDialogConfig: {\r\n      backdrop: 'static',\r\n      size: 'lg',\r\n      keyboard: false,\r\n      centered: false,\r\n    },\r\n  };\r\n\r\n  companyHistory: HistoryResponse[] = [];\r\n  @ViewChild('historyModal') private historyModalComponent: ModalComponent;\r\n\r\n  public companyCodeEditorModalConfig: ModalConfig = {\r\n    modalTitle: this.companyEditorModalTitle,\r\n    dismissButtonLabel: this.modalDismissButtonLabel,\r\n    closeButtonLabel: this.translateService.instant(\r\n      'FORM.BUTTON.CANCEL_BUTTON'\r\n    ),\r\n    onDismiss: () => {\r\n      this.companyCodeFormGroup.reset();\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      this.companyCodeFormGroup.reset();\r\n      return true;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.isSubmitted = true;\r\n      if (!this.companyCodeFormGroup.valid) {\r\n        return false;\r\n      }\r\n      this.addNewCompany();\r\n      return false;\r\n    },\r\n    modalDialogConfig: {\r\n      backdrop: 'static',\r\n      size: 'm',\r\n      keyboard: false,\r\n      centered: true,\r\n    },\r\n  };\r\n\r\n  public fusionIntegrationModalConfig: ModalConfig = {\r\n    modalTitle: this.fusionEnableModalTitle,\r\n    dismissButtonLabel: this.modalDismissButtonLabel,\r\n    closeButtonLabel: this.translateService.instant(\r\n      'FORM.BUTTON.CANCEL_BUTTON'\r\n    ),\r\n    onDismiss: () => {\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      return true;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.updateFussionIntegration();\r\n      return false;\r\n    },\r\n    modalDialogConfig: {\r\n      backdrop: 'static',\r\n      size: 'lg',\r\n      keyboard: false,\r\n      centered: true,\r\n    },\r\n  };\r\n  @ViewChild('fusionIntegrationModal')\r\n  private fusionIntegrationModal: ModalComponent;\r\n  public fusionIntegrationFormGroup: FormGroup;\r\n  public fusionIntegrationColumns: { id: number; col: string }[] = [];\r\n  public fusionIntegrationEnableFor: (string | undefined)[] = [];\r\n\r\n  constructor(\r\n    private readonly cdr: ChangeDetectorRef,\r\n    private readonly spinnerService: SpinnerService,\r\n    private readonly companiesService: CompaniesService,\r\n    private readonly translateService: TranslateService,\r\n    private readonly requestTypeData: RequestTypeData,\r\n    private readonly fb: FormBuilder\r\n  ) {\r\n    this.fusionIntegrationColumns = this.requestTypeData.data.map((d) => ({\r\n      id: d.id,\r\n      col: d.title,\r\n    }));\r\n    // Create a FormControl for each available colunm, initialize them as unchecked, and put them in an array\r\n    const formControls = this.fusionIntegrationColumns.map(\r\n      (col) =>\r\n        new FormControl(\r\n          this.selectedCompanyCode?.fusionIntegrationForRequestTypeIds?.includes(\r\n            col.id\r\n          )\r\n        )\r\n    );\r\n\r\n    // Create a FormControl for the select/unselect all checkbox\r\n    const selectAllControl = new FormControl();\r\n    this.fusionIntegrationFormGroup = this.fb.group({\r\n      requestTypes: new FormArray(formControls),\r\n      selectAll: selectAllControl,\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.companyCodeFormGroup = new FormGroup({\r\n      companyCode: new FormControl('', Validators.required),\r\n      companyName: new FormControl(\r\n        this.selectedEntity?.entityName || '',\r\n        Validators.required\r\n      ),\r\n    });\r\n\r\n    // Subscribe to changes on the selectAll checkbox\r\n    this.fusionIntegrationFormGroup\r\n      ?.get('selectAll')\r\n      ?.valueChanges.subscribe((bool) => {\r\n        this.fusionIntegrationFormGroup\r\n          ?.get('requestTypes')\r\n          ?.patchValue(Array(this.fusionIntegrationColumns.length).fill(bool), {\r\n            emitEvent: false,\r\n          });\r\n      });\r\n\r\n    // Subscribe to changes on the colunm name checkboxes\r\n    this.fusionIntegrationFormGroup\r\n      .get('requestTypes')\r\n      ?.valueChanges.subscribe((val) => {\r\n        const allSelected = val.every((bool: any) => bool);\r\n        if (\r\n          this.fusionIntegrationFormGroup.get('selectAll')?.value !==\r\n          allSelected\r\n        ) {\r\n          this.fusionIntegrationFormGroup\r\n            .get('selectAll')\r\n            ?.patchValue(allSelected, { emitEvent: false });\r\n        }\r\n      });\r\n\r\n    this.setFusionIntegrationEnabledFor();\r\n  }\r\n\r\n  ngOnChanges(): void { }\r\n\r\n  private setFusionIntegrationEnabledFor() {\r\n    this.fusionIntegrationEnableFor =\r\n      this.selectedCompanyCode?.fusionIntegrationForRequestTypeIds\r\n        ?.map((id) => this.requestTypeData.data.find((d) => d.id === id)?.title)\r\n        ?.filter((title) => !!title) || [];\r\n  }\r\n\r\n  public getRequestTypesControls() {\r\n    return (this.fusionIntegrationFormGroup.get('requestTypes') as FormArray)\r\n      .controls;\r\n  }\r\n\r\n  public isAnyRequestTypeSelected() {\r\n    return this.getRequestTypesControls().some((control: any) => control.value);\r\n  }\r\n\r\n  public updateMultiNaturalAccount() {\r\n    this.enableMultiNatualAccount = !this.enableMultiNatualAccount;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  public isAllRequestTypeSelected() {\r\n    return this.getRequestTypesControls().every(\r\n      (control: any) => control.value\r\n    );\r\n  }\r\n\r\n  public isRequiredError(FContorl: string): boolean | undefined {\r\n    return (\r\n      this.companyCodeFormGroup.get(FContorl)?.hasError('required') &&\r\n      (this.companyCodeFormGroup.get(FContorl)?.touched || this.isSubmitted)\r\n    );\r\n  }\r\n\r\n  public isPatternError(FContorl: string): boolean | undefined {\r\n    return (\r\n      this.companyCodeFormGroup.get(FContorl)?.hasError('pattern') &&\r\n      (this.companyCodeFormGroup.get(FContorl)?.touched || this.isSubmitted)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get the company code form controller reference.\r\n   */\r\n  get companyCodeFormController() {\r\n    return this.companyCodeFormGroup.get('companyCode');\r\n  }\r\n\r\n  /**\r\n   * Open company code editor.\r\n   * @returns\r\n   */\r\n  public async openCompanyCodeEditorModal() {\r\n    this.isSubmitted = false;\r\n    return await this.companyCodeEditorComponent.open();\r\n  }\r\n\r\n  /**\r\n   * Add new company in the business unit.\r\n   */\r\n  private addNewCompany() {\r\n    this.isSubmitted = false;\r\n    if (this.companyCodeFormGroup?.valid) {\r\n      const requestPayload = {\r\n        code: this.companyCodeFormGroup.get('companyCode')?.value,\r\n        name: this.companyCodeFormGroup.get('companyName')?.value,\r\n        entityId: this.selectedEntity.entityId,\r\n      };\r\n      this.spinnerService.startSpinner();\r\n      this.companiesService\r\n        .createCompanyCode(requestPayload)\r\n        .pipe(\r\n          finalize(() => {\r\n            this.spinnerService.stopSpinner();\r\n          })\r\n        )\r\n        .subscribe({\r\n          next: () => {\r\n            Swal.fire(\r\n              this.translateService.instant('SWAL.SUCCESS'),\r\n              this.translateService.instant(\r\n                'SWAL.ADD_NEW_COMPANY_CODE_SUCCESS'\r\n              ),\r\n              'success'\r\n            );\r\n            this.onCompanyChange.emit(true);\r\n            this.companyCodeFormGroup.reset();\r\n            this.companyCodeEditorComponent.close();\r\n            this.isSubmitted = true;\r\n          },\r\n          error: (error) => {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: this.translateService.instant(\r\n                'SWAL.ADD_NEW_COMPANY_CODE_ERROR'\r\n              ),\r\n              text: error.message,\r\n            });\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deactivate current active company code for the business unit entity.\r\n   */\r\n  public deactivateCompanyCode() {\r\n    Swal.fire({\r\n      title: this.translateService.instant('SWAL.CONFIRMATION'),\r\n      text: this.translateService.instant('SWAL.COMPANY_CODE_INACTIVE_CONFIRM'),\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#3085d6',\r\n      cancelButtonColor: '#d33',\r\n      confirmButtonText: this.translateService.instant(\r\n        'SWAL.DEACTIVATE_BUTTON'\r\n      ),\r\n    }).then((response) => {\r\n      if (response.isConfirmed && this.selectedCompanyCode) {\r\n        this.spinnerService.startSpinner();\r\n        this.companiesService\r\n          .deactivateCompanyCode(this.selectedCompanyCode?.id)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.spinnerService.stopSpinner();\r\n            })\r\n          )\r\n          .subscribe({\r\n            next: () => {\r\n              Swal.fire(\r\n                this.translateService.instant('SWAL.SUCCESS'),\r\n                this.translateService.instant(\r\n                  'SWAL.MARK_COMPANY_CODE_INACTIVE'\r\n                ),\r\n                'success'\r\n              );\r\n              this.onCompanyChange.emit(true);\r\n            },\r\n            error: (error) => {\r\n              Swal.fire({\r\n                icon: 'error',\r\n                title: this.translateService.instant(\r\n                  'SWAL.DEACTIVATE_COMPANY_CODE_ERROR'\r\n                ),\r\n                text: error.message,\r\n              });\r\n            },\r\n          });\r\n      }\r\n      this.cdr.detectChanges();\r\n    });\r\n  }\r\n\r\n  openHistoryModel() {\r\n    this.spinnerService.startSpinner();\r\n\r\n    this.companiesService\r\n      .getCompanyHistory(this.selectedEntity.entityId)\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.companyHistory = response as HistoryResponse[];\r\n          this.cdr.detectChanges();\r\n          this.historyModalComponent.open();\r\n          this.spinnerService.stopSpinner();\r\n        },\r\n        error: (_) => {\r\n          this.companyHistory = [] as HistoryResponse[];\r\n          this.cdr.detectChanges();\r\n          this.historyModalComponent.open();\r\n          this.spinnerService.stopSpinner();\r\n\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: this.translateService.instant('SWAL.ERROR'),\r\n            text: this.translateService.instant('SWAL.ERROR_HISTORY_FETCH'),\r\n          });\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Open fusion integration update modal.\r\n   */\r\n  public openFusionIntegrationModal() {\r\n    const selectedValues = this.fusionIntegrationColumns.map((value) => {\r\n      return this.selectedCompanyCode?.fusionIntegrationForRequestTypeIds?.includes(\r\n        value.id\r\n      );\r\n    });\r\n\r\n    const allValuesSelected = selectedValues.every((value) => value);\r\n\r\n    this.fusionIntegrationFormGroup\r\n      ?.get('requestTypes')\r\n      ?.patchValue(selectedValues, { emitEvent: false });\r\n\r\n    this.fusionIntegrationFormGroup\r\n      .get('selectAll')\r\n      ?.patchValue(allValuesSelected, { emitEvent: false });\r\n\r\n    this.fusionIntegrationModal.open();\r\n  }\r\n\r\n  /**\r\n   * Update the fusion integration enablement for the AFE request types\r\n   */\r\n  public updateFussionIntegration() {\r\n    const selectedRequestTypes =\r\n      this.fusionIntegrationFormGroup.value.requestTypes\r\n        .map((checked: any, index: any) =>\r\n          checked ? this.fusionIntegrationColumns[index].id : null\r\n        )\r\n        .filter((value: any) => !!value);\r\n\r\n    if (this.selectedCompanyCode) {\r\n      this.spinnerService.startSpinner();\r\n      this.companiesService\r\n        .updateFusionIntegration({\r\n          requestTypeIds: selectedRequestTypes,\r\n          id: toNumber(this.selectedCompanyCode.id),\r\n        })\r\n        .subscribe({\r\n          next: (_) => {\r\n            if (this.selectedCompanyCode) {\r\n              this.selectedCompanyCode.fusionIntegrationForRequestTypeIds =\r\n                selectedRequestTypes;\r\n            }\r\n            this.setFusionIntegrationEnabledFor();\r\n            this.cdr.detectChanges();\r\n            Swal.fire(\r\n              this.translateService.instant('SWAL.SUCCESS'),\r\n              this.translateService.instant(\r\n                'SWAL.FUSION_INTEGRATION_UPDATE_SUCCESS'\r\n              ),\r\n              'success'\r\n            );\r\n            this.spinnerService.stopSpinner();\r\n          },\r\n          error: (err) => {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: this.translateService.instant(\r\n                'SWAL.FUSION_INTEGRATION_UPDATE_ERROR'\r\n              ),\r\n              text: err.message,\r\n            });\r\n            this.spinnerService.stopSpinner();\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  openEvidenceModel() {\r\n    this.evidenceFormReady = false;\r\n    this.evidenceError = false;\r\n    this.evidenceDocuments = [];\r\n    this.deletedEvidenceDocuments = [];\r\n    this.cdr.detectChanges();\r\n    this.uploadEvidenceModelComponent.open();\r\n\r\n    setTimeout(() => {\r\n      this.evidenceFormReady = true;\r\n      this.cdr.detectChanges();\r\n    }, 100);\r\n  }\r\n\r\n  public uploadEvidenceConfig: ModalConfig = {\r\n    modalTitle: this.translateService.instant('FORM.BUTTON.UPLOAD_EVIDENCE_BUTTON'),\r\n    dismissButtonLabel: this.translateService.instant('FORM.BUTTON.UPLOAD_BUTTON'),\r\n    closeButtonLabel: this.translateService.instant(\r\n      'FORM.BUTTON.CANCEL_BUTTON'\r\n    ),\r\n    onDismiss: () => {\r\n      this.evidenceFormReady = false;\r\n      this.evidenceDocuments = [];\r\n      this.deletedEvidenceDocuments = [];\r\n      this.cdr.detectChanges();\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      this.evidenceFormReady = false;\r\n      this.evidenceDocuments = [];\r\n      this.deletedEvidenceDocuments = [];\r\n      this.cdr.detectChanges();\r\n      return true;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.uploadNewEvidence();\r\n      return false;\r\n    },\r\n    modalDialogConfig: {\r\n      backdrop: 'static',\r\n      size: 'lg',\r\n      keyboard: false,\r\n      centered: false,\r\n    },\r\n  };\r\n\r\n  onAttachmentEdited(attachments: Attachment[]) {\r\n    this.evidenceDocuments = attachments as Attachment[];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onAttachmentDelete(attachment: Attachment) {\r\n    if (!attachment.IsNew) {\r\n      this.deletedEvidenceDocuments.push(attachment);\r\n      this.cdr.detectChanges();\r\n    }\r\n\r\n    console.log(this.evidenceDocuments);\r\n    this.evidenceDocuments = this.evidenceDocuments.filter((x: any) => x !== attachment) as Attachment[];\r\n    console.log(this.evidenceDocuments);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  uploadNewEvidence() {\r\n    this.evidenceError = false;\r\n\r\n    console.log(this.evidenceDocuments);\r\n\r\n    if (!this.evidenceDocuments?.length) {\r\n      this.evidenceError = true;\r\n      this.cdr.detectChanges();\r\n      return;\r\n    }\r\n\r\n    if (this?.selectedCompanyCode?.id) {\r\n      this.spinnerService.startSpinner();\r\n\r\n      this.companiesService.uploadEvidence(this.selectedCompanyCode.id, this.evidenceDocuments).subscribe({\r\n        next: (response: any) => {\r\n          Swal.fire(\r\n            this.translateService.instant('SWAL.SUCCESS'),\r\n            this.translateService.instant('SWAL.UPLOAD_EVIDENCE_SUCCESS'),\r\n            'success'\r\n          );\r\n          this.evidenceDocuments = [];\r\n          this.deletedEvidenceDocuments = [];\r\n          this.evidenceFormReady = false;\r\n          this.cdr.detectChanges();\r\n          this.uploadEvidenceModelComponent.close();\r\n          this.spinnerService.stopSpinner();\r\n        },\r\n        error: (error: any) => {\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: this.translateService.instant('SWAL.ERROR'),\r\n            text: error.message,\r\n          });\r\n          this.spinnerService.stopSpinner();\r\n        },\r\n      });\r\n    }\r\n\r\n  }\r\n}\r\n", "<div *ngIf=\"selectedCompanyCode; else noDataMessage\" class=\"card mb-5 mb-xl-8\">\r\n  <!-- begin::Header -->\r\n  <div class=\"card-header border-0 pt-5\">\r\n    <h3 class=\"card-title align-items-start flex-column\">\r\n      <span class=\"card-label fw-bolder fs-3 mb-1\">{{\r\n        \"FORM.LABEL.COMPANY\" | translate\r\n      }}</span>\r\n    </h3>\r\n\r\n    <div class=\"row\">\r\n      <div class=\"d-flex justify-content-end\">\r\n        <div class=\"menu-item px-3\">\r\n          <button\r\n            (click)=\"openEvidenceModel()\"\r\n            class=\"btn btn-sm btn-primary fw-bold btnRounded px-6 py-2 mx-1\"\r\n          >\r\n            <span translate=\"FORM.BUTTON.UPLOAD_EVIDENCE_BUTTON\"></span>\r\n          </button>\r\n        </div>\r\n        <button\r\n          appToggleProfileMenu\r\n          [transform]=\"'-30px, 50.5px, 0px'\"\r\n          type=\"button\"\r\n          class=\"btn btn-sm btn-icon btn-color-primary btn-active-light-primary\"\r\n        >\r\n          <img\r\n            width=\"45px\"\r\n            src=\"./assets/media/svg/icons/dpw-icons/menu.png\"\r\n            alt=\"next\"\r\n          />\r\n        </button>\r\n        <span\r\n          class=\"fs-6 fw-bold menu menu-column menu-gray-600 menu-rounded menu-state-bg menu-state-primary menu-sub menu-sub-dropdown py-4 w-275px\"\r\n        >\r\n          <div class=\"menu-item px-3\">\r\n            <div\r\n              class=\"menu-content fs-6 text-dark fw-bolder px-3 py-4\"\r\n              translate=\"MENU.QUICK_ACTION\"\r\n            ></div>\r\n          </div>\r\n\r\n          <div class=\"separator mb-3 opacity-75\"></div>\r\n\r\n          <div class=\"menu-item px-3 mb-2\">\r\n            <a\r\n              title=\"{{ 'FORM.BUTTON.DEACTIVATE' | translate }}\"\r\n              (click)=\"deactivateCompanyCode()\"\r\n              class=\"menu-link px-3 cursor-pointer\"\r\n            >\r\n              {{ \"FORM.BUTTON.DEACTIVATE\" | translate }}\r\n              {{ \"FORM.LABEL.COMPANY\" | translate }}\r\n            </a>\r\n          </div>\r\n\r\n          <div class=\"menu-item px-3\">\r\n            <a\r\n              title=\"{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}\"\r\n              (click)=\"openHistoryModel()\"\r\n              class=\"menu-link px-3 cursor-pointer\"\r\n              translate=\"{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}\"\r\n            >\r\n            </a>\r\n          </div>\r\n        </span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!-- end::Header -->\r\n  <!-- begin::Body -->\r\n  <div class=\"card-body py-3\">\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-3 col-sm-6 mb-4 mb-lg-5\">\r\n        <label\r\n          class=\"fw-bold text-muted\"\r\n          translate=\"FORM.LABEL.COMPANY_FUSSION_NUMBER\"\r\n        ></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          <div>\r\n            {{ selectedCompanyCode.code }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-3 col-sm-6 mb-4 mb-lg-5\">\r\n        <label\r\n          class=\"fw-bold text-muted\"\r\n          translate=\"FORM.LABEL.COMPANY_NAME\"\r\n        ></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          <div>\r\n            {{ selectedCompanyCode.name }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-3 col-sm-6 mb-4 mb-lg-5\">\r\n        <label\r\n          class=\"fw-bold text-muted\"\r\n          translate=\"FORM.LABEL.ENTITY_NAME\"\r\n        ></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          <div>\r\n            {{ selectedEntity.entityName }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-3 col-sm-6 mb-4 mb-lg-5\">\r\n        <label\r\n          class=\"fw-bold text-muted\"\r\n          translate=\"FORM.LABEL.ENTITY_CODE\"\r\n        ></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          <div>\r\n            {{ selectedCompanyCode.entityCode }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-lg-6 col-sm-6 mb-4 mb-lg-5\">\r\n        <label\r\n          class=\"fw-bold text-muted\"\r\n          translate=\"FORM.LABEL.FUSION_INTEGRATION_ENABLED_FOR\"\r\n        ></label>\r\n        <button\r\n          (click)=\"openFusionIntegrationModal()\"\r\n          type=\"button\"\r\n          class=\"btn btn-sm mb-2 editBtn btnRounded fw-bold ps-4 pe-4 py-1 mx-2\"\r\n          translate=\"FORM.BUTTON.EDIT_BUTTON\"\r\n        ></button>\r\n        <div class=\"h4 text-gray-800\">\r\n          <div *ngIf=\"fusionIntegrationEnableFor.length; else disable\">\r\n            <ng-container\r\n              *ngFor=\"let requestType of fusionIntegrationEnableFor\"\r\n            >\r\n              <span class=\"badge badge-success badge-space\">\r\n                {{ requestType }}\r\n              </span>\r\n            </ng-container>\r\n          </div>\r\n          <ng-template #disable>\r\n            <div>\r\n              <span class=\"badge badge-danger\"> Disabled </span>\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-lg-6 col-sm-6 mb-4 mb-lg-5\">\r\n        <!-- Toggle Button to enable disable natural account -->\r\n        <div class=\"col-lg-6 col-md-6 col-sm-12\">\r\n          <label\r\n          class=\"fw-bold text-muted\"\r\n          translate=\"FORM.LABEL.FUSION_INTEGRATION_ENABLED_FOR\"\r\n        ></label>\r\n        \r\n          <div class=\"d-flex justify-content-end fw-bold\">\r\n            {{ \"FORM.LABEL.SINGLE_NATURAL_ACCOUNT\" | translate }}\r\n            <div class=\"form-switch form-check ms-2\">\r\n              <input\r\n                (click)=\"updateMultiNaturalAccount()\"\r\n                [(ngModel)]=\"enableMultiNatualAccount\"\r\n                type=\"checkbox\"\r\n                class=\"form-check-input\"\r\n                id=\"site_state\"\r\n              />\r\n            </div>\r\n            <label\r\n              for=\"site_state\"\r\n              class=\"form-check-label\"\r\n              translate=\"FORM.LABEL.MULTIPLE_NATURAL_ACCOUNTS\"\r\n            ></label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- begin::Body -->\r\n  </div>\r\n</div>\r\n<ng-template #noDataMessage>\r\n  <app-empty-state\r\n    [message]=\"\r\n      'EMPTY_STATE.EMPTY_COMPANY_CODE_LIST'\r\n        | translate : { entityName: selectedEntity.entityName }\r\n    \"\r\n    [showImage]=\"true\"\r\n  >\r\n    <div class=\"history d-flex justify-content-end\">\r\n      <div class=\"outline-btn-light\">\r\n        <button\r\n          type=\"button\"\r\n          title=\"{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}\"\r\n          (click)=\"openHistoryModel()\"\r\n          class=\"btn btn-sm outline-btn-light mx-2\"\r\n          translate=\"{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}\"\r\n        ></button>\r\n      </div>\r\n    </div>\r\n\r\n    <span class=\"action position-relative d-inline-block text-danger\">\r\n      <a\r\n        (click)=\"openCompanyCodeEditorModal()\"\r\n        class=\"text-danger opacity-75-hover cursor-pointer\"\r\n      >\r\n        {{ \"FORM.LABEL.SETUP_COMPANY\" | translate }}\r\n      </a>\r\n      <span\r\n        class=\"position-absolute opacity-15 bottom-0 start-0 border-4 border-danger border-bottom w-100\"\r\n      >\r\n      </span>\r\n    </span>\r\n  </app-empty-state>\r\n</ng-template>\r\n\r\n<app-modal #companyCodeEditorModal [modalConfig]=\"companyCodeEditorModalConfig\">\r\n  <div class=\"w-100 px-5 bg-body rounded\" [formGroup]=\"companyCodeFormGroup\">\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-6 col-md-6 col-6 mb-6 mb-lg-5\">\r\n        <label class=\"fw-bold text-muted\" translate=\"LIST.ENTITY_NAME\"></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          {{ selectedEntity.entityName }}\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-6 col-md-6 col-6 mb-6 mb-lg-5\">\r\n        <label class=\"fw-bold text-muted\" translate=\"LIST.ENTITY_CODE\"></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          {{ selectedEntity.entityCode }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"separator my-2\"></div>\r\n    <div class=\"row\">\r\n      <div class=\"col-md-12 mb-5\">\r\n        <label class=\"form-label required\">\r\n          {{ \"FORM.PLACEHOLDER.ENTER\" | translate }}\r\n          {{ \"FORM.LABEL.COMPANY_FUSSION_NUMBER\" | translate }}\r\n        </label>\r\n        <input\r\n          name=\"companyCode\"\r\n          placeholder=\"{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{\r\n            'FORM.LABEL.COMPANY_FUSSION_NUMBER' | translate | lowercase\r\n          }}\"\r\n          class=\"form-control form-control-lg form-control-solid\"\r\n          formControlName=\"companyCode\"\r\n          pattern=\"[a-zA-Z0-9]{4}\"\r\n        />\r\n        <ng-container *ngIf=\"isRequiredError('companyCode')\">\r\n          <app-input-error-message\r\n            errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\"\r\n          >\r\n          </app-input-error-message>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"isPatternError('companyCode')\">\r\n          <app-input-error-message\r\n            errorMessage=\"{{\r\n              'FORM.VALIDATION.LENGTH_ERROR'\r\n                | translate\r\n                  : { name: 'FORM.LABEL.FUSSION_NUMBER' | translate, length: 4 }\r\n            }}\"\r\n          >\r\n          </app-input-error-message>\r\n        </ng-container>\r\n      </div>\r\n\r\n      <div class=\"col-md-12 mb-5\">\r\n        <label class=\"form-label required\">\r\n          {{ \"FORM.PLACEHOLDER.ENTER\" | translate }}\r\n          {{ \"FORM.LABEL.COMPANY_NAME\" | translate }}\r\n        </label>\r\n        <input\r\n          name=\"companyName\"\r\n          placeholder=\"{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{\r\n            'FORM.LABEL.COMPANY_NAME' | translate | lowercase\r\n          }}\"\r\n          class=\"form-control form-control-lg form-control-solid\"\r\n          formControlName=\"companyName\"\r\n        />\r\n        <ng-container *ngIf=\"isRequiredError('companyName')\">\r\n          <app-input-error-message\r\n            errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\"\r\n          >\r\n          </app-input-error-message>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</app-modal>\r\n\r\n<app-modal #historyModal [modalConfig]=\"historyModalConfig\">\r\n  <div class=\"w-100 px-1 bg-body rounded py-1\">\r\n    <app-history-logs\r\n      [title]=\"'FORM.BUTTON.COMPANY_HISTORY_BUTTON' | translate\"\r\n      [historyLogs]=\"companyHistory\"\r\n    >\r\n    </app-history-logs>\r\n  </div>\r\n</app-modal>\r\n\r\n<app-modal #fusionIntegrationModal [modalConfig]=\"fusionIntegrationModalConfig\">\r\n  <ng-container>\r\n    <div\r\n      class=\"w-100 p-5 bg-body rounded\"\r\n      [formGroup]=\"fusionIntegrationFormGroup\"\r\n    >\r\n      <div class=\"row p-1 p-lg-3 p-md-3 p-sm-3\">\r\n        <div class=\"col-12 col-md-12 p-0 m-0\">\r\n          <div class=\"form-check\">\r\n            <input\r\n              class=\"form-check-input\"\r\n              type=\"checkbox\"\r\n              value=\"\"\r\n              formControlName=\"selectAll\"\r\n            />\r\n            <label class=\"form-label fw-bold\"> Select/Deselect all </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"row p-1 p-lg-3 p-md-3 p-sm-3\">\r\n        <div\r\n          class=\"col-6 p-0 m-0\"\r\n          formArrayName=\"requestTypes\"\r\n          *ngFor=\"let col of getRequestTypesControls(); let i = index\"\r\n        >\r\n          <div class=\"form-check mt-3\">\r\n            <input\r\n              class=\"form-check-input\"\r\n              type=\"checkbox\"\r\n              [formControlName]=\"i\"\r\n            />\r\n            <label class=\"form-label fw-bold\">\r\n              {{ fusionIntegrationColumns[i].col }}\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n</app-modal>\r\n\r\n<ng-container>\r\n  <app-modal #uploadEvidence [modalConfig]=\"uploadEvidenceConfig\">\r\n    <div *ngIf=\"evidenceFormReady\">\r\n      <div class=\"row\">\r\n        <div class=\"fv-row mb-5 mb-lg-7\">\r\n          <app-upload-attachment\r\n            [isBoxShadow]=\"true\"\r\n            [labelHeader]=\"\r\n              'AFE_MODULE.SECTION_TITLE.SUPPORTING_DOCUMENTS' | translate\r\n            \"\r\n            [labelSubHeader]=\"'COMMON.ATTACHMENTS' | translate\"\r\n            [required]=\"false\"\r\n            [isShowHeader]=\"true\"\r\n            [isDescriptionRequired]=\"false\"\r\n            [isAdd]=\"true\"\r\n            [attachments]=\"evidenceDocuments\"\r\n            (attachmentDelete)=\"onAttachmentDelete($event)\"\r\n            (attachmentEdited)=\"onAttachmentEdited($event)\"\r\n            [isViewOnly]=\"false\"\r\n          >\r\n          </app-upload-attachment>\r\n          <ng-container *ngIf=\"evidenceError\">\r\n            <app-input-error-message\r\n              errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\"\r\n            >\r\n            </app-input-error-message>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </app-modal>\r\n</ng-container>\r\n"]}, "metadata": {}, "sourceType": "module"}