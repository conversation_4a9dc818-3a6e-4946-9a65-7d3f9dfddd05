{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/MyWorkspace/Projects/DpWorld/AFE_Revamp/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { ReplaceUrlVariable } from '@core/constants/urls.constants';\nimport { BehaviorSubject, Subject, finalize, takeUntil } from 'rxjs';\nimport { LOCALSTORAGE_KEY } from '@core/constants';\nimport { PermissionEnum } from '@core/enums/Permission';\nlet MytaskComponent = class MytaskComponent {\n  constructor(taskService, spinnerService, cdr, router, localStorageService, translateService, permissionService) {\n    this.taskService = taskService;\n    this.spinnerService = spinnerService;\n    this.cdr = cdr;\n    this.router = router;\n    this.localStorageService = localStorageService;\n    this.translateService = translateService;\n    this.permissionService = permissionService;\n    this.tasks = [];\n    this.filteredTasks = [];\n    this.loading = true;\n    this.filterLocalStorageKey = LOCALSTORAGE_KEY.TASK_LIST_FILTER;\n    this.filterModalTitle = this.translateService.instant('MENU.FILTER');\n    this.modalDismissButtonLabel = this.translateService.instant('FORM.BUTTON.APPLY');\n    this.filterChangeEvent = new BehaviorSubject(true);\n    this.isFilterModalReady = false;\n    this.permissionEnum = PermissionEnum;\n    this.filterModalConfig = {\n      modalTitle: this.filterModalTitle,\n      dismissButtonLabel: this.modalDismissButtonLabel,\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.RESET'),\n      onDismiss: () => {\n        this.isFilterModalReady = false;\n        return true;\n      },\n      shouldClose: () => {\n        this.isFilterModalReady = false;\n        this.filtersData = null;\n        this.localStorageService.set(this.filterLocalStorageKey, '');\n        this.isFilterModalReady = true;\n        this.cdr.detectChanges();\n        return false;\n      },\n      shouldDismiss: () => {\n        this.cdr.detectChanges();\n        this.localStorageService.set(this.filterLocalStorageKey, this.filtersData);\n        this.filterChangeEvent.next(true);\n        this.isFilterModalReady = false;\n        return true;\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: true\n      }\n    };\n    this.destroy$ = new Subject();\n  }\n\n  ngOnInit() {\n    this.fetchList();\n  }\n\n  fetchList(assignedTo = null) {\n    this.taskService.getUserPendingTasksList(assignedTo).pipe(finalize(() => {\n      this.spinnerService.stopSpinner();\n      this.loading = false;\n      this.cdr.detectChanges();\n    })).subscribe({\n      next: response => {\n        this.tasks = response;\n        this.localStorageService.watch(this.filterLocalStorageKey).pipe(takeUntil(this.destroy$)).subscribe({\n          next: val => {\n            this.storedFilterData = val;\n            this.filterTasks();\n          }\n        });\n      },\n      error: _ => {\n        this.tasks = [];\n      }\n    });\n  }\n\n  onUserAdded(value) {\n    if (value) {\n      this.spinnerService.startSpinner();\n      this.fetchList((value === null || value === void 0 ? void 0 : value.loginId) || null);\n    }\n  }\n\n  validateUserPermission(permission) {\n    return this.permissionService.checkPermissionByName(permission);\n  }\n\n  nagivateToTaskAction(task) {\n    const {\n      taskRelUrl\n    } = task;\n    this.router.navigateByUrl(ReplaceUrlVariable(`${taskRelUrl}`, {\n      taskId: task.id\n    }));\n  }\n\n  filterTasks() {\n    this.filteredTasks = this.tasks.filter(task => {\n      var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x;\n\n      return (!((_c = (_b = (_a = this.storedFilterData) === null || _a === void 0 ? void 0 : _a.obj) === null || _b === void 0 ? void 0 : _b.submittedBy) === null || _c === void 0 ? void 0 : _c.loginId) || ((_e = (_d = task.additionalInfo) === null || _d === void 0 ? void 0 : _d.proposal_created_by) === null || _e === void 0 ? void 0 : _e.toLowerCase()) === ((_j = (_h = (_g = (_f = this.storedFilterData) === null || _f === void 0 ? void 0 : _f.obj) === null || _g === void 0 ? void 0 : _g.submittedBy) === null || _h === void 0 ? void 0 : _h.loginId) === null || _j === void 0 ? void 0 : _j.toLowerCase())) && (!((_l = (_k = this.storedFilterData) === null || _k === void 0 ? void 0 : _k.obj) === null || _l === void 0 ? void 0 : _l.afeReferenceNumber) || ((_m = task.additionalInfo) === null || _m === void 0 ? void 0 : _m.proposalProjectReferenceNumber) === ((_p = (_o = this.storedFilterData) === null || _o === void 0 ? void 0 : _o.obj) === null || _p === void 0 ? void 0 : _p.afeReferenceNumber)) && (!((_r = (_q = this.storedFilterData) === null || _q === void 0 ? void 0 : _q.obj) === null || _r === void 0 ? void 0 : _r.fromAssignedDate) || (task === null || task === void 0 ? void 0 : task.createdOn) && this.createDateObjectWithOutTime(new Date(task.createdOn)).getTime() >= this.createDateObject((_t = (_s = this.storedFilterData) === null || _s === void 0 ? void 0 : _s.obj) === null || _t === void 0 ? void 0 : _t.fromAssignedDate).getTime()) && (!((_v = (_u = this.storedFilterData) === null || _u === void 0 ? void 0 : _u.obj) === null || _v === void 0 ? void 0 : _v.toAssignedDate) || task.createdOn && this.createDateObjectWithOutTime(new Date(task.createdOn)).getTime() <= this.createDateObject((_x = (_w = this.storedFilterData) === null || _w === void 0 ? void 0 : _w.obj) === null || _x === void 0 ? void 0 : _x.toAssignedDate).getTime());\n    });\n    this.cdr.detectChanges();\n  }\n\n  createDateObject(obj) {\n    const {\n      day,\n      month,\n      year\n    } = obj;\n    return new Date(year, month - 1, day, 0, 0, 0, 0);\n  }\n\n  createDateObjectWithOutTime(date) {\n    return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);\n  }\n\n  getFilterPayload(event) {\n    this.filtersData = event;\n  }\n\n  openFilterModal() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.isFilterModalReady = true;\n\n      _this.cdr.detectChanges();\n\n      return yield _this.filterModal.open();\n    })();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.unsubscribe();\n  }\n\n};\n\n__decorate([ViewChild('filterModal')], MytaskComponent.prototype, \"filterModal\", void 0);\n\nMytaskComponent = __decorate([Component({\n  selector: 'app-mytask',\n  templateUrl: './mytask.component.html',\n  styleUrls: ['./mytask.component.scss']\n})], MytaskComponent);\nexport { MytaskComponent };", "map": {"version": 3, "mappings": ";;AAAA,SAEEA,SAFF,EAKEC,SALF,QAMO,eANP;AAQA,SAASC,kBAAT,QAAmC,gCAAnC;AAEA,SACEC,eADF,EAGEC,OAHF,EAIEC,QAJF,EAKEC,SALF,QAMO,MANP;AASA,SAASC,gBAAT,QAAiC,iBAAjC;AAKA,SAASC,cAAT,QAA+B,wBAA/B;AAQA,IAAaC,eAAe,GAA5B,MAAaA,eAAb,CAA4B;EAuD1BC,YACmBC,WADnB,EAEUC,cAFV,EAGUC,GAHV,EAIUC,MAJV,EAKmBC,mBALnB,EAMmBC,gBANnB,EAOmBC,iBAPnB,EAOuD;IANpC;IACT;IACA;IACA;IACS;IACA;IACA;IA7DZ,aAA2B,EAA3B;IACA,qBAAmC,EAAnC;IACA,eAAmB,IAAnB;IACS,6BACdV,gBAAgB,CAACW,gBADH;IAGT,wBACL,KAAKF,gBAAL,CAAsBG,OAAtB,CAA8B,aAA9B,CADK;IAEA,+BACL,KAAKH,gBAAL,CAAsBG,OAAtB,CAA8B,mBAA9B,CADK;IAIA,yBAAoB,IAAIhB,eAAJ,CAA6B,IAA7B,CAApB;IAIA,0BAA8B,KAA9B;IACP,sBAAiBK,cAAjB;IAEO,yBAAiC;MACtCY,UAAU,EAAE,KAAKC,gBADqB;MAEtCC,kBAAkB,EAAE,KAAKC,uBAFa;MAGtCC,gBAAgB,EAAE,KAAKR,gBAAL,CAAsBG,OAAtB,CAA8B,mBAA9B,CAHoB;MAItCM,SAAS,EAAE,MAAK;QACd,KAAKC,kBAAL,GAA0B,KAA1B;QACA,OAAO,IAAP;MACD,CAPqC;MAQtCC,WAAW,EAAE,MAAK;QAChB,KAAKD,kBAAL,GAA0B,KAA1B;QACA,KAAKE,WAAL,GAAmB,IAAnB;QACA,KAAKb,mBAAL,CAAyBc,GAAzB,CAA6B,KAAKC,qBAAlC,EAAyD,EAAzD;QACA,KAAKJ,kBAAL,GAA0B,IAA1B;QACA,KAAKb,GAAL,CAASkB,aAAT;QACA,OAAO,KAAP;MACD,CAfqC;MAgBtCC,aAAa,EAAE,MAAK;QAClB,KAAKnB,GAAL,CAASkB,aAAT;QACA,KAAKhB,mBAAL,CAAyBc,GAAzB,CACE,KAAKC,qBADP,EAEE,KAAKF,WAFP;QAIA,KAAKK,iBAAL,CAAuBC,IAAvB,CAA4B,IAA5B;QACA,KAAKR,kBAAL,GAA0B,KAA1B;QACA,OAAO,IAAP;MACD,CAzBqC;MA0BtCS,iBAAiB,EAAE;QACjBC,QAAQ,EAAE,QADO;QAEjBC,IAAI,EAAE,IAFW;QAGjBC,QAAQ,EAAE,KAHO;QAIjBC,QAAQ,EAAE;MAJO;IA1BmB,CAAjC;IAiCC,gBAA6B,IAAInC,OAAJ,EAA7B;EAUJ;;EAEJoC,QAAQ;IACN,KAAKC,SAAL;EACD;;EAEDA,SAAS,CAACC,aAA4B,IAA7B,EAAiC;IACxC,KAAK/B,WAAL,CACGgC,uBADH,CAC2BD,UAD3B,EAEGE,IAFH,CAGIvC,QAAQ,CAAC,MAAK;MACZ,KAAKO,cAAL,CAAoBiC,WAApB;MACA,KAAKC,OAAL,GAAe,KAAf;MACA,KAAKjC,GAAL,CAASkB,aAAT;IACD,CAJO,CAHZ,EASGgB,SATH,CASa;MACTb,IAAI,EAAGc,QAAD,IAAgC;QACpC,KAAKC,KAAL,GAAaD,QAAb;QACA,KAAKjC,mBAAL,CACGmC,KADH,CACS,KAAKpB,qBADd,EAEGc,IAFH,CAEQtC,SAAS,CAAC,KAAK6C,QAAN,CAFjB,EAGGJ,SAHH,CAGa;UACTb,IAAI,EAAGkB,GAAD,IAAQ;YACZ,KAAKC,gBAAL,GAAwBD,GAAxB;YACA,KAAKE,WAAL;UACD;QAJQ,CAHb;MASD,CAZQ;MAaTC,KAAK,EAAGC,CAAD,IAAM;QACX,KAAKP,KAAL,GAAa,EAAb;MACD;IAfQ,CATb;EA0BD;;EAEDQ,WAAW,CAACC,KAAD,EAAiB;IAC1B,IAAGA,KAAH,EAAU;MACR,KAAK9C,cAAL,CAAoB+C,YAApB;MACA,KAAKlB,SAAL,CAAe,MAAK,SAAL,SAAK,WAAL,GAAK,MAAL,QAAK,CAAEmB,OAAP,KAAkB,IAAjC;IACD;EACF;;EAEDC,sBAAsB,CAACC,UAAD,EAA2B;IAC/C,OAAO,KAAK7C,iBAAL,CAAuB8C,qBAAvB,CAA6CD,UAA7C,CAAP;EACD;;EAEDE,oBAAoB,CAACC,IAAD,EAAsB;IACxC,MAAM;MAAEC;IAAF,IAAiBD,IAAvB;IACA,KAAKnD,MAAL,CAAYqD,aAAZ,CACEjE,kBAAkB,CAAC,GAAGgE,UAAU,EAAd,EAAkB;MAAEE,MAAM,EAAEH,IAAI,CAACI;IAAf,CAAlB,CADpB;EAGD;;EAEOf,WAAW;IACjB,KAAKgB,aAAL,GAAqB,KAAKrB,KAAL,CAAWsB,MAAX,CAAmBN,IAAD,IAA0B;;;MAC/D,OACE,CAAC,EAAC,uBAAKZ,gBAAL,MAAqB,IAArB,IAAqBmB,aAArB,GAAqB,MAArB,GAAqBA,GAAEC,GAAvB,MAA0B,IAA1B,IAA0BC,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,WAA5B,MAAuC,IAAvC,IAAuCC,aAAvC,GAAuC,MAAvC,GAAuCA,GAAEhB,OAA1C,KACC,iBAAI,CAACiB,cAAL,MAAmB,IAAnB,IAAmBC,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,mBAArB,MAAwC,IAAxC,IAAwCC,aAAxC,GAAwC,MAAxC,GAAwCA,GAAEC,WAAF,EAAxC,OACE,6BAAK5B,gBAAL,MAAqB,IAArB,IAAqB6B,aAArB,GAAqB,MAArB,GAAqBA,GAAET,GAAvB,MAA0B,IAA1B,IAA0BU,aAA1B,GAA0B,MAA1B,GAA0BA,GAAER,WAA5B,MAAuC,IAAvC,IAAuCS,aAAvC,GAAuC,MAAvC,GAAuCA,GAAExB,OAAzC,MAAgD,IAAhD,IAAgDyB,aAAhD,GAAgD,MAAhD,GAAgDA,GAAEJ,WAAF,EADlD,CADF,MAGC,EAAC,iBAAK5B,gBAAL,MAAqB,IAArB,IAAqBiC,aAArB,GAAqB,MAArB,GAAqBA,GAAEb,GAAvB,MAA0B,IAA1B,IAA0Bc,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,kBAA7B,KACC,WAAI,CAACX,cAAL,MAAmB,IAAnB,IAAmBY,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,8BAArB,OACE,iBAAKrC,gBAAL,MAAqB,IAArB,IAAqBsC,aAArB,GAAqB,MAArB,GAAqBA,GAAElB,GAAvB,MAA0B,IAA1B,IAA0BmB,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEJ,kBAD9B,CAJF,MAMC,EAAC,iBAAKnC,gBAAL,MAAqB,IAArB,IAAqBwC,aAArB,GAAqB,MAArB,GAAqBA,GAAEpB,GAAvB,MAA0B,IAA1B,IAA0BqB,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,gBAA7B,KACE,KAAI,SAAJ,QAAI,WAAJ,GAAI,MAAJ,OAAI,CAAEC,SAAN,KACC,KAAKC,2BAAL,CACE,IAAIC,IAAJ,CAASjC,IAAI,CAAC+B,SAAd,CADF,EAEEG,OAFF,MAGE,KAAKC,gBAAL,CACE,iBAAK/C,gBAAL,MAAqB,IAArB,IAAqBgD,aAArB,GAAqB,MAArB,GAAqBA,GAAE5B,GAAvB,MAA0B,IAA1B,IAA0B6B,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEP,gBAD9B,EAEEI,OAFF,EAXN,MAcC,EAAC,iBAAK9C,gBAAL,MAAqB,IAArB,IAAqBkD,aAArB,GAAqB,MAArB,GAAqBA,GAAE9B,GAAvB,MAA0B,IAA1B,IAA0B+B,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEC,cAA7B,KACExC,IAAI,CAAC+B,SAAL,IACC,KAAKC,2BAAL,CACE,IAAIC,IAAJ,CAASjC,IAAI,CAAC+B,SAAd,CADF,EAEEG,OAFF,MAGE,KAAKC,gBAAL,CACE,iBAAK/C,gBAAL,MAAqB,IAArB,IAAqBqD,aAArB,GAAqB,MAArB,GAAqBA,GAAEjC,GAAvB,MAA0B,IAA1B,IAA0BkC,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEF,cAD9B,EAEEN,OAFF,EAnBN,CADF;IAwBD,CAzBoB,CAArB;IA0BA,KAAKtF,GAAL,CAASkB,aAAT;EACD;;EAEOqE,gBAAgB,CAAC3B,GAAD,EAAkD;IACxE,MAAM;MAAEmC,GAAF;MAAOC,KAAP;MAAcC;IAAd,IAAuBrC,GAA7B;IACA,OAAO,IAAIyB,IAAJ,CAASY,IAAT,EAAeD,KAAK,GAAG,CAAvB,EAA0BD,GAA1B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,EAAwC,CAAxC,CAAP;EACD;;EAEOX,2BAA2B,CAACc,IAAD,EAAW;IAC5C,OAAO,IAAIb,IAAJ,CACLa,IAAI,CAACC,WAAL,EADK,EAELD,IAAI,CAACE,QAAL,EAFK,EAGLF,IAAI,CAACG,OAAL,EAHK,EAIL,CAJK,EAKL,CALK,EAML,CANK,EAOL,CAPK,CAAP;EASD;;EAEMC,gBAAgB,CAACC,KAAD,EAAW;IAChC,KAAKxF,WAAL,GAAmBwF,KAAnB;EACD;;EAEKC,eAAe;IAAA;;IAAA;MACnB,KAAI,CAAC3F,kBAAL,GAA0B,IAA1B;;MACA,KAAI,CAACb,GAAL,CAASkB,aAAT;;MACA,aAAa,KAAI,CAACuF,WAAL,CAAiBC,IAAjB,EAAb;IAHmB;EAIpB;;EAEDC,WAAW;IACT,KAAKrE,QAAL,CAAcjB,IAAd,CAAmB,IAAnB;IACA,KAAKiB,QAAL,CAAcsE,WAAd;EACD;;AAhLyB,CAA5B;;AAe4BC,YAAzBzH,SAAS,CAAC,aAAD,CAAgB;;AAffQ,eAAe,eAL3BT,SAAS,CAAC;EACT2H,QAAQ,EAAE,YADD;EAETC,WAAW,EAAE,yBAFJ;EAGTC,SAAS,EAAE,CAAC,yBAAD;AAHF,CAAD,CAKkB,GAAfpH,eAAe,CAAf;SAAAA", "names": ["Component", "ViewChild", "ReplaceUrlVariable", "BehaviorSubject", "Subject", "finalize", "takeUntil", "LOCALSTORAGE_KEY", "PermissionEnum", "MytaskComponent", "constructor", "taskService", "spinnerService", "cdr", "router", "localStorageService", "translateService", "permissionService", "TASK_LIST_FILTER", "instant", "modalTitle", "filterModalTitle", "dismissButtonLabel", "modalDismissButtonLabel", "closeButtonLabel", "on<PERSON><PERSON><PERSON>", "isFilterModalReady", "shouldClose", "filtersData", "set", "filterLocalStorageKey", "detectChanges", "<PERSON><PERSON><PERSON><PERSON>", "filterChangeEvent", "next", "modalDialogConfig", "backdrop", "size", "keyboard", "centered", "ngOnInit", "fetchList", "assignedTo", "getUserPendingTasksList", "pipe", "stopSpinner", "loading", "subscribe", "response", "tasks", "watch", "destroy$", "val", "storedFilterData", "filterTasks", "error", "_", "onUserAdded", "value", "startSpinner", "loginId", "validateUserPermission", "permission", "checkPermissionByName", "nagivateToTaskAction", "task", "taskRelUrl", "navigateByUrl", "taskId", "id", "filteredTasks", "filter", "_a", "obj", "_b", "submittedBy", "_c", "additionalInfo", "_d", "proposal_created_by", "_e", "toLowerCase", "_f", "_g", "_h", "_j", "_k", "_l", "afeReferenceNumber", "_m", "proposalProjectReferenceNumber", "_o", "_p", "_q", "_r", "fromAssignedDate", "createdOn", "createDateObjectWithOutTime", "Date", "getTime", "createDateObject", "_s", "_t", "_u", "_v", "toAssignedDate", "_w", "_x", "day", "month", "year", "date", "getFullYear", "getMonth", "getDate", "getFilterPayload", "event", "openFilterModal", "filterModal", "open", "ngOnDestroy", "unsubscribe", "__decorate", "selector", "templateUrl", "styleUrls"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\modules\\mytask\\mytask.component.ts"], "sourcesContent": ["import {\r\n  ChangeDetectorR<PERSON>,\r\n  Component,\r\n  On<PERSON><PERSON>roy,\r\n  OnInit,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ReplaceUrlVariable } from '@core/constants/urls.constants';\r\nimport { SpinnerService } from '@core/services/common/spinner.service';\r\nimport {\r\n  BehaviorSubject,\r\n  Observable,\r\n  Subject,\r\n  finalize,\r\n  takeUntil,\r\n} from 'rxjs';\r\nimport { TaskDetailModel } from './core/models/task-detail.model';\r\nimport { TaskService } from './core/services';\r\nimport { LOCALSTORAGE_KEY } from '@core/constants';\r\nimport { LocalStorageService } from '@core/services/common/localStorage.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { ModalComponent, ModalConfig } from '@core/modules/partials';\r\nimport { UserModel } from '@core/models/basic/user';\r\nimport { PermissionEnum } from '@core/enums/Permission';\r\nimport { PermissionService } from '@core/services/common/permission.service';\r\n\r\n@Component({\r\n  selector: 'app-mytask',\r\n  templateUrl: './mytask.component.html',\r\n  styleUrls: ['./mytask.component.scss'],\r\n})\r\nexport class MytaskComponent implements OnInit, OnDestroy {\r\n  public tasks: TaskDetailModel[] = [];\r\n  public filteredTasks: TaskDetailModel[] = [];\r\n  public loading: boolean = true;\r\n  public readonly filterLocalStorageKey: string =\r\n    LOCALSTORAGE_KEY.TASK_LIST_FILTER;\r\n\r\n  public filterModalTitle: string =\r\n    this.translateService.instant('MENU.FILTER');\r\n  public modalDismissButtonLabel: string =\r\n    this.translateService.instant('FORM.BUTTON.APPLY');\r\n  public filtersData: any;\r\n  public storedFilterData: any | null;\r\n  public filterChangeEvent = new BehaviorSubject<boolean>(true);\r\n  public filterLocalStorage: Observable<any>;\r\n  @ViewChild('filterModal') private filterModal: ModalComponent;\r\n\r\n  public isFilterModalReady: boolean = false;\r\n  permissionEnum = PermissionEnum;\r\n  \r\n  public filterModalConfig: ModalConfig = {\r\n    modalTitle: this.filterModalTitle,\r\n    dismissButtonLabel: this.modalDismissButtonLabel,\r\n    closeButtonLabel: this.translateService.instant('FORM.BUTTON.RESET'),\r\n    onDismiss: () => {\r\n      this.isFilterModalReady = false;\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      this.isFilterModalReady = false;\r\n      this.filtersData = null;\r\n      this.localStorageService.set(this.filterLocalStorageKey, '');\r\n      this.isFilterModalReady = true;\r\n      this.cdr.detectChanges();\r\n      return false;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.cdr.detectChanges();\r\n      this.localStorageService.set(\r\n        this.filterLocalStorageKey,\r\n        this.filtersData\r\n      );\r\n      this.filterChangeEvent.next(true);\r\n      this.isFilterModalReady = false;\r\n      return true;\r\n    },\r\n    modalDialogConfig: {\r\n      backdrop: 'static',\r\n      size: 'lg',\r\n      keyboard: false,\r\n      centered: true,\r\n    },\r\n  };\r\n  private destroy$: Subject<boolean> = new Subject<boolean>();\r\n\r\n  constructor(\r\n    private readonly taskService: TaskService,\r\n    private spinnerService: SpinnerService,\r\n    private cdr: ChangeDetectorRef,\r\n    private router: Router,\r\n    private readonly localStorageService: LocalStorageService,\r\n    private readonly translateService: TranslateService,\r\n    private readonly permissionService: PermissionService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.fetchList();\r\n  }\r\n\r\n  fetchList(assignedTo: string | null = null) {\r\n    this.taskService\r\n      .getUserPendingTasksList(assignedTo)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.spinnerService.stopSpinner();\r\n          this.loading = false;\r\n          this.cdr.detectChanges();\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: TaskDetailModel[]) => {\r\n          this.tasks = response;\r\n          this.localStorageService\r\n            .watch(this.filterLocalStorageKey)\r\n            .pipe(takeUntil(this.destroy$))\r\n            .subscribe({\r\n              next: (val) => {\r\n                this.storedFilterData = val;\r\n                this.filterTasks();\r\n              },\r\n            });\r\n        },\r\n        error: (_) => {\r\n          this.tasks = [];\r\n        },\r\n      });\r\n  }\r\n\r\n  onUserAdded(value: UserModel) {\r\n    if(value) {\r\n      this.spinnerService.startSpinner();\r\n      this.fetchList(value?.loginId || null);\r\n    }\r\n  }\r\n\r\n  validateUserPermission(permission: PermissionEnum) {\r\n    return this.permissionService.checkPermissionByName(permission);\r\n  }\r\n\r\n  nagivateToTaskAction(task: TaskDetailModel) {\r\n    const { taskRelUrl } = task;\r\n    this.router.navigateByUrl(\r\n      ReplaceUrlVariable(`${taskRelUrl}`, { taskId: task.id })\r\n    );\r\n  }\r\n\r\n  private filterTasks() {\r\n    this.filteredTasks = this.tasks.filter((task: TaskDetailModel) => {\r\n      return (\r\n        (!this.storedFilterData?.obj?.submittedBy?.loginId ||\r\n          task.additionalInfo?.proposal_created_by?.toLowerCase() ===\r\n            this.storedFilterData?.obj?.submittedBy?.loginId?.toLowerCase()) &&\r\n        (!this.storedFilterData?.obj?.afeReferenceNumber ||\r\n          task.additionalInfo?.proposalProjectReferenceNumber ===\r\n            this.storedFilterData?.obj?.afeReferenceNumber) &&\r\n        (!this.storedFilterData?.obj?.fromAssignedDate ||\r\n          (task?.createdOn &&\r\n            this.createDateObjectWithOutTime(\r\n              new Date(task.createdOn)\r\n            ).getTime() >=\r\n              this.createDateObject(\r\n                this.storedFilterData?.obj?.fromAssignedDate\r\n              ).getTime())) &&\r\n        (!this.storedFilterData?.obj?.toAssignedDate ||\r\n          (task.createdOn &&\r\n            this.createDateObjectWithOutTime(\r\n              new Date(task.createdOn)\r\n            ).getTime() <=\r\n              this.createDateObject(\r\n                this.storedFilterData?.obj?.toAssignedDate\r\n              ).getTime()))\r\n      );\r\n    });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private createDateObject(obj: { day: number; month: number; year: number }) {\r\n    const { day, month, year } = obj;\r\n    return new Date(year, month - 1, day, 0, 0, 0, 0);\r\n  }\r\n\r\n  private createDateObjectWithOutTime(date: Date) {\r\n    return new Date(\r\n      date.getFullYear(),\r\n      date.getMonth(),\r\n      date.getDate(),\r\n      0,\r\n      0,\r\n      0,\r\n      0\r\n    );\r\n  }\r\n\r\n  public getFilterPayload(event: any) {\r\n    this.filtersData = event;\r\n  }\r\n\r\n  async openFilterModal() {\r\n    this.isFilterModalReady = true;\r\n    this.cdr.detectChanges();\r\n    return await this.filterModal.open();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next(true);\r\n    this.destroy$.unsubscribe();\r\n  }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}