"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OneAppController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../core/guards");
const enums_1 = require("../../shared/enums");
const dtos_1 = require("../dtos");
const get_afe_detail_request_dto_1 = require("../dtos/request/get-afe-detail-request.dto");
const user_email_request_dto_1 = require("../dtos/request/user-email-request.dto");
const services_1 = require("../services");
let OneAppController = class OneAppController {
    constructor(oneAppService) {
        this.oneAppService = oneAppService;
    }
    getAfeTasks(userEmailRequestDto) {
        return this.oneAppService.getAfeTasks(userEmailRequestDto);
    }
    getAfeDetail(getAfeDetailRequestDto) {
        return this.oneAppService.getAfeDetail(getAfeDetailRequestDto);
    }
    getAfeAttachment(getAfeAttachmentRequestDto) {
        return this.oneAppService.getAfeAttachment(getAfeAttachmentRequestDto);
    }
    getAfeHistory(getAfeHistoryDto) {
        return this.oneAppService.getAfeHistory(getAfeHistoryDto);
    }
    approveTask(taskApprovalRequestDto) {
        const { taskid, useremail, comment, afeid } = taskApprovalRequestDto;
        return this.oneAppService.taskApprovalByTaskId(afeid, taskid, useremail, enums_1.TASK_ACTION.APPROVE, comment);
    }
    rejectTask(taskApprovalRequestDto) {
        const { taskid, useremail, comment, afeid } = taskApprovalRequestDto;
        return this.oneAppService.taskApprovalByTaskId(afeid, taskid, useremail, enums_1.TASK_ACTION.REJECT, comment);
    }
    delegateTask(taskApprovalRequestDto) {
        const { taskid, useremail, comment, afeid, delegateuserupn } = taskApprovalRequestDto;
        return this.oneAppService.taskApprovalByTaskId(afeid, taskid, useremail, enums_1.TASK_ACTION.DELEGATE, comment, delegateuserupn);
    }
    askMoreDetailTaskApproval(taskApprovalRequestDto) {
        const { taskid, useremail, comment, afeid, assignee } = taskApprovalRequestDto;
        return this.oneAppService.taskApprovalByTaskId(afeid, taskid, useremail, enums_1.TASK_ACTION.MORE_DETAIL, comment, assignee);
    }
    reassignTask(taskApprovalRequestDto) {
        const { taskid, useremail, comment, afeid, assigneeupn } = taskApprovalRequestDto;
        return this.oneAppService.taskApprovalByTaskId(afeid, taskid, useremail, enums_1.TASK_ACTION.REASSIGNE, comment, assigneeupn);
    }
    sendBackTaskAction(taskApprovalRequestDto) {
        const { taskid, useremail, comment, afeid } = taskApprovalRequestDto;
        return this.oneAppService.taskApprovalByTaskId(afeid, taskid, useremail, enums_1.TASK_ACTION.SEND_BACK, comment);
    }
    getTaskApprovalActions(getTaskApprovalActionsRequestDto) {
        const { taskid, afeid } = getTaskApprovalActionsRequestDto;
        return this.oneAppService.getTaskApprovalActions(afeid, taskid);
    }
    getApproversForMoreDetailTaskAction(getApproversForMoreDetailRequestDto) {
        const { afeid } = getApproversForMoreDetailRequestDto;
        return this.oneAppService.getApproversForMoreDetailTaskAction(afeid);
    }
    getUserActionedAFEProposals(getUserActionedAfeProposalRequestDto) {
        const { useremail, pageSize, activePage, type } = getUserActionedAfeProposalRequestDto;
        return this.oneAppService.getUserActionedAFEProposals(useremail, pageSize, activePage, type);
    }
    getUserSubmittedAFEProposal(getUserSubmittedAfeProposalRequestDto) {
        const { useremail, pageSize, activePage } = getUserSubmittedAfeProposalRequestDto;
        return this.oneAppService.getUserSubmittedAFEProposal(useremail, pageSize, activePage);
    }
    getAFECompleteDetail(maximoRequestDto) {
        return this.oneAppService.getAFECompleteDetail(maximoRequestDto);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get assigned AFE task for requested email id.',
        type: [dtos_1.TaskListResponseDTO]
    }),
    (0, common_1.Post)('/getAfeTasks'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_email_request_dto_1.UserEmailRequestDto]),
    __metadata("design:returntype", Promise)
], OneAppController.prototype, "getAfeTasks", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE detail for requested AFE ID & EMAIL ID.',
        type: dtos_1.GetAfeDetailResponseDTO
    }),
    (0, common_1.Post)('/getAfe'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_afe_detail_request_dto_1.GetAfeDetailRequestDto]),
    __metadata("design:returntype", Promise)
], OneAppController.prototype, "getAfeDetail", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE attachement metadata and content.',
        type: dtos_1.GetAttachmentDTO
    }),
    (0, common_1.Post)('/getAfeAttachment'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.GetAfeAttachmentRequestDto]),
    __metadata("design:returntype", Promise)
], OneAppController.prototype, "getAfeAttachment", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE history.',
        type: [dtos_1.GetAFEHistoryResponseDTO]
    }),
    (0, common_1.Post)('/getAfeHistory'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_afe_detail_request_dto_1.GetAfeDetailRequestDto]),
    __metadata("design:returntype", Promise)
], OneAppController.prototype, "getAfeHistory", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perform approve action on the afe proposal task',
        type: String
    }),
    (0, common_1.Post)('task/approve'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.TaskApprovalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "approveTask", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perform reject action on the afe proposal task',
        type: String
    }),
    (0, common_1.Post)('task/reject'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.TaskApprovalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "rejectTask", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perform delegate action on the afe proposal task',
        type: String
    }),
    (0, common_1.Post)('task/delegate'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.TaskApprovalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "delegateTask", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Ask more details action on the afe proposal task',
        type: String
    }),
    (0, common_1.Post)('task/more-details'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.TaskApprovalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "askMoreDetailTaskApproval", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Reassign approval action on the afe proposal task',
        type: String
    }),
    (0, common_1.Post)('task/reassign'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.TaskApprovalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "reassignTask", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Send back approval action on the afe proposal task',
        type: String
    }),
    (0, common_1.Post)('task/send-back'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.TaskApprovalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "sendBackTaskAction", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return approval actions for the afe proposal task',
        type: [String]
    }),
    (0, common_1.Post)('task/approval-actions'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.GetTaskApprovalActionsRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "getTaskApprovalActions", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return approvers for ask more details action on the afe proposal task',
        type: [String]
    }),
    (0, common_1.Post)('task/more-details/approvers'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.GetApproversForMoreDetailRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "getApproversForMoreDetailTaskAction", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return user actioned AFE proposals',
    }),
    (0, common_1.Post)('user-actioned/afe-proposals'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.GetUserActionedAfeProposalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "getUserActionedAFEProposals", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return user submitted AFE proposals',
        type: [dtos_1.GetAfeDetailResponseDTO]
    }),
    (0, common_1.Post)('user/afe-proposals'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.GetUserSubmittedAfeProposalRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "getUserSubmittedAFEProposal", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'AFE For Maximo Integration',
        type: [dtos_1.GetAfeDetailResponseDTO]
    }),
    (0, common_1.Post)('maximo/afe-proposals'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.MaximoRequestDto]),
    __metadata("design:returntype", void 0)
], OneAppController.prototype, "getAFECompleteDetail", null);
OneAppController = __decorate([
    (0, swagger_1.ApiTags)('One App Mobile API'),
    (0, common_1.Controller)('one-mobile-app'),
    (0, swagger_1.ApiHeader)({ name: 'api-key', description: 'API key for one app endpoint authentication.' }),
    (0, common_1.UseGuards)(guards_1.OneAppApiKeyGuard),
    __metadata("design:paramtypes", [services_1.OneAppService])
], OneAppController);
exports.OneAppController = OneAppController;
//# sourceMappingURL=one-app.controller.js.map