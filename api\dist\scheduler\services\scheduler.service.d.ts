import { AfeProposalApproverRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { ConfigService } from 'src/config/config.service';
import { QueueLogRepository } from 'src/queue/repositories';
import { AdminApiClient, MSGraphApiClient, TaskApiClient } from 'src/shared/clients';
import { SharedNotificationService } from 'src/shared/services';
import { SchedulerRepository } from '../repositories';
import { OracleFusionService } from 'src/oracle-fusion/services';
import { LoggerService } from 'src/core/services';
import { BusinessEntityService } from 'src/business-entity/services';
import { SftpService } from 'src/sftp-service/sftp.service';
export declare class SchedulerService {
    private readonly schedulerRepository;
    private readonly queueLogRepository;
    private readonly adminApiClient;
    private readonly sharedNotificationService;
    private readonly afeProposalRepository;
    private readonly afeProposalApproverRepository;
    private readonly configService;
    private readonly mSGraphApiClient;
    private readonly oracleFusionService;
    private readonly loggerService;
    private readonly businessEntityService;
    private readonly sftpService;
    private readonly taskApiClient;
    constructor(schedulerRepository: SchedulerRepository, queueLogRepository: QueueLogRepository, adminApiClient: AdminApiClient, sharedNotificationService: SharedNotificationService, afeProposalRepository: AfeProposalRepository, afeProposalApproverRepository: AfeProposalApproverRepository, configService: ConfigService, mSGraphApiClient: MSGraphApiClient, oracleFusionService: OracleFusionService, loggerService: LoggerService, businessEntityService: BusinessEntityService, sftpService: SftpService, taskApiClient: TaskApiClient);
    private readonly RETRY_THRESHOLD;
    runScheduler(type: string): Promise<void>;
    runNonLiveScheduler(type: any): Promise<void>;
    private sendNotificationOfAfeList;
    runLiveScheduler(): Promise<void>;
    private isRuleConditionsValid;
    runReminderScheduler(): Promise<void>;
    runSubmitterScheduler(): Promise<void>;
    private returnEmailsListFromRecipients;
    private getAfeBasedUsersEmail;
    private getUsersEmailByRoleAndLevel;
    private getAFEListData;
    private filterTableData;
}
