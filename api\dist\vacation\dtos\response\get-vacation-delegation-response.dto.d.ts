declare class FilterParam {
    request_type_id: number;
}
declare class AdditionalInfo {
    filter_param: FilterParam;
}
export declare class GetVacationDelegationResponseDto {
    id: number;
    delegate_for_username: string;
    delegate_to_username: string;
    delegate_from_date: Date;
    delegate_to_date: Date;
    created_on: Date;
    created_by: string;
    modified_by: string;
    modified_on: Date;
    additional_info?: AdditionalInfo;
    business_entity_id: number;
    delegate_comments: string;
    constructor(partial?: Partial<GetVacationDelegationResponseDto>);
}
export {};
