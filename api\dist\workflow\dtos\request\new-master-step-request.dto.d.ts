export declare class NewMasterStepRequestDto {
    workflowMasterSettingId: number;
    title: string;
    associateType: string;
    associateRole?: string;
    associateLevel?: string;
    associatedUser?: string;
    associatedColumn?: string;
    parallelIdentifier?: string;
    skipLimitRuleId: number;
    canWorkflowStart: boolean;
    isMandatory: boolean;
    includeBelowSteps: boolean;
    canRemovedAtChildLevel: boolean;
    canShareLimitToChild?: boolean;
    ruleId?: number;
    singleLimit?: number;
    aggregateLimit?: number;
    lengthOfCommitment?: number;
    unpublishedVersion?: boolean;
}
