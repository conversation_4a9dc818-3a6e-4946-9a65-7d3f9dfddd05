import { WorkflowResponseDto, ComputeAfeApproversListDto } from '../dtos';
import { WorkflowService } from '../services/workflow.service';
import { RequestContext } from 'src/shared/types';
export declare class WorkflowController {
    private readonly workflowService;
    constructor(workflowService: WorkflowService);
    getAfeApproversList(computeAfeApproversListDto: ComputeAfeApproversListDto, request: RequestContext): Promise<WorkflowResponseDto>;
}
