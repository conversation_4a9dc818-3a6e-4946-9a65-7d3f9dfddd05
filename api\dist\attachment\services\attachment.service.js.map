{"version": 3, "file": "attachment.service.js", "sourceRoot": "", "sources": ["../../../src/attachment/services/attachment.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,kEAAqG;AACrG,sFAA+E;AAC/E,8CAAsE;AACtE,wDAAsD;AACtD,kDAAmF;AAEnF,wDAA6D;AAC7D,kCAAuD;AACvD,+DAAgE;AAGhE,IAAa,iBAAiB,GAA9B,MAAa,iBAAiB;IAC7B,YACkB,mBAAwC,EACxC,qBAA4C,EAC5C,oBAA0C,EAC1C,kBAAsC,EACtC,6BAA4D;QAJ5D,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,yBAAoB,GAApB,oBAAoB,CAAsB;QAC1C,uBAAkB,GAAlB,kBAAkB,CAAoB;QACtC,kCAA6B,GAA7B,6BAA6B,CAA+B;IAC1E,CAAC;IAMQ,kBAAkB,CAAC,MAAc,EAAE,cAA8B,EAAE,MAAe;;YAC9F,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAC9E,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC,IAAI,CAAC;YAEzC,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC;YAEjG,IAAG,UAAU,KAAK,8BAAsB,CAAC,SAAS,EAAE;gBACnD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBACnE,IAAI,CAAC,KAAK,EAAE;oBACX,MAAM,IAAI,0BAAa,CAAC,sBAAsB,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;iBACtE;gBACD,IAAG,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,EAAE;oBAC5D,MAAM,IAAI,0BAAa,CAAC,sDAAsD,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;iBACtG;aACD;iBAAM;gBACN,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;gBACrG,IAAI,CAAC,QAAQ,EAAE;oBACd,MAAM,IAAI,0BAAa,CAAC,oBAAoB,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;iBACpE;gBACD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,sCAAsC,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;gBACnI,IAAI,CAAC,iBAAiB,EAAE;oBACvB,MAAM,IAAI,0BAAa,CAAC,sDAAsD,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;iBACtG;aACD;YAED,IAAI,CAAC,WAAW,EAAE;gBACjB,MAAM,IAAI,0BAAa,CAAC,qCAAqC,MAAM,EAAE,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aAC7F;YACD,OAAO,IAAA,gCAAsB,EAAC,mCAA4B,EAAE,WAAW,CAAC,CAAC;QAC1E,CAAC;KAAA;IAOY,6BAA6B,CACzC,aAAqB,EACrB,cAA8B,EAC9B,MAAe;;YAEf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;YAC7F,IAAI,CAAC,QAAQ,EAAE;gBACd,MAAM,IAAI,0BAAa,CAAC,oBAAoB,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aACpE;YACD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,sCAAsC,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;YACnI,IAAI,CAAC,iBAAiB,EAAE;gBACvB,MAAM,IAAI,0BAAa,CAAC,sDAAsD,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aACtG;YACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAChE,aAAa,EACb,8BAAsB,CAAC,UAAU,CACjC,CAAC;YACF,OAAO,IAAA,+BAAqB,EAAC,mCAA4B,EAAE,QAAQ,CAAC,CAAC;QACtE,CAAC;KAAA;IAOY,iCAAiC,CAC7C,aAAqB,EACrB,cAA8B,EAC9B,MAAe;;YAGf,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;YAE7F,IAAI,CAAC,QAAQ,EAAE;gBACd,MAAM,IAAI,0BAAa,CAAC,oBAAoB,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aACpE;YAED,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,sCAAsC,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;YACnI,IAAI,CAAC,iBAAiB,EAAE;gBACvB,MAAM,IAAI,0BAAa,CAAC,sDAAsD,EAAE,kBAAU,CAAC,SAAS,CAAC,CAAC;aACtG;YAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;gBAC9E,WAAW,EAAE,8BAAsB,CAAC,UAAU;gBAC9C,WAAW,EAAE,aAAa,CAAC,QAAQ,EAAE;aACrC,CAAC,CAAC;YAEH,OAAO,IAAA,+BAAqB,EAAC,mCAA4B,EAAE,CAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,MAAM,EAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5G,CAAC;KAAA;IAOY,6BAA6B,CACzC,UAAkB;;YAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAChE,UAAU,EACV,8BAAsB,CAAC,UAAU,CACjC,CAAC;YACF,OAAO,IAAA,+BAAqB,EAAC,mCAA4B,EAAE,QAAQ,CAAC,CAAC;QACtE,CAAC;KAAA;CACD,CAAA;AAjHY,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAG2B,2CAAmB;QACjB,oCAAqB;QACtB,iCAAoB;QACtB,iCAAkB;QACP,4CAA6B;GANlE,iBAAiB,CAiH7B;AAjHY,8CAAiB"}