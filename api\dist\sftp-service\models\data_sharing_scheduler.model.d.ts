import { SchedulerRecipients } from 'src/scheduler/types';
import { FREQUENCY } from 'src/shared/enums';
import { BaseModel } from 'src/shared/models';
import { RuleCondition } from 'src/shared/types';
export declare class DataSharingSchedulers extends BaseModel<DataSharingSchedulers> {
    title: string;
    frequency: FREQUENCY;
    monthDay?: number | null;
    rule?: RuleCondition | null;
    entities: number[];
    recipients: SchedulerRecipients;
    lastRunAt?: Date | null;
    nextRunAt?: Date | null;
}
