import { QUEUE_LOG_ACTION, SCHEDULER_TYPE } from 'src/shared/enums';
import { BaseModel } from 'src/shared/models';
import { RuleCondition } from 'src/shared/types';
import { SchedulerRecipients } from '../types';
export declare class Scheduler extends BaseModel<Scheduler> {
    title: string;
    rule: RuleCondition;
    entities?: number[] | null;
    actions?: QUEUE_LOG_ACTION[] | null;
    finalApproval: boolean;
    recipients: SchedulerRecipients;
    templateName: string;
    forLevel?: string;
    lastRunAt?: Date | null;
    placeholderFields?: string[] | null;
    type: SCHEDULER_TYPE;
}
