{"version": 3, "file": "perform-action-on-afe-proposal-request.dto.js", "sourceRoot": "", "sources": ["../../../../src/task/dtos/request/perform-action-on-afe-proposal-request.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,yDAAyC;AACzC,qDAAsF;AACtF,iDAA2D;AAE3D,MAAa,mBAAmB;CAgB/B;AAbA;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,4BAAU,GAAE;;sDACK;AAIlB;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,4BAAU,GAAE;;qDACI;AAIjB;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,4BAAU,GAAE;;oDACG;AAIhB;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,4BAAU,GAAE;;kDACC;AAff,kDAgBC;AAED,MAAa,oCAAoC;CAiChD;AA7BA;IAHC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;gEACF;AAKX;IAHC,IAAA,qBAAW,GAAE;IACb,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;oEACG;AAKhB;IAHC,IAAA,wBAAM,EAAC,+BAAuB,CAAC;IAC/B,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,+BAAuB,EAAE,CAAC;IAC9C,IAAA,4BAAU,GAAE;;oEACmB;AAIhC;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,4BAAU,GAAE;;sEACK;AAKlB;IAHC,IAAA,qBAAW,GAAE;IACb,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;oFACqB;AAIjC;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,4BAAU,GAAE;;yEACI;AAKjB;IAHC,IAAA,qBAAW,EAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC;IAC1C,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC;8BACpB,mBAAmB;uEAAC;AAhCjC,oFAiCC"}