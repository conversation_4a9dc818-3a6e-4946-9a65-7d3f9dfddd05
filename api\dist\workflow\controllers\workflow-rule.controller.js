"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowRuleController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../core/guards");
const decorators_1 = require("../../core/decorators");
const enums_1 = require("../../shared/enums");
const services_1 = require("../services");
const dtos_1 = require("../dtos");
const dtos_2 = require("../../shared/dtos");
let WorkflowRuleController = class WorkflowRuleController {
    constructor(workflowRuleService) {
        this.workflowRuleService = workflowRuleService;
    }
    createWorkflowRule(createWorkflowRuleRequestDto, request) {
        return this.workflowRuleService.createWorkflowRule(createWorkflowRuleRequestDto, request.currentContext);
    }
    getWorkflowRuleById(id) {
        return this.workflowRuleService.getWorkflowRuleById(id);
    }
    deleteWorkflowRuleById(id, request) {
        return this.workflowRuleService.deleteWorkflowRuleById(id, request.currentContext);
    }
    updateWorkflowRule(updateWorkflowRuleRequestDto, request) {
        return this.workflowRuleService.updateWorkflowRule(updateWorkflowRuleRequestDto, request.currentContext);
    }
    getWorkflowRulesList(limit = 10, page = 1, noLimit = false, searchTerm = '') {
        return this.workflowRuleService.getWorkflowRulesList(page, limit, searchTerm, noLimit);
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add new workflow rule.',
        type: dtos_1.CreateWorkflowRuleResponseDto,
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.CreateWorkflowRuleRequestDto, Object]),
    __metadata("design:returntype", Promise)
], WorkflowRuleController.prototype, "createWorkflowRule", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get workflow rule details by its id.',
        type: dtos_1.GetWorkflowRuleResponseDto,
    }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowRuleController.prototype, "getWorkflowRuleById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete workflow rule by its id.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WorkflowRuleController.prototype, "deleteWorkflowRuleById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update workflow rule details.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Put)(''),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.UpdateWorkflowRuleRequestDto, Object]),
    __metadata("design:returntype", Promise)
], WorkflowRuleController.prototype, "updateWorkflowRule", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        type: Number,
        description: 'Number of records in response.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        type: Number,
        description: 'Page number of the paginated record.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchTerm',
        type: String,
        description: 'Search term for workflow rule listing.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'noLimit',
        type: Boolean,
        description: 'If not limit get complete list without limit.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get paginated workflow rules listing.',
        type: [dtos_1.PaginatedWorkflowRulesResponseDto],
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Query)('limit')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('noLimit')),
    __param(3, (0, common_1.Query)('searchTerm')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Boolean, String]),
    __metadata("design:returntype", Promise)
], WorkflowRuleController.prototype, "getWorkflowRulesList", null);
WorkflowRuleController = __decorate([
    (0, swagger_1.ApiTags)('Workflow Rules APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('workflow-rules'),
    __metadata("design:paramtypes", [services_1.WorkflowRuleService])
], WorkflowRuleController);
exports.WorkflowRuleController = WorkflowRuleController;
//# sourceMappingURL=workflow-rule.controller.js.map