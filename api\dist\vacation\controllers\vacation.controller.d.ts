import { RequestContext } from 'src/shared/types';
import { DeleteVacationDelegationRequestDto, SetupNewVacationDelegationRequestDto, UpdateVacationDelegationRequestDto } from '../dtos';
import { VacationService } from '../services';
export declare class VacationController {
    private vacationService;
    constructor(vacationService: VacationService);
    getAllUpcomingDelegations(request: RequestContext, filterQuery?: string): Promise<any>;
    addUpcomingDelegation(request: RequestContext, addDelegationDto: SetupNewVacationDelegationRequestDto): Promise<number>;
    updateUpcomingDelegation(request: RequestContext, updateDelegationDto: UpdateVacationDelegationRequestDto): Promise<any>;
    deleteUpcomingDelegation(request: RequestContext, id: number): Promise<any>;
    deleteAllUpcomingDelegation(request: RequestContext, deleteDelegationDto: DeleteVacationDelegationRequestDto): Promise<any>;
}
