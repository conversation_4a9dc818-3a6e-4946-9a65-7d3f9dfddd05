import { Controller, Get } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { OracleFusionService } from '../services/oracle-fusion.service';

@ApiTags('Oracle Fusion Integration API')
@Controller('oracle-fusion')
export class OracleFusionController {

	constructor(private readonly oracleFusionService: OracleFusionService) { }

	/**
	 * Post data to oracle fusion for all the recent approved AFE whose entity is fusion enabled.
	 * @returns
	 */
	@ApiResponse({
		status: 200,
		description: 'Send data to oracle fusion.',
	})
	@Get('/send-data')
	public getProjectComponents() {

		if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'staging') {
			return this.oracleFusionService.sendFusionEnabledApprovedAfe();
		}

		return `Service not available on ${process.env.NODE_ENV}!`;
	}
}
