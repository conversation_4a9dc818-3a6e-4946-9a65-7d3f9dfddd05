"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Notification = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const notification_type_enum_1 = require("../../shared/enums/notification-type.enum");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
let Notification = class Notification extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Notification.prototype, "title", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], Notification.prototype, "description", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Notification.prototype, "url", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'expire_at', allowNull: false, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], Notification.prototype, "expireAt", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'type',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(notification_type_enum_1.NOTIFICATION_TYPE)),
        allowNull: false,
        defaultValue: notification_type_enum_1.NOTIFICATION_TYPE.INFO,
    }),
    __metadata("design:type", String)
], Notification.prototype, "type", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'viewed_by', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Notification.prototype, "viewedBy", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'subscribers', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Notification.prototype, "subscribers", void 0);
Notification = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'notifications' })
], Notification);
exports.Notification = Notification;
//# sourceMappingURL=notification.model.js.map