import { APPROVAL_SEQUENCE_TYPE, APPROVER_STATUS, ASSOCIATED_COLUMN } from 'src/shared/enums';
import { ASSOCIATED_TYPE } from 'src/shared/enums/associated-type.enum';
export declare class ApproverUserDetailResponseDto {
    title: string;
    loginId: string;
    lastName: string;
    firstName: string;
}
export declare class AfeProposalApproverResponseDto {
    id: number;
    title: string;
    stepId: number;
    approvers: ApproverUserDetailResponseDto[];
    isCustomUser: boolean;
    associateRole: string;
    associateType: ASSOCIATED_TYPE;
    associateLevel: string;
    sequenceNumber: number;
    associatedColumn: ASSOCIATED_COLUMN;
    parallelIdentifier: string;
    approvalSequenceType: APPROVAL_SEQUENCE_TYPE;
    actionBy: string;
    originalApprover: ApproverUserDetailResponseDto;
    actionDate: Date;
    actionStatus: APPROVER_STATUS;
    comment: string;
}
