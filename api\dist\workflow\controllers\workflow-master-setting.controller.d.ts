import { Response } from 'express';
import { Pagination } from 'src/core/pagination';
import { MessageResponseDto } from 'src/shared/dtos';
import { IWorkFlowFilter } from 'src/shared/interfaces/filter.interface';
import { RequestContext } from 'src/shared/types';
import { GetMasterSettingResponseDTO, NewMasterWorkflowRequestDto, NewMasterWorkflowResponseDto, OverrideWorkflowRequestDto, PublishedOverriddenWorkflowRequest, UnpublishedVersionRequest, WorkflowDetailResponseDTO } from '../dtos';
import { CloneWorkflowRequestDto, NewVersionWorkflowRequestDto } from '../dtos/request/clone-workflow-request.dto';
import { ListMasterWorkflowRequestDto } from '../dtos/request/list-master-workflow-request.dto';
import { WorkflowMasterSettingService } from '../services';
export declare class WorkflowMasterSetingController {
    private workflowMasterSettingService;
    constructor(workflowMasterSettingService: WorkflowMasterSettingService);
    getAllMasterSettingList(query: IWorkFlowFilter, listMasterWorkflowRequestDto: ListMasterWorkflowRequestDto): Promise<Pagination<GetMasterSettingResponseDTO>>;
    getAllMasterSetting(query: IWorkFlowFilter): Promise<Pagination<GetMasterSettingResponseDTO>>;
    getOverrideAllSettingsDetail(workflowSettingId: number): Promise<import("../dtos/response/get-overriden-setting-list-response.dto").GetOverridenSettingListResponseDTO[]>;
    getAllAssignedUniqueRolesForWorkflowSetting(workflowSettingId: number): Promise<any[]>;
    getAllUnpublishedOverriden(parentId: number): Promise<import("../dtos/response/get-overriden-setting-list-response.dto").GetOverridenSettingListResponseDTO[]>;
    addNewMasterWorkflowSetting(request: RequestContext, newMasterWorkflowRequestDto: NewMasterWorkflowRequestDto): Promise<NewMasterWorkflowResponseDto>;
    getWorkflowDetailByWorkflowSettingId(workflowSettingId: number): Promise<WorkflowDetailResponseDTO>;
    getPolicyDetailByWorkflowSettingId(workflowSettingId: number): Promise<WorkflowDetailResponseDTO>;
    downloadPolicyDetailByWorkflowSettingId(res: Response, workflowSettingId: number): Promise<Response<any, Record<string, any>>>;
    getWorkflowSettingHistory(workflowSettingId: number): Promise<Record<string, any>>;
    publishWorkflowSetting(request: RequestContext, workflowSettingId: number, unpublishedVersionRequestDto: UnpublishedVersionRequest): Promise<MessageResponseDto>;
    validateOverridesEntity(request: RequestContext, workflowSettingId: number, overrideWorkflowRequestDto: OverrideWorkflowRequestDto): Promise<any[]>;
    overrideMasterWorkflowSetting(request: RequestContext, workflowSettingId: number, overrideWorkflowRequestDto: OverrideWorkflowRequestDto): Promise<NewMasterWorkflowResponseDto[]>;
    cloneWorkflowSettingSteps(request: RequestContext, workflowSettingId: number, CloneWorkflowRequestDto: CloneWorkflowRequestDto): Promise<WorkflowDetailResponseDTO>;
    deleteWorkflowSetting(request: RequestContext, workflowSettingId: number): Promise<MessageResponseDto>;
    deleteMultipleOverridenWorkflowSetting(request: RequestContext, overridenWorkflowIds: number[]): Promise<MessageResponseDto>;
    deleteUnpublishedNewVersionWorkflowSetting(request: RequestContext, workflowSettingId: number): Promise<MessageResponseDto>;
    deleteOverriddenWorkflowSetting(request: RequestContext, workflowSettingId: number): Promise<MessageResponseDto>;
    deleteUnpublishedOverriddenWorkflowSetting(request: RequestContext, workflowSettingId: number): Promise<MessageResponseDto>;
    createNewVersionWorkflowSetting(request: RequestContext, newVersionWorkflowRequestDto: NewVersionWorkflowRequestDto): Promise<{
        message: string;
    }>;
    publishOverriddenWorkflowSetting(request: RequestContext, workflowMasterSettingId: number, publishedOverriddenWorkflowRequest: PublishedOverriddenWorkflowRequest): Promise<{
        message: string;
    }>;
    getUnpublishedVersionWorkflowDetailBySettingId(workflowSettingId: number): Promise<WorkflowDetailResponseDTO>;
}
