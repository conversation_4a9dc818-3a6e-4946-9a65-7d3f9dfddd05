export declare class GetRepresentativeResponseDto {
    id: number;
    delegate_for_username: string;
    delegate_to_username: string;
    delegate_from_date: Date;
    delegate_to_date: Date;
    created_on: Date;
    created_by: string;
    modified_by: string;
    modified_on: Date;
    additional_info: any;
    business_entity_id: number;
    delegate_comments: string;
    constructor(partial?: Partial<GetRepresentativeResponseDto>);
}
