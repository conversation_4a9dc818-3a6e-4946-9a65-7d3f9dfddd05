"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedGetRoleBasedStepsResponseDTO = exports.GetRoleBasedStepsResponse = exports.GetRoleBasedSettingResponse = exports.ProjectComponentTitle = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const enums_1 = require("../../../shared/enums");
const associated_type_enum_1 = require("../../../shared/enums/associated-type.enum");
class ProjectComponentTitle {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ProjectComponentTitle.prototype, "title", void 0);
exports.ProjectComponentTitle = ProjectComponentTitle;
class GetRoleBasedSettingResponse {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRoleBasedSettingResponse.prototype, "requestTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedSettingResponse.prototype, "budgetType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRoleBasedSettingResponse.prototype, "projectComponentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRoleBasedSettingResponse.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedSettingResponse.prototype, "entityCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedSettingResponse.prototype, "entityTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedSettingResponse.prototype, "entityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", ProjectComponentTitle)
], GetRoleBasedSettingResponse.prototype, "projectComponent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRoleBasedSettingResponse.prototype, "year", void 0);
exports.GetRoleBasedSettingResponse = GetRoleBasedSettingResponse;
class GetRoleBasedStepsResponse {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRoleBasedStepsResponse.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRoleBasedStepsResponse.prototype, "workflowMasterStepId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRoleBasedStepsResponse.prototype, "workflowMasterSettingId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "associateLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "associateRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "associateType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "associatedColumn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "associatedUser", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetRoleBasedStepsResponse.prototype, "createdOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetRoleBasedStepsResponse.prototype, "updatedOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetRoleBasedStepsResponse.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", GetRoleBasedSettingResponse)
], GetRoleBasedStepsResponse.prototype, "workflowMasterSetting", void 0);
exports.GetRoleBasedStepsResponse = GetRoleBasedStepsResponse;
class PaginatedGetRoleBasedStepsResponseDTO {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PaginatedGetRoleBasedStepsResponseDTO.prototype, "pageTotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ isArray: true, type: GetRoleBasedStepsResponse }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], PaginatedGetRoleBasedStepsResponseDTO.prototype, "records", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PaginatedGetRoleBasedStepsResponseDTO.prototype, "total", void 0);
exports.PaginatedGetRoleBasedStepsResponseDTO = PaginatedGetRoleBasedStepsResponseDTO;
//# sourceMappingURL=get-role-based-steps-response.dto.js.map