"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var AfeProposalReadService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeProposalReadService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const repositories_1 = require("../../afe-config/repositories");
const length_of_commitment_repository_1 = require("../../afe-config/repositories/length-of-commitment.repository");
const pagination_1 = require("../../core/pagination");
const services_1 = require("../../core/services");
const repositories_2 = require("../../finance/repositories");
const pdf_generator_service_1 = require("../../pdf-generator/pdf-generator.service");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const mappings_1 = require("../../shared/mappings");
const validators_1 = require("../../shared/validators");
const services_2 = require("../../task/services");
const dtos_1 = require("../dtos");
const afe_proposal_approver_response_1 = require("../dtos/response/afe-proposal-approver-response");
const repositories_3 = require("../repositories");
const services_3 = require("../../business-entity/services");
const services_4 = require("../../shared/services");
const config_service_1 = require("../../config/config.service");
const constants_1 = require("../../shared/constants");
let AfeProposalReadService = AfeProposalReadService_1 = class AfeProposalReadService {
    constructor(afeProposalRepository, afeProposalApproverRepository, afeProposalAmountSplitRepository, historyApiClient, analysisCodeRepository, naturalAccountNumberRepository, costCenterRepository, afeBudgetTypeRepository, currencyTypeRepository, taskService, taskApiClient, afeProposalValidator, loggerService, pdfGeneratorService, attachmentApiClient, lengthOfCommitmentRepository, businessEntityService, permissionService, configService) {
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.afeProposalAmountSplitRepository = afeProposalAmountSplitRepository;
        this.historyApiClient = historyApiClient;
        this.analysisCodeRepository = analysisCodeRepository;
        this.naturalAccountNumberRepository = naturalAccountNumberRepository;
        this.costCenterRepository = costCenterRepository;
        this.afeBudgetTypeRepository = afeBudgetTypeRepository;
        this.currencyTypeRepository = currencyTypeRepository;
        this.taskService = taskService;
        this.taskApiClient = taskApiClient;
        this.afeProposalValidator = afeProposalValidator;
        this.loggerService = loggerService;
        this.pdfGeneratorService = pdfGeneratorService;
        this.attachmentApiClient = attachmentApiClient;
        this.lengthOfCommitmentRepository = lengthOfCommitmentRepository;
        this.businessEntityService = businessEntityService;
        this.permissionService = permissionService;
        this.configService = configService;
    }
    getAfeListing(currentContext, limit = 10, page = 1, forApprovalHistory = false, filters) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const { businessEntities } = filters;
            const locations = yield this.permissionService.getAllLocationIdForGivenPermission(user.username, enums_1.PERMISSIONS.AFE_VIEW);
            if (businessEntities === null || businessEntities === void 0 ? void 0 : businessEntities.length) {
                filters.businessEntities = yield this.businessEntityService.getBusinessEntitiesChildIds(businessEntities);
            }
            let extraPermissions = null;
            if (!forApprovalHistory) {
                extraPermissions = yield this.permissionService.getExtraPermissionObject(user.username);
            }
            const result = forApprovalHistory
                ? yield this.afeProposalRepository.getAfeListsThatApprovedByUser(currentContext, limit, page, filters)
                : yield this.afeProposalRepository.getPaginatedAfeProposals(currentContext, locations, extraPermissions, filters, limit, page);
            const afeList = yield Promise.all(result.rows.map((afe) => __awaiter(this, void 0, void 0, function* () {
                var _a;
                const approversList = yield this.afeProposalApproverRepository.getApproversByProposalId(afe.id);
                let approvalPendingWith = [];
                if (afe.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.APPROVED &&
                    afe.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.REJECTED &&
                    afe.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.CANCELLED) {
                }
                {
                    for (const approver of approversList) {
                        if (approver.actionStatus === enums_1.APPROVER_STATUS.IN_PROGRESS) {
                            const users = approver.otherInfo.usersDetail
                                .map(user => `${user.firstName || ''} ${user.lastName || ''}`)
                                .filter(user => !!user.trim());
                            approvalPendingWith.push(...users);
                        }
                    }
                    approvalPendingWith = [...new Set(approvalPendingWith)];
                }
                const currentUserSteps = yield this.taskService.getCurrentTaskSteps(currentContext.user.username, approversList);
                afe.totalAmount = ((_a = afe === null || afe === void 0 ? void 0 : afe.supplementalDeltaAmounts) === null || _a === void 0 ? void 0 : _a.totalAmount)
                    ? afe.supplementalDeltaAmounts.totalAmount
                    : afe.totalAmount;
                if (currentUserSteps.length) {
                    try {
                        const [task] = yield this.taskApiClient.getAllTasks(currentUserSteps[currentUserSteps.length - 1].id, enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK);
                        return Object.assign(Object.assign({}, afe), { task: {
                                approvalType: currentUserSteps[currentUserSteps.length - 1].otherInfo.approvalType,
                                taskId: task.id,
                                relUrl: task.task_rel_url,
                            }, approvalPendingWith });
                    }
                    catch (error) {
                        this.loggerService.error(error, error.stack, AfeProposalReadService_1.name);
                        return Object.assign(Object.assign({}, afe), { approvalPendingWith });
                    }
                }
                return Object.assign(Object.assign({}, afe), { approvalPendingWith });
            })));
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.AfeProposalListingResponseDto, afeList);
            return new pagination_1.Pagination({ records, total: result.count });
        });
    }
    getAfeProposalById(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            const { user } = currentContext;
            if (!result) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(result, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const { id, afeRequestTypeId, entityId, entityCode, entityTitle, category, isApprovedByBoard, createdOn, readers, globalProcurementQuesAns, userStatus, internalStatus, data, parentAfeId, budgetType, projectReferenceNumber, submitterId, supplementalData, yearOfCommitment, additionalCurrencyAmount, marketValueAdditionalCurrencyAmount, workflowYear, subscribers, version, isNewFfoSetting, supplementalDeltaAmounts, location } = result;
            let budgetTypeDetail = null;
            if (budgetType) {
                budgetTypeDetail = yield this.afeBudgetTypeRepository.getBudgetTypeById(mappings_1.BUDGET_TYPE_MAPPING_WITH_ID[budgetType]);
            }
            const currencyDetail = yield this.currencyTypeRepository.getCurrencyConversionRateToPrimary(additionalCurrencyAmount.currency);
            let prevAfeLatestDetail = {};
            if (category === enums_1.AFE_CATEGORY.SUPPLEMENTAL) {
                const prevAfeDetail = yield this.afeProposalRepository.getPreviousAfeDetailByIdAndVersion(parentAfeId, version);
                if (prevAfeDetail) {
                    let budgetTypeDetail = null;
                    if (prevAfeDetail === null || prevAfeDetail === void 0 ? void 0 : prevAfeDetail.budgetType) {
                        budgetTypeDetail = yield this.afeBudgetTypeRepository.getBudgetTypeById(mappings_1.BUDGET_TYPE_MAPPING_WITH_ID[prevAfeDetail.budgetType]);
                    }
                    let projectSplits = [];
                    let budgetBasedProjectSplits = [];
                    const amountSplits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndTypes((0, lodash_1.toNumber)(prevAfeDetail.id), [
                        enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT,
                        enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT,
                    ], ['id', 'objectId', 'objectTitle', 'additionalCurrencyAmount', 'type', 'budgetType']);
                    amountSplits.forEach(amountSplit => {
                        if (amountSplit.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT) {
                            projectSplits.push({
                                amountSplitId: amountSplit.id,
                                id: amountSplit.objectId,
                                title: amountSplit.objectTitle,
                                amount: amountSplit.additionalCurrencyAmount.amount,
                                currency: amountSplit.additionalCurrencyAmount.currency,
                            });
                        }
                        if (amountSplit.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT) {
                            budgetBasedProjectSplits.push({
                                amountSplitId: amountSplit.id,
                                objectId: amountSplit.objectId,
                                objectTitle: amountSplit.objectTitle,
                                amount: amountSplit.amount,
                                additionalCurrencyAmount: amountSplit.additionalCurrencyAmount,
                                currency: amountSplit.additionalCurrencyAmount.currency,
                                budgetType: amountSplit.budgetType,
                            });
                        }
                    });
                    prevAfeLatestDetail = Object.assign(Object.assign({}, prevAfeLatestDetail), { parentAfeLatestBudgetType: budgetTypeDetail, parentAfeTotalAmount: prevAfeDetail.additionalCurrencyAmount.amount, parentAfeLatestProjectComponentInfo: projectSplits, parentAfeLatestBudgetBasedProjectSplit: (budgetBasedProjectSplits === null || budgetBasedProjectSplits === void 0 ? void 0 : budgetBasedProjectSplits.length)
                            ? (0, helpers_1.serializeBudgetBasedProjectAmountSplits)(budgetBasedProjectSplits)
                            : [] });
                }
            }
            const afeDetail = Object.assign({ id, requestTypeId: afeRequestTypeId, entityId, isSupplemental: category === enums_1.AFE_CATEGORY.SUPPLEMENTAL, isApprovedByBoard, totalAmount: additionalCurrencyAmount.amount, totalMarketValue: marketValueAdditionalCurrencyAmount === null || marketValueAdditionalCurrencyAmount === void 0 ? void 0 : marketValueAdditionalCurrencyAmount.amount, businessEntity: { id: entityId, code: entityCode, name: entityTitle }, budgetType: budgetType ? { id: budgetTypeDetail.id, title: budgetType } : null, currencyDetail,
                projectReferenceNumber,
                data,
                internalStatus,
                userStatus,
                parentAfeId,
                readers,
                createdOn,
                isNewFfoSetting,
                submitterId, questionAnswers: globalProcurementQuesAns, supplementalData, year: workflowYear, lengthOfCommitment: yearOfCommitment, isSubscriptionOn: (subscribers === null || subscribers === void 0 ? void 0 : subscribers.includes(user.username)) || false, supplementalDeltaAmounts,
                location }, prevAfeLatestDetail);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.AfeProposalResposeDto, afeDetail);
        });
    }
    getApproversListOfAfePoposal(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const result = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
            const approvers = result.map(approver => {
                var _a;
                return ({
                    id: approver.id,
                    title: approver.title,
                    approvers: (_a = approver.otherInfo) === null || _a === void 0 ? void 0 : _a.usersDetail,
                    stepId: approver.workflowMasterStepsId,
                    isCustomUser: !approver.workflowMasterStepsId,
                    associateRole: approver.assignedTo,
                    associateType: approver.assginedType,
                    associateLevel: approver.assignedLevel,
                    sequenceNumber: approver.approvalSequence,
                    associatedColumn: approver.associatedColumn,
                    parallelIdentifier: approver.parallelIdentifier,
                    approvalSequenceType: approver.parallelIdentifier
                        ? enums_1.APPROVAL_SEQUENCE_TYPE.PARALLEL
                        : enums_1.APPROVAL_SEQUENCE_TYPE.ANYONE,
                    actionStatus: approver.actionStatus,
                    actionBy: approver.actionBy,
                    comment: approver.comment,
                    actionDate: approver.actionDate,
                    originalApprover: approver.userDetail ? approver.userDetail.originalApprover : null,
                });
            });
            return (0, helpers_1.singleObjectToInstance)(afe_proposal_approver_response_1.AfeProposalApproverResponseDto, approvers);
        });
    }
    getAmountSplitsOfAfeProposal(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            return this.getAfeProposalAmountSplitData(proposal);
        });
    }
    getAfeProposalAmountSplitData(afePropsal) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        return __awaiter(this, void 0, void 0, function* () {
            const amountSplits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalId(afePropsal.id);
            let budgetTypeSplit = [];
            let costCenterSplit = [];
            let projectComponentSplit = [];
            let analysisCodeSplit = [];
            let naturalAccountNumberSplit = [];
            let chartOfAccounts = [];
            let budgetReferenceNumberSplit = [];
            let budgetBasedProjectSplit = [];
            for (let split of amountSplits) {
                const { objectId, objectTitle, additionalCurrencyAmount, id } = split;
                const splitData = {
                    amountSplitId: +id,
                    id: objectId,
                    title: objectTitle,
                    amount: additionalCurrencyAmount.amount,
                    currency: additionalCurrencyAmount.currency,
                };
                switch (split.type) {
                    case enums_1.AMOUNT_SPLIT.BUDGET_TYPE_SPLIT:
                        budgetTypeSplit.push(Object.assign({}, splitData));
                        break;
                    case enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT:
                        projectComponentSplit.push(Object.assign({}, splitData));
                        break;
                    case enums_1.AMOUNT_SPLIT.ANALYSIS_CODE_SPLIT:
                        const { code: analysisCode } = yield this.analysisCodeRepository.getAnalysisCodeByIdIncludingInactiveAndDeleted(split.objectId);
                        analysisCodeSplit.push(Object.assign(Object.assign({}, splitData), { code: analysisCode }));
                        break;
                    case enums_1.AMOUNT_SPLIT.NATURAL_ACCOUNT_SPLIT:
                        const { number: naturalAccountNumber } = yield ((_a = this.naturalAccountNumberRepository) === null || _a === void 0 ? void 0 : _a.getNaturalAccountNumberByIdIncludingInactiveAndDeleted(split.objectId));
                        naturalAccountNumberSplit.push(Object.assign(Object.assign({}, splitData), { number: naturalAccountNumber }));
                        break;
                    case enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT:
                        const costCenterDetail = yield this.costCenterRepository.getCostCenterByIdIncludingInactiveAndDeleted(split.objectId);
                        let moreAccountDetail = {};
                        if ((_b = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _b === void 0 ? void 0 : _b.analysisCode) {
                            moreAccountDetail = Object.assign(Object.assign({}, moreAccountDetail), { analysisCode: ((_c = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _c === void 0 ? void 0 : _c.analysisCode) || null });
                        }
                        if ((_d = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _d === void 0 ? void 0 : _d.naturalAccount) {
                            moreAccountDetail = Object.assign(Object.assign({}, moreAccountDetail), { naturalAccount: ((_e = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _e === void 0 ? void 0 : _e.naturalAccount) || null });
                        }
                        if ((_f = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _f === void 0 ? void 0 : _f.budgetReferenceNumberSplit) {
                            const budgetRefNumberList = (_g = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _g === void 0 ? void 0 : _g.budgetReferenceNumberSplit.map((budgetReferenceNumber) => {
                                var _a, _b;
                                return {
                                    amount: ((_a = budgetReferenceNumber === null || budgetReferenceNumber === void 0 ? void 0 : budgetReferenceNumber.additionalCurrencyAmount) === null || _a === void 0 ? void 0 : _a.amount) || 0,
                                    currency: ((_b = budgetReferenceNumber === null || budgetReferenceNumber === void 0 ? void 0 : budgetReferenceNumber.additionalCurrencyAmount) === null || _b === void 0 ? void 0 : _b.currency) || 'USD',
                                    number: (budgetReferenceNumber === null || budgetReferenceNumber === void 0 ? void 0 : budgetReferenceNumber.number) || '',
                                };
                            });
                            moreAccountDetail = Object.assign(Object.assign({}, moreAccountDetail), { budgetReferenceNumberSplit: budgetRefNumberList });
                        }
                        costCenterSplit.push(Object.assign(Object.assign(Object.assign({}, splitData), { code: (costCenterDetail === null || costCenterDetail === void 0 ? void 0 : costCenterDetail.code) || 0, section: ((_h = split === null || split === void 0 ? void 0 : split.additionalInfo) === null || _h === void 0 ? void 0 : _h.section) || null }), moreAccountDetail));
                        break;
                    case enums_1.AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT:
                        const { title } = splitData, updatedSplit = __rest(splitData, ["title"]);
                        budgetReferenceNumberSplit.push(Object.assign(Object.assign({}, updatedSplit), { number: split.objectTitle }));
                        break;
                    case enums_1.AMOUNT_SPLIT.GL_CODE_SPLIT:
                        const segments = split.objectTitle.split('-');
                        chartOfAccounts.push({
                            amountSplitId: +split.id,
                            amount: additionalCurrencyAmount.amount,
                            currency: additionalCurrencyAmount.currency,
                            segments: {
                                segment1: segments[0],
                                segment2: segments[1],
                                segment3: segments[2],
                                segment4: segments[3],
                                segment5: segments[4],
                                segment6: segments[5],
                                segment7: segments[6],
                                segment8: segments[7],
                            },
                        });
                        break;
                }
            }
            const projectComponentSplitByBudgetType = amountSplits.filter(split => split.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT);
            if (projectComponentSplitByBudgetType.length && afePropsal.budgetType === enums_1.BUDGET_TYPE.MIXED) {
                budgetBasedProjectSplit = (0, helpers_1.serializeBudgetBasedProjectAmountSplits)(projectComponentSplitByBudgetType);
            }
            const splits = {
                budgetTypeSplit,
                costCenterSplit,
                projectComponentSplit,
                analysisCodeSplit,
                naturalAccountNumberSplit,
                chartOfAccounts,
                budgetBasedProjectSplit,
            };
            return (0, helpers_1.singleObjectToInstance)(dtos_1.AfeProposalAmountSplitResponseDto, splits);
        });
    }
    getAfeProposalActionHistory(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const histories = yield this.historyApiClient.getRequestHistory(afeProposalId, enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL);
            const nonHiddenHistories = histories.filter(history => { var _a; return !((_a = history.additional_info) === null || _a === void 0 ? void 0 : _a.hidden); });
            return nonHiddenHistories.map(history => (0, helpers_1.instanceToPlain)(new dtos_1.AfeProposalActionHistoryResponseDto(history)));
        });
    }
    searchAfeListByProjectReferenceNumber(projectReferenceNumber, afeRequestTypeIds, currentContext, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const viewPermissionLocations = yield this.permissionService.getAllLocationIdForGivenPermission(user.unique_name, enums_1.PERMISSIONS.AFE_VIEW);
            const submitPermissionLocations = yield this.permissionService.getAllLocationIdForGivenPermission(user.unique_name, enums_1.PERMISSIONS.AFE_SUBMIT);
            const locations = [...new Set([...viewPermissionLocations, ...submitPermissionLocations])];
            const entityIds = entityId ? [entityId] : locations;
            const afeList = yield this.afeProposalRepository.searchAfeListByProjectRefernceNumber(projectReferenceNumber, afeRequestTypeIds, entityIds);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.SearchAfeByProjectRefernceNoResponse, afeList);
        });
    }
    getAllRelatedSupplementalAfes(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const afeParent = yield this.afeProposalRepository.getParentIdOfAfe(afeProposalId);
            let parentAfeId = afeParent ? afeParent.parentAfeId : afeProposalId;
            const afes = yield this.afeProposalRepository.getAfeAndItsChildern(parentAfeId);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.RelatedAfesInfoResponse, afes);
        });
    }
    isAfeOrItsSupplementalInProgress(afeProposalId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const afes = yield this.afeProposalRepository.getAfeAndItsChildern(afeProposalId);
            for (const afe of afes) {
                if (afe.isNewVersionInProgress) {
                    return (0, helpers_1.singleObjectToInstance)(dtos_1.CheckAfeOrItsSupplementalInProgress, {
                        inprogressAfeReferenceNumber: afe.projectReferenceNumber,
                    });
                }
            }
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CheckAfeOrItsSupplementalInProgress, {
                inprogressAfeReferenceNumber: null,
            });
        });
    }
    getLatestVersionAfeInfoByParentAfeRefNumber(afeProposalRefNumber, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const afe = yield this.afeProposalRepository.getAfeProposalIdWithSplitByRefNumber(afeProposalRefNumber);
            if (!(afe === null || afe === void 0 ? void 0 : afe.id)) {
                throw new exceptions_1.HttpException(`Afe doesn't exist`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(afe, currentContext);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const latestApprovedVersionAfe = yield this.afeProposalRepository.getLatestVersionInfoOfAfe(afe.id);
            latestApprovedVersionAfe.parentAfeId = Number(afe.id);
            latestApprovedVersionAfe.totalAmount = latestApprovedVersionAfe.additionalCurrencyAmount.amount;
            latestApprovedVersionAfe.marketValue =
                (_a = latestApprovedVersionAfe === null || latestApprovedVersionAfe === void 0 ? void 0 : latestApprovedVersionAfe.marketValueAdditionalCurrencyAmount) === null || _a === void 0 ? void 0 : _a.amount;
            return (0, helpers_1.singleObjectToInstance)(dtos_1.LatestAfeVersionInfoResponseDto, latestApprovedVersionAfe);
        });
    }
    generateAfePdf(afeProposalId, currentContext, taskId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u;
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to download this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const attachments = yield this.attachmentApiClient.getAllAttachments(afeProposalId, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT);
            for (const attachment of attachments) {
                const file = yield this.attachmentApiClient.getContentByFileId(attachment.file_id);
                if (file.attachment_content_type.includes('image')) {
                    attachment.base64 = `data:${file.attachment_content_type};base64,${Buffer.from(file.contents.data).toString('base64')}`;
                }
            }
            const approvers = yield this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
            const approversObj = approvers.map(approver => {
                var _a, _b, _c, _d, _e;
                return ({
                    title: approver.title,
                    actionStatus: enums_1.APPROVER_STATUS_TITLE_FOR_PDF[approver.actionStatus],
                    actionBy: approver.actionBy || '-',
                    comment: approver.comment || '-',
                    actionDate: approver.actionDate || '-',
                    approvers: ((_a = approver === null || approver === void 0 ? void 0 : approver.otherInfo) === null || _a === void 0 ? void 0 : _a.usersDetail.map(user => `${user.firstName} ${user.lastName}`).join(', ')) || '-',
                    userDetail: approver.userDetail
                        ? `${((_c = (_b = approver.userDetail) === null || _b === void 0 ? void 0 : _b.originalApprover) === null || _c === void 0 ? void 0 : _c.firstName) || ''} ${((_e = (_d = approver.userDetail) === null || _d === void 0 ? void 0 : _d.originalApprover) === null || _e === void 0 ? void 0 : _e.lastName) || ''}`
                        : null,
                });
            });
            let allApproversAttachments = [];
            if (approvers === null || approvers === void 0 ? void 0 : approvers.length) {
                const anyActionPerformed = approvers.find(approver => approver.actionBy);
                if (anyActionPerformed) {
                    allApproversAttachments = yield this.attachmentApiClient.getAttachmentsByMetadata({
                        entity_type: enums_1.ATTACHMENT_ENTITY_TYPE.AFE_ACTION,
                        meta_data_1: afeProposalId.toString(),
                    });
                }
            }
            for (const approverAttachment of allApproversAttachments) {
                const file = yield this.attachmentApiClient.getContentByFileId(approverAttachment.file_id);
                if (file.attachment_content_type.includes('image')) {
                    approverAttachment.base64 = `data:${file.attachment_content_type};base64,${Buffer.from(file.contents.data).toString('base64')}`;
                }
            }
            let splits = yield this.getAfeProposalAmountSplitData(proposal);
            if (proposal.supplementalDeltaAmounts) {
                const { afeProposalAmountSplits, totalAmount, additionalCurrencyAmount, marketValue, marketValueAdditionalCurrencyAmount, } = proposal.supplementalDeltaAmounts;
                splits = Object.assign(Object.assign({}, splits), { budgetTypeSplit: afeProposalAmountSplits.budgetTypeSplit.map(s => (Object.assign({ id: s.id, title: s.title }, s.additionalCurrencyAmount))), projectComponentSplit: afeProposalAmountSplits.projectComponentSplit.map(s => (Object.assign({ id: s.id, title: s.title }, s.additionalCurrencyAmount))), budgetBasedProjectSplit: afeProposalAmountSplits.budgetBasedProjectSplit.map(s => (Object.assign(Object.assign({ id: s.id, title: s.title }, s.additionalCurrencyAmountForBudgetBasedProjectSplit), { currency: additionalCurrencyAmount.currency }))) });
                proposal.totalAmount = totalAmount;
                proposal.additionalCurrencyAmount = additionalCurrencyAmount;
                proposal.marketValue = marketValue;
                proposal.marketValueAdditionalCurrencyAmount = marketValueAdditionalCurrencyAmount;
            }
            splits.budgetTypeSplit.forEach(split => {
                split.title = enums_1.BUDGET_TYPE_TITLE[split.title];
            });
            const histories = yield this.historyApiClient.getRequestHistory(afeProposalId, enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL);
            const nonHiddenHistories = histories.filter(history => { var _a; return !((_a = history.additional_info) === null || _a === void 0 ? void 0 : _a.hidden); });
            const afeHistory = nonHiddenHistories.map(history => ({
                actionPerformed: mappings_1.HistoryActionNameMapping[history.action_performed],
                actionComments: history.action_comments,
                actionDate: (0, helpers_1.formatDateString)(history.action_date),
                createdBy: history.created_by,
            }));
            const config = this.configService.getAppConfig();
            const data = {
                afeRequestTypeId: proposal.afeRequestTypeId,
                afeRequestType: mappings_1.AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[proposal.afeRequestTypeId],
                projectRefNum: proposal.projectReferenceNumber,
                createAt: proposal.createdOn,
                currentStatus: proposal.userStatus,
                projectName: proposal.name,
                businessEntity: proposal.entityTitle,
                projectJustification: (_b = (_a = proposal.data) === null || _a === void 0 ? void 0 : _a.projectDetails) === null || _b === void 0 ? void 0 : _b.projectJustification,
                projectLeaderName: (_d = (_c = proposal.data) === null || _c === void 0 ? void 0 : _c.projectDetails) === null || _d === void 0 ? void 0 : _d.projectLeader.displayName,
                projectLeaderTitle: (_g = (_f = (_e = proposal.data) === null || _e === void 0 ? void 0 : _e.projectDetails) === null || _f === void 0 ? void 0 : _f.projectLeader) === null || _g === void 0 ? void 0 : _g.jobTitle,
                projectLeaderContactNum: (_j = (_h = proposal.data) === null || _h === void 0 ? void 0 : _h.projectDetails) === null || _j === void 0 ? void 0 : _j.projectLeaderNumber,
                keyContacts: ((_l = (_k = proposal.data) === null || _k === void 0 ? void 0 : _k.projectDetails) === null || _l === void 0 ? void 0 : _l.keyContacts) || '-',
                supplementalData: proposal.supplementalData || null,
                nature: ((_o = (_m = proposal.data) === null || _m === void 0 ? void 0 : _m.natureType) === null || _o === void 0 ? void 0 : _o.title) || null,
                type: ((_p = proposal.data) === null || _p === void 0 ? void 0 : _p.type) || null,
                afeCategory: proposal.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL ? 'Supplemental' : '',
                afeSubCategory: ((_q = proposal.data) === null || _q === void 0 ? void 0 : _q.subType) || null,
                budgetType: proposal.budgetType ? enums_1.BUDGET_TYPE_TITLE[proposal.budgetType] : null,
                budgetTypeJustification: ((_r = proposal === null || proposal === void 0 ? void 0 : proposal.data) === null || _r === void 0 ? void 0 : _r.budgetTypeJustification) || null,
                totalAmount: proposal.totalAmount,
                currencyType: proposal.currencyType,
                marketValue: (proposal === null || proposal === void 0 ? void 0 : proposal.marketValue) ? proposal.marketValue : null,
                marketValueCurrency: (proposal === null || proposal === void 0 ? void 0 : proposal.marketValueCurrency) ? proposal.marketValueCurrency : null,
                additionalCurrencyAmount: proposal.additionalCurrencyAmount,
                marketValueAdditionalCurrencyAmount: proposal === null || proposal === void 0 ? void 0 : proposal.marketValueAdditionalCurrencyAmount,
                isApprovedByBoard: (proposal === null || proposal === void 0 ? void 0 : proposal.isApprovedByBoard) ? 'Yes' : 'No',
                yearOfCommitment: proposal.yearOfCommitment
                    ? (_s = (yield this.lengthOfCommitmentRepository.getLengthOfCommitmentByNumberOfYears(proposal.yearOfCommitment))) === null || _s === void 0 ? void 0 : _s.title
                    : null,
                submitYear: proposal.workflowYear,
                readers: ((_t = proposal.readers) === null || _t === void 0 ? void 0 : _t.map(reader => `${reader.displayName}`).join(', ')) || 'No Reader Added',
                approversList: approversObj,
                attachments: attachments.length ? attachments : [],
                globalProcurementQuesAns: proposal === null || proposal === void 0 ? void 0 : proposal.globalProcurementQuesAns,
                splits: splits,
                history: afeHistory,
                baseUrl: config.uiClient.baseUrl,
                allApproversAttachments: (allApproversAttachments === null || allApproversAttachments === void 0 ? void 0 : allApproversAttachments.length) ? allApproversAttachments : [],
                base64DpWorldLogo: constants_1.BASE64_DP_WORLD_LOGO,
                location: ((_u = proposal === null || proposal === void 0 ? void 0 : proposal.location) === null || _u === void 0 ? void 0 : _u.title) || null
            };
            const pdf = yield this.pdfGeneratorService.generatePdf(data, 'afeDetail');
            return pdf;
        });
    }
};
AfeProposalReadService = AfeProposalReadService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_3.AfeProposalRepository,
        repositories_3.AfeProposalApproverRepository,
        repositories_3.AfeProposalAmountSplitRepository,
        clients_1.HistoryApiClient,
        repositories_2.AnalysisCodeRepository,
        repositories_2.NaturalAccountNumberRepository,
        repositories_2.CostCenterRepository,
        repositories_1.AfeBudgetTypeRepository,
        repositories_2.CurrencyTypeRepository,
        services_2.TaskService,
        clients_1.TaskApiClient,
        validators_1.AfeProposalValidator,
        services_1.LoggerService,
        pdf_generator_service_1.PdfGeneratorService,
        clients_1.AttachmentApiClient,
        length_of_commitment_repository_1.LengthOfCommitmentRepository,
        services_3.BusinessEntityService,
        services_4.SharedPermissionService,
        config_service_1.ConfigService])
], AfeProposalReadService);
exports.AfeProposalReadService = AfeProposalReadService;
//# sourceMappingURL=afe-proposal-read.service.js.map