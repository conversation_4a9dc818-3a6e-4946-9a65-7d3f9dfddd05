{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/MyWorkspace/Projects/DpWorld/AFE_Revamp/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';\nimport { PAGINATION_DEFAULT_LIMIT } from '@core/constants';\nimport { toNumber } from 'lodash';\nimport { finalize } from 'rxjs';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/common/spinner.service\";\nimport * as i2 from \"../../core/services\";\nimport * as i3 from \"@ngx-translate/core\";\nimport * as i4 from \"@core/services/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../../core/modules/partials/input-error-message/input-error-message.component\";\nimport * as i7 from \"../../../../core/modules/partials/ad-user-search/ad-user-search.component\";\nimport * as i8 from \"../../../../core/modules/partials/skeleton-loader/list-skeleton-loader/list-skeleton-loader.component\";\nimport * as i9 from \"../../../../core/modules/partials/skeleton-loader/form-skeleton-loader/form-skeleton-loader.component\";\nimport * as i10 from \"../../../../core/modules/partials/empty-state/empty-state.component\";\nimport * as i11 from \"../../../../core/modules/partials/history-logs/history-logs.component\";\nimport * as i12 from \"../../../../core/modules/partials/upload-attachment/add-attachment/add-attachment.component\";\nimport * as i13 from \"../../../../core/modules/partials/modals/modal/modal.component\";\nimport * as i14 from \"ngx-pagination\";\nimport * as i15 from \"@angular/forms\";\nimport * as i16 from \"@core/directives/toggle-profile-menu.directive\";\nconst _c0 = [\"costCenterEditorModal\"];\nconst _c1 = [\"historyModal\"];\n\nfunction CostCentersComponent_ng_container_0_div_1_ng_container_44_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_ng_container_0_div_1_ng_container_44_span_23_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const costCenter_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r15.openCostCenterEditorModal(true, costCenter_r12.id));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const costCenter_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" +\", costCenter_r12.sectionHead.length - 1, \" \");\n  }\n}\n\nfunction CostCentersComponent_ng_container_0_div_1_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 36)(2, \"div\", 27)(3, \"div\", 37);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 38)(7, \"a\", 39);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 28)(10, \"div\", 37);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 38);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 28)(16, \"div\", 37);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 38)(20, \"div\", 38)(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, CostCentersComponent_ng_container_0_div_1_ng_container_44_span_23_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 28)(25, \"div\", 37);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 38);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 28)(31, \"div\", 37);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 38);\n    i0.ɵɵtext(35);\n    i0.ɵɵpipe(36, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 41)(38, \"div\", 14)(39, \"div\", 42)(40, \"button\", 16);\n    i0.ɵɵelement(41, \"img\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 18)(43, \"div\", 19);\n    i0.ɵɵelement(44, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(45, \"div\", 21);\n    i0.ɵɵelementStart(46, \"div\", 19)(47, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_ng_container_0_div_1_ng_container_44_Template_a_click_47_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const costCenter_r12 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.openCostCenterEditorModal(true, costCenter_r12.id));\n    });\n    i0.ɵɵpipe(48, \"translate\");\n    i0.ɵɵpipe(49, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 19)(51, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_ng_container_0_div_1_ng_container_44_Template_a_click_51_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const costCenter_r12 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.deleteCostCenter(costCenter_r12.id));\n    });\n    i0.ɵɵpipe(52, \"translate\");\n    i0.ɵɵpipe(53, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 19)(55, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_ng_container_0_div_1_ng_container_44_Template_a_click_55_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const costCenter_r12 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.openHistoryModel(costCenter_r12.id));\n    });\n    i0.ɵɵpipe(56, \"translate\");\n    i0.ɵɵpipe(57, \"translate\");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const costCenter_r12 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 18, \"LIST.NAME\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", costCenter_r12.name, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 20, \"LIST.CODE\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", costCenter_r12.code, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 22, \"FORM.LABEL.SECTIONS\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", costCenter_r12.sectionHead.length ? costCenter_r12.sectionHead[0].title : \"-\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", costCenter_r12.sectionHead.length >= 2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(27, 24, \"LIST.DEPARTMENT_HEAD\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (costCenter_r12 == null ? null : costCenter_r12.departmentHead == null ? null : costCenter_r12.departmentHead.displayName) || \"-\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(33, 26, \"LIST.UPDATED_AT\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(36, 28, costCenter_r12.updatedOn, \"medium\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"transform\", \"-30px, 50.5px, 0px\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(48, 31, \"FORM.BUTTON.EDIT_BUTTON\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(49, 33, \"FORM.BUTTON.EDIT_BUTTON\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(52, 35, \"COMMON.REMOVE\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(53, 37, \"WORKFLOW.RULE.BUTTON.DELETE\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(56, 39, \"FORM.BUTTON.HISTORY_BUTTON\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(57, 41, \"FORM.BUTTON.HISTORY_BUTTON\"));\n  }\n}\n\nconst _c2 = function (a1, a2, a3) {\n  return {\n    id: \"costCenterList\",\n    itemsPerPage: a1,\n    currentPage: a2,\n    totalItems: a3\n  };\n};\n\nfunction CostCentersComponent_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"h3\", 12)(3, \"span\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"div\", 15)(8, \"button\", 16);\n    i0.ɵɵelement(9, \"img\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 18)(11, \"div\", 19);\n    i0.ɵɵelement(12, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"div\", 21);\n    i0.ɵɵelementStart(14, \"div\", 19)(15, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_ng_container_0_div_1_Template_a_click_15_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.openCostCenterEditorModal());\n    });\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 23)(19, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_ng_container_0_div_1_Template_a_click_19_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.exportToExcelSheet());\n    });\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(22, \"div\", 24)(23, \"div\", 14)(24, \"div\", 25)(25, \"div\", 26)(26, \"div\", 27);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 28);\n    i0.ɵɵtext(30);\n    i0.ɵɵpipe(31, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 28);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 28);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 28);\n    i0.ɵɵtext(39);\n    i0.ɵɵpipe(40, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 29);\n    i0.ɵɵtext(42);\n    i0.ɵɵpipe(43, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, CostCentersComponent_ng_container_0_div_1_ng_container_44_Template, 58, 43, \"ng-container\", 30);\n    i0.ɵɵpipe(45, \"paginate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 31);\n    i0.ɵɵelement(47, \"div\", 32);\n    i0.ɵɵelementStart(48, \"div\", 33)(49, \"div\", 34);\n    i0.ɵɵtext(50);\n    i0.ɵɵpipe(51, \"translate\");\n    i0.ɵɵpipe(52, \"translate\");\n    i0.ɵɵpipe(53, \"lowercase\");\n    i0.ɵɵpipe(54, \"translate\");\n    i0.ɵɵpipe(55, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"pagination-controls\", 35);\n    i0.ɵɵlistener(\"pageChange\", function CostCentersComponent_ng_container_0_div_1_Template_pagination_controls_pageChange_56_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.handlePageChange($event));\n    });\n    i0.ɵɵelementEnd()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 21, \"MENU.COST_CENTER_LIST\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"transform\", \"-30px, 50.5px, 0px\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(16, 23, \"FORM.BUTTON.ADD_COST_CENTER_BUTTON\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(17, 25, \"FORM.BUTTON.ADD_COST_CENTER_BUTTON\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(20, 27, \"FORM.BUTTON.EXPORT_TO_EXCEL\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(21, 29, \"FORM.BUTTON.EXPORT_TO_EXCEL\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(28, 31, \"LIST.NAME\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(31, 33, \"LIST.CODE\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(34, 35, \"FORM.LABEL.SECTIONS\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(37, 37, \"LIST.DEPARTMENT_HEAD\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(40, 39, \"LIST.UPDATED_AT\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(43, 41, \"LIST.ACTION\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(45, 43, ctx_r8.costCenters, i0.ɵɵpureFunction3(56, _c2, ctx_r8.pagination.limit, ctx_r8.pagination.page, ctx_r8.totalRecords)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate7(\" \", i0.ɵɵpipeBind1(51, 46, \"COMMON.SHOWING\"), \" \", ctx_r8.pagination.page === 1 ? 1 : (ctx_r8.pagination.page - 1) * ctx_r8.pagination.limit + 1, \" \", i0.ɵɵpipeBind1(52, 48, \"COMMON.TO\"), \" \", ctx_r8.pagination.limit * ctx_r8.pagination.page <= ctx_r8.totalRecords ? ctx_r8.pagination.limit * ctx_r8.pagination.page : ctx_r8.totalRecords, \" \", i0.ɵɵpipeBind1(53, 50, i0.ɵɵpipeBind1(54, 52, \"COMMON.RECORD\")), \" \", i0.ɵɵpipeBind1(55, 54, \"COMMON.OF\"), \" \", ctx_r8.totalRecords, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"responsive\", true);\n  }\n}\n\nfunction CostCentersComponent_ng_container_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"app-empty-state\", 44);\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementStart(2, \"span\", 45)(3, \"a\", 46);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_ng_container_0_ng_template_2_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.openCostCenterEditorModal());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"span\", 47);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"message\", i0.ɵɵpipeBind1(1, 2, \"EMPTY_STATE.EMPTY_COST_CENTER_LIST\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(5, 4, \"FORM.LABEL.SETUP_COST_CENTER\"), \" \");\n  }\n}\n\nfunction CostCentersComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CostCentersComponent_ng_container_0_div_1_Template, 57, 60, \"div\", 8);\n    i0.ɵɵtemplate(2, CostCentersComponent_ng_container_0_ng_template_2_Template, 7, 6, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r9 = i0.ɵɵreference(3);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.costCenters.length > 0)(\"ngIfElse\", _r9);\n  }\n}\n\nfunction CostCentersComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-list-skeleton-loader\", 48);\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"loaderCount\", 4);\n  }\n}\n\nconst _c3 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction CostCentersComponent_div_5_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"input\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function CostCentersComponent_div_5_div_11_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.isMulti = $event);\n    })(\"click\", function CostCentersComponent_div_5_div_11_Template_input_click_2_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.changeAddOption(\"manual\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"label\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 59)(5, \"input\", 62);\n    i0.ɵɵlistener(\"ngModelChange\", function CostCentersComponent_div_5_div_11_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.isMulti = $event);\n    })(\"click\", function CostCentersComponent_div_5_div_11_Template_input_click_5_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.changeAddOption(\"import\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"label\", 63);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r29.isMulti)(\"ngModelOptions\", i0.ɵɵpureFunction0(6, _c3))(\"value\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r29.isMulti)(\"ngModelOptions\", i0.ɵɵpureFunction0(7, _c3))(\"value\", true);\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nconst _c4 = function (a0) {\n  return {\n    name: a0,\n    length: 4\n  };\n};\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind2(2, 1, \"FORM.VALIDATION.LENGTH_ERROR\", i0.ɵɵpureFunction1(6, _c4, i0.ɵɵpipeBind1(3, 4, \"FORM.LABEL.CODE\"))));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 54);\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_h5_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"h5\", 85);\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_48_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 21);\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_48_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_48_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.NAME_ALREADY_EXIST\"));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_48_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-input-error-message\", 84);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"errorMessage\", i0.ɵɵpipeBind1(2, 1, \"FORM.VALIDATION.REQUIRED_FIELD\"));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_ng_container_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0, 86);\n    i0.ɵɵtemplate(1, CostCentersComponent_div_5_ng_container_12_ng_container_48_div_1_Template, 1, 0, \"div\", 87);\n    i0.ɵɵelementStart(2, \"div\", 14)(3, \"div\", 67)(4, \"label\", 65);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 88);\n    i0.ɵɵlistener(\"input\", function CostCentersComponent_div_5_ng_container_12_ng_container_48_Template_input_input_7_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r51.sectionExist());\n    });\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵpipe(9, \"lowercase\");\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, CostCentersComponent_div_5_ng_container_12_ng_container_48_ng_container_11_Template, 3, 3, \"ng-container\", 56);\n    i0.ɵɵtemplate(12, CostCentersComponent_div_5_ng_container_12_ng_container_48_ng_container_12_Template, 3, 3, \"ng-container\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 67)(14, \"label\", 89);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 90);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵpipe(19, \"lowercase\");\n    i0.ɵɵpipe(20, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 91);\n    i0.ɵɵelement(22, \"label\", 92);\n    i0.ɵɵelementStart(23, \"app-ad-user-search\", 75);\n    i0.ɵɵlistener(\"userSelected\", function CostCentersComponent_div_5_ng_container_12_ng_container_48_Template_app_ad_user_search_userSelected_23_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const i_r46 = restoredCtx.index;\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r53.costCenterSectionHeadAdded($event, i_r46));\n    });\n    i0.ɵɵpipe(24, \"translate\");\n    i0.ɵɵpipe(25, \"lowercase\");\n    i0.ɵɵpipe(26, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, CostCentersComponent_div_5_ng_container_12_ng_container_48_ng_container_27_Template, 3, 3, \"ng-container\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 93);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_div_5_ng_container_12_ng_container_48_Template_div_click_28_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const i_r46 = restoredCtx.index;\n      const ctx_r54 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r54.removeSection(i_r46));\n    });\n    i0.ɵɵelement(29, \"img\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const section_r45 = ctx.$implicit;\n    const i_r46 = ctx.index;\n    const ctx_r44 = i0.ɵɵnextContext(3);\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_11_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r46);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r46);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(6, 15, \"FORM.LABEL.NAME\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(8, 17, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(9, 19, i0.ɵɵpipeBind1(10, 21, \"FORM.LABEL.NAME\")), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = section_r45.get(\"title\")) == null ? null : tmp_4_0.hasError(\"required\")) && (ctx_r44.isSubmitted || ((tmp_4_0 = section_r45.get(\"title\")) == null ? null : tmp_4_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = section_r45.get(\"title\")) == null ? null : tmp_5_0.hasError(\"alreadyExist\")) && (ctx_r44.isSubmitted || ((tmp_5_0 = section_r45.get(\"title\")) == null ? null : tmp_5_0.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(16, 23, \"FORM.LABEL.CODE\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(18, 25, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(19, 27, i0.ɵɵpipeBind1(20, 29, \"FORM.LABEL.CODE\")), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(24, 31, \"FORM.PLACEHOLDER.SEARCH\"), \" \", i0.ɵɵpipeBind1(25, 33, i0.ɵɵpipeBind1(26, 35, \"FORM.LABEL.USER\")), \"\");\n    i0.ɵɵproperty(\"isMultiSelect\", false)(\"defaultUsers\", ctx_r44.getSectionHeadUser(i_r46));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = section_r45.get(\"user\")) == null ? null : tmp_11_0.hasError(\"required\")) && (ctx_r44.isSubmitted || ((tmp_11_0 = section_r45.get(\"user\")) == null ? null : tmp_11_0.touched)));\n  }\n}\n\nfunction CostCentersComponent_div_5_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"div\", 64)(3, \"label\", 65);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 66);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵpipe(9, \"lowercase\");\n    i0.ɵɵpipe(10, \"translate\");\n    i0.ɵɵtemplate(11, CostCentersComponent_div_5_ng_container_12_ng_container_11_Template, 3, 3, \"ng-container\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 14)(13, \"div\", 67)(14, \"label\", 65);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵpipe(17, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 68);\n    i0.ɵɵpipe(19, \"translate\");\n    i0.ɵɵpipe(20, \"lowercase\");\n    i0.ɵɵpipe(21, \"translate\");\n    i0.ɵɵtemplate(22, CostCentersComponent_div_5_ng_container_12_ng_container_22_Template, 3, 3, \"ng-container\", 56);\n    i0.ɵɵtemplate(23, CostCentersComponent_div_5_ng_container_12_ng_container_23_Template, 4, 8, \"ng-container\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 67);\n    i0.ɵɵelement(25, \"label\", 69);\n    i0.ɵɵelementStart(26, \"select\", 70)(27, \"option\", 71);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"translate\");\n    i0.ɵɵpipe(30, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"option\", 72);\n    i0.ɵɵtext(32, \"No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"option\", 73);\n    i0.ɵɵtext(34, \"Yes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, CostCentersComponent_div_5_ng_container_12_ng_container_35_Template, 3, 3, \"ng-container\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 14)(37, \"div\", 67);\n    i0.ɵɵelement(38, \"label\", 74);\n    i0.ɵɵelementStart(39, \"app-ad-user-search\", 75);\n    i0.ɵɵlistener(\"userSelected\", function CostCentersComponent_div_5_ng_container_12_Template_app_ad_user_search_userSelected_39_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.costCenterHeadAdded($event, \"departmentHead\"));\n    });\n    i0.ɵɵpipe(40, \"translate\");\n    i0.ɵɵpipe(41, \"lowercase\");\n    i0.ɵɵpipe(42, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(43, CostCentersComponent_div_5_ng_container_12_ng_container_43_Template, 3, 3, \"ng-container\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"div\");\n    i0.ɵɵtemplate(45, CostCentersComponent_div_5_ng_container_12_div_45_Template, 1, 0, \"div\", 76);\n    i0.ɵɵelementStart(46, \"div\", 77);\n    i0.ɵɵtemplate(47, CostCentersComponent_div_5_ng_container_12_h5_47_Template, 1, 0, \"h5\", 78);\n    i0.ɵɵtemplate(48, CostCentersComponent_div_5_ng_container_12_ng_container_48_Template, 30, 37, \"ng-container\", 79);\n    i0.ɵɵelementStart(49, \"div\", 80)(50, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_div_5_ng_container_12_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r57.addNewSection());\n    });\n    i0.ɵɵelement(51, \"img\", 82);\n    i0.ɵɵelementStart(52, \"span\", 83);\n    i0.ɵɵtext(53);\n    i0.ɵɵpipe(54, \"translate\");\n    i0.ɵɵpipe(55, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(5, 24, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(6, 26, \"FORM.LABEL.NAME\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(8, 28, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(9, 30, i0.ɵɵpipeBind1(10, 32, \"FORM.LABEL.NAME\")), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isRequiredError(\"name\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(16, 34, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(17, 36, \"FORM.LABEL.COST_CENTER_CODE\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(19, 38, \"FORM.PLACEHOLDER.ENTER\"), \" \", i0.ɵɵpipeBind1(20, 40, i0.ɵɵpipeBind1(21, 42, \"FORM.LABEL.COST_CENTER_CODE\")), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isRequiredError(\"code\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isPatternError(\"code\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(29, 44, \"FORM.PLACEHOLDER.SELECT\"), \" \", i0.ɵɵpipeBind1(30, 46, \"LIST.COST_CENTER_OPERATING\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isRequiredError(\"operating\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate2(\"placeholder\", \"\", i0.ɵɵpipeBind1(40, 48, \"FORM.PLACEHOLDER.SEARCH\"), \" \", i0.ɵɵpipeBind1(41, 50, i0.ɵɵpipeBind1(42, 52, \"FORM.LABEL.DEPARTMENT_HEAD\")), \"\");\n    i0.ɵɵproperty(\"isMultiSelect\", false)(\"defaultUsers\", ctx_r30.departmentHead);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.isRequiredError(\"departmentHead\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.sections == null ? null : ctx_r30.sections.controls == null ? null : ctx_r30.sections.controls.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.sections == null ? null : ctx_r30.sections.controls == null ? null : ctx_r30.sections.controls.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r30.sections.controls);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind1(54, 54, \"FORM.BUTTON.ADD_BUTTON\"), \" \", i0.ɵɵpipeBind1(55, 56, \"FORM.LABEL.SECTION\"), \" \");\n  }\n}\n\nconst _c5 = function () {\n  return {\n    isDescriptionRequired: false\n  };\n};\n\nfunction CostCentersComponent_div_5_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"app-add-attachment\", 95);\n    i0.ɵɵlistener(\"bufferAttachment\", function CostCentersComponent_div_5_div_13_Template_app_add_attachment_bufferAttachment_1_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.addBufferAttachment($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function CostCentersComponent_div_5_div_13_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.exportBlankFormat());\n    });\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formatMessage\", \"COMMON.COST_CENTER_SECTION_FORMAT\")(\"isSubmitted\", ctx_r31.isSubmitted)(\"labelTitle\", \"FORM.LABEL.UPLOAD_COST_CENTER\")(\"data\", i0.ɵɵpureFunction0(10, _c5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(4, 6, \"FORM.BUTTON.EXPORT_FORMAT\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(5, 8, \"FORM.BUTTON.EXPORT_FORMAT\"));\n  }\n}\n\nfunction CostCentersComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 14)(2, \"div\", 50);\n    i0.ɵɵelement(3, \"label\", 51);\n    i0.ɵɵelementStart(4, \"div\", 52);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵelement(7, \"label\", 53);\n    i0.ɵɵelementStart(8, \"div\", 52);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(10, \"div\", 54);\n    i0.ɵɵtemplate(11, CostCentersComponent_div_5_div_11_Template, 7, 8, \"div\", 55);\n    i0.ɵɵtemplate(12, CostCentersComponent_div_5_ng_container_12_Template, 56, 58, \"ng-container\", 56);\n    i0.ɵɵtemplate(13, CostCentersComponent_div_5_div_13_Template, 6, 11, \"div\", 57);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.costCenterFormGroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.selectedCompanyCode.code, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.selectedCompanyCode.entityCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isEditMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isMulti);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isMulti);\n  }\n}\n\nfunction CostCentersComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-form-skeleton-loader\", 48);\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"loaderCount\", 2);\n  }\n}\n\nexport class CostCentersComponent {\n  constructor(cdr, spinnerService, companiesService, translateService, reportService, elementRef) {\n    this.cdr = cdr;\n    this.spinnerService = spinnerService;\n    this.companiesService = companiesService;\n    this.translateService = translateService;\n    this.reportService = reportService;\n    this.elementRef = elementRef;\n    this.loading = true;\n    this.pagination = {\n      limit: PAGINATION_DEFAULT_LIMIT,\n      offset: 0,\n      page: 1\n    };\n    this.costCenters = [];\n    this.isEditMode = false;\n    this.isSubmitted = false;\n    this.costCenterEditorModalTitle = this.translateService.instant('MENU.ADD_COST_CENTER');\n    this.modalDismissButtonLabel = this.translateService.instant('FORM.BUTTON.SAVE_BUTTON');\n    this.sectionHead = null;\n    this.departmentHead = null;\n    this.isMulti = false;\n    this.hideForm = true;\n    this.historyModalConfig = {\n      modalTitle: this.translateService.instant('FORM.BUTTON.COST_CENTER_HISTORY_BUTTON'),\n\n      hideCloseButton() {\n        return true;\n      },\n\n      hideDismissButton() {\n        return true;\n      },\n\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: false\n      }\n    };\n    this.companyHistory = [];\n    this.costCenterEditorModalConfig = {\n      modalTitle: this.costCenterEditorModalTitle,\n      dismissButtonLabel: this.modalDismissButtonLabel,\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),\n      onDismiss: () => {\n        this.formReset();\n        return true;\n      },\n      shouldClose: () => {\n        this.formReset();\n        return true;\n      },\n      shouldDismiss: () => {\n        this.isSubmitted = true;\n        this.cdr.detectChanges();\n\n        if (this.isMulti) {\n          this.uploadMultipleCostCenter();\n          return false;\n        }\n\n        if (!this.costCenterFormGroup.valid) {\n          return false;\n        } else {\n          this.cdr.detectChanges();\n\n          if (this.isEditMode) {\n            this.updateCostCenter();\n          } else {\n            this.createNewCostCenter();\n          }\n\n          return false;\n        }\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: true\n      }\n    };\n  }\n\n  ngOnInit() {\n    this.cdr.detectChanges();\n    this.initForm();\n  }\n\n  ngOnChanges() {\n    this.fetchCostCentersList();\n  }\n\n  toggleOpen() {\n    // Manually trigger the click event\n    const divElement = this.elementRef.nativeElement.querySelector('.separator');\n    divElement.click();\n  }\n\n  initForm() {\n    this.costCenterFormGroup = new FormGroup({\n      name: new FormControl('', [Validators.required]),\n      code: new FormControl('', [Validators.required]),\n      operating: new FormControl(true, [Validators.required]),\n      departmentHead: new FormControl('', [Validators.required]),\n      sectionHead: new FormArray([])\n    }); // sections: new FormArray([this.sectionControls]),\n\n    this.cdr.detectChanges();\n  }\n\n  sectionExist() {\n    var _a, _b;\n\n    (_b = (_a = this.sections) === null || _a === void 0 ? void 0 : _a.controls) === null || _b === void 0 ? void 0 : _b.forEach((section, index) => {\n      var _a;\n\n      const sectionControl = section.get('title');\n\n      if ((_a = sectionControl === null || sectionControl === void 0 ? void 0 : sectionControl.errors) === null || _a === void 0 ? void 0 : _a.alreadyExist) {\n        const errors = sectionControl.errors;\n        delete errors.alreadyExist;\n        sectionControl.setErrors(errors && Object.keys(errors).length > 0 ? errors : null);\n        this.cdr.detectChanges();\n      }\n\n      const value = sectionControl === null || sectionControl === void 0 ? void 0 : sectionControl.value;\n\n      if (value) {\n        // Check if the same title exists in the section FormArray\n        const isTitleDuplicate = this.sections.controls.filter((_, i) => i !== index) // Exclude the current section\n        .some(section => {\n          var _a, _b;\n\n          return ((_b = (_a = section.get('title')) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === value.toLowerCase();\n        });\n\n        if (isTitleDuplicate) {\n          sectionControl.setErrors({\n            alreadyExist: true\n          });\n          this.cdr.detectChanges();\n        }\n      }\n    });\n    this.cdr.detectChanges();\n  }\n\n  sectionCodeExist() {\n    var _a, _b;\n\n    (_b = (_a = this.sections) === null || _a === void 0 ? void 0 : _a.controls) === null || _b === void 0 ? void 0 : _b.forEach((section, index) => {\n      var _a;\n\n      const sectionControl = section.get('code');\n\n      if ((_a = sectionControl === null || sectionControl === void 0 ? void 0 : sectionControl.errors) === null || _a === void 0 ? void 0 : _a.alreadyExist) {\n        const errors = sectionControl.errors;\n        delete errors.alreadyExist;\n        sectionControl.setErrors(errors && Object.keys(errors).length > 0 ? errors : null);\n        this.cdr.detectChanges();\n      }\n\n      const value = sectionControl === null || sectionControl === void 0 ? void 0 : sectionControl.value;\n\n      if (value) {\n        // Check if the same code exists in the section FormArray\n        const isCodeDuplicate = this.sections.controls.filter((_, i) => i !== index) // Exclude the current section\n        .some(section => {\n          var _a, _b;\n\n          return ((_b = (_a = section.get('code')) === null || _a === void 0 ? void 0 : _a.value) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === value.toLowerCase();\n        });\n\n        if (isCodeDuplicate) {\n          sectionControl.setErrors({\n            alreadyExist: true\n          });\n          this.cdr.detectChanges();\n        }\n      }\n    });\n    this.cdr.detectChanges();\n  }\n\n  get sectionControls() {\n    return new FormGroup({\n      title: new FormControl('', [Validators.required]),\n      code: new FormControl('', []),\n      user: new FormControl('', [Validators.required])\n    });\n  }\n\n  getSectionControlsDefault(value) {\n    return new FormGroup({\n      title: new FormControl(value.title, [Validators.required]),\n      code: new FormControl(value.code, []),\n      user: new FormControl(value.user, [Validators.required])\n    });\n  }\n\n  removeSection(index) {\n    this.sections.removeAt(index);\n    this.cdr.detectChanges();\n    this.sectionExist();\n  }\n\n  addNewSection() {\n    this.sections.push(this.sectionControls);\n    this.cdr.detectChanges();\n  }\n\n  formReset() {\n    this.costCenterFormGroup.reset();\n    const sectionHeadArray = this.costCenterFormGroup.get('sectionHead');\n    sectionHeadArray.clear();\n    this.sectionHead = null;\n    this.departmentHead = null;\n    this.cdr.detectChanges();\n    console.log('Reset');\n    console.log(this.costCenterFormGroup);\n  }\n\n  get sections() {\n    var _a;\n\n    return (_a = this.costCenterFormGroup) === null || _a === void 0 ? void 0 : _a.controls[\"sectionHead\"];\n  }\n\n  isPatternError(FContorl) {\n    var _a, _b;\n\n    return ((_a = this.costCenterFormGroup.get(FContorl)) === null || _a === void 0 ? void 0 : _a.hasError('pattern')) && (((_b = this.costCenterFormGroup.get(FContorl)) === null || _b === void 0 ? void 0 : _b.touched) || this.isSubmitted);\n  }\n\n  fetchCostCentersList() {\n    if (this.selectedCompanyCode) {\n      this.loading = true;\n      this.spinnerService.startSpinner();\n      this.companiesService.getCostCentersByCompanyId(this.selectedCompanyCode.id, this.pagination).pipe(finalize(() => {\n        this.spinnerService.stopSpinner();\n        this.loading = false;\n        this.cdr.detectChanges();\n      })).subscribe({\n        next: response => {\n          this.costCenters = response.records;\n          this.totalRecords = response.total;\n        },\n        error: () => {\n          this.costCenters = [];\n        }\n      });\n    }\n  }\n\n  handlePageChange(event) {\n    this.spinnerService.startSpinner();\n    this.loading = true;\n    this.pagination.offset = +event - 1;\n    this.pagination.page = event;\n    this.fetchCostCentersList();\n  }\n\n  isRequiredError(FContorl) {\n    var _a, _b;\n\n    return ((_a = this.costCenterFormGroup.get(FContorl)) === null || _a === void 0 ? void 0 : _a.hasError('required')) && (((_b = this.costCenterFormGroup.get(FContorl)) === null || _b === void 0 ? void 0 : _b.touched) || this.isSubmitted);\n  }\n\n  deleteCostCenter(costCenterId) {\n    Swal.fire({\n      title: this.translateService.instant('SWAL.CONFIRMATION'),\n      text: this.translateService.instant('SWAL.COST_CENTER_DELETE_CONFIRM'),\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#3085d6',\n      cancelButtonColor: '#d33',\n      confirmButtonText: this.translateService.instant('SWAL.DELETE_BUTTON')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.spinnerService.startSpinner();\n        this.companiesService.deleteCostCenterById(costCenterId).pipe(finalize(() => {\n          this.spinnerService.stopSpinner();\n        })).subscribe({\n          next: () => {\n            Swal.fire(this.translateService.instant('SWAL.COST_CENTER_DELETE_TITLE'), this.translateService.instant('SWAL.COST_CENTER_DELETE_SUCCESS'), 'success');\n            this.fetchCostCentersList();\n          },\n          error: error => {\n            Swal.fire({\n              icon: 'error',\n              title: this.translateService.instant('SWAL.OOPS'),\n              text: error.message\n            });\n          }\n        });\n      }\n    });\n  }\n  /**\r\n   * Open modal in edit or create new cost center mode.\r\n   * @param isEditMode\r\n   * @param editCostCenterId\r\n   * @returns\r\n   */\n\n\n  openCostCenterEditorModal(isEditMode = false, editCostCenterId) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.departmentHead = null;\n      _this.sectionHead = null;\n      _this.isSubmitted = false;\n      _this.isEditMode = isEditMode;\n      _this.isMulti = false;\n      _this.hideForm = true;\n\n      _this.cdr.detectChanges();\n\n      _this.costCenterEditorModalConfig.modalTitle = isEditMode ? _this.translateService.instant('MENU.UPDATE_COST_CENTER') : _this.translateService.instant('MENU.ADD_COST_CENTER');\n      _this.costCenterEditorModalConfig.dismissButtonLabel = isEditMode ? _this.translateService.instant('FORM.BUTTON.UPDATE_BUTTON') : _this.translateService.instant('FORM.BUTTON.ADD_BUTTON');\n\n      if (isEditMode && editCostCenterId) {\n        const currentCostCenter = _this.costCenters.find(costCenter => costCenter.id === editCostCenterId);\n\n        if (currentCostCenter) {\n          const {\n            name,\n            code,\n            departmentHead,\n            operating,\n            sectionHead = []\n          } = currentCostCenter;\n\n          _this.costCenterFormGroup.patchValue({\n            name,\n            code,\n            departmentHead,\n            operating\n          });\n\n          console.log(sectionHead);\n\n          if (sectionHead && (sectionHead === null || sectionHead === void 0 ? void 0 : sectionHead.length)) {\n            sectionHead === null || sectionHead === void 0 ? void 0 : sectionHead.forEach(section => {\n              _this.sections.push(_this.getSectionControlsDefault(section));\n\n              _this.cdr.detectChanges();\n            });\n          }\n\n          _this.sectionExist();\n\n          _this.departmentHead = departmentHead;\n        }\n\n        _this.editCostCenterId = editCostCenterId;\n      } else {\n        _this.formReset();\n      }\n\n      _this.hideForm = false;\n\n      _this.cdr.detectChanges();\n\n      return yield _this.costCenterEditorModal.open();\n    })();\n  }\n\n  costCenterHeadAdded(userData, controllerName) {\n    if (userData) {\n      this.costCenterFormGroup.patchValue({\n        [controllerName]: userData\n      });\n    } else {\n      this.costCenterFormGroup.patchValue({\n        [controllerName]: ''\n      });\n    }\n  }\n\n  costCenterSectionHeadAdded(userData, index) {\n    this.sections.at(index).patchValue({\n      user: userData ? userData : ''\n    });\n    this.cdr.detectChanges();\n  }\n\n  getSectionHeadUser(index) {\n    var _a;\n\n    return (_a = this.sections.at(index).getRawValue()) === null || _a === void 0 ? void 0 : _a.user;\n  }\n\n  getCostCenterRequestPayload() {\n    var _a, _b, _c, _d, _e, _f;\n\n    const requestPayload = {\n      companyCodeId: +this.selectedCompanyCode.id,\n      code: (_a = this.costCenterFormGroup.get('code')) === null || _a === void 0 ? void 0 : _a.value,\n      name: (_b = this.costCenterFormGroup.get('name')) === null || _b === void 0 ? void 0 : _b.value,\n      operating: ((_c = this.costCenterFormGroup.get('operating')) === null || _c === void 0 ? void 0 : _c.value) == 'true' || ((_d = this.costCenterFormGroup.get('operating')) === null || _d === void 0 ? void 0 : _d.value) == true,\n      departmentHead: (_e = this.costCenterFormGroup.get('departmentHead')) === null || _e === void 0 ? void 0 : _e.value,\n      sectionHead: (_f = this.costCenterFormGroup.get('sectionHead')) === null || _f === void 0 ? void 0 : _f.value\n    };\n    return requestPayload;\n  }\n  /**\r\n   * Create new cost center for the company.\r\n   */\n\n\n  createNewCostCenter() {\n    this.isSubmitted = true;\n    this.cdr.detectChanges();\n    this.sectionExist();\n    console.log(this.costCenterFormGroup);\n\n    if (this.costCenterFormGroup.valid) {\n      this.spinnerService.startSpinner();\n      this.companiesService.createCostCenter(this.getCostCenterRequestPayload()).pipe(finalize(() => {\n        this.spinnerService.stopSpinner();\n      })).subscribe({\n        next: () => {\n          this.formReset();\n          Swal.fire(this.translateService.instant('SWAL.SUCCESS'), this.translateService.instant('SWAL.ADD_COST_CENTER_CODE_SUCCESS'), 'success');\n          this.costCenterEditorModal.close();\n          this.fetchCostCentersList();\n          this.isSubmitted = false;\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          Swal.fire({\n            icon: 'error',\n            title: this.translateService.instant('SWAL.ADD_COST_CENTER_CODE_ERROR'),\n            text: error.message\n          });\n        }\n      });\n    }\n  }\n  /**\r\n   * Update the cost center details.\r\n   */\n\n\n  updateCostCenter() {\n    var _a, _b;\n\n    this.isSubmitted = true;\n    this.cdr.detectChanges();\n    this.sectionExist();\n    console.log(this.costCenterFormGroup);\n\n    if (((_a = this.costCenterFormGroup) === null || _a === void 0 ? void 0 : _a.valid) && ((_b = this.selectedCompanyCode) === null || _b === void 0 ? void 0 : _b.id)) {\n      this.spinnerService.startSpinner();\n      this.companiesService.updateCostCenter(Object.assign({\n        id: +this.editCostCenterId\n      }, this.getCostCenterRequestPayload())).pipe(finalize(() => {\n        this.spinnerService.stopSpinner();\n      })).subscribe({\n        next: () => {\n          Swal.fire(this.translateService.instant('SWAL.SUCCESS'), this.translateService.instant('SWAL.UPDATE_COST_CENTER_CODE_SUCCESS'), 'success');\n          this.costCenterEditorModal.close();\n          this.fetchCostCentersList();\n          this.isSubmitted = false;\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          Swal.fire({\n            icon: 'error',\n            title: this.translateService.instant('SWAL.UPDATE_COST_CENTER_CODE_ERROR'),\n            text: error.message\n          });\n        }\n      });\n    }\n  }\n\n  exportToExcelSheet() {\n    this.toggleOpen();\n    this.reportService.downloadCostCenterExcelReport(toNumber(this.selectedCompanyCode.id));\n  }\n\n  exportBlankFormat() {\n    this.reportService.downloadCostCenterExcelReport(toNumber(0));\n  }\n\n  addBufferAttachment(bufferData) {\n    this.bufferData = bufferData;\n    this.cdr.detectChanges();\n  }\n\n  changeAddOption(type) {\n    this.isSubmitted = false;\n\n    if (!this.isMulti) {\n      this.initForm();\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  uploadMultipleCostCenter() {\n    if (this.selectedCompanyCode.id && this.bufferData) {\n      this.spinnerService.startSpinner();\n      this.companiesService.importCostCenter({\n        companyCodeId: toNumber(this.selectedCompanyCode.id),\n        bufferData: this.bufferData\n      }).subscribe({\n        next: response => {\n          console.log(response);\n          this.costCenterEditorModal.close();\n          Swal.fire(this.translateService.instant('SWAL.SUCCESS'), this.translateService.instant('SWAL.IMPORT_COST_CENTER_SUCCESS'), 'success');\n          this.fetchCostCentersList();\n        },\n        error: err => {\n          console.log(err);\n          Swal.fire({\n            icon: 'error',\n            title: this.translateService.instant('SWAL.OOPS'),\n            html: err.message\n          });\n          this.spinnerService.stopSpinner();\n        }\n      });\n    }\n  }\n\n  openHistoryModel(costCenterId) {\n    this.spinnerService.startSpinner();\n    this.companiesService.getCostCenterHistory(costCenterId).subscribe({\n      next: response => {\n        this.companyHistory = response;\n        this.cdr.detectChanges();\n        this.historyModalComponent.open();\n        this.spinnerService.stopSpinner();\n      },\n      error: err => {\n        console.log(err);\n        this.companyHistory = [];\n        this.cdr.detectChanges();\n        this.historyModalComponent.open();\n        this.spinnerService.stopSpinner();\n        Swal.fire({\n          icon: 'error',\n          title: this.translateService.instant('SWAL.ERROR'),\n          text: this.translateService.instant('SWAL.ERROR_HISTORY_FETCH')\n        });\n      }\n    });\n  }\n\n}\n\nCostCentersComponent.ɵfac = function CostCentersComponent_Factory(t) {\n  return new (t || CostCentersComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.SpinnerService), i0.ɵɵdirectiveInject(i2.CompaniesService), i0.ɵɵdirectiveInject(i3.TranslateService), i0.ɵɵdirectiveInject(i4.ReportService), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nCostCentersComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CostCentersComponent,\n  selectors: [[\"app-cost-centers\"]],\n  viewQuery: function CostCentersComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.costCenterEditorModal = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.historyModalComponent = _t.first);\n    }\n  },\n  inputs: {\n    selectedCompanyCode: \"selectedCompanyCode\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 13,\n  vars: 9,\n  consts: [[4, \"ngIf\", \"ngIfElse\"], [\"loadingPage\", \"\"], [3, \"modalConfig\"], [\"costCenterEditorModal\", \"\"], [\"class\", \"w-100 px-5 bg-body rounded\", 3, \"formGroup\", 4, \"ngIf\"], [\"historyModal\", \"\"], [1, \"w-100\", \"px-1\", \"bg-body\", \"rounded\", \"py-1\"], [3, \"title\", \"historyLogs\"], [\"class\", \"card mb-5 mb-xl-8\", 4, \"ngIf\", \"ngIfElse\"], [\"noDataMessage\", \"\"], [1, \"card\", \"mb-5\", \"mb-xl-8\"], [1, \"card-header\", \"border-0\", \"pt-5\"], [1, \"card-title\", \"align-items-start\", \"flex-column\"], [1, \"card-label\", \"fw-bolder\", \"fs-3\", \"mb-1\"], [1, \"row\"], [1, \"d-flex\", \"justify-content-end\"], [\"appToggleProfileMenu\", \"\", \"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-color-primary\", \"btn-active-light-primary\", 3, \"transform\"], [\"width\", \"45px\", \"src\", \"./assets/media/svg/icons/dpw-icons/menu.png\", \"alt\", \"next\"], [1, \"fs-6\", \"fw-bold\", \"menu\", \"menu-column\", \"menu-gray-600\", \"menu-rounded\", \"menu-state-bg\", \"menu-state-primary\", \"menu-sub\", \"menu-sub-dropdown\", \"py-4\", \"w-275px\"], [1, \"menu-item\", \"px-3\"], [\"translate\", \"MENU.QUICK_ACTION\", 1, \"menu-content\", \"fs-6\", \"text-dark\", \"fw-bolder\", \"px-3\", \"py-4\"], [1, \"separator\", \"mb-3\", \"opacity-75\"], [1, \"menu-link\", \"px-3\", \"cursor-pointer\", 3, \"title\", \"translate\", \"click\"], [1, \"menu-item\", \"px-3\", \"mb-2\"], [1, \"card-body\", \"py-3\"], [1, \"col-12\", \"align-middle\"], [1, \"row\", \"solid-hr\", \"fw-bolder\", \"text-muted\", \"font-size-10\", \"py-3\", \"d-md-flex\", \"d-none\", \"my-1\", \"mx-2\", \"bg-light\"], [1, \"col-md-3\"], [1, \"col-md-2\"], [1, \"col-md-1\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-md-12\", \"text-end\"], [1, \"separator\", \"separator-dashed\", \"separator-border-1\", \"my-5\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"fw-bold\"], [\"id\", \"costCenterList\", \"previousLabel\", \"Prev\", \"nextLabel\", \"Next\", 3, \"responsive\", \"pageChange\"], [1, \"row\", \"font-size-14\", \"my-2\", \"my-md-1\", \"py-3\", \"card-body\", \"p-5\", \"solid-hr\", \"note-box-sm\", \"align-items-center\"], [1, \"d-md-none\", \"mt-3\"], [1, \"text-none\"], [1, \"d-value\", \"text-dark\", \"fw-bolder\", \"text-hover-primary\", \"fs-8\"], [\"class\", \"badge badge-success ml-2 cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"col-md-1\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"badge\", \"badge-success\", \"ml-2\", \"cursor-pointer\", 3, \"click\"], [3, \"message\"], [1, \"action\", \"position-relative\", \"d-inline-block\", \"text-danger\"], [1, \"text-danger\", \"opacity-75-hover\", \"cursor-pointer\", 3, \"click\"], [1, \"position-absolute\", \"opacity-15\", \"bottom-0\", \"start-0\", \"border-4\", \"border-danger\", \"border-bottom\", \"w-100\"], [3, \"loaderCount\"], [1, \"w-100\", \"px-5\", \"bg-body\", \"rounded\", 3, \"formGroup\"], [1, \"col-lg-6\", \"col-md-6\", \"col-6\", \"mb-6\", \"mb-lg-5\"], [\"translate\", \"FORM.LABEL.COMPANY_FUSSION_NUMBER\", 1, \"fw-bold\", \"text-muted\"], [1, \"h4\", \"text-gray-800\"], [\"translate\", \"LIST.ENTITY_CODE\", 1, \"fw-bold\", \"text-muted\"], [1, \"separator\", \"my-2\"], [\"class\", \"row m-3\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"row\", \"m-3\"], [1, \"form-check\", \"form-check-inline\", \"col\"], [\"name\", \"addAnalysisCode\", \"type\", \"radio\", \"id\", \"singleAddition\", 1, \"form-check-input\", \"success\", 3, \"ngModel\", \"ngModelOptions\", \"value\", \"ngModelChange\", \"click\"], [\"for\", \"singleAddition\", \"translate\", \"COMMON.ADD_MANUALLY\", 1, \"form-check-label\", \"fw-bold\"], [\"name\", \"addAnalysisCode\", \"type\", \"radio\", \"id\", \"multiAddition\", 1, \"form-check-input\", \"danger\", 3, \"ngModel\", \"ngModelOptions\", \"value\", \"ngModelChange\", \"click\"], [\"for\", \"multiAddition\", \"translate\", \"COMMON.IMPORT\", 1, \"form-check-label\", \"fw-bold\"], [1, \"col-md-12\", \"mb-5\"], [1, \"form-label\", \"required\"], [\"name\", \"title\", \"formControlName\", \"name\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", 3, \"placeholder\"], [1, \"col-md-6\", \"mb-5\"], [\"name\", \"title\", \"pattern\", \"[a-zA-Z0-9]{4}\", \"formControlName\", \"code\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", 3, \"placeholder\"], [\"translate\", \"FORM.LABEL.IS_COST_CENTER_OPERATING\", 1, \"form-label\", \"required\"], [\"name\", \"operating\", \"formControlName\", \"operating\", 1, \"form-select\", \"form-select-lg\", \"form-select-solid\"], [\"value\", \"null\"], [\"value\", \"false\"], [\"value\", \"true\"], [\"translate\", \"FORM.LABEL.DEPARTMENT_HEAD\", 1, \"form-label\", \"required\"], [3, \"placeholder\", \"isMultiSelect\", \"defaultUsers\", \"userSelected\"], [\"class\", \"separator my-2\", 4, \"ngIf\"], [\"formArrayName\", \"sectionHead\", 1, \"row\"], [\"translate\", \"FORM.LABEL.SECTIONS\", \"class\", \"fw-bold d-flex align-items-center\", 4, \"ngIf\"], [3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"pt-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-primary\", \"ps-4\", \"pe-4\", \"py-1\", 3, \"click\"], [\"width\", \"12px\", \"src\", \"./assets/media/svg/icons/dpw-icons/plus.png\", \"alt\", \"Next\"], [1, \"px-1\", \"indicator-label\"], [3, \"errorMessage\"], [\"translate\", \"FORM.LABEL.SECTIONS\", 1, \"fw-bold\", \"d-flex\", \"align-items-center\"], [3, \"formGroupName\"], [\"class\", \"separator mb-3 opacity-75\", 4, \"ngIf\"], [\"name\", \"title\", \"formControlName\", \"title\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", 3, \"placeholder\", \"input\"], [1, \"form-label\"], [\"name\", \"code\", \"formControlName\", \"code\", 1, \"form-control\", \"form-control-lg\", \"form-control-solid\", 3, \"placeholder\"], [1, \"col-md-11\", \"mb-5\"], [\"translate\", \"FORM.LABEL.USER\", 1, \"form-label\", \"required\"], [1, \"col-md-1\", \"cursor-pointer\", \"mt-5\", \"mb-5\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [\"width\", \"32px\", \"src\", \"./assets/media/svg/icons/dpw-icons/cross2.png\", \"alt\", \"Remove\"], [3, \"formatMessage\", \"isSubmitted\", \"labelTitle\", \"data\", \"bufferAttachment\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-primary\", \"mx-2\", 3, \"title\", \"translate\", \"click\"]],\n  template: function CostCentersComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CostCentersComponent_ng_container_0_Template, 4, 2, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, CostCentersComponent_ng_template_1_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(3, \"app-modal\", 2, 3);\n      i0.ɵɵtemplate(5, CostCentersComponent_div_5_Template, 14, 6, \"div\", 4);\n      i0.ɵɵtemplate(6, CostCentersComponent_ng_template_6_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"app-modal\", 2, 5)(10, \"div\", 6);\n      i0.ɵɵelement(11, \"app-history-logs\", 7);\n      i0.ɵɵpipe(12, \"translate\");\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading)(\"ngIfElse\", _r1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"modalConfig\", ctx.costCenterEditorModalConfig);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.hideForm);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"modalConfig\", ctx.historyModalConfig);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"title\", i0.ɵɵpipeBind1(12, 7, \"FORM.BUTTON.COMPANY_HISTORY_BUTTON\"))(\"historyLogs\", ctx.companyHistory);\n    }\n  },\n  dependencies: [i5.NgForOf, i5.NgIf, i6.InputErrorMessageComponent, i7.AdUserSearchComponent, i8.ListSkeletonLoaderComponent, i9.FormSkeletonLoaderComponent, i10.EmptyStateComponent, i11.HistoryLogsComponent, i12.AddAttachmentComponent, i3.TranslateDirective, i13.ModalComponent, i14.PaginationControlsComponent, i15.NgSelectOption, i15.ɵNgSelectMultipleOption, i15.DefaultValueAccessor, i15.SelectControlValueAccessor, i15.RadioControlValueAccessor, i15.NgControlStatus, i15.NgControlStatusGroup, i15.PatternValidator, i15.NgModel, i15.FormGroupDirective, i15.FormControlName, i15.FormGroupName, i15.FormArrayName, i16.ToggleProfileMenuDirective, i5.LowerCasePipe, i5.DatePipe, i3.TranslatePipe, i14.PaginatePipe],\n  styles: [\".form-check[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  position: relative;\\n  top: -2px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNvc3QtY2VudGVycy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGtCQUFBO0VBQ0EsU0FBQTtBQUNKIiwiZmlsZSI6ImNvc3QtY2VudGVycy5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi5mb3JtLWNoZWNrIGxhYmVsIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIHRvcDogLTJweDtcclxufSJdfQ== */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,SAAT,EAAoBC,WAApB,EAAiCC,SAAjC,EAA4CC,UAA5C,QAA8D,gBAA9D;AACA,SAAwBC,wBAAxB,QAAwD,iBAAxD;AAQA,SAASC,QAAT,QAAyB,QAAzB;AACA,SAAuBC,QAAvB,QAAuC,MAAvC;AACA,OAAOC,IAAP,MAAiB,aAAjB;;;;;;;;;;;;;;;;;;;;;;;;;ICiJoBC;IACEA;MAAAA;MAAA;MAAA;MAAA,OAASA,iDAA0B,IAA1B,EAA8BC,iBAA9B,EAAT;IAAuD,CAAvD;IAIAD;IACFA;;;;;IADEA;IAAAA;;;;;;;;IAxDZA;IAaEA,gCAEC,CAFD,EAEC,KAFD,EAEC,EAFD,EAEC,CAFD,EAEC,KAFD,EAEC,EAFD;IAKMA;;IACFA;IACAA,gCAAuB,CAAvB,EAAuB,GAAvB,EAAuB,EAAvB;IAIIA;IACFA;IAIJA,gCAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB;IAEIA;;IACFA;IACAA;IACEA;IACFA;IAGFA,iCAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB;IAEIA;;IACFA;IACAA,iCAAuB,EAAvB,EAAuB,KAAvB,EAAuB,EAAvB,EAAuB,EAAvB,EAAuB,MAAvB;IAGMA;IAKFA;IACAA;IAOFA;IAIJA,iCAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB;IAEIA;;IACFA;IACAA;IACEA;IACFA;IAGFA,iCAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB;IAEIA;;IACFA;IACAA;IACEA;;IACFA;IAGFA,iCAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB,EAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB,EAAsB,EAAtB,EAAsB,QAAtB,EAAsB,EAAtB;IASQA;IAKFA;IACAA,kCAEC,EAFD,EAEC,KAFD,EAEC,EAFD;IAIIA;IAIFA;IAEAA;IAEAA,iCAA4B,EAA5B,EAA4B,GAA5B,EAA4B,EAA5B;IAGIA;MAAA;MAAA;MAAA;MAAA,OAC+BA,iDAClD,IADkD,EAC9CC,iBAD8C,EAD/B;IAEC,CAFD;;;IAQFD;IAGFA,iCAA4B,EAA5B,EAA4B,GAA5B,EAA4B,EAA5B;IAGIA;MAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;;;IAMFA;IAGFA,iCAA4B,EAA5B,EAA4B,GAA5B,EAA4B,EAA5B;IAGIA;MAAA;MAAA;MAAA;MAAA,OAASA,2DAAT;IAAwC,CAAxC;;;IAMFA;IAqBdA;;;;;IAjJQA;IAAAA;IAMEA;IAAAA;IAOFA;IAAAA;IAGAA;IAAAA;IAMAA;IAAAA;IAKIA;IAAAA;IAQCA;IAAAA;IAWLA;IAAAA;IAGAA;IAAAA;IAMAA;IAAAA;IAGAA;IAAAA;IASIA;IAAAA;IAwBIA;IAAAA;IAIAA;IAUAA;IAAAA;IAEAA;IAUAA;IAAAA;IAEAA;;;;;;;;;;;;;;;;;IAlPxBA,gCAGC,CAHD,EAGC,KAHD,EAGC,EAHD,EAGC,CAHD,EAGC,IAHD,EAGC,EAHD,EAGC,CAHD,EAGC,MAHD,EAGC,EAHD;IAOmDA;;IAE3CA;IAGJA,gCAAiB,CAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,CAAjB,EAAiB,QAAjB,EAAiB,EAAjB;IAQMA;IAKFA;IACAA,kCAEC,EAFD,EAEC,KAFD,EAEC,EAFD;IAIIA;IAIFA;IAEAA;IAEAA,iCAA4B,EAA5B,EAA4B,GAA5B,EAA4B,EAA5B;IAGIA;MAAAA;MAAA;MAAA,OAASA,mDAAT;IAAoC,CAApC;;;IAMFA;IAGFA,iCAAiC,EAAjC,EAAiC,GAAjC,EAAiC,EAAjC;IAGIA;MAAAA;MAAA;MAAA,OAASA,4CAAT;IAA6B,CAA7B;;;IAIFA;IAuBVA,iCAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B,EAA4B,EAA5B,EAA4B,KAA5B,EAA4B,EAA5B;IAOUA;;IACFA;IACAA;IACEA;;IACFA;IACAA;IACEA;;IACFA;IACAA;IACEA;;IACFA;IACAA;IACEA;;IACFA;IACAA;IACEA;;IACFA;IAEFA;;IAoKFA;IACAA;IACEA;IAEAA,iCAA4C,EAA5C,EAA4C,KAA5C,EAA4C,EAA5C;IAEIA;;;;;;IAcFA;IAEAA;IAKEA;MAAAA;MAAA;MAAA,OAAcA,gDAAd;IAAsC,CAAtC;IACDA;;;;;IAlSwCA;IAAAA;IASzCA;IAAAA;IAwBIA;IAAAA;IAGAA;IASAA;IAAAA;IAGAA;IAgCFA;IAAAA;IAGAA;IAAAA;IAGAA;IAAAA;IAGAA;IAAAA;IAGAA;IAAAA;IAGAA;IAAAA;IAK8BA;IAAAA;IAwK9BA;IAAAA;IAoBAA;IAAAA;;;;;;;;IAWVA;;IAGEA,iCAAkE,CAAlE,EAAkE,GAAlE,EAAkE,EAAlE;IAGIA;MAAAA;MAAA;MAAA,OAASA,mDAAT;IAAoC,CAApC;IAEAA;;IACFA;IACAA;IAIFA;;;;IAbAA;IAOIA;IAAAA;;;;;;IA3TVA;IACEA;IAiTAA;IAkBFA;;;;;;;IAlUKA;IAAAA,qDAA8B,UAA9B,EAA8BE,GAA9B;;;;;;IAqUHF;;;;IAA0BA;;;;;;;;;;;;;;IA4BxBA,gCAAyC,CAAzC,EAAyC,KAAzC,EAAyC,EAAzC,EAAyC,CAAzC,EAAyC,OAAzC,EAAyC,EAAzC;IAGMA;MAAAA;MAAA;MAAA;IAAA,GAAqB,OAArB,EAAqB;MAAAA;MAAA;MAAA,OACZA,uCAAgB,QAAhB,EADY;IACa,CADlC;IADFA;IAUAA;IAKFA;IACAA,gCAA8C,CAA9C,EAA8C,OAA9C,EAA8C,EAA9C;IAEIA;MAAAA;MAAA;MAAA;IAAA,GAAqB,OAArB,EAAqB;MAAAA;MAAA;MAAA,OACZA,uCAAgB,QAAhB,EADY;IACa,CADlC;IADFA;IAUAA;IAKFA;;;;;IA/BIA;IAAAA,0CAAqB,gBAArB,EAAqBA,0BAArB,EAAqB,OAArB,EAAqB,KAArB;IAiBAA;IAAAA,0CAAqB,gBAArB,EAAqBA,0BAArB,EAAqB,OAArB,EAAqB,IAArB;;;;;;IAgCAA;IACEA;;IAIFA;;;;IAHIA;IAAAA;;;;;;IAqBJA;IACEA;;IAIFA;;;;IAHIA;IAAAA;;;;;;;;;;;;;IAIJA;IACEA;;;IAQFA;;;;IAPIA;IAAAA;;;;;;IA2BJA;IACEA;;IAIFA;;;;IAHIA;IAAAA;;;;;;IAsBJA;IACEA;;IAIFA;;;;IAHIA;IAAAA;;;;;;IAuBNA;;;;;;IAGEA;;;;;;IASEA;;;;;;IAkBIA;IAMEA;;IAMFA;;;;IALIA;IAAAA;;;;;;IAOJA;IAMEA;;IAMFA;;;;IALIA;IAAAA;;;;;;IAqEJA;IAMEA;;IAMFA;;;;IALIA;IAAAA;;;;;;;;IAvHVA;IAIEA;IAIAA,gCAAiB,CAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,CAAjB,EAAiB,OAAjB,EAAiB,EAAjB;IAGMA;;IACFA;IACAA;IACEA;MAAAA;MAAA;MAAA,OAASA,sCAAT;IAAuB,CAAvB;;;;IADFA;IASAA;IAcAA;IAaFA;IAGAA,iCAA2B,EAA3B,EAA2B,OAA3B,EAA2B,EAA3B;IAEIA;;IACFA;IACAA;;;;IAyCFA;IAGAA;IACEA;IAEAA;IAKEA;MAAA;MAAA;MAAA;MAAA,OAAgBA,iEAAhB;IAAqD,CAArD;;;;IAGFA;IACAA;IAaFA;IACAA;IACEA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAT;IAAyB,CAAzB;IAGAA;IAKFA;IAEJA;;;;;;;;;;IAvIEA;IAEMA;IAAAA;IAOAA;IAAAA;IAKAA;IAAAA;IAOCA;IAAAA;IAcAA;IAAAA;IAiBDA;IAAAA;IAIAA;IAAAA;IA8CAA;IAAAA;IAGAA,sCAAuB,cAAvB,EAAuBG,iCAAvB;IAMCH;IAAAA;;;;;;;;IAnPfA;IACEA,gCAAiB,CAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,CAAjB,EAAiB,OAAjB,EAAiB,EAAjB;IAGMA;;;IAEFA;IACAA;;;;IAQAA;IAMFA;IAEFA,iCAAiB,EAAjB,EAAiB,KAAjB,EAAiB,EAAjB,EAAiB,EAAjB,EAAiB,OAAjB,EAAiB,EAAjB;IAGMA;;;IAEFA;IACAA;;;;IASAA;IAMAA;IAUFA;IACAA;IACEA;IAKAA,oCAIC,EAJD,EAIC,QAJD,EAIC,EAJD;IAMIA;;;IAEFA;IACAA;IAAsBA;IAAEA;IACxBA;IAAqBA;IAAGA;IAE1BA;IAMFA;IAEFA,iCAAiB,EAAjB,EAAiB,KAAjB,EAAiB,EAAjB;IAEIA;IAKAA;IAKEA;MAAAA;MAAA;MAAA,OAAgBA,mDAA4B,gBAA5B,EAAhB;IAA6D,CAA7D;;;;IAGFA;IACAA;IAMFA;IAiBAA;IAEAA;IAEAA;IACEA;IAKAA;IA0IAA,iCAA6C,EAA7C,EAA6C,QAA7C,EAA6C,EAA7C;IAEIA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAwB,CAAxB;IAIAA;IAKAA;IACEA;;;IAEFA;IAKVA;;;;;IA3RQA;IAAAA;IAKAA;IAAAA;IAMaA;IAAAA;IAWbA;IAAAA;IAKAA;IAAAA;IAOaA;IAAAA;IAMAA;IAAAA;IAuBXA;IAAAA;IAMWA;IAAAA;IAgBbA;IAAAA;IAGAA,sCAAuB,cAAvB,EAAuBI,sBAAvB;IAKaJ;IAAAA;IAyBYA;IAAAA;IAKxBA;IAAAA;IAImBA;IAAAA;IAqJhBA;IAAAA;;;;;;;;;;;;;;IASZA,gCAAiC,CAAjC,EAAiC,oBAAjC,EAAiC,EAAjC;IAMIA;MAAAA;MAAA;MAAA,OAAoBA,mDAApB;IAA+C,CAA/C;IACDA;IAEDA,gCAAwC,CAAxC,EAAwC,QAAxC,EAAwC,EAAxC;IAMIA;MAAAA;MAAA;MAAA,OAASA,2CAAT;IAA4B,CAA5B;;;IACDA;;;;;IAdDA;IAAAA,oEAAqD,aAArD,EAAqDK,mBAArD,EAAqD,YAArD,EAAqD,+BAArD,EAAqD,MAArD,EAAqDL,2BAArD;IAUEA;IAAAA;IAEAA;;;;;;IA5WRA,gCAIC,CAJD,EAIC,KAJD,EAIC,EAJD,EAIC,CAJD,EAIC,KAJD,EAIC,EAJD;IAOMA;IAIAA;IACEA;IACFA;IAEFA;IACEA;IACAA;IACEA;IACFA;IAGJA;IAEAA;IAqCAA;IAiSAA;IAmBFA;;;;;IA9WEA;IASMA;IAAAA;IAMAA;IAAAA;IAMgBA;IAAAA;IAqCPA;IAAAA;IAiSGA;IAAAA;;;;;;IAsBlBA;;;;IAA0BA;;;;ADzqB9B,OAAM,MAAOM,oBAAP,CAA2B;EA0E/BC,YACmBC,GADnB,EAEmBC,cAFnB,EAGmBC,gBAHnB,EAImBC,gBAJnB,EAKmBC,aALnB,EAMUC,UANV,EAMgC;IALb;IACA;IACA;IACA;IACA;IACT;IA7EH,eAAmB,IAAnB;IACA,kBAA0B;MAAEC,KAAK,EAAElB,wBAAT;MAAmCmB,MAAM,EAAE,CAA3C;MAA8CC,IAAI,EAAE;IAApD,CAA1B;IACA,mBAAoC,EAApC;IAEA,kBAAsB,KAAtB;IACA,mBAAuB,KAAvB;IAIA,kCAAqC,KAAKL,gBAAL,CAAsBM,OAAtB,CAA8B,sBAA9B,CAArC;IACA,+BAAkC,KAAKN,gBAAL,CAAsBM,OAAtB,CAA8B,yBAA9B,CAAlC;IAEA,mBAAgC,IAAhC;IACA,sBAAmC,IAAnC;IAEP,eAAmB,KAAnB;IAEA,gBAAoB,IAApB;IAIA,0BAAkC;MAChCC,UAAU,EAAE,KAAKP,gBAAL,CAAsBM,OAAtB,CAA8B,wCAA9B,CADoB;;MAEhCE,eAAe;QACb,OAAO,IAAP;MACD,CAJ+B;;MAKhCC,iBAAiB;QACf,OAAO,IAAP;MACD,CAP+B;;MAQhCC,iBAAiB,EAAE;QAAEC,QAAQ,EAAE,QAAZ;QAAsBC,IAAI,EAAE,IAA5B;QAAkCC,QAAQ,EAAE,KAA5C;QAAmDC,QAAQ,EAAE;MAA7D;IARa,CAAlC;IAWA,sBAAoC,EAApC;IAGO,mCAA2C;MAChDP,UAAU,EAAE,KAAKQ,0BAD+B;MAEhDC,kBAAkB,EAAE,KAAKC,uBAFuB;MAGhDC,gBAAgB,EAAE,KAAKlB,gBAAL,CAAsBM,OAAtB,CAA8B,2BAA9B,CAH8B;MAIhDa,SAAS,EAAE,MAAK;QACd,KAAKC,SAAL;QACA,OAAO,IAAP;MACD,CAP+C;MAQhDC,WAAW,EAAE,MAAK;QAChB,KAAKD,SAAL;QACA,OAAO,IAAP;MACD,CAX+C;MAYhDE,aAAa,EAAE,MAAK;QAClB,KAAKC,WAAL,GAAmB,IAAnB;QACA,KAAK1B,GAAL,CAAS2B,aAAT;;QAEA,IAAI,KAAKC,OAAT,EAAkB;UAChB,KAAKC,wBAAL;UACA,OAAO,KAAP;QACD;;QAED,IAAI,CAAC,KAAKC,mBAAL,CAAyBC,KAA9B,EAAqC;UACnC,OAAO,KAAP;QACD,CAFD,MAEO;UACL,KAAK/B,GAAL,CAAS2B,aAAT;;UACA,IAAI,KAAKK,UAAT,EAAqB;YACnB,KAAKC,gBAAL;UACD,CAFD,MAEO;YACL,KAAKC,mBAAL;UACD;;UACD,OAAO,KAAP;QACD;MACF,CAhC+C;MAiChDrB,iBAAiB,EAAE;QAAEC,QAAQ,EAAE,QAAZ;QAAsBC,IAAI,EAAE,IAA5B;QAAkCC,QAAQ,EAAE,KAA5C;QAAmDC,QAAQ,EAAE;MAA7D;IAjC6B,CAA3C;EA4CF;;EAELkB,QAAQ;IACN,KAAKnC,GAAL,CAAS2B,aAAT;IACA,KAAKS,QAAL;EACD;;EAEDC,WAAW;IACT,KAAKC,oBAAL;EACD;;EAEDC,UAAU;IACR;IACA,MAAMC,UAAU,GAAgB,KAAKnC,UAAL,CAAgBoC,aAAhB,CAA8BC,aAA9B,CAA4C,YAA5C,CAAhC;IACAF,UAAU,CAACG,KAAX;EACD;;EAEDP,QAAQ;IACN,KAAKN,mBAAL,GAA2B,IAAI5C,SAAJ,CAAc;MACvC0D,IAAI,EAAE,IAAI3D,WAAJ,CAAgB,EAAhB,EAAoB,CAACE,UAAU,CAAC0D,QAAZ,CAApB,CADiC;MAEvCC,IAAI,EAAE,IAAI7D,WAAJ,CAAgB,EAAhB,EAAoB,CAACE,UAAU,CAAC0D,QAAZ,CAApB,CAFiC;MAGvCE,SAAS,EAAE,IAAI9D,WAAJ,CAAgB,IAAhB,EAAsB,CAACE,UAAU,CAAC0D,QAAZ,CAAtB,CAH4B;MAIvCG,cAAc,EAAE,IAAI/D,WAAJ,CAAgB,EAAhB,EAAoB,CAACE,UAAU,CAAC0D,QAAZ,CAApB,CAJuB;MAKvCI,WAAW,EAAE,IAAIjE,SAAJ,CAAc,EAAd;IAL0B,CAAd,CAA3B,CADM,CASN;;IAEA,KAAKgB,GAAL,CAAS2B,aAAT;EACD;;EAEDuB,YAAY;;;IAEV,iBAAKC,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEC,QAAf,MAAuB,IAAvB,IAAuBC,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEC,OAAF,CAAU,CAACC,OAAD,EAAUC,KAAV,KAAmB;;;MAClD,MAAMC,cAAc,GAAGF,OAAO,CAACG,GAAR,CAAY,OAAZ,CAAvB;;MAEA,IAAI,oBAAc,SAAd,kBAAc,WAAd,GAAc,MAAd,iBAAc,CAAEC,MAAhB,MAAsB,IAAtB,IAAsBR,aAAtB,GAAsB,MAAtB,GAAsBA,GAAES,YAA5B,EAA0C;QACxC,MAAMD,MAAM,GAAGF,cAAc,CAACE,MAA9B;QACA,OAAOA,MAAM,CAACC,YAAd;QACAH,cAAc,CAACI,SAAf,CAA0BF,MAAM,IAAIG,MAAM,CAACC,IAAP,CAAYJ,MAAZ,EAAoBK,MAApB,GAA6B,CAAxC,GAA6CL,MAA7C,GAAsD,IAA/E;QACA,KAAK5D,GAAL,CAAS2B,aAAT;MACD;;MAED,MAAMuC,KAAK,GAAGR,cAAc,SAAd,kBAAc,WAAd,GAAc,MAAd,iBAAc,CAAEQ,KAA9B;;MACA,IAAIA,KAAJ,EAAW;QACT;QACA,MAAMC,gBAAgB,GAAG,KAAKhB,QAAL,CAAcE,QAAd,CACtBe,MADsB,CACf,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,KAAKb,KADD,EACQ;QADR,CAEtBc,IAFsB,CAEjBf,OAAO,IAAG;UAAA;;UAAC,2BAAO,CAACG,GAAR,CAAY,OAAZ,OAAoB,IAApB,IAAoBP,aAApB,GAAoB,MAApB,GAAoBA,GAAEc,KAAtB,MAA2B,IAA3B,IAA2BZ,aAA3B,GAA2B,MAA3B,GAA2BA,GAAEkB,WAAF,EAA3B,MAA+CN,KAAK,CAACM,WAAN,EAA/C;QAAkE,CAF5D,CAAzB;;QAIA,IAAIL,gBAAJ,EAAsB;UAEpBT,cAAc,CAACI,SAAf,CAAyB;YACvBD,YAAY,EAAE;UADS,CAAzB;UAIA,KAAK7D,GAAL,CAAS2B,aAAT;QACD;MACF;IACF,CA1BsB,CAAvB;IA2BA,KAAK3B,GAAL,CAAS2B,aAAT;EACD;;EAED8C,gBAAgB;;;IAEd,iBAAKtB,QAAL,MAAa,IAAb,IAAaC,aAAb,GAAa,MAAb,GAAaA,GAAEC,QAAf,MAAuB,IAAvB,IAAuBC,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEC,OAAF,CAAU,CAACC,OAAD,EAAUC,KAAV,KAAmB;;;MAClD,MAAMC,cAAc,GAAGF,OAAO,CAACG,GAAR,CAAY,MAAZ,CAAvB;;MAEA,IAAI,oBAAc,SAAd,kBAAc,WAAd,GAAc,MAAd,iBAAc,CAAEC,MAAhB,MAAsB,IAAtB,IAAsBR,aAAtB,GAAsB,MAAtB,GAAsBA,GAAES,YAA5B,EAA0C;QACxC,MAAMD,MAAM,GAAGF,cAAc,CAACE,MAA9B;QACA,OAAOA,MAAM,CAACC,YAAd;QACAH,cAAc,CAACI,SAAf,CAA0BF,MAAM,IAAIG,MAAM,CAACC,IAAP,CAAYJ,MAAZ,EAAoBK,MAApB,GAA6B,CAAxC,GAA6CL,MAA7C,GAAsD,IAA/E;QACA,KAAK5D,GAAL,CAAS2B,aAAT;MACD;;MAED,MAAMuC,KAAK,GAAGR,cAAc,SAAd,kBAAc,WAAd,GAAc,MAAd,iBAAc,CAAEQ,KAA9B;;MACA,IAAIA,KAAJ,EAAW;QACT;QACA,MAAMQ,eAAe,GAAG,KAAKvB,QAAL,CAAcE,QAAd,CACrBe,MADqB,CACd,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,KAAKb,KADF,EACS;QADT,CAErBc,IAFqB,CAEhBf,OAAO,IAAG;UAAA;;UAAC,2BAAO,CAACG,GAAR,CAAY,MAAZ,OAAmB,IAAnB,IAAmBP,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEc,KAArB,MAA0B,IAA1B,IAA0BZ,aAA1B,GAA0B,MAA1B,GAA0BA,GAAEkB,WAAF,EAA1B,MAA8CN,KAAK,CAACM,WAAN,EAA9C;QAAiE,CAF5D,CAAxB;;QAIA,IAAIE,eAAJ,EAAqB;UAEnBhB,cAAc,CAACI,SAAf,CAAyB;YACvBD,YAAY,EAAE;UADS,CAAzB;UAIA,KAAK7D,GAAL,CAAS2B,aAAT;QACD;MACF;IACF,CA1BsB,CAAvB;IA2BA,KAAK3B,GAAL,CAAS2B,aAAT;EACD;;EAEkB,IAAfgD,eAAe;IACjB,OAAO,IAAIzF,SAAJ,CAAc;MACnB0F,KAAK,EAAE,IAAI3F,WAAJ,CAAgB,EAAhB,EAAoB,CAACE,UAAU,CAAC0D,QAAZ,CAApB,CADY;MAEnBC,IAAI,EAAE,IAAI7D,WAAJ,CAAgB,EAAhB,EAAoB,EAApB,CAFa;MAGnB4F,IAAI,EAAE,IAAI5F,WAAJ,CAAgB,EAAhB,EAAoB,CAACE,UAAU,CAAC0D,QAAZ,CAApB;IAHa,CAAd,CAAP;EAKD;;EAEDiC,yBAAyB,CAACZ,KAAD,EAAW;IAClC,OAAO,IAAIhF,SAAJ,CAAc;MACnB0F,KAAK,EAAE,IAAI3F,WAAJ,CAAgBiF,KAAK,CAACU,KAAtB,EAA6B,CAACzF,UAAU,CAAC0D,QAAZ,CAA7B,CADY;MAEnBC,IAAI,EAAE,IAAI7D,WAAJ,CAAgBiF,KAAK,CAACpB,IAAtB,EAA4B,EAA5B,CAFa;MAGnB+B,IAAI,EAAE,IAAI5F,WAAJ,CAAgBiF,KAAK,CAACW,IAAtB,EAA4B,CAAC1F,UAAU,CAAC0D,QAAZ,CAA5B;IAHa,CAAd,CAAP;EAKD;;EAEDkC,aAAa,CAACtB,KAAD,EAAc;IACzB,KAAKN,QAAL,CAAc6B,QAAd,CAAuBvB,KAAvB;IACA,KAAKzD,GAAL,CAAS2B,aAAT;IACA,KAAKuB,YAAL;EACD;;EAED+B,aAAa;IACX,KAAK9B,QAAL,CAAc+B,IAAd,CAAmB,KAAKP,eAAxB;IACA,KAAK3E,GAAL,CAAS2B,aAAT;EACD;;EAEOJ,SAAS;IACf,KAAKO,mBAAL,CAAyBqD,KAAzB;IAEA,MAAMC,gBAAgB,GAAG,KAAKtD,mBAAL,CAAyB6B,GAAzB,CAA6B,aAA7B,CAAzB;IACAyB,gBAAgB,CAACC,KAAjB;IAEA,KAAKpC,WAAL,GAAmB,IAAnB;IACA,KAAKD,cAAL,GAAsB,IAAtB;IACA,KAAKhD,GAAL,CAAS2B,aAAT;IACA2D,OAAO,CAACC,GAAR,CAAY,OAAZ;IACAD,OAAO,CAACC,GAAR,CAAY,KAAKzD,mBAAjB;EACD;;EAEW,IAARqB,QAAQ;;;IACV,OAAO,WAAKrB,mBAAL,MAAwB,IAAxB,IAAwBsB,aAAxB,GAAwB,MAAxB,GAAwBA,GAAEC,QAAF,CAAW,aAAX,CAA/B;EACD;;EAEMmC,cAAc,CAACC,QAAD,EAAiB;;;IACpC,OACE,YAAK3D,mBAAL,CAAyB6B,GAAzB,CAA6B8B,QAA7B,OAAsC,IAAtC,IAAsCrC,aAAtC,GAAsC,MAAtC,GAAsCA,GAAEsC,QAAF,CAAW,SAAX,CAAtC,MACI,YAAK5D,mBAAL,CAAyB6B,GAAzB,CAA6B8B,QAA7B,OAAsC,IAAtC,IAAsCnC,aAAtC,GAAsC,MAAtC,GAAsCA,GAAEqC,OAAxC,KAAmD,KAAKjE,WAD5D,CADF;EAID;;EAEOY,oBAAoB;IAC1B,IAAI,KAAKsD,mBAAT,EAA8B;MAC5B,KAAKC,OAAL,GAAe,IAAf;MACA,KAAK5F,cAAL,CAAoB6F,YAApB;MACA,KAAK5F,gBAAL,CAAsB6F,yBAAtB,CAAgD,KAAKH,mBAAL,CAAyBI,EAAzE,EAA6E,KAAKC,UAAlF,EACGC,IADH,CACQ5G,QAAQ,CAAC,MAAK;QAClB,KAAKW,cAAL,CAAoBkG,WAApB;QACA,KAAKN,OAAL,GAAe,KAAf;QACA,KAAK7F,GAAL,CAAS2B,aAAT;MACD,CAJa,CADhB,EAMGyE,SANH,CAMa;QACTC,IAAI,EAAGC,QAAD,IAAsC;UAC1C,KAAKC,WAAL,GAAmBD,QAAQ,CAACE,OAA5B;UACA,KAAKC,YAAL,GAAoBH,QAAQ,CAACI,KAA7B;QACD,CAJQ;QAKTC,KAAK,EAAE,MAAK;UACV,KAAKJ,WAAL,GAAmB,EAAnB;QACD;MAPQ,CANb;IAeD;EACF;;EAEMK,gBAAgB,CAACC,KAAD,EAAc;IACnC,KAAK5G,cAAL,CAAoB6F,YAApB;IACA,KAAKD,OAAL,GAAe,IAAf;IACA,KAAKI,UAAL,CAAgB1F,MAAhB,GAAyB,CAACsG,KAAD,GAAS,CAAlC;IACA,KAAKZ,UAAL,CAAgBzF,IAAhB,GAAuBqG,KAAvB;IACA,KAAKvE,oBAAL;EACD;;EAEMwE,eAAe,CAACrB,QAAD,EAAiB;;;IACrC,OACE,YAAK3D,mBAAL,CAAyB6B,GAAzB,CAA6B8B,QAA7B,OAAsC,IAAtC,IAAsCrC,aAAtC,GAAsC,MAAtC,GAAsCA,GAAEsC,QAAF,CAAW,UAAX,CAAtC,MACI,YAAK5D,mBAAL,CAAyB6B,GAAzB,CAA6B8B,QAA7B,OAAsC,IAAtC,IAAsCnC,aAAtC,GAAsC,MAAtC,GAAsCA,GAAEqC,OAAxC,KAAmD,KAAKjE,WAD5D,CADF;EAID;;EAEMqF,gBAAgB,CAACC,YAAD,EAAqB;IAC1CzH,IAAI,CAAC0H,IAAL,CAAU;MACRrC,KAAK,EAAE,KAAKzE,gBAAL,CAAsBM,OAAtB,CAA8B,mBAA9B,CADC;MAERyG,IAAI,EAAE,KAAK/G,gBAAL,CAAsBM,OAAtB,CAA8B,iCAA9B,CAFE;MAGR0G,IAAI,EAAE,SAHE;MAIRC,gBAAgB,EAAE,IAJV;MAKRC,kBAAkB,EAAE,SALZ;MAMRC,iBAAiB,EAAE,MANX;MAORC,iBAAiB,EAAE,KAAKpH,gBAAL,CAAsBM,OAAtB,CAA8B,oBAA9B;IAPX,CAAV,EAQG+G,IARH,CAQSC,MAAD,IAAW;MACjB,IAAIA,MAAM,CAACC,WAAX,EAAwB;QACtB,KAAKzH,cAAL,CAAoB6F,YAApB;QACA,KAAK5F,gBAAL,CAAsByH,oBAAtB,CAA2CX,YAA3C,EACGd,IADH,CACQ5G,QAAQ,CAAC,MAAK;UAClB,KAAKW,cAAL,CAAoBkG,WAApB;QACD,CAFa,CADhB,EAGMC,SAHN,CAGgB;UACZC,IAAI,EAAE,MAAK;YACT9G,IAAI,CAAC0H,IAAL,CACE,KAAK9G,gBAAL,CAAsBM,OAAtB,CAA8B,+BAA9B,CADF,EAEE,KAAKN,gBAAL,CAAsBM,OAAtB,CAA8B,iCAA9B,CAFF,EAGE,SAHF;YAKA,KAAK6B,oBAAL;UACD,CARW;UASZqE,KAAK,EAAGA,KAAD,IAAU;YACfpH,IAAI,CAAC0H,IAAL,CAAU;cACRE,IAAI,EAAE,OADE;cAERvC,KAAK,EAAE,KAAKzE,gBAAL,CAAsBM,OAAtB,CAA8B,WAA9B,CAFC;cAGRyG,IAAI,EAAEP,KAAK,CAACiB;YAHJ,CAAV;UAKD;QAfW,CAHhB;MAoBD;IACF,CAhCD;EAiCD;EAED;;;;;;;;EAMaC,yBAAyB,CAAC7F,aAAsB,KAAvB,EAA8B8F,gBAA9B,EAAuD;IAAA;;IAAA;MAC3F,KAAI,CAAC9E,cAAL,GAAsB,IAAtB;MACA,KAAI,CAACC,WAAL,GAAmB,IAAnB;MACA,KAAI,CAACvB,WAAL,GAAmB,KAAnB;MACA,KAAI,CAACM,UAAL,GAAkBA,UAAlB;MACA,KAAI,CAACJ,OAAL,GAAe,KAAf;MACA,KAAI,CAACmG,QAAL,GAAgB,IAAhB;;MAEA,KAAI,CAAC/H,GAAL,CAAS2B,aAAT;;MACA,KAAI,CAACqG,2BAAL,CAAiCtH,UAAjC,GAA8CsB,UAAU,GAAG,KAAI,CAAC7B,gBAAL,CAAsBM,OAAtB,CAA8B,yBAA9B,CAAH,GAA8D,KAAI,CAACN,gBAAL,CAAsBM,OAAtB,CAA8B,sBAA9B,CAAtH;MAEA,KAAI,CAACuH,2BAAL,CAAiC7G,kBAAjC,GAAsDa,UAAU,GAAG,KAAI,CAAC7B,gBAAL,CAAsBM,OAAtB,CAA8B,2BAA9B,CAAH,GAAgE,KAAI,CAACN,gBAAL,CAAsBM,OAAtB,CAA8B,wBAA9B,CAAhI;;MAEA,IAAIuB,UAAU,IAAI8F,gBAAlB,EAAoC;QAClC,MAAMG,iBAAiB,GAAG,KAAI,CAAC1B,WAAL,CAAiB2B,IAAjB,CAAsBC,UAAU,IAAIA,UAAU,CAACnC,EAAX,KAAkB8B,gBAAtD,CAA1B;;QACA,IAAIG,iBAAJ,EAAuB;UACrB,MAAM;YAAErF,IAAF;YAAQE,IAAR;YAAcE,cAAd;YAA8BD,SAA9B;YAAyCE,WAAW,GAAG;UAAvD,IAA8DgF,iBAApE;;UACA,KAAI,CAACnG,mBAAL,CAAyBsG,UAAzB,CAAoC;YAAExF,IAAF;YAAQE,IAAR;YAAcE,cAAd;YAA8BD;UAA9B,CAApC;;UAEAuC,OAAO,CAACC,GAAR,CAAYtC,WAAZ;;UAEA,IAAIA,WAAW,KAAIA,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEgB,MAAjB,CAAf,EAAwC;YACtChB,WAAW,SAAX,eAAW,WAAX,GAAW,MAAX,cAAW,CAAEM,OAAb,CAAsBC,OAAD,IAAiB;cACpC,KAAI,CAACL,QAAL,CAAc+B,IAAd,CAAmB,KAAI,CAACJ,yBAAL,CAA+BtB,OAA/B,CAAnB;;cACA,KAAI,CAACxD,GAAL,CAAS2B,aAAT;YACD,CAHD;UAID;;UAED,KAAI,CAACuB,YAAL;;UAEA,KAAI,CAACF,cAAL,GAAsBA,cAAtB;QACD;;QACD,KAAI,CAAC8E,gBAAL,GAAwBA,gBAAxB;MACD,CApBD,MAoBO;QACL,KAAI,CAACvG,SAAL;MACD;;MAED,KAAI,CAACwG,QAAL,GAAgB,KAAhB;;MACA,KAAI,CAAC/H,GAAL,CAAS2B,aAAT;;MACA,aAAa,KAAI,CAAC0G,qBAAL,CAA2BC,IAA3B,EAAb;IAvC2F;EAwC5F;;EAEMC,mBAAmB,CAACC,QAAD,EAAsBC,cAAtB,EAA4C;IACpE,IAAID,QAAJ,EAAc;MACZ,KAAK1G,mBAAL,CAAyBsG,UAAzB,CAAoC;QAAE,CAACK,cAAD,GAAkBD;MAApB,CAApC;IACD,CAFD,MAEO;MACL,KAAK1G,mBAAL,CAAyBsG,UAAzB,CAAoC;QAAE,CAACK,cAAD,GAAkB;MAApB,CAApC;IACD;EACF;;EAEMC,0BAA0B,CAACF,QAAD,EAAsB/E,KAAtB,EAAmC;IAClE,KAAKN,QAAL,CAAcwF,EAAd,CAAiBlF,KAAjB,EAAwB2E,UAAxB,CAAmC;MACjCvD,IAAI,EAAE2D,QAAQ,GAAGA,QAAH,GAAc;IADK,CAAnC;IAGA,KAAKxI,GAAL,CAAS2B,aAAT;EACD;;EAEDiH,kBAAkB,CAACnF,KAAD,EAAc;;;IAC9B,OAAO,WAAKN,QAAL,CAAcwF,EAAd,CAAiBlF,KAAjB,EAAwBoF,WAAxB,QAAqC,IAArC,IAAqCzF,aAArC,GAAqC,MAArC,GAAqCA,GAAEyB,IAA9C;EACD;;EAEOiE,2BAA2B;;;IACjC,MAAMC,cAAc,GAA4B;MAC9CC,aAAa,EAAE,CAAC,KAAKpD,mBAAL,CAAyBI,EADK;MAE9ClD,IAAI,EAAE,WAAKhB,mBAAL,CAAyB6B,GAAzB,CAA6B,MAA7B,OAAoC,IAApC,IAAoCP,aAApC,GAAoC,MAApC,GAAoCA,GAAEc,KAFE;MAG9CtB,IAAI,EAAE,WAAKd,mBAAL,CAAyB6B,GAAzB,CAA6B,MAA7B,OAAoC,IAApC,IAAoCL,aAApC,GAAoC,MAApC,GAAoCA,GAAEY,KAHE;MAI9CnB,SAAS,EAAG,YAAKjB,mBAAL,CAAyB6B,GAAzB,CAA6B,WAA7B,OAAyC,IAAzC,IAAyCsF,aAAzC,GAAyC,MAAzC,GAAyCA,GAAE/E,KAA3C,KAAoD,MAApD,IAA8D,YAAKpC,mBAAL,CAAyB6B,GAAzB,CAA6B,WAA7B,OAAyC,IAAzC,IAAyCuF,aAAzC,GAAyC,MAAzC,GAAyCA,GAAEhF,KAA3C,KAAoD,IAJhF;MAK9ClB,cAAc,EAAE,WAAKlB,mBAAL,CAAyB6B,GAAzB,CAA6B,gBAA7B,OAA8C,IAA9C,IAA8CwF,aAA9C,GAA8C,MAA9C,GAA8CA,GAAEjF,KALlB;MAM9CjB,WAAW,EAAE,WAAKnB,mBAAL,CAAyB6B,GAAzB,CAA6B,aAA7B,OAA2C,IAA3C,IAA2CyF,aAA3C,GAA2C,MAA3C,GAA2CA,GAAElF;IANZ,CAAhD;IAQA,OAAO6E,cAAP;EACD;EAGD;;;;;EAGQ7G,mBAAmB;IACzB,KAAKR,WAAL,GAAmB,IAAnB;IACA,KAAK1B,GAAL,CAAS2B,aAAT;IAEA,KAAKuB,YAAL;IAEAoC,OAAO,CAACC,GAAR,CAAY,KAAKzD,mBAAjB;;IAGA,IAAI,KAAKA,mBAAL,CAAyBC,KAA7B,EAAoC;MAClC,KAAK9B,cAAL,CAAoB6F,YAApB;MACA,KAAK5F,gBAAL,CAAsBmJ,gBAAtB,CAAuC,KAAKP,2BAAL,EAAvC,EACG5C,IADH,CACQ5G,QAAQ,CAAC,MAAK;QAClB,KAAKW,cAAL,CAAoBkG,WAApB;MACD,CAFa,CADhB,EAIGC,SAJH,CAIa;QACTC,IAAI,EAAE,MAAK;UACT,KAAK9E,SAAL;UACAhC,IAAI,CAAC0H,IAAL,CACE,KAAK9G,gBAAL,CAAsBM,OAAtB,CAA8B,cAA9B,CADF,EAEE,KAAKN,gBAAL,CAAsBM,OAAtB,CAA8B,mCAA9B,CAFF,EAGE,SAHF;UAKA,KAAK4H,qBAAL,CAA2BiB,KAA3B;UACA,KAAKhH,oBAAL;UACA,KAAKZ,WAAL,GAAmB,KAAnB;UACA,KAAK1B,GAAL,CAAS2B,aAAT;QACD,CAZQ;QAaTgF,KAAK,EAAGA,KAAD,IAAU;UACfpH,IAAI,CAAC0H,IAAL,CAAU;YACRE,IAAI,EAAE,OADE;YAERvC,KAAK,EAAE,KAAKzE,gBAAL,CAAsBM,OAAtB,CAA8B,iCAA9B,CAFC;YAGRyG,IAAI,EAAEP,KAAK,CAACiB;UAHJ,CAAV;QAKD;MAnBQ,CAJb;IAyBD;EACF;EAED;;;;;EAGQ3F,gBAAgB;;;IACtB,KAAKP,WAAL,GAAmB,IAAnB;IACA,KAAK1B,GAAL,CAAS2B,aAAT;IAEA,KAAKuB,YAAL;IACAoC,OAAO,CAACC,GAAR,CAAY,KAAKzD,mBAAjB;;IAEA,IAAI,YAAKA,mBAAL,MAAwB,IAAxB,IAAwBsB,aAAxB,GAAwB,MAAxB,GAAwBA,GAAErB,KAA1B,MAAmC,WAAK6D,mBAAL,MAAwB,IAAxB,IAAwBtC,aAAxB,GAAwB,MAAxB,GAAwBA,GAAE0C,EAA7D,CAAJ,EAAqE;MACnE,KAAK/F,cAAL,CAAoB6F,YAApB;MACA,KAAK5F,gBAAL,CAAsB+B,gBAAtB,CAAsC8B;QAAGiC,EAAE,EAAE,CAAC,KAAK8B;MAAb,GAAkC,KAAKgB,2BAAL,EAAlC,CAAtC,EACG5C,IADH,CACQ5G,QAAQ,CAAC,MAAK;QAClB,KAAKW,cAAL,CAAoBkG,WAApB;MACD,CAFa,CADhB,EAIGC,SAJH,CAIa;QACTC,IAAI,EAAE,MAAK;UACT9G,IAAI,CAAC0H,IAAL,CACE,KAAK9G,gBAAL,CAAsBM,OAAtB,CAA8B,cAA9B,CADF,EAEE,KAAKN,gBAAL,CAAsBM,OAAtB,CAA8B,sCAA9B,CAFF,EAGE,SAHF;UAKA,KAAK4H,qBAAL,CAA2BiB,KAA3B;UACA,KAAKhH,oBAAL;UACA,KAAKZ,WAAL,GAAmB,KAAnB;UACA,KAAK1B,GAAL,CAAS2B,aAAT;QACD,CAXQ;QAYTgF,KAAK,EAAGA,KAAD,IAAU;UACfpH,IAAI,CAAC0H,IAAL,CAAU;YACRE,IAAI,EAAE,OADE;YAERvC,KAAK,EAAE,KAAKzE,gBAAL,CAAsBM,OAAtB,CAA8B,oCAA9B,CAFC;YAGRyG,IAAI,EAAEP,KAAK,CAACiB;UAHJ,CAAV;QAKD;MAlBQ,CAJb;IAwBD;EACF;;EAEM2B,kBAAkB;IACvB,KAAKhH,UAAL;IACA,KAAKnC,aAAL,CAAmBoJ,6BAAnB,CAAiDnK,QAAQ,CAAC,KAAKuG,mBAAL,CAAyBI,EAA1B,CAAzD;EACD;;EAEMyD,iBAAiB;IACtB,KAAKrJ,aAAL,CAAmBoJ,6BAAnB,CAAiDnK,QAAQ,CAAC,CAAD,CAAzD;EACD;;EAEDqK,mBAAmB,CAACC,UAAD,EAAuB;IACxC,KAAKA,UAAL,GAAkBA,UAAlB;IACA,KAAK3J,GAAL,CAAS2B,aAAT;EACD;;EAEDiI,eAAe,CAACC,IAAD,EAAa;IAC1B,KAAKnI,WAAL,GAAmB,KAAnB;;IACA,IAAI,CAAC,KAAKE,OAAV,EAAmB;MACjB,KAAKQ,QAAL;IACD;;IACD,KAAKpC,GAAL,CAAS2B,aAAT;EACD;;EAEDE,wBAAwB;IAEtB,IAAI,KAAK+D,mBAAL,CAAyBI,EAAzB,IAA+B,KAAK2D,UAAxC,EAAoD;MAClD,KAAK1J,cAAL,CAAoB6F,YAApB;MAEA,KAAK5F,gBAAL,CAAsB4J,gBAAtB,CAAuC;QACrCd,aAAa,EAAE3J,QAAQ,CAAC,KAAKuG,mBAAL,CAAyBI,EAA1B,CADc;QAErC2D,UAAU,EAAE,KAAKA;MAFoB,CAAvC,EAGGvD,SAHH,CAGa;QACXC,IAAI,EAAGC,QAAD,IAAa;UACjBhB,OAAO,CAACC,GAAR,CAAYe,QAAZ;UACA,KAAK+B,qBAAL,CAA2BiB,KAA3B;UACA/J,IAAI,CAAC0H,IAAL,CACE,KAAK9G,gBAAL,CAAsBM,OAAtB,CAA8B,cAA9B,CADF,EAEE,KAAKN,gBAAL,CAAsBM,OAAtB,CAA8B,iCAA9B,CAFF,EAGE,SAHF;UAKA,KAAK6B,oBAAL;QACD,CAVU;QAWXqE,KAAK,EAAGoD,GAAD,IAAQ;UACbzE,OAAO,CAACC,GAAR,CAAYwE,GAAZ;UACAxK,IAAI,CAAC0H,IAAL,CAAU;YACRE,IAAI,EAAE,OADE;YAERvC,KAAK,EAAE,KAAKzE,gBAAL,CAAsBM,OAAtB,CAA8B,WAA9B,CAFC;YAGRuJ,IAAI,EAAED,GAAG,CAACnC;UAHF,CAAV;UAKA,KAAK3H,cAAL,CAAoBkG,WAApB;QACD;MAnBU,CAHb;IAwBD;EAEF;;EAED8D,gBAAgB,CAACjD,YAAD,EAAqB;IACnC,KAAK/G,cAAL,CAAoB6F,YAApB;IAEA,KAAK5F,gBAAL,CAAsBgK,oBAAtB,CAA2ClD,YAA3C,EAAyDZ,SAAzD,CAAmE;MACjEC,IAAI,EAAGC,QAAD,IAAa;QACjB,KAAK6D,cAAL,GAAsB7D,QAAtB;QACA,KAAKtG,GAAL,CAAS2B,aAAT;QACA,KAAKyI,qBAAL,CAA2B9B,IAA3B;QACA,KAAKrI,cAAL,CAAoBkG,WAApB;MAED,CAPgE;MAQjEQ,KAAK,EAAGoD,GAAD,IAAQ;QACbzE,OAAO,CAACC,GAAR,CAAYwE,GAAZ;QACA,KAAKI,cAAL,GAAsB,EAAtB;QACA,KAAKnK,GAAL,CAAS2B,aAAT;QACA,KAAKyI,qBAAL,CAA2B9B,IAA3B;QACA,KAAKrI,cAAL,CAAoBkG,WAApB;QAEA5G,IAAI,CAAC0H,IAAL,CAAU;UACRE,IAAI,EAAE,OADE;UAERvC,KAAK,EAAE,KAAKzE,gBAAL,CAAsBM,OAAtB,CAA8B,YAA9B,CAFC;UAGRyG,IAAI,EAAE,KAAK/G,gBAAL,CAAsBM,OAAtB,CAA8B,0BAA9B;QAHE,CAAV;MAKD;IApBgE,CAAnE;EAuBD;;AA/hB8B;;;mBAApBX,sBAAoBN;AAAA;;;QAApBM;EAAoBuK;EAAAC;IAAA;;;;;;;;;;;;;;;;;;;;;MCtBjC9K;MAsUAA;MAIAA;MACEA;MAmXAA;MAGFA;MAEAA,wCAA4D,EAA5D,EAA4D,KAA5D,EAA4D,CAA5D;MAEIA;;MAKFA;;;;;;MA1sBaA,oCAAgB,UAAhB,EAAgB+K,GAAhB;MA0UmB/K;MAAAA;MAE7BA;MAAAA;MAuXoBA;MAAAA;MAGnBA;MAAAA,oFAA0D,aAA1D,EAA0DgL,kBAA1D", "names": ["FormArray", "FormControl", "FormGroup", "Validators", "PAGINATION_DEFAULT_LIMIT", "toNumber", "finalize", "<PERSON><PERSON>", "i0", "costCenter_r12", "_r9", "ctx_r44", "ctx_r30", "ctx_r31", "CostCentersComponent", "constructor", "cdr", "spinnerService", "companiesService", "translateService", "reportService", "elementRef", "limit", "offset", "page", "instant", "modalTitle", "hideClose<PERSON><PERSON>on", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modalDialogConfig", "backdrop", "size", "keyboard", "centered", "costCenterEditorModalTitle", "dismissButtonLabel", "modalDismissButtonLabel", "closeButtonLabel", "on<PERSON><PERSON><PERSON>", "formReset", "shouldClose", "<PERSON><PERSON><PERSON><PERSON>", "isSubmitted", "detectChanges", "is<PERSON><PERSON><PERSON>", "uploadMultipleCostCenter", "costCenterFormGroup", "valid", "isEditMode", "updateCostCenter", "createNewCostCenter", "ngOnInit", "initForm", "ngOnChanges", "fetchCostCentersList", "toggle<PERSON><PERSON>", "divElement", "nativeElement", "querySelector", "click", "name", "required", "code", "operating", "departmentHead", "sectionHead", "sectionExist", "sections", "_a", "controls", "_b", "for<PERSON>ach", "section", "index", "sectionControl", "get", "errors", "alreadyExist", "setErrors", "Object", "keys", "length", "value", "isTitleDuplicate", "filter", "_", "i", "some", "toLowerCase", "sectionCodeExist", "isCodeDuplicate", "sectionControls", "title", "user", "getSectionControlsDefault", "removeSection", "removeAt", "addNewSection", "push", "reset", "sectionHeadArray", "clear", "console", "log", "isPatternError", "FContorl", "<PERSON><PERSON><PERSON><PERSON>", "touched", "selectedCompanyCode", "loading", "startSpinner", "getCostCentersByCompanyId", "id", "pagination", "pipe", "stopSpinner", "subscribe", "next", "response", "costCenters", "records", "totalRecords", "total", "error", "handlePageChange", "event", "isRequiredError", "deleteCostCenter", "costCenterId", "fire", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "then", "result", "isConfirmed", "deleteCostCenterById", "message", "openCostCenterEditorModal", "editCostCenterId", "hideForm", "costCenterEditorModalConfig", "currentCostCenter", "find", "costCenter", "patchValue", "costCenterEditorModal", "open", "costCenterHeadAdded", "userData", "controllerName", "costCenterSectionHeadAdded", "at", "getSectionHeadUser", "getRawValue", "getCostCenterRequestPayload", "requestPayload", "companyCodeId", "_c", "_d", "_e", "_f", "createCostCenter", "close", "exportToExcelSheet", "downloadCostCenterExcelReport", "exportBlankFormat", "addBufferAttachment", "bufferData", "changeAddOption", "type", "importCostCenter", "err", "html", "openHistoryModel", "getCostCenterHistory", "companyHistory", "historyModalComponent", "selectors", "viewQuery", "_r1", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\modules\\admin\\companies\\cost-centers\\cost-centers.component.ts", "C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\modules\\admin\\companies\\cost-centers\\cost-centers.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnInit, ViewChild } from '@angular/core';\r\nimport { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { DEBOUNCE_TIME, PAGINATION_DEFAULT_LIMIT } from '@core/constants';\r\nimport { IPagination } from '@core/interfaces/api';\r\nimport { HistoryResponse } from '@core/interfaces/history-response';\r\nimport { UserModel } from '@core/models/basic/user';\r\nimport { ModalComponent, ModalConfig } from '@core/modules/partials';\r\nimport { ReportService } from '@core/services/api';\r\nimport { SpinnerService } from '@core/services/common/spinner.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { toNumber } from 'lodash';\r\nimport { debounceTime, finalize } from 'rxjs';\r\nimport Swal from 'sweetalert2';\r\nimport { CompanyCodeResponse, CostCenterResponse, CostCentersListResponse, CreateCostCenterRequest } from '../../core/models';\r\nimport { CompaniesService } from '../../core/services';\r\n\r\n@Component({\r\n  selector: 'app-cost-centers',\r\n  templateUrl: './cost-centers.component.html',\r\n  styleUrls: ['./cost-centers.component.scss']\r\n})\r\n\r\nexport class CostCentersComponent implements OnInit, OnChanges {\r\n  @Input('selectedCompanyCode') selectedCompanyCode: CompanyCodeResponse;\r\n\r\n  public loading: boolean = true;\r\n  public pagination: IPagination = { limit: PAGINATION_DEFAULT_LIMIT, offset: 0, page: 1 };\r\n  public costCenters: CostCenterResponse[] = [];\r\n  public totalRecords: number;\r\n  public isEditMode: boolean = false;\r\n  public isSubmitted: boolean = false;\r\n  public editCostCenterId: number;\r\n\r\n  public costCenterFormGroup: FormGroup;\r\n  public costCenterEditorModalTitle: string = this.translateService.instant('MENU.ADD_COST_CENTER');\r\n  public modalDismissButtonLabel: string = this.translateService.instant('FORM.BUTTON.SAVE_BUTTON');\r\n\r\n  public sectionHead: UserModel | null = null;\r\n  public departmentHead: UserModel | null = null;\r\n\r\n  isMulti: boolean = false;\r\n  bufferData: Uint8Array;\r\n  hideForm: boolean = true;\r\n\r\n  @ViewChild('costCenterEditorModal') private costCenterEditorModal: ModalComponent;\r\n\r\n  historyModalConfig: ModalConfig = {\r\n    modalTitle: this.translateService.instant('FORM.BUTTON.COST_CENTER_HISTORY_BUTTON'),\r\n    hideCloseButton() {\r\n      return true\r\n    },\r\n    hideDismissButton() {\r\n      return true;\r\n    },\r\n    modalDialogConfig: { backdrop: 'static', size: 'lg', keyboard: false, centered: false }\r\n  };\r\n\r\n  companyHistory: HistoryResponse[] = [];\r\n  @ViewChild('historyModal') private historyModalComponent: ModalComponent;\r\n\r\n  public costCenterEditorModalConfig: ModalConfig = {\r\n    modalTitle: this.costCenterEditorModalTitle,\r\n    dismissButtonLabel: this.modalDismissButtonLabel,\r\n    closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),\r\n    onDismiss: () => {\r\n      this.formReset();\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      this.formReset();\r\n      return true;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.isSubmitted = true;\r\n      this.cdr.detectChanges();\r\n\r\n      if (this.isMulti) {\r\n        this.uploadMultipleCostCenter();\r\n        return false;\r\n      }\r\n\r\n      if (!this.costCenterFormGroup.valid) {\r\n        return false;\r\n      } else {\r\n        this.cdr.detectChanges();\r\n        if (this.isEditMode) {\r\n          this.updateCostCenter();\r\n        } else {\r\n          this.createNewCostCenter();\r\n        }\r\n        return false;\r\n      }\r\n    },\r\n    modalDialogConfig: { backdrop: 'static', size: 'lg', keyboard: false, centered: true }\r\n  };\r\n\r\n  constructor(\r\n    private readonly cdr: ChangeDetectorRef,\r\n    private readonly spinnerService: SpinnerService,\r\n    private readonly companiesService: CompaniesService,\r\n    private readonly translateService: TranslateService,\r\n    private readonly reportService: ReportService,\r\n    private elementRef: ElementRef\r\n\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.cdr.detectChanges();\r\n    this.initForm();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.fetchCostCentersList();\r\n  }\r\n\r\n  toggleOpen() {\r\n    // Manually trigger the click event\r\n    const divElement: HTMLElement = this.elementRef.nativeElement.querySelector('.separator');\r\n    divElement.click();\r\n  }\r\n\r\n  initForm() {\r\n    this.costCenterFormGroup = new FormGroup({\r\n      name: new FormControl('', [Validators.required]),\r\n      code: new FormControl('', [Validators.required]),\r\n      operating: new FormControl(true, [Validators.required]),\r\n      departmentHead: new FormControl('', [Validators.required]),\r\n      sectionHead: new FormArray([]),\r\n    });\r\n\r\n    // sections: new FormArray([this.sectionControls]),\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  sectionExist() {\r\n\r\n    this.sections?.controls?.forEach((section, index) => {\r\n      const sectionControl = section.get('title');\r\n\r\n      if (sectionControl?.errors?.alreadyExist) {\r\n        const errors = sectionControl.errors;\r\n        delete errors.alreadyExist;\r\n        sectionControl.setErrors((errors && Object.keys(errors).length > 0) ? errors : null);\r\n        this.cdr.detectChanges();\r\n      }\r\n\r\n      const value = sectionControl?.value;\r\n      if (value) {\r\n        // Check if the same title exists in the section FormArray\r\n        const isTitleDuplicate = this.sections.controls\r\n          .filter((_, i) => i !== index) // Exclude the current section\r\n          .some(section => section.get('title')?.value?.toLowerCase() === value.toLowerCase());\r\n\r\n        if (isTitleDuplicate) {\r\n\r\n          sectionControl.setErrors({\r\n            alreadyExist: true\r\n          });\r\n\r\n          this.cdr.detectChanges();\r\n        }\r\n      }\r\n    });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  sectionCodeExist() {\r\n\r\n    this.sections?.controls?.forEach((section, index) => {\r\n      const sectionControl = section.get('code');\r\n\r\n      if (sectionControl?.errors?.alreadyExist) {\r\n        const errors = sectionControl.errors;\r\n        delete errors.alreadyExist;\r\n        sectionControl.setErrors((errors && Object.keys(errors).length > 0) ? errors : null);\r\n        this.cdr.detectChanges();\r\n      }\r\n\r\n      const value = sectionControl?.value;\r\n      if (value) {\r\n        // Check if the same code exists in the section FormArray\r\n        const isCodeDuplicate = this.sections.controls\r\n          .filter((_, i) => i !== index) // Exclude the current section\r\n          .some(section => section.get('code')?.value?.toLowerCase() === value.toLowerCase());\r\n\r\n        if (isCodeDuplicate) {\r\n\r\n          sectionControl.setErrors({\r\n            alreadyExist: true\r\n          });\r\n\r\n          this.cdr.detectChanges();\r\n        }\r\n      }\r\n    });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  get sectionControls() {\r\n    return new FormGroup({\r\n      title: new FormControl('', [Validators.required]),\r\n      code: new FormControl('', []),\r\n      user: new FormControl('', [Validators.required]),\r\n    })\r\n  }\r\n\r\n  getSectionControlsDefault(value: any) {\r\n    return new FormGroup({\r\n      title: new FormControl(value.title, [Validators.required]),\r\n      code: new FormControl(value.code, []),\r\n      user: new FormControl(value.user, [Validators.required]),\r\n    })\r\n  }\r\n\r\n  removeSection(index: number) {\r\n    this.sections.removeAt(index);\r\n    this.cdr.detectChanges();\r\n    this.sectionExist();\r\n  }\r\n\r\n  addNewSection() {\r\n    this.sections.push(this.sectionControls);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private formReset() {\r\n    this.costCenterFormGroup.reset();\r\n\r\n    const sectionHeadArray = this.costCenterFormGroup.get('sectionHead') as FormArray;\r\n    sectionHeadArray.clear();\r\n\r\n    this.sectionHead = null;\r\n    this.departmentHead = null;\r\n    this.cdr.detectChanges();\r\n    console.log('Reset')\r\n    console.log(this.costCenterFormGroup);\r\n  }\r\n\r\n  get sections(): FormArray {\r\n    return this.costCenterFormGroup?.controls[\"sectionHead\"] as FormArray;\r\n  }\r\n\r\n  public isPatternError(FContorl: string): (boolean | undefined) {\r\n    return (\r\n      this.costCenterFormGroup.get(FContorl)?.hasError('pattern')\r\n      && (this.costCenterFormGroup.get(FContorl)?.touched || this.isSubmitted)\r\n    );\r\n  }\r\n\r\n  private fetchCostCentersList() {\r\n    if (this.selectedCompanyCode) {\r\n      this.loading = true;\r\n      this.spinnerService.startSpinner();\r\n      this.companiesService.getCostCentersByCompanyId(this.selectedCompanyCode.id, this.pagination)\r\n        .pipe(finalize(() => {\r\n          this.spinnerService.stopSpinner();\r\n          this.loading = false;\r\n          this.cdr.detectChanges();\r\n        }))\r\n        .subscribe({\r\n          next: (response: CostCentersListResponse) => {\r\n            this.costCenters = response.records;\r\n            this.totalRecords = response.total;\r\n          },\r\n          error: () => {\r\n            this.costCenters = [];\r\n          }\r\n        })\r\n    }\r\n  }\r\n\r\n  public handlePageChange(event: number): void {\r\n    this.spinnerService.startSpinner();\r\n    this.loading = true;\r\n    this.pagination.offset = +event - 1;\r\n    this.pagination.page = event;\r\n    this.fetchCostCentersList();\r\n  }\r\n\r\n  public isRequiredError(FContorl: string): (boolean | undefined) {\r\n    return (\r\n      this.costCenterFormGroup.get(FContorl)?.hasError('required')\r\n      && (this.costCenterFormGroup.get(FContorl)?.touched || this.isSubmitted)\r\n    );\r\n  }\r\n\r\n  public deleteCostCenter(costCenterId: number) {\r\n    Swal.fire({\r\n      title: this.translateService.instant('SWAL.CONFIRMATION'),\r\n      text: this.translateService.instant('SWAL.COST_CENTER_DELETE_CONFIRM'),\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#3085d6',\r\n      cancelButtonColor: '#d33',\r\n      confirmButtonText: this.translateService.instant('SWAL.DELETE_BUTTON'),\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.spinnerService.startSpinner();\r\n        this.companiesService.deleteCostCenterById(costCenterId)\r\n          .pipe(finalize(() => {\r\n            this.spinnerService.stopSpinner();\r\n          })).subscribe({\r\n            next: () => {\r\n              Swal.fire(\r\n                this.translateService.instant('SWAL.COST_CENTER_DELETE_TITLE'),\r\n                this.translateService.instant('SWAL.COST_CENTER_DELETE_SUCCESS'),\r\n                'success'\r\n              )\r\n              this.fetchCostCentersList();\r\n            },\r\n            error: (error) => {\r\n              Swal.fire({\r\n                icon: 'error',\r\n                title: this.translateService.instant('SWAL.OOPS'),\r\n                text: error.message\r\n              })\r\n            }\r\n          })\r\n      }\r\n    })\r\n  }\r\n\r\n  /**\r\n   * Open modal in edit or create new cost center mode.\r\n   * @param isEditMode \r\n   * @param editCostCenterId \r\n   * @returns \r\n   */\r\n  public async openCostCenterEditorModal(isEditMode: boolean = false, editCostCenterId?: number) {\r\n    this.departmentHead = null;\r\n    this.sectionHead = null;\r\n    this.isSubmitted = false;\r\n    this.isEditMode = isEditMode;\r\n    this.isMulti = false;\r\n    this.hideForm = true;\r\n\r\n    this.cdr.detectChanges();\r\n    this.costCenterEditorModalConfig.modalTitle = isEditMode ? this.translateService.instant('MENU.UPDATE_COST_CENTER') : this.translateService.instant('MENU.ADD_COST_CENTER');\r\n\r\n    this.costCenterEditorModalConfig.dismissButtonLabel = isEditMode ? this.translateService.instant('FORM.BUTTON.UPDATE_BUTTON') : this.translateService.instant('FORM.BUTTON.ADD_BUTTON');\r\n\r\n    if (isEditMode && editCostCenterId) {\r\n      const currentCostCenter = this.costCenters.find(costCenter => costCenter.id === editCostCenterId);\r\n      if (currentCostCenter) {\r\n        const { name, code, departmentHead, operating, sectionHead = [] } = currentCostCenter;\r\n        this.costCenterFormGroup.patchValue({ name, code, departmentHead, operating });\r\n\r\n        console.log(sectionHead);\r\n\r\n        if (sectionHead && sectionHead?.length) {\r\n          sectionHead?.forEach((section: any) => {\r\n            this.sections.push(this.getSectionControlsDefault(section));\r\n            this.cdr.detectChanges();\r\n          });\r\n        }\r\n\r\n        this.sectionExist();\r\n\r\n        this.departmentHead = departmentHead;\r\n      }\r\n      this.editCostCenterId = editCostCenterId;\r\n    } else {\r\n      this.formReset();\r\n    }\r\n\r\n    this.hideForm = false;\r\n    this.cdr.detectChanges();\r\n    return await this.costCenterEditorModal.open();\r\n  }\r\n\r\n  public costCenterHeadAdded(userData: UserModel, controllerName: string) {\r\n    if (userData) {\r\n      this.costCenterFormGroup.patchValue({ [controllerName]: userData });\r\n    } else {\r\n      this.costCenterFormGroup.patchValue({ [controllerName]: '' });\r\n    }\r\n  }\r\n\r\n  public costCenterSectionHeadAdded(userData: UserModel, index: number) {\r\n    this.sections.at(index).patchValue({\r\n      user: userData ? userData : ''\r\n    });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  getSectionHeadUser(index: number) {\r\n    return this.sections.at(index).getRawValue()?.user;\r\n  }\r\n\r\n  private getCostCenterRequestPayload() {\r\n    const requestPayload: CreateCostCenterRequest = {\r\n      companyCodeId: +this.selectedCompanyCode.id,\r\n      code: this.costCenterFormGroup.get('code')?.value,\r\n      name: this.costCenterFormGroup.get('name')?.value,\r\n      operating: (this.costCenterFormGroup.get('operating')?.value == 'true' || this.costCenterFormGroup.get('operating')?.value == true),\r\n      departmentHead: this.costCenterFormGroup.get('departmentHead')?.value,\r\n      sectionHead: this.costCenterFormGroup.get('sectionHead')?.value\r\n    };\r\n    return requestPayload;\r\n  }\r\n\r\n\r\n  /**\r\n   * Create new cost center for the company.\r\n   */\r\n  private createNewCostCenter() {\r\n    this.isSubmitted = true;\r\n    this.cdr.detectChanges();\r\n\r\n    this.sectionExist();\r\n\r\n    console.log(this.costCenterFormGroup);\r\n\r\n\r\n    if (this.costCenterFormGroup.valid) {\r\n      this.spinnerService.startSpinner();\r\n      this.companiesService.createCostCenter(this.getCostCenterRequestPayload())\r\n        .pipe(finalize(() => {\r\n          this.spinnerService.stopSpinner();\r\n        }))\r\n        .subscribe({\r\n          next: () => {\r\n            this.formReset();\r\n            Swal.fire(\r\n              this.translateService.instant('SWAL.SUCCESS'),\r\n              this.translateService.instant('SWAL.ADD_COST_CENTER_CODE_SUCCESS'),\r\n              'success'\r\n            );\r\n            this.costCenterEditorModal.close();\r\n            this.fetchCostCentersList();\r\n            this.isSubmitted = false;\r\n            this.cdr.detectChanges();\r\n          },\r\n          error: (error) => {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: this.translateService.instant('SWAL.ADD_COST_CENTER_CODE_ERROR'),\r\n              text: error.message,\r\n            })\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update the cost center details.\r\n   */\r\n  private updateCostCenter() {\r\n    this.isSubmitted = true;\r\n    this.cdr.detectChanges();\r\n\r\n    this.sectionExist();\r\n    console.log(this.costCenterFormGroup);\r\n\r\n    if (this.costCenterFormGroup?.valid && this.selectedCompanyCode?.id) {\r\n      this.spinnerService.startSpinner();\r\n      this.companiesService.updateCostCenter({ id: +this.editCostCenterId, ...this.getCostCenterRequestPayload() })\r\n        .pipe(finalize(() => {\r\n          this.spinnerService.stopSpinner();\r\n        }))\r\n        .subscribe({\r\n          next: () => {\r\n            Swal.fire(\r\n              this.translateService.instant('SWAL.SUCCESS'),\r\n              this.translateService.instant('SWAL.UPDATE_COST_CENTER_CODE_SUCCESS'),\r\n              'success'\r\n            );\r\n            this.costCenterEditorModal.close();\r\n            this.fetchCostCentersList();\r\n            this.isSubmitted = false;\r\n            this.cdr.detectChanges();\r\n          },\r\n          error: (error) => {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: this.translateService.instant('SWAL.UPDATE_COST_CENTER_CODE_ERROR'),\r\n              text: error.message,\r\n            })\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  public exportToExcelSheet() {\r\n    this.toggleOpen();\r\n    this.reportService.downloadCostCenterExcelReport(toNumber(this.selectedCompanyCode.id));\r\n  }\r\n\r\n  public exportBlankFormat() {\r\n    this.reportService.downloadCostCenterExcelReport(toNumber(0));\r\n  }\r\n\r\n  addBufferAttachment(bufferData: Uint8Array) {\r\n    this.bufferData = bufferData;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  changeAddOption(type: string) {\r\n    this.isSubmitted = false;\r\n    if (!this.isMulti) {\r\n      this.initForm();\r\n    }\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  uploadMultipleCostCenter() {\r\n\r\n    if (this.selectedCompanyCode.id && this.bufferData) {\r\n      this.spinnerService.startSpinner();\r\n\r\n      this.companiesService.importCostCenter({\r\n        companyCodeId: toNumber(this.selectedCompanyCode.id),\r\n        bufferData: this.bufferData as Uint8Array\r\n      }).subscribe({\r\n        next: (response) => {\r\n          console.log(response);\r\n          this.costCenterEditorModal.close();\r\n          Swal.fire(\r\n            this.translateService.instant('SWAL.SUCCESS'),\r\n            this.translateService.instant('SWAL.IMPORT_COST_CENTER_SUCCESS'),\r\n            'success'\r\n          );\r\n          this.fetchCostCentersList();\r\n        },\r\n        error: (err) => {\r\n          console.log(err);\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: this.translateService.instant('SWAL.OOPS'),\r\n            html: err.message\r\n          })\r\n          this.spinnerService.stopSpinner();\r\n        }\r\n      })\r\n    }\r\n\r\n  }\r\n\r\n  openHistoryModel(costCenterId: number) {\r\n    this.spinnerService.startSpinner();\r\n\r\n    this.companiesService.getCostCenterHistory(costCenterId).subscribe({\r\n      next: (response) => {\r\n        this.companyHistory = response as HistoryResponse[];\r\n        this.cdr.detectChanges();\r\n        this.historyModalComponent.open();\r\n        this.spinnerService.stopSpinner();\r\n\r\n      },\r\n      error: (err) => {\r\n        console.log(err);\r\n        this.companyHistory = [] as HistoryResponse[];\r\n        this.cdr.detectChanges();\r\n        this.historyModalComponent.open();\r\n        this.spinnerService.stopSpinner();\r\n\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: this.translateService.instant('SWAL.ERROR'),\r\n          text: this.translateService.instant('SWAL.ERROR_HISTORY_FETCH')\r\n        })\r\n      }\r\n    })\r\n\r\n  }\r\n}\r\n", "<ng-container *ngIf=\"!loading; else loadingPage\">\r\n  <div\r\n    *ngIf=\"costCenters.length > 0; else noDataMessage\"\r\n    class=\"card mb-5 mb-xl-8\"\r\n  >\r\n    <!-- begin::Header -->\r\n    <div class=\"card-header border-0 pt-5\">\r\n      <h3 class=\"card-title align-items-start flex-column\">\r\n        <span class=\"card-label fw-bolder fs-3 mb-1\">{{\r\n          \"MENU.COST_CENTER_LIST\" | translate\r\n        }}</span>\r\n      </h3>\r\n\r\n      <div class=\"row\">\r\n        <div class=\"d-flex justify-content-end\">\r\n          <button\r\n            appToggleProfileMenu\r\n            [transform]=\"'-30px, 50.5px, 0px'\"\r\n            type=\"button\"\r\n            class=\"btn btn-sm btn-icon btn-color-primary btn-active-light-primary\"\r\n          >\r\n            <img\r\n              width=\"45px\"\r\n              src=\"./assets/media/svg/icons/dpw-icons/menu.png\"\r\n              alt=\"next\"\r\n            />\r\n          </button>\r\n          <span\r\n            class=\"fs-6 fw-bold menu menu-column menu-gray-600 menu-rounded menu-state-bg menu-state-primary menu-sub menu-sub-dropdown py-4 w-275px\"\r\n          >\r\n            <div class=\"menu-item px-3\">\r\n              <div\r\n                class=\"menu-content fs-6 text-dark fw-bolder px-3 py-4\"\r\n                translate=\"MENU.QUICK_ACTION\"\r\n              ></div>\r\n            </div>\r\n\r\n            <div class=\"separator mb-3 opacity-75\"></div>\r\n\r\n            <div class=\"menu-item px-3\">\r\n              <a\r\n                title=\"{{ 'FORM.BUTTON.ADD_COST_CENTER_BUTTON' | translate }}\"\r\n                (click)=\"openCostCenterEditorModal()\"\r\n                class=\"menu-link px-3 cursor-pointer\"\r\n                translate=\"{{\r\n                  'FORM.BUTTON.ADD_COST_CENTER_BUTTON' | translate\r\n                }}\"\r\n              >\r\n              </a>\r\n            </div>\r\n\r\n            <div class=\"menu-item px-3 mb-2\">\r\n              <a\r\n                title=\"{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}\"\r\n                (click)=\"exportToExcelSheet()\"\r\n                class=\"menu-link px-3 cursor-pointer\"\r\n                translate=\"{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}\"\r\n              >\r\n              </a>\r\n            </div>\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- \r\n            <div>\r\n                <button type=\"button\" title=\"{{ 'FORM.BUTTON.ADD_BUTTON' | translate }}\"\r\n                    (click)=\"openCostCenterEditorModal()\" class=\"btn btn-sm btn-primary mx-2\"\r\n                    translate=\"{{ 'FORM.BUTTON.ADD_BUTTON' | translate }}\">\r\n                </button>\r\n                <button\r\n                    type=\"button\"\r\n                    title=\"{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}\"\r\n                    class=\"btn btn-sm btn-primary mx-2\"\r\n                    translate=\"{{ 'FORM.BUTTON.EXPORT_TO_EXCEL' | translate }}\"\r\n                    (click)=\"exportToExcelSheet()\"\r\n                ></button>\r\n            </div> -->\r\n    </div>\r\n    <!-- end::Header -->\r\n    <!-- begin::Body -->\r\n    <div class=\"card-body py-3\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 align-middle\">\r\n          <div\r\n            class=\"row solid-hr fw-bolder text-muted font-size-10 py-3 d-md-flex d-none my-1 mx-2 bg-light\"\r\n          >\r\n            <div class=\"col-md-3\">\r\n              {{ \"LIST.NAME\" | translate }}\r\n            </div>\r\n            <div class=\"col-md-2\">\r\n              {{ \"LIST.CODE\" | translate }}\r\n            </div>\r\n            <div class=\"col-md-2\">\r\n              {{ \"FORM.LABEL.SECTIONS\" | translate }}\r\n            </div>\r\n            <div class=\"col-md-2\">\r\n              {{ \"LIST.DEPARTMENT_HEAD\" | translate }}\r\n            </div>\r\n            <div class=\"col-md-2\">\r\n              {{ \"LIST.UPDATED_AT\" | translate }}\r\n            </div>\r\n            <div class=\"col-md-1 text-center\">\r\n              {{ \"LIST.ACTION\" | translate }}\r\n            </div>\r\n          </div>\r\n          <ng-container\r\n            *ngFor=\"\r\n              let costCenter of costCenters\r\n                | paginate\r\n                  : {\r\n                      id: 'costCenterList',\r\n                      itemsPerPage: pagination.limit,\r\n                      currentPage: pagination.page,\r\n                      totalItems: totalRecords\r\n                    };\r\n              let i = index\r\n            \"\r\n          >\r\n            <div\r\n              class=\"row font-size-14 my-2 my-md-1 py-3 card-body p-5 solid-hr note-box-sm align-items-center\"\r\n            >\r\n              <div class=\"col-md-3\">\r\n                <div class=\"d-md-none mt-3\">\r\n                  {{ \"LIST.NAME\" | translate }}\r\n                </div>\r\n                <div class=\"text-none\">\r\n                  <a\r\n                    class=\"d-value text-dark fw-bolder text-hover-primary fs-8\"\r\n                  >\r\n                    {{ costCenter.name }}\r\n                  </a>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"col-md-2\">\r\n                <div class=\"d-md-none mt-3\">\r\n                  {{ \"LIST.CODE\" | translate }}\r\n                </div>\r\n                <div class=\"text-none\">\r\n                  {{ costCenter.code }}\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"col-md-2\">\r\n                <div class=\"d-md-none mt-3\">\r\n                  {{ \"FORM.LABEL.SECTIONS\" | translate }}\r\n                </div>\r\n                <div class=\"text-none\">\r\n                  <div class=\"text-none\">\r\n                    <span>\r\n                      {{\r\n                        costCenter.sectionHead.length\r\n                          ? costCenter.sectionHead[0].title\r\n                          : \"-\"\r\n                      }}\r\n                    </span>\r\n                    <span\r\n                      (click)=\"openCostCenterEditorModal(true, costCenter.id)\"\r\n                      *ngIf=\"costCenter.sectionHead.length >= 2\"\r\n                      class=\"badge badge-success ml-2 cursor-pointer\"\r\n                    >\r\n                      +{{ costCenter.sectionHead.length - 1 }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"col-md-2\">\r\n                <div class=\"d-md-none mt-3\">\r\n                  {{ \"LIST.DEPARTMENT_HEAD\" | translate }}\r\n                </div>\r\n                <div class=\"text-none\">\r\n                  {{ costCenter?.departmentHead?.displayName || \"-\" }}\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"col-md-2\">\r\n                <div class=\"d-md-none mt-3\">\r\n                  {{ \"LIST.UPDATED_AT\" | translate }}\r\n                </div>\r\n                <div class=\"text-none\">\r\n                  {{ costCenter.updatedOn | date : \"medium\" }}\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"col-md-1\">\r\n                <div class=\"row\">\r\n                  <div class=\"d-flex justify-content-center\">\r\n                    <button\r\n                      appToggleProfileMenu\r\n                      [transform]=\"'-30px, 50.5px, 0px'\"\r\n                      type=\"button\"\r\n                      class=\"btn btn-sm btn-icon btn-color-primary btn-active-light-primary\"\r\n                    >\r\n                      <img\r\n                        width=\"45px\"\r\n                        src=\"./assets/media/svg/icons/dpw-icons/menu.png\"\r\n                        alt=\"next\"\r\n                      />\r\n                    </button>\r\n                    <span\r\n                      class=\"fs-6 fw-bold menu menu-column menu-gray-600 menu-rounded menu-state-bg menu-state-primary menu-sub menu-sub-dropdown py-4 w-275px\"\r\n                    >\r\n                      <div class=\"menu-item px-3\">\r\n                        <div\r\n                          class=\"menu-content fs-6 text-dark fw-bolder px-3 py-4\"\r\n                          translate=\"MENU.QUICK_ACTION\"\r\n                        ></div>\r\n                      </div>\r\n\r\n                      <div class=\"separator mb-3 opacity-75\"></div>\r\n\r\n                      <div class=\"menu-item px-3\">\r\n                        <a\r\n                          title=\"{{ 'FORM.BUTTON.EDIT_BUTTON' | translate }}\"\r\n                          (click)=\"\r\n                            openCostCenterEditorModal(true, costCenter.id)\r\n                          \"\r\n                          translate=\"{{\r\n                            'FORM.BUTTON.EDIT_BUTTON' | translate\r\n                          }}\"\r\n                          class=\"menu-link px-3 cursor-pointer\"\r\n                        >\r\n                        </a>\r\n                      </div>\r\n\r\n                      <div class=\"menu-item px-3\">\r\n                        <a\r\n                          title=\"{{ 'COMMON.REMOVE' | translate }}\"\r\n                          (click)=\"deleteCostCenter(costCenter.id)\"\r\n                          translate=\"{{\r\n                            'WORKFLOW.RULE.BUTTON.DELETE' | translate\r\n                          }}\"\r\n                          class=\"menu-link px-3 cursor-pointer\"\r\n                        >\r\n                        </a>\r\n                      </div>\r\n\r\n                      <div class=\"menu-item px-3\">\r\n                        <a\r\n                          title=\"{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}\"\r\n                          (click)=\"openHistoryModel(costCenter.id)\"\r\n                          translate=\"{{\r\n                            'FORM.BUTTON.HISTORY_BUTTON' | translate\r\n                          }}\"\r\n                          class=\"menu-link px-3 cursor-pointer\"\r\n                        >\r\n                        </a>\r\n                      </div>\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- <div class=\"d-flex justify-content-center ml-5\">\r\n                                    <div class=\"outline-btn-light\">\r\n                                        <button type=\"button\" title=\"{{ 'COMMON.REMOVE' | translate }}\"\r\n                                        class=\"btn btn-sm outline-btn-light mx-2\"\r\n                                        (click)=\"deleteCostCenter(costCenter.id)\"\r\n                                        translate=\"{{ 'WORKFLOW.RULE.BUTTON.DELETE' | translate }}\"></button>\r\n                                    </div>\r\n                                    <button type=\"button\" title=\"{{ 'FORM.BUTTON.EDIT_BUTTON' | translate }}\"\r\n                                        class=\"btn btn-sm btn-primary mx-2\"\r\n                                        (click)=\"openCostCenterEditorModal(true, costCenter.id)\"\r\n                                        translate=\"{{ 'FORM.BUTTON.EDIT_BUTTON' | translate }}\">\r\n                                    </button>\r\n                                </div> -->\r\n              </div>\r\n            </div>\r\n          </ng-container>\r\n        </div>\r\n        <div class=\"col-md-12 text-end\">\r\n          <div class=\"separator separator-dashed separator-border-1 my-5\"></div>\r\n\r\n          <div class=\"d-flex justify-content-between\">\r\n            <div class=\"fw-bold\">\r\n              {{ \"COMMON.SHOWING\" | translate }}\r\n              {{\r\n                pagination.page === 1\r\n                  ? 1\r\n                  : (pagination.page - 1) * pagination.limit + 1\r\n              }}\r\n              {{ \"COMMON.TO\" | translate }}\r\n              {{\r\n                pagination.limit * pagination.page <= totalRecords\r\n                  ? pagination.limit * pagination.page\r\n                  : totalRecords\r\n              }}\r\n              {{ \"COMMON.RECORD\" | translate | lowercase }}\r\n              {{ \"COMMON.OF\" | translate }} {{ totalRecords }}\r\n            </div>\r\n\r\n            <pagination-controls\r\n              id=\"costCenterList\"\r\n              previousLabel=\"Prev\"\r\n              nextLabel=\"Next\"\r\n              [responsive]=\"true\"\r\n              (pageChange)=\"handlePageChange($event)\"\r\n            ></pagination-controls>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- begin::Body -->\r\n  </div>\r\n\r\n  <ng-template #noDataMessage>\r\n    <app-empty-state\r\n      [message]=\"'EMPTY_STATE.EMPTY_COST_CENTER_LIST' | translate\"\r\n    >\r\n      <span class=\"action position-relative d-inline-block text-danger\">\r\n        <a\r\n          class=\"text-danger opacity-75-hover cursor-pointer\"\r\n          (click)=\"openCostCenterEditorModal()\"\r\n        >\r\n          {{ \"FORM.LABEL.SETUP_COST_CENTER\" | translate }}\r\n        </a>\r\n        <span\r\n          class=\"position-absolute opacity-15 bottom-0 start-0 border-4 border-danger border-bottom w-100\"\r\n        >\r\n        </span>\r\n      </span>\r\n    </app-empty-state>\r\n  </ng-template>\r\n</ng-container>\r\n\r\n<ng-template #loadingPage>\r\n  <app-list-skeleton-loader [loaderCount]=\"4\"></app-list-skeleton-loader>\r\n</ng-template>\r\n\r\n<app-modal #costCenterEditorModal [modalConfig]=\"costCenterEditorModalConfig\">\r\n  <div\r\n    *ngIf=\"!hideForm\"\r\n    class=\"w-100 px-5 bg-body rounded\"\r\n    [formGroup]=\"costCenterFormGroup\"\r\n  >\r\n    <div class=\"row\">\r\n      <div class=\"col-lg-6 col-md-6 col-6 mb-6 mb-lg-5\">\r\n        <label\r\n          class=\"fw-bold text-muted\"\r\n          translate=\"FORM.LABEL.COMPANY_FUSSION_NUMBER\"\r\n        ></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          {{ selectedCompanyCode.code }}\r\n        </div>\r\n      </div>\r\n      <div class=\"col-lg-6 col-md-6 col-6 mb-6 mb-lg-5\">\r\n        <label class=\"fw-bold text-muted\" translate=\"LIST.ENTITY_CODE\"></label>\r\n        <div class=\"h4 text-gray-800\">\r\n          {{ selectedCompanyCode.entityCode }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"separator my-2\"></div>\r\n\r\n    <div class=\"row m-3\" *ngIf=\"!isEditMode\">\r\n      <div class=\"form-check form-check-inline col\">\r\n        <input\r\n          [(ngModel)]=\"isMulti\"\r\n          (click)=\"changeAddOption('manual')\"\r\n          [ngModelOptions]=\"{ standalone: true }\"\r\n          class=\"form-check-input success\"\r\n          name=\"addAnalysisCode\"\r\n          type=\"radio\"\r\n          id=\"singleAddition\"\r\n          [value]=\"false\"\r\n        />\r\n        <label\r\n          class=\"form-check-label fw-bold\"\r\n          for=\"singleAddition\"\r\n          translate=\"COMMON.ADD_MANUALLY\"\r\n        ></label>\r\n      </div>\r\n      <div class=\"form-check form-check-inline col\">\r\n        <input\r\n          [(ngModel)]=\"isMulti\"\r\n          (click)=\"changeAddOption('import')\"\r\n          [ngModelOptions]=\"{ standalone: true }\"\r\n          class=\"form-check-input danger\"\r\n          name=\"addAnalysisCode\"\r\n          type=\"radio\"\r\n          id=\"multiAddition\"\r\n          [value]=\"true\"\r\n        />\r\n        <label\r\n          class=\"form-check-label fw-bold\"\r\n          for=\"multiAddition\"\r\n          translate=\"COMMON.IMPORT\"\r\n        ></label>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"!isMulti\">\r\n      <div class=\"row\">\r\n        <div class=\"col-md-12 mb-5\">\r\n          <label class=\"form-label required\">\r\n            {{ \"FORM.PLACEHOLDER.ENTER\" | translate }}\r\n            {{ \"FORM.LABEL.NAME\" | translate }}\r\n          </label>\r\n          <input\r\n            name=\"title\"\r\n            placeholder=\"{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{\r\n              'FORM.LABEL.NAME' | translate | lowercase\r\n            }}\"\r\n            class=\"form-control form-control-lg form-control-solid\"\r\n            formControlName=\"name\"\r\n          />\r\n          <ng-container *ngIf=\"isRequiredError('name')\">\r\n            <app-input-error-message\r\n              errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\"\r\n            >\r\n            </app-input-error-message>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6 mb-5\">\r\n          <label class=\"form-label required\">\r\n            {{ \"FORM.PLACEHOLDER.ENTER\" | translate }}\r\n            {{ \"FORM.LABEL.COST_CENTER_CODE\" | translate }}\r\n          </label>\r\n          <input\r\n            name=\"title\"\r\n            placeholder=\"{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{\r\n              'FORM.LABEL.COST_CENTER_CODE' | translate | lowercase\r\n            }}\"\r\n            class=\"form-control form-control-lg form-control-solid\"\r\n            pattern=\"[a-zA-Z0-9]{4}\"\r\n            formControlName=\"code\"\r\n          />\r\n          <ng-container *ngIf=\"isRequiredError('code')\">\r\n            <app-input-error-message\r\n              errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\"\r\n            >\r\n            </app-input-error-message>\r\n          </ng-container>\r\n          <ng-container *ngIf=\"isPatternError('code')\">\r\n            <app-input-error-message\r\n              errorMessage=\"{{\r\n                'FORM.VALIDATION.LENGTH_ERROR'\r\n                  | translate\r\n                    : { name: 'FORM.LABEL.CODE' | translate, length: 4 }\r\n              }}\"\r\n            >\r\n            </app-input-error-message>\r\n          </ng-container>\r\n        </div>\r\n        <div class=\"col-md-6 mb-5\">\r\n          <label\r\n            class=\"form-label required\"\r\n            translate=\"FORM.LABEL.IS_COST_CENTER_OPERATING\"\r\n          >\r\n          </label>\r\n          <select\r\n            name=\"operating\"\r\n            formControlName=\"operating\"\r\n            class=\"form-select form-select-lg form-select-solid\"\r\n          >\r\n            <option value=\"null\">\r\n              {{ \"FORM.PLACEHOLDER.SELECT\" | translate }}\r\n              {{ \"LIST.COST_CENTER_OPERATING\" | translate }}\r\n            </option>\r\n            <option value=\"false\">No</option>\r\n            <option value=\"true\">Yes</option>\r\n          </select>\r\n          <ng-container *ngIf=\"isRequiredError('operating')\">\r\n            <app-input-error-message\r\n              errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\"\r\n            >\r\n            </app-input-error-message>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6 mb-5\">\r\n          <label\r\n            class=\"form-label required\"\r\n            translate=\"FORM.LABEL.DEPARTMENT_HEAD\"\r\n          >\r\n          </label>\r\n          <app-ad-user-search\r\n            placeholder=\"{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{\r\n              'FORM.LABEL.DEPARTMENT_HEAD' | translate | lowercase\r\n            }}\"\r\n            [isMultiSelect]=\"false\"\r\n            (userSelected)=\"costCenterHeadAdded($event, 'departmentHead')\"\r\n            [defaultUsers]=\"departmentHead\"\r\n          >\r\n          </app-ad-user-search>\r\n          <ng-container *ngIf=\"isRequiredError('departmentHead')\">\r\n            <app-input-error-message\r\n              errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\"\r\n            >\r\n            </app-input-error-message>\r\n          </ng-container>\r\n        </div>\r\n        <!--\r\n                    <div class=\"col-md-6 mb-5\">\r\n                        <label class=\"form-label required\" translate=\"FORM.LABEL.SECTION_HEAD\">\r\n                        </label>\r\n                        <app-ad-user-search placeholder=\"{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{\r\n                            'FORM.LABEL.SECTION_HEAD' | translate | lowercase\r\n                            }}\" [isMultiSelect]=\"false\" (userSelected)=\"costCenterHeadAdded($event, 'sectionHead')\"\r\n                            [defaultUsers]=\"sectionHead\">\r\n                        </app-ad-user-search>\r\n        \r\n                        <ng-container *ngIf=\"isRequiredError('sectionHead')\">\r\n                            <app-input-error-message errorMessage=\"{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}\">\r\n                            </app-input-error-message>\r\n                        </ng-container>\r\n                    </div>\r\n                -->\r\n        <div></div>\r\n\r\n        <div class=\"separator my-2\" *ngIf=\"sections?.controls?.length\"></div>\r\n\r\n        <div formArrayName=\"sectionHead\" class=\"row\">\r\n          <h5\r\n            translate=\"FORM.LABEL.SECTIONS\"\r\n            *ngIf=\"sections?.controls?.length\"\r\n            class=\"fw-bold d-flex align-items-center\"\r\n          ></h5>\r\n          <ng-container\r\n            *ngFor=\"let section of sections.controls; let i = index\"\r\n            [formGroupName]=\"i\"\r\n          >\r\n            <div *ngIf=\"i\" class=\"separator mb-3 opacity-75\">\r\n\r\n            </div>\r\n\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6 mb-5\">\r\n                <label class=\"form-label required\">\r\n                  {{ \"FORM.LABEL.NAME\" | translate }}\r\n                </label>\r\n                <input\r\n                  (input)=\"sectionExist()\"\r\n                  name=\"title\"\r\n                  placeholder=\"{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{\r\n                    'FORM.LABEL.NAME' | translate | lowercase\r\n                  }}\"\r\n                  class=\"form-control form-control-lg form-control-solid\"\r\n                  formControlName=\"title\"\r\n                />\r\n                <ng-container\r\n                  *ngIf=\"\r\n                    section.get('title')?.hasError('required') &&\r\n                    (isSubmitted || section.get('title')?.touched)\r\n                  \"\r\n                >\r\n                  <app-input-error-message\r\n                    errorMessage=\"{{\r\n                      'FORM.VALIDATION.REQUIRED_FIELD' | translate\r\n                    }}\"\r\n                  >\r\n                  </app-input-error-message>\r\n                </ng-container>\r\n\r\n                <ng-container\r\n                  *ngIf=\"\r\n                    section.get('title')?.hasError('alreadyExist') &&\r\n                    (isSubmitted || section.get('title')?.touched)\r\n                  \"\r\n                >\r\n                  <app-input-error-message\r\n                    errorMessage=\"{{\r\n                      'FORM.VALIDATION.NAME_ALREADY_EXIST' | translate\r\n                    }}\"\r\n                  >\r\n                  </app-input-error-message>\r\n                </ng-container>\r\n              </div>\r\n\r\n              <!-- Section Code START -->\r\n              <div class=\"col-md-6 mb-5\">\r\n                <label class=\"form-label\">\r\n                  {{ \"FORM.LABEL.CODE\" | translate }}\r\n                </label>\r\n                <input\r\n                  name=\"code\"\r\n                  placeholder=\"{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{\r\n                    'FORM.LABEL.CODE' | translate | lowercase\r\n                  }}\"\r\n                  class=\"form-control form-control-lg form-control-solid\"\r\n                  formControlName=\"code\"\r\n                />\r\n                <!-- <ng-container\r\n                                    *ngIf=\"\r\n                                        section\r\n                                        .get('code')\r\n                                        ?.hasError('required') &&\r\n                                        (isSubmitted ||\r\n                                        section.get('code')?.touched)\r\n                                    \"\r\n                                    >\r\n                                    <app-input-error-message\r\n                                        errorMessage=\"{{\r\n                                        'FORM.VALIDATION.REQUIRED_FIELD' | translate\r\n                                        }}\"\r\n                                    >\r\n                                    </app-input-error-message>\r\n                                </ng-container>\r\n\r\n                                <ng-container\r\n                                    *ngIf=\"\r\n                                        section\r\n                                        .get('code')\r\n                                        ?.hasError('alreadyExist') &&\r\n                                        (isSubmitted ||\r\n                                        section.get('code')?.touched)\r\n                                    \"\r\n                                    >\r\n                                    <app-input-error-message\r\n                                        errorMessage=\"{{\r\n                                        'FORM.VALIDATION.CODE_ALREADY_EXIST' | translate\r\n                                        }}\"\r\n                                    >\r\n                                    </app-input-error-message>\r\n                                </ng-container> -->\r\n              </div>\r\n              <!-- Section Code END -->\r\n\r\n              <div class=\"col-md-11 mb-5\">\r\n                <label class=\"form-label required\" translate=\"FORM.LABEL.USER\">\r\n                </label>\r\n                <app-ad-user-search\r\n                  placeholder=\"{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{\r\n                    'FORM.LABEL.USER' | translate | lowercase\r\n                  }}\"\r\n                  [isMultiSelect]=\"false\"\r\n                  (userSelected)=\"costCenterSectionHeadAdded($event, i)\"\r\n                  [defaultUsers]=\"getSectionHeadUser(i)\"\r\n                >\r\n                </app-ad-user-search>\r\n                <ng-container\r\n                  *ngIf=\"\r\n                    section.get('user')?.hasError('required') &&\r\n                    (isSubmitted || section.get('user')?.touched)\r\n                  \"\r\n                >\r\n                  <app-input-error-message\r\n                    errorMessage=\"{{\r\n                      'FORM.VALIDATION.REQUIRED_FIELD' | translate\r\n                    }}\"\r\n                  >\r\n                  </app-input-error-message>\r\n                </ng-container>\r\n              </div>\r\n              <div\r\n                (click)=\"removeSection(i)\"\r\n                class=\"col-md-1 cursor-pointer mt-5 mb-5 d-flex justify-content-center align-items-center\"\r\n              >\r\n                <img\r\n                  width=\"32px\"\r\n                  src=\"./assets/media/svg/icons/dpw-icons/cross2.png\"\r\n                  alt=\"Remove\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </ng-container>\r\n          <div class=\"d-flex justify-content-end pt-4\">\r\n            <button\r\n              (click)=\"addNewSection()\"\r\n              type=\"button\"\r\n              class=\"btn btn-sm btn-primary ps-4 pe-4 py-1\"\r\n            >\r\n              <img\r\n                width=\"12px\"\r\n                src=\"./assets/media/svg/icons/dpw-icons/plus.png\"\r\n                alt=\"Next\"\r\n              />\r\n              <span class=\"px-1 indicator-label\">\r\n                {{ \"FORM.BUTTON.ADD_BUTTON\" | translate }}\r\n                {{ \"FORM.LABEL.SECTION\" | translate }}\r\n              </span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-container>\r\n\r\n    <div class=\"row\" *ngIf=\"isMulti\">\r\n      <app-add-attachment\r\n        [formatMessage]=\"'COMMON.COST_CENTER_SECTION_FORMAT'\"\r\n        [isSubmitted]=\"isSubmitted\"\r\n        [labelTitle]=\"'FORM.LABEL.UPLOAD_COST_CENTER'\"\r\n        [data]=\"{ isDescriptionRequired: false }\"\r\n        (bufferAttachment)=\"addBufferAttachment($event)\"\r\n      ></app-add-attachment>\r\n\r\n      <div class=\"d-flex justify-content-end\">\r\n        <button\r\n          type=\"button\"\r\n          title=\"{{ 'FORM.BUTTON.EXPORT_FORMAT' | translate }}\"\r\n          class=\"btn btn-sm btn-primary mx-2\"\r\n          translate=\"{{ 'FORM.BUTTON.EXPORT_FORMAT' | translate }}\"\r\n          (click)=\"exportBlankFormat()\"\r\n        ></button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <ng-template #loadingPage>\r\n    <app-form-skeleton-loader [loaderCount]=\"2\"></app-form-skeleton-loader>\r\n  </ng-template>\r\n</app-modal>\r\n\r\n<app-modal #historyModal [modalConfig]=\"historyModalConfig\">\r\n  <div class=\"w-100 px-1 bg-body rounded py-1\">\r\n    <app-history-logs\r\n      [title]=\"'FORM.BUTTON.COMPANY_HISTORY_BUTTON' | translate\"\r\n      [historyLogs]=\"companyHistory\"\r\n    >\r\n    </app-history-logs>\r\n  </div>\r\n</app-modal>\r\n"]}, "metadata": {}, "sourceType": "module"}