import { Pagination } from 'src/core/pagination';
import { CurrentContext, NotificationPayload } from 'src/shared/types';
import { NotificationResponseDto } from '../dtos';
import { NotificationRepository } from '../repositories';
export declare class NotificationService {
    private readonly notificationRepository;
    constructor(notificationRepository: NotificationRepository);
    createNotification(notificationPayload: NotificationPayload, currentContext: CurrentContext): Promise<NotificationResponseDto>;
    getNotificationsListForUser(currentContext: CurrentContext, limit?: number, page?: number): Promise<Pagination<NotificationResponseDto>>;
    notificationsViewedByUser(notificationsIds: number[], currentContext: CurrentContext): Promise<void>;
}
