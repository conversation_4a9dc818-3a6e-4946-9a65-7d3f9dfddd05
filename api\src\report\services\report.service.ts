import { Injectable } from '@nestjs/common';
import _, { toNumber } from 'lodash';
import { AfeProposalRepository } from 'src/afe-proposal/repositories';
import { AnalysisCodeRepository, CostCenterRepository, NaturalAccountNumberRepository } from 'src/finance/repositories';
import { AD_USER_TYPE, HttpStatus, PERMISSIONS } from 'src/shared/enums';
import { SequlizeOperator, currentDateInDDMMYYYY, instanceToPlain } from 'src/shared/helpers';
import { AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING, AFE_REQUEST_TYPE_ID_WITH_FILE_NAME_MAPPING, BUDGET_TYPE_NAME_MAPPING, REQUEST_TYPE } from 'src/shared/mappings';
import { ExcelSheetService, SharedPermissionService } from 'src/shared/services';
import { CurrentContext } from 'src/shared/types';
import { AfeWorkflowReportFilterDto, AnalysisCodeReportResponseDto, NaturalAccountReportResponseDto } from '../dtos';
import { ExportSubmittedAfeRequestDto } from '../dtos/request/export-submitted-afe-request.dto';
import { CostCenterReportResponseDto } from '../dtos/response/cost-center-report-reponse.dto';
import { BusinessEntityService } from 'src/business-entity/services';
import { AdminApiClient } from 'src/shared/clients';
import { HttpException } from 'src/shared/exceptions';
import { WorkflowMasterSettingRepository } from 'src/workflow/repositories';
import { ASSOCIATED_TYPE } from 'src/shared/enums/associated-type.enum';

@Injectable()
export class ReportService {
    constructor(
        private readonly excelSheetService: ExcelSheetService,
        private readonly afeProposalRepository: AfeProposalRepository,
        private readonly analysisCodeRepository: AnalysisCodeRepository,
        private readonly costCenterRepository: CostCenterRepository,
        private readonly naturalAccountNumberRepository: NaturalAccountNumberRepository,
        private readonly businessEntityService: BusinessEntityService,
        private readonly permissionService: SharedPermissionService,
        private readonly adminApiClient: AdminApiClient,
        private readonly workflowSettingRepository: WorkflowMasterSettingRepository,
        private readonly sequlizeOperator: SequlizeOperator
    ) { }

    /**
     * Generate excel report for submitted AFEs.
     * @param filters 
     * @param currentContext 
     * @returns 
     */
    public async downloadSubmittedAfesReport(exportSubmittedAfeRequestDto: ExportSubmittedAfeRequestDto, currentContext: CurrentContext): Promise<{ report: any, filename: string }> {
        const { filters, selectedColumns } = exportSubmittedAfeRequestDto;

        if (filters?.businessEntities?.length) {
            filters.businessEntities = await this.businessEntityService.getBusinessEntitiesChildIds(filters.businessEntities);
        }

        const SHEET_NAME = 'AFE List';
        const { user } = currentContext;
        const locations = await this.permissionService.getAllLocationIdForGivenPermission(
            user.unique_name,
            PERMISSIONS.AFE_VIEW,
        );
        const extraPermissions = await this.permissionService.getExtraPermissionObject(user.username);

        let reportData = await this.afeProposalRepository.getSubmittedAfesReportData(filters, user.username, locations?.length ? locations : [], extraPermissions);

        console.log('Report pre >>>>>>>> ');
        console.dir(reportData, { depth: 10 });

        console.log('selectedColumns >>>>>>>> ');
        console.dir(selectedColumns, { depth: 10 });

        if (selectedColumns?.length) {
            reportData = reportData.map((item) => {
                return Object.fromEntries(
                    Object.entries(item).filter(([key]) => selectedColumns.includes(key))
                );
            });
        }

        console.log('Report post >>>>>>>> ');

        console.dir(reportData, { depth: 10 });


        const report = await this.excelSheetService.createExcelSheet(reportData, SHEET_NAME);
        return { report, filename: SHEET_NAME };
    }

    public async downloadAfeRequestTypeLimitsReport() {
        //TODO: Create report data.
    }

    public async downloadBuLevelWorkflowReport(filter: AfeWorkflowReportFilterDto, currentContext: CurrentContext) {
        const { entityId, workflowMasterSettingId } = filter;
        const { username } = currentContext.user;

        const masterWorkflowDetail = await this.workflowSettingRepository.getMasterSettingWithoutStepById(workflowMasterSettingId);

        if (!masterWorkflowDetail) {
            throw new HttpException('Workflow setting is not available.', HttpStatus.NOT_FOUND);
        }

        if (masterWorkflowDetail.parentId) {
            throw new HttpException('The master workflow can only be selected.', HttpStatus.NOT_FOUND);
        }

        const buChildList = await this.adminApiClient.getAllBusinessHierarchyByUserAndPermission(username, PERMISSIONS.AFE_REPORTS, entityId, null);

        if (!buChildList) {
            throw new HttpException('No Business Unit available for selected entity.', HttpStatus.NOT_FOUND);
        }

        let businessUnitList = [];
        let isBuSelected = false;

        if (buChildList?.children?.length) {
            buChildList.children.forEach((childrenDetail) => {
                if (childrenDetail?.children?.length) {
                    throw new HttpException('It is not permitted to select an entity other than a region or a business unit.', HttpStatus.BAD_REQUEST);
                }
            });

            businessUnitList = buChildList.children
        } else {
            isBuSelected = true;
            businessUnitList.push(buChildList);
        }

        const buEntityIds = businessUnitList.map(businessUnit => toNumber(businessUnit.id));

        let businessUnitWorkflows: any = await this.workflowSettingRepository.getMasterFlowSettingWithStepsByCondition({
            entityId: this.sequlizeOperator.inOperator(buEntityIds),
            parentId: workflowMasterSettingId
        });

        let parentWorkflow = null;
        let parentEntityIds = await this.adminApiClient.getParentIdsOfEntity(entityId);

        if (businessUnitWorkflows.length !== buEntityIds.length) {
            if (isBuSelected) {
                parentEntityIds = parentEntityIds.filter(parentId => (toNumber(entityId) !== toNumber(parentId)));
            }

            parentEntityIds = parentEntityIds.reverse();

            const settingCondition = this.sequlizeOperator.orOperator({
                id: workflowMasterSettingId,
                parentId: workflowMasterSettingId
            })

            const parentWorkflows = await this.workflowSettingRepository.getMasterFlowSettingWithStepsByCondition({
                entityId: this.sequlizeOperator.inOperator(parentEntityIds),
                ...settingCondition
            });

            if (parentWorkflows?.length) {

                const immediateParentWorkflowEntityId = parentEntityIds.find((parentEntityId) => {
                    return parentWorkflows.find((parentWorkflowDetail) => {
                        return toNumber(parentWorkflowDetail.entityId) === parentEntityId
                    });
                });

                if (immediateParentWorkflowEntityId) {
                    parentWorkflow = parentWorkflows.find((parentWorkflowDetail) => {
                        return toNumber(parentWorkflowDetail.entityId) === immediateParentWorkflowEntityId
                    });
                }

                if (parentWorkflow) {
                    businessUnitList.forEach((businessUnitDetail) => {

                        const isEntityOverriddenAvailable = businessUnitWorkflows.find((businessUnitWorkflow) => {
                            return (toNumber(businessUnitWorkflow.entityId) === toNumber(businessUnitDetail.id));
                        });

                        if (!isEntityOverriddenAvailable) {
                            businessUnitWorkflows.push({
                                ...parentWorkflow,
                                entityCode: businessUnitDetail?.code || '',
                                entityTitle: businessUnitDetail?.full_name || businessUnitDetail?.short_name || '',
                                entityType: businessUnitDetail?.entity_type || '',
                                entityId: toNumber(businessUnitDetail.id),
                                isParentWorkflow: true,
                                immediateParentEntity: {
                                    entityCode: parentWorkflow.entityCode,
                                    entityTitle: parentWorkflow.entityTitle,
                                    entityId: parentWorkflow.entityId,
                                    entityType: parentWorkflow.entityType
                                }
                            });
                        }
                    });
                }
            }
        }

        if (!businessUnitWorkflows.length) {
            throw new HttpException('No workflow available for selected entity and setting.', HttpStatus.NOT_FOUND);
        }

        businessUnitWorkflows = businessUnitWorkflows.map((businessUnitWorkflow) => {
            const buEntityParentIds = parentEntityIds.filter(parentId => (toNumber(businessUnitWorkflow.entityId) !== toNumber(parentId)));
            return {
                ...businessUnitWorkflow,
                buEntityParentIds
            };
        });

        let uniqueRoles = [];

        let allEntityIdWithCostCenterStep = [];

        businessUnitWorkflows.forEach((businessUnitWorkflow) => {
            businessUnitWorkflow.workflowMasterStep.forEach((workflowMasterStep) => {
                if (workflowMasterStep.associateType === ASSOCIATED_TYPE.ROLE) {
                    uniqueRoles.push(workflowMasterStep.associateRole);
                }
                if (workflowMasterStep.associateType === ASSOCIATED_TYPE.COST_CENTER) {
                    allEntityIdWithCostCenterStep.push(businessUnitWorkflow.entityId);
                }
            });
        });

        let allRoleUsers = [];
        if (uniqueRoles.length) {
            uniqueRoles = Array.from(new Set(uniqueRoles));
            const roleUserListsPromises = uniqueRoles.map(role => this.adminApiClient.getRoleUserListByRoleName(role));
            const roleUsersArrays = await Promise.all(roleUserListsPromises);
            allRoleUsers = roleUsersArrays.flat();
        }

        let allWorkflowWithUserDetail = businessUnitWorkflows.map((businessUnitWorkflow) => {
            const workflowSteps = businessUnitWorkflow.workflowMasterStep.map((workflowMasterStep) => {
                if (workflowMasterStep.associateType === ASSOCIATED_TYPE.ROLE) {
                    const allEntityIds = [businessUnitWorkflow.entityId, ...businessUnitWorkflow.buEntityParentIds];

                    const roleUsers = allRoleUsers.filter((roleUserDetail) => allEntityIds.includes(toNumber(roleUserDetail.business_entity_id)) && roleUserDetail.role === workflowMasterStep.associateRole);

                    return { roleUsers, ...workflowMasterStep };
                } else {
                    return workflowMasterStep;
                }
            });
            return { ...businessUnitWorkflow, workflowMasterStep: workflowSteps };
        });

        // return {allRoleUsers, uniqueRoles, masterWorkflowDetail, allWorkflowWithUserDetail};

        // const getUsersByRoleOfAnEntity = await this.adminApiClient.getUsersByRoleOfAnEntity('DivisionalCOO', 0);
        // const getUsersByRoleOfAnEntityWithChild = await this.adminApiClient.getUsersByRoleOfAnEntityWithChild('DivisionalCOO', 0);

        //User Mapping Jobs for each Steps.

        const FILE_NAME = (masterWorkflowDetail?.requestType?.id ? (AFE_REQUEST_TYPE_ID_WITH_FILE_NAME_MAPPING[masterWorkflowDetail.requestType.id] + ' - ') : '') + (masterWorkflowDetail?.budgetType ? BUDGET_TYPE_NAME_MAPPING[masterWorkflowDetail.budgetType] + ' - ' : '') + (masterWorkflowDetail?.projectComponent?.title ? masterWorkflowDetail?.projectComponent?.title + ' - ' : '') + buChildList.full_name + ' (' + buChildList.entity_type + ' Level)';

        const workflowColumns = [
            'Sr. No.',
            'Business Unit Code',
            'Business Unit Name',
            'Step Title',
            'Step Role',
            'Step Type',
            'Current Approvers',
            'Rules',
            'Single Limit'
        ];

        if (masterWorkflowDetail.isAggregateLimitApplicable) {
            workflowColumns.push('Aggregate Limit')
        }

        // if(masterWorkflowDetail.isCommitmentLengthApplicable) {
        //     workflowColumns.push('Length Of Commitment')
        // }

        let headers = [];

        headers.push('Report For - ' + masterWorkflowDetail.requestType?.title + ' ' + (masterWorkflowDetail?.budgetType ? BUDGET_TYPE_NAME_MAPPING[masterWorkflowDetail.budgetType] : '') + ' ' + (masterWorkflowDetail?.projectComponent?.title || ' '));
        headers.push('\nEntity Selected - ' + buChildList.full_name + ' (' + buChildList.entity_type + ')');
        headers.push('\nWorkflow Year - ' + masterWorkflowDetail.year);
        headers.push('\nReport Date - ' + currentDateInDDMMYYYY());


        let allCostCenters = [];

        if (allEntityIdWithCostCenterStep && allEntityIdWithCostCenterStep.length) {
            const uniqueEnittyWithCostCenter = Array.from(new Set(allEntityIdWithCostCenterStep));
            allCostCenters = await this.costCenterRepository.getCostCentersWithCompanyCodeByEntityIds(uniqueEnittyWithCostCenter);
        }


        const report = await this.excelSheetService.downloadBuLevelWorkflow(workflowColumns, allWorkflowWithUserDetail, allCostCenters, headers);

        return { report, filename: FILE_NAME };
    }

    public async downloadAnalysisCode(companyCodeId: number) {
        //TODO: Create report data.
        const SHEET_NAME = 'Analysis Code';
        const FILE_NAME = 'analysis_code_' + (companyCodeId ? companyCodeId : 'format');

        const analysisCodes = await this.analysisCodeRepository.getAllAnalysisCodesListByCompanyCodeId(companyCodeId);

        const analysisCodeList = analysisCodes.map(d => instanceToPlain(new AnalysisCodeReportResponseDto(d), { excludeExtraneousValues: true }));

        let finalList = [];
        analysisCodeList.forEach((analysisCode) => {

            let requestTypes = (analysisCode['Request Type'].length == 2) ? 'Both' : analysisCode['Request Type'].map((requestId) => {
                return AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[requestId];
            }).join(',');

            finalList.push({
                ...analysisCode,
                'Request Type': requestTypes,
                'Is Deleted?': false
            })
        });

        const dataValidations = [{
            column: 'C',
            dataList: [AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[1], AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[2], 'Both'],
            allowBlank: false,
            lastRow: 10000
        }, {
            column: 'D',
            dataList: [true, false],
            allowBlank: false,
            lastRow: 10000
        }];

        if (!finalList.length) {
            finalList.push({
                'Title': '',
                'Analysis Code': '',
                'Request Type': '',
                'Is Deleted?': ''
            });
        }

        const report = await this.excelSheetService.createExcelSheet(finalList, SHEET_NAME, dataValidations);

        return { report, filename: FILE_NAME };
    }

    public async downloadCostCenters(companyCodeId: number) {
        //TODO: Create report data.
        const SHEET_NAME = 'Cost Center';
        const FILE_NAME = 'cost_center_' + (companyCodeId ? companyCodeId : 'format');

        const costCenters = await this.costCenterRepository.getAllCostCentersListByCompanyCodeId(companyCodeId);

        const costCentersList = costCenters.map((costCenter) => {

            const costCenterDetail = {
                ...costCenter,
                departmentHead: (costCenter.departmentHead.userType === AD_USER_TYPE.GUEST) ? costCenter.departmentHead.mail.toLowerCase() : costCenter.departmentHead.userPrincipalName.toLowerCase(),
                sectionHead: costCenter?.sectionHead?.length ? costCenter?.sectionHead.map(
                    (entry: any) => `${entry.title}#${(entry.user.userType === AD_USER_TYPE.GUEST) ? entry.user.mail.toLowerCase() : entry.user.userPrincipalName.toLowerCase()}`
                ).join(";") : ''
            }

            return instanceToPlain(new CostCenterReportResponseDto(costCenterDetail), { excludeExtraneousValues: true })
        });

        let finalList = [];
        costCentersList.forEach((costCenter) => {

            finalList.push({
                ...costCenter,
                'Is Deleted?': false
            })
        });

        const dataValidations = [{
            column: 'C',
            dataList: [true, false],
            allowBlank: false,
            lastRow: 10000
        }, {
            column: 'F',
            dataList: [true, false],
            allowBlank: false,
            lastRow: 10000
        }];

        if (!finalList.length) {
            finalList.push({
                'Cost Center Code': '',
                'Cost Center Name': '',
                'Is Operating?': '',
                'Department Head Email Id': '',
                'Sections': '',
                'Is Deleted?': ''
            });
        }
        const report = await this.excelSheetService.createExcelSheet(finalList, SHEET_NAME, dataValidations);
        return { report, filename: FILE_NAME };
    }

    public async downloadNaturalAccounts(companyCodeId: number) {
        //TODO: Create report data.
        const SHEET_NAME = 'Natural Account';
        const FILE_NAME = 'natural_accounts_' + (companyCodeId ? companyCodeId : 'format')

        const naturalAccounts = await this.naturalAccountNumberRepository.getAllNaturalAccountNumbersListByCompanyCodeId(companyCodeId)

        const naturalAccountsList = naturalAccounts.map((naturalAccount) => {

            const naturalAccountDetail = {
                ...naturalAccount
            }

            return instanceToPlain(new NaturalAccountReportResponseDto(naturalAccountDetail), { excludeExtraneousValues: true })
        });

        let finalList = [];
        naturalAccountsList.forEach((naturalAccount) => {

            let requestTypes = (naturalAccount['Request Type'].length == 2) ? 'Both' : naturalAccount['Request Type'].map((requestId) => {
                return AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[requestId];
            }).join(',');

            finalList.push({
                ...naturalAccount,
                'Request Type': requestTypes,
                'Is Deleted?': false
            })
        });

        const dataValidations = [{
            column: 'C',
            dataList: [AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[1], AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[2], 'Both'],
            allowBlank: false,
            lastRow: 10000
        }, {
            column: 'D',
            dataList: [true, false],
            allowBlank: false,
            lastRow: 10000
        }];

        if (!finalList.length) {
            finalList.push({
                'Title': '',
                'Account Number': '',
                'Request Type': '',
                'Is Deleted?': ''
            });
        }
        const report = await this.excelSheetService.createExcelSheet(finalList, SHEET_NAME, dataValidations);
        return { report, filename: FILE_NAME };
    }
}
