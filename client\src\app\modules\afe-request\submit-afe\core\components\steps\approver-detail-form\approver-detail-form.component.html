<ng-container *ngIf="formReady; else loadingPage">
  <!-- Approvers Section -->
  <div
    *ngIf="approversList.length; else noApprovalList"
    class="w-100 p-5 bg-body rounded"
  >
    <div class="col-12 rounded border p-5 pb-0">
      <div class="pb-1 border-bottom mb-5 d-flex justify-content-between align-items-center">
        <h3 class="fw-bolder d-flex align-items-center">
          {{ "FORM.LABEL.APPROVERS" | translate }}
          {{ "COMMON.LIST" | translate }}
        </h3>

        <ng-container *ngIf="isBalanceListAvailable">
          <button
            *ngIf="approverList?.controls?.length && getObjectLength()"
            (click)="openBalanceCheckModal()"
            type="button"
            class="btn btn-sm mb-3 editBtn btnRounded fw-bold ps-4 pe-4 py-1 mx-2"
            translate="FORM.BUTTON.CHECK_AVAILABLE_LIMIT"
          ></button>
        </ng-container>
      </div>

      <div class="card-body py-3">
        <div class="row">
          <div class="col-12">
            <div class="row d-none d-lg-block">
              <div
                class="row fw-bold text-gray-600 font-size-10 d-flex justify-content-between flex-wrap pb-3 mb-3 border-bottom"
              >
                <div
                  class="col-md-2"
                  translate="COMMON.WORKFLOW_START"
                  *ngIf="showWorkflowStart"
                ></div>

                <div
                  class="col-md-7 px-0"
                  [ngClass]="{
                    'col-md-7': showWorkflowStart,
                    'col-md-9': !showWorkflowStart
                  }"
                >
                  {{ "FORM.LABEL.APPROVERS" | translate }}
                  {{ "COMMON.ROLE" | translate }}
                </div>

                <div class="col-md-3 d-flex justify-content-end px-2">
                  <span
                    >{{ "COMMON.ADD" | translate }}/{{
                      "COMMON.REMOVE" | translate
                    }}</span
                  >
                </div>
              </div>
            </div>
            <ng-container
              *ngFor="let approver of approverList.controls; let i = index"
            >
              <ng-container *ngIf="newApproverPlacement === 'before-' + i">
                <div class="row px-0 py-2 solid-hr">
                  <div class="col-12">
                    <div class="col-lg-12 w-100 pb-0">
                      <app-ad-user-search
                        [defaultUsers]="defaultValues.approvers"
                        placeholder="{{
                          'FORM.PLACEHOLDER.SEARCH' | translate
                        }} {{ 'FORM.LABEL.APPROVER' | translate | lowercase }}"
                        [isMultiSelect]="false"
                        (userSelected)="onAddApprover($event)"
                      >
                      </app-ad-user-search>
                    </div>
                  </div>
                </div>
              </ng-container>

              <div
                class="row d-flex justify-content-between flex-wrap border-bottom py-3"
              >
                <div class="col-12 col-md-2" *ngIf="showWorkflowStart">
                  <div class="w-100 d-flex flex-row justify-content-start">
                    <div>
                      <span
                        class="form-check form-check-custom form-check-solid"
                      >
                        <input
                          class="mt-1 mx-0 mx-lg-7 mx-md-7 form-check-input"
                          *ngIf="approver.value.canWorkflowStart"
                          type="radio"
                          [checked]="
                            approver.value.sequenceNumber === approvalStartId
                          "
                          #radio
                          name="canWorkFlowStart"
                          (change)="onChangeCanWorkFlowStart(radio)"
                          [id]="approver.value.sequenceNumber"
                        />
                      </span>
                    </div>

                    <div
                      *ngIf="approver.value.canWorkflowStart"
                      class="d-md-none fw-bold px-2"
                      translate="COMMON.WORKFLOW_START"
                    ></div>
                  </div>
                </div>

                <div
                  class="col-12 px-12 px-md-0"
                  [ngClass]="{
                    'col-md-7': showWorkflowStart,
                    'col-md-9': !showWorkflowStart
                  }"
                >
                  <div class="d-md-none fw-bold">
                    {{ "FORM.LABEL.APPROVERS" | translate }}
                    {{ "COMMON.ROLE" | translate }}
                  </div>
                  <div
                    class="text-none"
                    *ngIf="approver.value.isCustomUser; else workflowUser"
                  >
                    <span
                      class="fw-bold"
                      *ngFor="let approverDetail of approver.value.approvers"
                    >
                      {{ approverDetail.firstName }}
                      {{ approverDetail.lastName }}
                    </span>
                    <span>( {{ approver.value.title }} )</span>
                  </div>

                  <ng-template #workflowUser>
                    <div class="text-none">
                      <span class="fw-bold">{{ approver.value.title }}</span>
                      <ng-container
                        *ngIf="approver.value.approvers.length; else noApprover"
                      >
                        (
                        <span
                          *ngFor="
                            let approverDetail of approver.value.approvers;
                            let i = index
                          "
                          class=""
                        >
                          <span *ngIf="i">, </span>
                          {{ approverDetail.firstName }}
                          {{ approverDetail.lastName }}
                        </span>
                        )
                      </ng-container>

                      <ng-template #noApprover>
                        <app-input-error-message
                          errorMessage="({{
                            'ERROR.NO_APPROVER_USER_FOUND' | translate
                          }})"
                        >
                        </app-input-error-message>
                      </ng-template>
                    </div>
                  </ng-template>

                  <!-- Display All Parallel Approvers Start -->
                  <div
                    *ngFor="
                      let moreApprover of approver.value.parallelApprovers
                    "
                    class="text-none"
                  >
                    <span class="fw-bold">{{ moreApprover.title }}</span>
                    (<span
                      *ngFor="
                        let moreApproverDetail of moreApprover.approvers;
                        let j = index
                      "
                      class=""
                    >
                      <span *ngIf="j">, </span>
                      {{ moreApproverDetail.firstName }}
                      {{ moreApproverDetail.lastName }} </span
                    >)
                  </div>
                  <!-- Display All Parallel Approvers End -->
                </div>
                <!-- Action Buttons Start -->
                <div class="col-12 col-md-3 text-right">
                  <div class="d-none mt-5 text-end mb-2">
                    {{ "COMMON.ADD" | translate }}/{{
                      "COMMON.REMOVE" | translate
                    }}
                    {{ "FORM.LABEL.APPROVERS" | translate }}
                  </div>
                  <div
                    class="d-flex flex-wrap justify-content-end py-4 py-lg-0 py-md-0 py-sm-0"
                  >
                    <a
                      class="pt-2 cursor-pointer  px-5"
                      title="{{ 'COMMON.REMOVE' | translate }} {{
                        approver.value.approvers.firstName
                      }}"
                      *ngIf="approver.value.isCustomUser"
                      (click)="removeApprover(i)"
                    >
                      <!-- <i class="mx-1 d-md-flex d-none fas fa-times-circle text-dark"></i> -->
                      <img
                        class="mx-1 d-md-flex d-none text-dark"
                        width="32px"
                        src="./assets/media/svg/icons/dpw-icons/cross2.png"
                        alt="delete"
                      />
                      <img
                        class="mx-1 d-md-none text-none"
                        width="32px"
                        src="./assets/media/svg/icons/dpw-icons/cross2.png"
                        alt="delete"
                      />
                      <!-- <i class="mx-1 d-md-none fas fa-times-circle text-none"></i> -->
                    </a>

                    <button
                      type="button"
                      title="{{ 'COMMON.ADD' | translate }} {{
                        'COMMON.NEW' | translate
                      }} {{ 'FORM.LABEL.APPROVER' | translate }} {{
                        'COMMON.BEFORE' | translate
                      }} {{ approver.value.title }}"
                      class="btn btn-sm fw-bold ps-4 pe-4 py-2 approveBtn btnRounded"
                      (click)="
                        onAddNewApprover(
                          i,
                          'before',
                          approver.value.canWorkflowStart,
                          approver.value.sequenceNumber
                        )
                      "
                    >
                      <span
                        class="indicator-label"
                        translate="{{ 'COMMON.ADD' | translate }} &nbsp;{{
                          'FORM.LABEL.APPROVER' | translate
                        }}"
                      ></span>
                    </button>
                  </div>
                </div>

                <!-- Action Buttons Ends -->
              </div>

              <div
                class="row fw-bold d-flex justify-content-between flex-wrap"
                *ngIf="approverList.controls.length === +i + 1"
              >
                <div class="col-12 col-md-2" *ngIf="showWorkflowStart"></div>
                <div
                  class="col-12"
                  [ngClass]="{
                    'col-md-7': showWorkflowStart,
                    'col-md-9': !showWorkflowStart
                  }"
                ></div>
                <div class="col-12 col-md-3 text-right py-3">
                  <div class="d-flex justify-content-end">
                    <button
                      type="button"
                      title="{{ 'COMMON.ADD' | translate }} {{
                        'COMMON.NEW' | translate
                      }} {{ 'FORM.LABEL.APPROVER' | translate }} {{
                        'COMMON.AFTER' | translate
                      }} {{ approver.value.title }}"
                      class="btn btn-sm fw-bold approveBtn btnRounded ps-4 pe-4 py-2"
                      (click)="
                        onAddNewApprover(
                          i,
                          'after',
                          approver.value.canWorkflowStart,
                          approver.value.sequenceNumber
                        )
                      "
                    >
                      <span
                        class="indicator-label"
                        translate="{{ 'COMMON.ADD' | translate }}&nbsp;{{
                          'FORM.LABEL.APPROVER' | translate
                        }}"
                      >
                      </span>
                    </button>
                  </div>
                </div>
              </div>

              <ng-container *ngIf="newApproverPlacement === 'after-' + i">
                <div class="row d-flex flex-wrap my-3">
                  <div class="col-12">
                    <div class="col-lg-12 w-100">
                      <app-ad-user-search
                        [defaultUsers]="defaultValues.approvers"
                        placeholder="{{
                          'FORM.PLACEHOLDER.SEARCH' | translate
                        }} {{ 'FORM.LABEL.APPROVER' | translate | lowercase }}"
                        [isMultiSelect]="false"
                        (userSelected)="onAddApprover($event)"
                      >
                      </app-ad-user-search>
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #noApprovalList>
    <app-no-data
      [message]="
        'ERROR - No approval list found for this AFE. Please contact adimistrator.'
      "
    ></app-no-data>
  </ng-template>

  <!-- Readers Section -->
  <div class="w-100 p-5 bg-body rounded mt-5">
    <div class="col-12 rounded border p-5 pb-0">
      <div class="pb-1 border-bottom mb-5">
        <h3 class="fw-bolder d-flex align-items-center">
          {{ "COMMON.ADDITIONAL" | translate }}
          {{ "COMMON.READER" | translate }}
        </h3>
      </div>

      <div class="col-lg-12 w-100 mb-5">
        <app-ad-user-search
          [defaultUsers]="defaultValues.readers"
          placeholder="{{ 'FORM.PLACEHOLDER.SEARCH' | translate }} {{
            'COMMON.READER' | translate | lowercase
          }}"
          [isMultiSelect]="true"
          (userSelected)="onReaderAdded($event)"
        >
        </app-ad-user-search>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #loadingPage>
  <app-form-skeleton-loader></app-form-skeleton-loader>
</ng-template>


<app-modal #aggregateBalance [modalConfig]="aggregateBalanceConfig">
  
  <ng-container 
  *ngIf="
    balanceModalReady && 
    typeOfAggregateBalance?.length"
  >
    <div *ngFor="let budgetType of typeOfAggregateBalance;">
      <h3 *ngIf="budgetType" class="fw-bolder d-flex align-items-center">
        {{getBudgetTypeTitle(budgetType)}} Limit
      </h3>
      <div *ngIf="budgetType" class="separator mt-5 mb-3 opacity-75"></div>

      <div class="table-responsive">
        <table class="table table-bordered border">
          <thead class="table-light border border-gray-300">
            <tr class="fw-bolder">
              <th class="p-2" style="vertical-align: middle;" scope="col" rowspan="2">
                Approver
              </th>
              <ng-container *ngFor="let projectComponent of getTypeOfProjectComponent(budgetType)">
                <th *ngIf="projectComponent" class="p-2" style="text-align: center;" scope="col" colspan="3">
                    {{ projectComponent }}
                </th>
              </ng-container>
            </tr>
            <tr class="fw-bolder">
              <ng-container *ngFor="let projectComponent of getTypeOfProjectComponent(budgetType)">
                  <th class="p-2" scope="col"> Total </th>
                  <th class="p-2" scope="col"> Utilized </th>
                  <th class="p-2" scope="col"> In Pipeline </th>
              </ng-container>
            </tr>
          </thead>

          <tbody class="border border-gray-300">
            <tr class="border border-gray-300" *ngFor="let stepId of typeOfSteps;">

              <th class="p-2 table-light fw-bold" scope="row" *ngFor="let stepDetail of getFirstLimit(budgetType, stepId);">
                {{stepDetail.title}} <span *ngIf="stepDetail.costCenterTitle"> ({{stepDetail.costCenterTitle}}) </span>
              </th>

              <ng-container *ngFor="let stepDetail of getLimitList(budgetType, stepId);">
                <ng-container *ngIf="stepDetail.totalAggregateLimit">
                  <td class="p-2 fw-bold">
                    {{stepDetail.totalAggregateLimit | currency : "USD" : "symbol" : "1.2-2"}}
                  </td>
  
                  <td class="p-2 fw-bold">
                    {{stepDetail.totalAmountSpent | currency : "USD" : "symbol" : "1.2-2"}}
                  </td>
  
                  <td class="p-2 fw-bold">
                    {{stepDetail.totalAmountInPipeline | currency : "USD" : "symbol" : "1.2-2"}}
                  </td>
                </ng-container>
                <ng-container *ngIf="!stepDetail?.totalAggregateLimit">
                  <td colspan="3" class="p-2 fw-bold bg-light-warning"></td>
                </ng-container>
              </ng-container>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!--
    <ngb-accordion [activeIds]="['panel1']">
      <ng-container *ngFor="let budgetType of typeOfAggregateBalance; let i = index;">
        <ngb-panel id="panel{{i+1}}">
          <ng-template ngbPanelTitle>
            <h3 *ngIf="budgetType" class="fw-bolder d-flex align-items-center">
              {{ getBudgetTypeTitle(budgetType) }} Limit
            </h3>
          </ng-template>

          <ng-template ngbPanelContent>
            <div class="table-responsive">
              <table class="table table-bordered border">
                <thead class="table-light border border-gray-300">
                  <tr class="fw-bolder">
                    <th class="p-2" style="vertical-align: middle;" scope="col" rowspan="2">
                      Approver
                    </th>
                    <ng-container *ngFor="let projectComponent of getTypeOfProjectComponent(budgetType)">
                      <th *ngIf="projectComponent" class="p-2" style="text-align: center;" scope="col" colspan="3">
                          {{ projectComponent }}
                      </th>
                    </ng-container>
                  </tr>
                  <tr class="fw-bolder">
                    <ng-container *ngFor="let projectComponent of getTypeOfProjectComponent(budgetType)">
                      <th class="p-2" scope="col"> Total </th>
                      <th class="p-2" scope="col"> Utilized </th>
                      <th class="p-2" scope="col"> In Pipeline </th>
                    </ng-container>
                  </tr>
                </thead>
    
                <tbody class="border border-gray-300">
                  <tr class="border border-gray-300" *ngFor="let stepId of typeOfSteps;">
                    <th class="p-2 fw-bold" scope="row" *ngFor="let stepDetail of getFirstLimit(budgetType, stepId);">
                      {{ stepDetail.title }}
                    </th>
    
                    <ng-container *ngFor="let stepDetail of getLimitList(budgetType, stepId);">
                      <td class="p-2 fw-bold">
                        {{ stepDetail.totalAggregateLimit | currency : "USD" : "symbol" : "1.2-2" }}
                      </td>
    
                      <td class="p-2 fw-bold">
                        {{ stepDetail.totalAmountSpent | currency : "USD" : "symbol" : "1.2-2" }}
                      </td>
    
                      <td class="p-2 fw-bold">
                        {{ stepDetail.totalAmountInPipeline | currency : "USD" : "symbol" : "1.2-2" }}
                      </td>
                    </ng-container>
                  </tr>
                </tbody>
              </table>
            </div>
          </ng-template>
        </ngb-panel>
      </ng-container>
    </ngb-accordion>
    -->
  </ng-container>

  <ng-container *ngIf="balanceModalReady && !typeOfAggregateBalance?.length">
    <div
      class="h-10 d-flex justify-content-center align-item-center fw-bold"
      translate="ERROR.NO_LIMIT_FOUND"
    ></div>
  </ng-container>
</app-modal>