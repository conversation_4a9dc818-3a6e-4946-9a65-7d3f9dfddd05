"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectComponentController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const length_of_commitment_response_dto_1 = require("../dtos/response/length-of-commitment-response.dto");
const project_component_response_dto_1 = require("../dtos/response/project-component-response.dto");
const services_1 = require("../services");
let ProjectComponentController = class ProjectComponentController {
    constructor(projectComponentService) {
        this.projectComponentService = projectComponentService;
    }
    getProjectComponents(requestTypeId, locationId, budgetTypeId, childProject = true) {
        return this.projectComponentService.getProjectComponents(requestTypeId, locationId, childProject, budgetTypeId);
    }
    getLengthOfCommitment() {
        return this.projectComponentService.getLengthOfCommitment();
    }
};
__decorate([
    (0, swagger_1.ApiQuery)({
        name: 'requestTypeId',
        type: Number,
        description: 'Afe request type id.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'locationId',
        type: Number,
        description: 'Business entity id.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'childProject',
        type: Number,
        description: 'If child project component is required.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'budgetTypeId',
        type: Number,
        description: 'Budget type id.',
        required: false,
        allowEmptyValue: true,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get project components by afe request type.',
        type: [project_component_response_dto_1.ProjectComponentDto],
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Query)('requestTypeId')),
    __param(1, (0, common_1.Query)('locationId')),
    __param(2, (0, common_1.Query)('budgetTypeId')),
    __param(3, (0, common_1.Query)('childProject')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number, Boolean]),
    __metadata("design:returntype", void 0)
], ProjectComponentController.prototype, "getProjectComponents", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get list of length of commitment.',
        type: [length_of_commitment_response_dto_1.LengthOfCommitmentResponseDTO],
    }),
    (0, common_1.Get)('/lengthOfCommitment'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProjectComponentController.prototype, "getLengthOfCommitment", null);
ProjectComponentController = __decorate([
    (0, swagger_1.ApiTags)('Project Components APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('project-components'),
    __metadata("design:paramtypes", [services_1.ProjectComponentService])
], ProjectComponentController);
exports.ProjectComponentController = ProjectComponentController;
//# sourceMappingURL=project-component.controller.js.map