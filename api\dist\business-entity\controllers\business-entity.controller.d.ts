import { RequestContext } from 'src/shared/types';
import { BusinessEntityService } from '../services';
export declare class BusinessEntityController {
    private readonly businessEntityService;
    constructor(businessEntityService: BusinessEntityService);
    getBusinessEntitiesForGivenPermissionForUser(request: RequestContext, permission: string, parentId?: number, lastLevel?: string, isSkipLevelRequired?: boolean, isFilterRequest?: boolean): Promise<Record<string, any>>;
    getAllBusinessEntityLevels(): Promise<Record<string, any>>;
    getAllBusinessEntityRoles(entityLevel: string): Promise<Record<string, any>>;
    getUsersByRoleOfAnEntity(entityId: number, roleName: string): Promise<Record<string, any>>;
}
