"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const class_transformer_1 = require("class-transformer");
const lodash_1 = require("lodash");
const repositories_1 = require("../../afe-draft/repositories");
const repositories_2 = require("../../afe-proposal/repositories");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const request_type_wise_afe_response_dto_1 = require("../dtos/response/request-type-wise-afe-response.dto");
const status_wise_afe_response_dto_1 = require("../dtos/response/status-wise-afe-response.dto");
let DashboardService = class DashboardService {
    constructor(afeProposalRepository, adminApiClient, draftAfeRepository, sequlizeOperator) {
        this.afeProposalRepository = afeProposalRepository;
        this.adminApiClient = adminApiClient;
        this.draftAfeRepository = draftAfeRepository;
        this.sequlizeOperator = sequlizeOperator;
    }
    getStatusWiseCount(currentContext, year) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username } = currentContext.user;
            const accessLocation = currentContext.locations;
            let condition = {
                active: true,
                deleted: false
            };
            let yearCondition;
            if (year) {
                yearCondition = this.sequlizeOperator.whereOperator(this.sequlizeOperator.sequelizeLiteral('extract(YEAR FROM created_on)'), '=', year);
            }
            const draftCount = yield this.draftAfeRepository.getCountByCondition(this.sequlizeOperator.andOperator([Object.assign(Object.assign({}, condition), { createdBy: username })]));
            if (accessLocation === null || accessLocation === void 0 ? void 0 : accessLocation.length) {
                condition = Object.assign(Object.assign({}, condition), { entityId: this.sequlizeOperator.inOperator(accessLocation) });
            }
            const totalSubmittedAfe = yield this.afeProposalRepository.getCountByCondition(this.sequlizeOperator.andOperator([
                yearCondition,
                Object.assign({}, condition)
            ]));
            condition = Object.assign(Object.assign({}, condition), { internalStatus: [
                    enums_1.AFE_PROPOSAL_STATUS.APPROVED,
                    enums_1.AFE_PROPOSAL_STATUS.REJECTED,
                    enums_1.AFE_PROPOSAL_STATUS.IN_PROGRESS,
                    enums_1.AFE_PROPOSAL_STATUS.SUBMITTED,
                    enums_1.AFE_PROPOSAL_STATUS.SENT_BACK
                ] });
            const statuWiseCount = yield this.afeProposalRepository.getCountByInternalStatus(this.sequlizeOperator.andOperator([
                yearCondition,
                Object.assign({}, condition)
            ]));
            let responseCount = {
                total: { count: 0, amount: 0 },
                approved: { count: 0, amount: 0 },
                rejected: { count: 0, amount: 0 },
                inprocess: { count: 0, amount: 0 },
                sentBack: { count: 0, amount: 0 },
                drafted: { count: 0, amount: 0 },
            };
            if (totalSubmittedAfe) {
                responseCount.total.count = totalSubmittedAfe;
            }
            if (draftCount) {
                responseCount.drafted.count = draftCount;
            }
            if (statuWiseCount.length) {
                statuWiseCount.forEach((statusCount) => {
                    if (statusCount.status === enums_1.AFE_PROPOSAL_STATUS.APPROVED) {
                        responseCount.approved.count = (0, lodash_1.toNumber)(statusCount.totalCount);
                        responseCount.approved.amount = (0, lodash_1.toNumber)(statusCount.totalAmount);
                    }
                    else if (statusCount.status === enums_1.AFE_PROPOSAL_STATUS.REJECTED) {
                        responseCount.rejected.count = (0, lodash_1.toNumber)(statusCount.totalCount);
                        responseCount.rejected.amount = (0, lodash_1.toNumber)(statusCount.totalAmount);
                    }
                    else if (statusCount.status === enums_1.AFE_PROPOSAL_STATUS.SENT_BACK) {
                        responseCount.sentBack.count = (0, lodash_1.toNumber)(statusCount.totalCount);
                        responseCount.sentBack.amount = (0, lodash_1.toNumber)(statusCount.totalAmount);
                    }
                    else {
                        responseCount.inprocess.count = (0, lodash_1.toNumber)(responseCount.inprocess.count) + (0, lodash_1.toNumber)(statusCount.totalCount);
                        responseCount.inprocess.amount = (0, lodash_1.toNumber)(responseCount.inprocess.amount) + (0, lodash_1.toNumber)(statusCount.totalAmount);
                    }
                    responseCount.total.amount = responseCount.total.amount + (0, lodash_1.toNumber)(statusCount.totalAmount);
                });
            }
            return (0, helpers_1.singleObjectToInstance)(status_wise_afe_response_dto_1.StatusWiseAFEResponseDto, responseCount);
        });
    }
    getRequestWiseMonthlyCount(currentContext, year, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            let yearCondition;
            let condition = {
                active: true,
                deleted: false
            };
            if (year) {
                yearCondition = this.sequlizeOperator.whereOperator(this.sequlizeOperator.sequelizeLiteral('extract(YEAR FROM created_on)'), '=', year);
            }
            if (entityId) {
                const allChildrenEntity = yield this.adminApiClient.getChildernListOfBusinessEntity(entityId);
                allChildrenEntity.push(entityId);
                condition = Object.assign(Object.assign({}, condition), { entityId: this.sequlizeOperator.inOperator(allChildrenEntity) });
            }
            const { username } = currentContext.user;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            if (!isAdministrator) {
                condition = Object.assign(Object.assign({}, condition), { createdBy: username });
            }
            const requestTypeMonthlyCount = yield this.afeProposalRepository.getCountByRequestTypeId(this.sequlizeOperator.andOperator([
                yearCondition,
                Object.assign({}, condition)
            ]));
            return requestTypeMonthlyCount.map(requestTypeCount => (0, class_transformer_1.instanceToPlain)(new request_type_wise_afe_response_dto_1.RequestTypeWiseAfeResponseDto(requestTypeCount)));
        });
    }
};
DashboardService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_2.AfeProposalRepository,
        clients_1.AdminApiClient,
        repositories_1.DraftAfeRepository,
        helpers_1.SequlizeOperator])
], DashboardService);
exports.DashboardService = DashboardService;
//# sourceMappingURL=dashboard.service.js.map