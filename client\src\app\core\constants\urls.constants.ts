import { IFilter } from "../interfaces/api";

export const GlobalUrls = {
  ACCESS_DENIED: '/error/403',
  COMMON_API: {
    GET_USER_PERMISSION: '/permissions/user',
    GET_MENU_LIST: '/afe-config/nav-bar/{position}',
    GET_LOCATION_HIERARCHY_BY_PERMISSION: '/business-entities?permission={permission}&parentId={parentId}&lastLevel={lastLevel}&isSkipLevelRequired={isSkipLevelRequired}&isFilterRequest={isFilterRequest}',
    GET_ALL_BUSINESS_ENTITY_LEVELS: '/business-entities/entity-levels',
    GET_ALL_ROLES_BY_LEVEL: '/business-entities/entity-roles?entityLevel={entityLevel}',
    GET_ALL_USERS_BY_ENTITY_AND_ROLE: '/business-entities/user-list?entityId={entityId}&roleName={roleName}',
    GET_DOCUMENT_ATTACHMENT_BY_ID: '/attachment/get-attachement-by-id/{fileId}',
    GET_DOCUMENT_CONTENT_BY_ID: '/attachment/content/{fileId}?taskId={taskId}'
  },
  AFE_API: {
    REQUEST_TYPE: '/afe-config/request-types',
    PARALLEL_IDENTIFIER: '/afe-config/parallel-identifier',
    NATURE_TYPE: '/afe-config/nature-types?requestTypeId={requestTypeId}&locationId={locationId}',
    AFE_REQUEST_TYPE: '/afe-config/request-types/{requestTypeId}/afe-types',
    BUDGET_TYPE: '/afe-config/budget-types?requestTypeId={requestTypeId}&typeId={typeId}',
    SUB_TYPE_ID: '/afe-config/sub-types?typeId={typeId}&entityId={entityId}',
    GLOBAL_PROCUREMENT_QUESTION: '/afe-config/questions?requestTypeId={requestTypeId}&locationId={locationId}&questionType={questionType}',
    GET_ADDITIONAL_LOCATIONS: '/afe-config/get-locations?requestTypeId={requestTypeId}&entityId={entityId}',
    GET_ADDITIONAL_LOCATIONS_BY_ENTITY_IDS: '/afe-config/get-locations-by-entity-ids',
    PROJECT_COMPONENT: '/project-components?requestTypeId={requestTypeId}&locationId={locationId}&childProject={childProject}&budgetTypeId={budgetTypeId}',
    LENGTH_OF_COMMITMENT: '/project-components/lengthOfCommitment',
    NATURAL_ACCOUNT_NUMBER: '/finance/request-type/{requestTypeId}/natural-account-numbers?entityId={locationId}',
    INTERCOMPANY_LIST: '/finance/get-company-code-excluding/{excludedCompanyCode}',
    ANALYSIS_CODE: '/finance/request-type/{requestTypeId}/analysis-codes?entityId={locationId}',
    COMPANY_CODE_EXISTS: '/finance/entity/{entityId}/company-codes/exists',
    COMPANY_DETAIL_BY_ENTITY: '/finance/entity/{entityId}/company-detail',
    COST_CENTER: '/finance/entity/{locationId}/cost-centers',
    GET_COST_CENTERS_BY_ENTITY_IDS: '/finance/cost-center-by-entity-ids',
    GET_ALL_COST_CENTERS: '/finance/get-all-cost-centers',
    CONVERSION_RATE: '/finance/currency/{currencyType}/conversion-rate',
    ENTITY_CONVERSION_RATE: '/finance/entity/{locationId}/currency',
    GET_AFE_HISTORY: '/afe-proposals/{afeProposalId}/history',
    AFE_SUB_TYPE: '/afe-config/sub-types',
    AFE_TYPE: '/afe-config/afe-types',
    SETTINGS: {
      WORKFLOW: '/settings/workflow'
    },
    DRAFT_AFE: {
      GET_ALL_DRAFT: '/afe-draft?limit={limit}&page={page}',
      GET_DRAFT_BY_ID: '/afe-draft/{id}',
      SAVE_DRAFT: '/afe-draft',
      UPDATE_DRAFT: '/afe-draft',
      DELETE_DRAFT: '/afe-draft/{id}',
    },
    AFE_PROPOSAL: {
      GET_APPROVAL_LIST: '/workflows/approvers',
      SUBMIT_AFE: '/afe-proposals',
      WITHDRAW_AFE: '/afe-proposals/withdraw',
      RE_OPEN_AFE: '/afe-proposals/re-open',
      REVERT_SENT_BACK_ACTION: '/afe-proposals/revert-sent-back-action',
      SEND_BACK_AFE: '/afe-proposals/send-back',
      RESUBMIT_AFE: '/afe-proposals/resubmit',
      GET_SUBMITED_AFE_LIST: '/afe-proposals/list?limit={limit}&page={page}&forApprovalHistory={forApprovalHistory}',
      GET_AFE_PROPOSAL_DETAIL: '/afe-proposals/{proposalId}?taskId={taskId}',
      GET_AFE_AMOUNT_SPLIT: '/afe-proposals/{proposalId}/amount-splits?taskId={taskId}',
      GET_AFE_APPROVERS: '/afe-proposals/{proposalId}/approvers?taskId={taskId}',
      GET_AFE_ACTION_HISTORY: '/afe-proposals/{proposalId}/history?taskId={taskId}',
      GET_ATTACHEMENT: '/attachment/{proposalId}/meta-data?taskId={taskId}',
      GET_AFE_RELATED_SUPPLEMENTAL: '/afe-proposals/{proposalId}/supplementals?taskId={taskId}',
      GET_LATEST_VERSION_AFE_INFO: '/afe-proposals/supplemental/latest-version-info?afeRefNumber={afeRefNumber}',
      SEARCH_PARENT_APPROVED_AFES: '/afe-proposals/approved/search?projectReferenceNumber={projectReferenceNumber}&afeRequestTypeIds={afeRequestTypeIds}&entityId={entityId}',
      CHECK_AFE_OR_SUPPLEMENTAL_IN_PROGRESS: '/afe-proposals/{proposalId}/check-inprogress',
      DOWNLOAD_AFE_PDF: '/afe-proposals/{proposalId}/download-pdf',
      AFE_SUBSCRIPTION: '/afe-proposals/{afeProposalId}/subscribe/{toggleValue}',
      UPDATE_AFE_APPROVERS: '/afe-proposals/{afeProposalId}/approvers',
      ADD_NEW_READERS: '/afe-proposals/{afeProposalId}/add-new-readers',
      UPLOAD_NEW_EVIDENCE: '/afe-proposals/{afeProposalId}/upload-evidence',
      UPDATE_APPROVER_USER: '/afe-proposals/{afeProposalId}/update-approver-user'
    },
    REPORT: {
      AFE_LIST_REPORT: '/reports/afe-list/download',
      ANALYSIS_CODE_LIST_REPORT: '/finance-admin/analysis-codes/{companyId}/download',
      COST_CENTER_LIST_REPORT: '/finance-admin/cost-centers/{companyId}/download',
      NATURAL_ACCOUNT_LIST_REPORT: '/finance-admin/natural-accounts/{companyId}/download',
      BU_LEVEL_WORKFLOW: '/reports/bu-level-workflow/download'
    },
    APPROVER: {
      GET_ALL_APPROVER_ATTACHEMENT: '/attachment/approver/{proposalId}/all-meta-data?taskId={taskId}',
      GET_APPROVER_ATTACHEMENT: '/attachment/approver/{approverId}/meta-data',
    },
    TASK: {
      GET_USER_PENDING_TASKS: '/tasks/pending',
      GET_OTHER_USER_PENDING_TASKS: '/tasks/pending?assignedTo={assignedTo}',
      PERFORM_TASK_ACTION: '/tasks/action/{actionType}',
      GET_TASK_BY_ID: '/tasks/{id}',
      GET_CURRENT_USER_TASK: '/tasks/afe-proposal/{id}',
      SEND_REMINDER: '/tasks/send-reminder/{approverId}',
    },
    NOTIFICATION: {
      GET_NOTIFICATION: '/notifications',
      READ_NOTIFICATION: '/notifications/viewed'
    },
    VACATION_DELEGATION: {
      GET_UPCOMING_DELEGATION_LIST: '/vacation/upcoming-delegations?delegateFor={delegateFor}',
      ADD_UPCOMING_DELEGATION: '/vacation/upcoming-delegation',
      DELETE_UPCOMING_DELEGATION: '/vacation/upcoming-delegation/{id}',
      DELETE_ALL_UPCOMING_DELEGATION: '/vacation/delete-all-upcoming-delegation'
    },
    REPRESENTATIVE: {
      GET_REPRESENTATIVE: '/representatives?representativeFor={representativeFor}',
      ADD_REPRESENTATIVE: '/representatives',
      UPDATE_REPRESENTATIVE: '/representatives',
      DELETE_REPRESENTATIVE: '/representatives/{id}'
    },
    ADMIN: {
      WORKFLOW: {
        GET_MASTER_SETTING_LIST: '/workflow-setting/list?limit={limit}&page={page}',
        GET_ALL_MASTER_SETTING_LIST: '/workflow-setting',
        ADD_NEW_MASTER_WORKFLOW: '/workflow-setting',
        GET_WORKFLOW_DETAIL_BY_ID: '/workflow-setting/{workflowId}',
        GET_UNPUBLISHED_WORKFLOW_DETAIL_BY_ID: '/workflow-setting/{workflowId}/unpublished-version',
        GET_POLICY_DETAIL_BY_ID: '/workflow-setting/{workflowId}/policy',
        GET_ALL_OVERRIDEN_WITH_GROUP_BY_ID: '/workflow-setting/override-workflow-setting/{id}',
        GET_ALL_UNPUBLISHED_OVERRIDEN_BY_PARENT_ID: '/workflow-setting/unpublished-overridden-settings/{parentId}',
        GET_ALL_UNPUBLISHED_UNIQUE_ASSIGNED_ROLES: '/workflow-setting/get-all-unique-assigned-roles/{parentId}',
        DOWNLOAD_POLICY_DETAIL_BY_ID: '/workflow-setting/{workflowId}/policy/download',
        PUBLISH_WORKFLOW: '/workflow-setting/{workflowSettingId}/publish',
        PUBLISH_OVERRIDDEN_WORKFLOW: '/workflow-setting/{workflowSettingId}/publish-overridden-workflow',
        UNPUBLISH_WORKFLOW: '/workflow-setting/{workflowSettingId}/unpublish',
        CREATE_NEW_VERSION_WORKFLOW: '/workflow-setting/new-version',
        OVERRIDE_WORKFLOW: '/workflow-setting/{workflowSettingId}/override',
        VALIDATE_OVERRIDE_WORKFLOW: '/workflow-setting/{workflowSettingId}/validate-override',
        HISTORY_WORKFLOW: '/workflow-setting/{workflowSettingId}/history',
        HISTORY_STEP: '/workflow-step/{stepId}/history',
        CLONE_WORKFLOW: '/workflow-setting/{workflowSettingId}/clone',
        DELETE_WORKFLOW: '/workflow-setting/{workflowSettingId}',
        DELETE_SELECTED_OVERRIDEN_WORKFLOW: '/workflow-setting/delete-selected-overriden-workflow',
        DELETE_OVERRIDDEN_WORKFLOW: '/workflow-setting/{workflowSettingId}/override',
        DELETE_UNPUBLISHED_WORKFLOW: '/workflow-setting/{workflowSettingId}/unpublished-version',
        DELETE_UNPUBLISHED_OVERRIDDEN_WORKFLOW: '/workflow-setting/{workflowSettingId}/unpublished-override-version',
        ADD_NEW_STEP: '/workflow-step',
        SHARE_CHILD_LIMIT_STEP: '/workflow-step/{stepId}/limit-share',
        COPY_STEP_TO_OVERRIDDEN: '/workflow-step/copy-step-to-overridden',
        DELETE_STEP_TO_OVERRIDDEN: '/workflow-step/delete-step-from-overridden',
        GET_STEP_BALANCE: '/workflow-step/{stepId}/aggregate-balance?entityId={entityId}',
        GET_EXCEPTIONS_STEPS: '/workflow-step/{stepId}/exception-steps',
        SHARE_CHILD_LIMIT_LIST: '/workflow-shared-child-limit/{stepId}?entityId={entityId}',
        GET_STEP_DETAIL: '/workflow-step/{stepId}',
        DELETE_STEP: '/workflow-step/{stepId}',
        DELETE_UNPUBLISHED_VERSION_STEP: '/workflow-step/{stepId}/unpublished-version',
        MOVE_STEP: '/workflow-step/{stepId}/update-sequence',
        SHARE_NEW_CHILD_LIMIT: '/workflow-shared-child-limit',
        DELETE_SHARE_CHILD_LIMIT: '/workflow-shared-child-limit/{sharedChildLimitId}?entityId={entityId}',
        UPDATE_SHARE_CHILD_LIMIT: '/workflow-shared-child-limit/{sharedChildLimitId}',
        GET_WORKFLOW_SHARED_BUCKET: '/workflow-shared-bucket/{stepId}',
        DELETE_WORKFLOW_SHARED_BUCKET: '/workflow-shared-bucket/{id}',
        ROLE_BASED_SHARE_CHILD_LIMIT_STEPS: '/workflow-step/role-based-steps?limit={limit}&page={page}',
      },
      RULES: {
        GET_WORKFLOW_RULES_LIST: '/workflow-rules?limit={limit}&page={page}&noLimit={noLimit}',
        GET_ALL_WORKFLOW_RULES_LIST: '/workflow-rules?noLimit={noLimit}',
        GET_WORKFLOW_RULE_BY_ID: '/workflow-rules/{id}',
        DELETE_WORKFLOW_RULE_BY_ID: '/workflow-rules/{id}',
        CREATE_WORKFLOW_RULE: '/workflow-rules',
        UPADTE_WORKFLOW_RULE: '/workflow-rules',
      },
      COMPANIES: {
        GET_COMPANIES_BY_ENTITY_ID: '/finance-admin/entities/{entityId}/company-codes?limit={limit}&page={page}',
        GET_COMPANY_CODE_BY_ENTITY_ID: '/finance-admin/entities/{entityId}/company-code',
        CREATE_COMPANY_CODE: '/finance-admin/company-code',
        UPDATE_COMPANY_CODE: '/finance-admin/company-code',
        DELETE_COMPANY_CODE_BY_ID: '/finance-admin/company-code/{id}',
        TOGGLE_COMPANY_ACTIVE_STATE: '/finance-admin/toggle-company-state',
        DEACTIVATE_COMPANY_CODE: '/finance-admin/companies/{companyId}/deactivate',
        UPDATE_MULTI_NATURAL_ACCOUNT: '/finance-admin/companies/update-multi-natural-account',
        HISTORY_COMPANY: '/finance-admin/companies/{entityId}/history',
        EVIDENCE_UPLOAD: '/finance-admin/companies/{companyId}/upload-evidence',
        GET_EVIDENCES: '/finance-admin/companies/{companyId}/evidences'
      },
      COST_CENTER: {
        GET_COST_CENTERS_BY_COMPANY_ID: '/finance-admin/companies/{companyId}/cost-centers?limit={limit}&page={page}',
        CREATE_COST_CENTER: '/finance-admin/cost-center',
        UPDATE_COST_CENTER: '/finance-admin/cost-center',
        DELETE_COST_CENTER_BY_ID: '/finance-admin/cost-center/{id}',
        IMPORT_COST_CENTER: '/finance-admin/import-cost-center',
        HISTORY: '/finance-admin/cost-center/{costCenterId}/history',
      },
      ANALYSIS_CODE: {
        GET_ANALYSIS_CODE_BY_COMPANY_ID: '/finance-admin/companies/{companyId}/analysis-codes?limit={limit}&page={page}',
        CREATE_ANALYSIS_CODE: '/finance-admin/analysis-code',
        UPDATE_ANALYSIS_CODE: '/finance-admin/analysis-code',
        UPDATE_FUSION_INTEGRATION: '/finance-admin/fusion-integration',
        DELETE_ANALYSIS_CODE_BY_ID: '/finance-admin/analysis-code/{id}',
        IMPORT_ANALYSIS_CODE: '/finance-admin/import-analysis-code',
      },
      NATURAL_ACCOUNT_NUMBER: {
        GET_NATURAL_ACCOUNT_NUMBER_BY_ENTITY_ID: '/finance-admin/companies/{companyId}/natural-account-numbers?limit={limit}&page={page}',
        CREATE_NATURAL_ACCOUNT_NUMBER: '/finance-admin/natural-account-number',
        UPDATE_NATURAL_ACCOUNT_NUMBER: '/finance-admin/natural-account-number',
        DELETE_NATURAL_ACCOUNT_NUMBER_ID: '/finance-admin/natural-account-number/{id}',
        IMPORT_NATURAL_ACCOUNT: '/finance-admin/import-natural-account-number',
      },
      UPDATE_USER_LOGIN_ID: '/admin/replace-login-id'
    },
    DASHBOARD: {
      STATUS_WISE_COUNT: '/dashboard/status-wise-count?year={year}',
      REQUEST_WISE_MONTHLY_COUNT: '/dashboard/request-type-wise-count?year={year}&entityId={entityId}'
    },
    GRAPH_USER: {
      SEARCH_USERS: '/graph-users/search-users?searchText={searchText}&orderBy={orderBy}&count={count}',
      GET_USER_DETAIL: '/graph-users?userId={userId}',
      GET_USERS_BY_USER_IDS: '/graph-users/by-userIds?userIds={emailIds}',
    }
  }
};

export const ReplaceUrlVariable = (url: string, variables: any) => {
  Object.keys(variables).forEach((variableKey: string) => {
    var re = new RegExp('{' + variableKey + '}', 'g');
    url = url.replace(re, variables[variableKey])
  })
  return url;
}

export const FilterConverter = (filters: IFilter[]) => {
  let filterString = '';

  filters.forEach((filter) => {
    filterString = (filterString ? filterString + ';' : '') + filter.title + ':' + filter.value
  })

  return filterString;
}
