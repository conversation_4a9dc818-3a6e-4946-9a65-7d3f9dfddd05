"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskPrivateController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const enums_1 = require("../../shared/enums");
const dtos_2 = require("../dtos");
const services_1 = require("../services");
let TaskPrivateController = class TaskPrivateController {
    constructor(taskService) {
        this.taskService = taskService;
    }
    approveTask(approvalActionOnTaskRequestDto) {
        const { task_id, action_by } = approvalActionOnTaskRequestDto;
        return this.taskService.taskApprovalByTaskId(task_id, action_by, enums_1.TASK_ACTION.APPROVE);
    }
    rejectTask(approvalActionOnTaskRequestDto) {
        const { task_id, action_by } = approvalActionOnTaskRequestDto;
        return this.taskService.taskApprovalByTaskId(task_id, action_by, enums_1.TASK_ACTION.REJECT);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perform approve action on the afe proposal task',
        type: dtos_1.MessageResponseDto
    }),
    (0, common_1.Post)('approve'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_2.ApprovalActionOnTaskRequestDto]),
    __metadata("design:returntype", void 0)
], TaskPrivateController.prototype, "approveTask", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perform reject action on the afe proposal task',
        type: dtos_1.MessageResponseDto
    }),
    (0, common_1.Post)('reject'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_2.ApprovalActionOnTaskRequestDto]),
    __metadata("design:returntype", void 0)
], TaskPrivateController.prototype, "rejectTask", null);
TaskPrivateController = __decorate([
    (0, swagger_1.ApiTags)('Task Private APIs'),
    (0, common_1.Controller)('/private/tasks'),
    (0, swagger_1.ApiHeader)({ name: 'apikey', description: 'API key for private endpoint authentication.' }),
    (0, common_1.UseGuards)(guards_1.ApiKeyGuard),
    __metadata("design:paramtypes", [services_1.TaskService])
], TaskPrivateController);
exports.TaskPrivateController = TaskPrivateController;
//# sourceMappingURL=task-private.controller.js.map