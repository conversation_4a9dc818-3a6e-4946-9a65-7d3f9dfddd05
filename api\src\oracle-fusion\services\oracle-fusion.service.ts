import { Injectable } from '@nestjs/common';
import { toNumber } from 'lodash';
import {
	AfeProposalAmountSplitRepository,
	AfeProposalRepository,
} from 'src/afe-proposal/repositories';
import {
	AFE_CATEGORY,
	AMOUNT_SPLIT,
	BUDGET_TYPE_TITLE,
	BUDGET_TYPE_TITLE_FOR_FUSION,
	NOTIFICATION_ENTITY_TYPE,
} from 'src/shared/enums';
import { SequlizeOperator } from 'src/shared/helpers';
import { ORACLE_FUSION_DATA } from '../constants';
import { FinanceAdminService } from 'src/finance/services';
import {
	AdminApiClient,
	FusionApiClient,
	FusionUaeApiClient,
	MSGraphApiClient,
} from 'src/shared/clients';
import { CostCenterRepository } from 'src/finance/repositories';
import { SharedNotificationService } from 'src/shared/services';

@Injectable()
export class OracleFusionService {
	constructor(
		private readonly afeProposalRepository: AfeProposalRepository,
		private readonly afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository,
		private readonly fussionApi: FusionApiClient,
		private readonly fussionUaeApi: FusionUaeApiClient,
		private readonly sequlizeOperator: SequlizeOperator,
		private readonly financeAdminService: FinanceAdminService,
		private readonly adminApiClient: AdminApiClient,
		private readonly costCenterRepository: CostCenterRepository,
		private readonly mSGraphApiClient: MSGraphApiClient,
		private readonly sharedNotificationService: SharedNotificationService,
	) { }

	/**
	 * Post data to oracle fusion for all the recent approved AFE whose entity is fusion enabled.
	 * @returns
	 */
	public async sendFusionEnabledApprovedAfe() {

		console.log('sendFusionEnabledApprovedAfe');

		const businessHierarchy = await this.adminApiClient.getAllBusinessHierarchy();

		console.log('sendFusionEnabledApprovedAfe 1');

		const fusionReadyAfeList = await this.afeProposalRepository.getFusionReadyAfeProposals();

		console.log('sendFusionEnabledApprovedAfe 2', fusionReadyAfeList.length);

		let fusionIntegrationAfeList = [];

		if (fusionReadyAfeList.length) {
			const isNotProd = process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'staging';

			for (let i = 0; i < fusionReadyAfeList.length; i++) {
				const result = fusionReadyAfeList[i];
				let afeProposalId = toNumber(result.id);

				const proposalAmountSplit =
					await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndTypes(
						afeProposalId,
						[
							AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT,
							AMOUNT_SPLIT.GL_CODE_SPLIT,
							AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT,
							AMOUNT_SPLIT.COST_CENTER_SPLIT,
						],
						['objectId', 'objectTitle', 'type', 'amount', 'currency', 'additionalCurrencyAmount'],
					);

				let projectComponentList = [];
				let budgetReferenceNumbers = [];
				let glCodes = [];
				let costCenterCode = '';
				let fusionIntegrationData: any = {
					id: null,
					isUAEEntity: false,
					Projects: [],
					Budgets: [],
					ProjectClassifications: [],
					fusionIntegrationResponse: [],
					submitterId: null,
					projectLeaderEmail: null,
					sendEmailToSubmitter: false,
					sendEmailToLeader: false,
					Tasks: []
				};

				for (let i = 0; i < proposalAmountSplit.length; i++) {
					const proposalAmount = proposalAmountSplit[i];

					if (proposalAmount.type === AMOUNT_SPLIT.COST_CENTER_SPLIT) {
						const costCenterId = proposalAmount.objectId; // Working for single cost center only.

						const costCenterDetail = await this.costCenterRepository.getCostCenterById(
							costCenterId,
						);

						if (costCenterDetail?.code) {
							costCenterCode = costCenterDetail.code;
						}

						if (proposalAmount?.additionalInfo?.budgetReferenceNumberSplit?.length) {
							proposalAmount.additionalInfo.budgetReferenceNumberSplit.forEach(
								budgetReferenceNumberSplit => {
									budgetReferenceNumbers.push(budgetReferenceNumberSplit.number);
								},
							);
						}
					}

					if (proposalAmount.type === AMOUNT_SPLIT.GL_CODE_SPLIT) {
						glCodes.push({
							code: proposalAmount.objectTitle,
							additionalCurrencyAmount: proposalAmount.additionalCurrencyAmount,
						});
					}

					if (proposalAmount.type === AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT) {
						projectComponentList.push(proposalAmount.objectTitle);
					}
				}

				let {
					id,
					afeRequestTypeId,
					category,
					createdOn,
					data,
					parentAfeId,
					budgetType,
					projectReferenceNumber,
					supplementalData,
					additionalCurrencyAmount,
					workflowYear,
					name: projectTitle,
					afeRequestType,
					fusionIntegrationResponse,
					entityId,
					submitterId,
					entityCode
				} = result;

				const submitterDetail = await this.mSGraphApiClient.getUserDetails(submitterId.toLowerCase());


				if (submitterDetail) {
					submitterId = submitterDetail?.mail || submitterDetail?.userPrincipalName || submitterId;
					fusionIntegrationData.sendEmailToSubmitter = !!submitterDetail?.mail;
				}

				let projectLeaderEmail = data.projectDetails.projectLeader.mail;

				if (data?.projectDetails?.projectLeader?.mail) {
					const projectLeaderDetail = await this.mSGraphApiClient.getUserDetails(data.projectDetails.projectLeader.mail.toLowerCase());

					projectLeaderEmail = projectLeaderDetail?.mail || projectLeaderDetail?.userPrincipalName || submitterId;

					fusionIntegrationData.sendEmailToLeader = !!projectLeaderDetail?.mail;
				}


				// For Equity Infusion Custom GL Code
				if (afeRequestTypeId === 3) {
					// Equity Infusion - 8846(based on entity selected get company code), get natural account number for selected entity and keep rest all segment 0.
					// 8846-0000-********-0000-000000-000000-********-0000

					const companyDetail = await this.financeAdminService.getEntityActiveCompanyCodeDetails(
						entityId,
						false,
					);

					if (companyDetail?.code) {
						const naturalAccountList =
							await this.financeAdminService.getCapexNaturalAccountNumbersByCompanyCodeId(
								companyDetail.id,
							);

						let EiNaturalAccount = '';
						if (naturalAccountList?.length) {
							EiNaturalAccount = naturalAccountList[0].number;
						}
						const EiGlCode =
							companyDetail.code +
							'-0000-' +
							(EiNaturalAccount || '********') +
							'-0000-000000-000000-********-0000';

						// glCodes.push(EiGlCode);
						glCodes.push({
							code: EiGlCode,
							additionalCurrencyAmount
						});
					}
				}

				const projectTitleRefNumber = projectTitle + ' ' + projectReferenceNumber;
				const createdOnDateObj = new Date(createdOn);

				let supplimentaryAFENumbers = '';
				let budgetRefNumbers = '';
				let allAfeIds = [];

				if (category === AFE_CATEGORY.SUPPLEMENTAL) {
					const afeNumbers = await this.afeProposalRepository.getApprovedAfeAndItsChildern(
						parentAfeId,
					);

					supplimentaryAFENumbers = '';

					afeNumbers.forEach((afeNumber, key) => {
						supplimentaryAFENumbers =
							supplimentaryAFENumbers + (key ? '|' : '') + afeNumber.projectReferenceNumber;
						allAfeIds.push(toNumber(afeNumber.id));
					});

					const budgetRefAfes = allAfeIds.filter(afeId => {
						if (toNumber(afeId) !== toNumber(afeProposalId)) {
							return afeId;
						}
					});

					const proposalBudgetRefSplit =
						await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdsAndTypes(
							budgetRefAfes,
							[AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT],
						);
					const budgetRefClubbed = [];

					proposalBudgetRefSplit.forEach(proposalBudgetRef => {
						if (budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId]) {
							budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId].push(proposalBudgetRef);
						} else {
							budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId] = [];
							budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId].push(proposalBudgetRef);
						}
					});

					budgetRefAfes.forEach((afeNumber, mainKey) => {
						if (budgetRefClubbed['BR' + afeNumber]) {
							budgetRefNumbers = budgetRefNumbers + (mainKey ? '|' : '');

							budgetRefClubbed['BR' + afeNumber].forEach((budgetRef, childKey) => {
								budgetRefNumbers = budgetRefNumbers + (childKey ? ',' : '') + budgetRef.objectTitle;
							});
						} else {
							budgetRefNumbers =
								budgetRefNumbers +
								(budgetRefNumbers ? '|' : '') +
								ORACLE_FUSION_DATA.UNBUDGETED_REF_NUMBER;
						}
					});

					if (budgetReferenceNumbers.length) {
						budgetReferenceNumbers.forEach((budgetReferenceNumber, key) => {
							budgetRefNumbers =
								budgetRefNumbers +
								(budgetRefNumbers && !key ? '|' : '') +
								(key ? ',' : '') +
								budgetReferenceNumber;
						});
					} else {
						budgetRefNumbers =
							budgetRefNumbers +
							(budgetRefNumbers ? '|' : '') +
							ORACLE_FUSION_DATA.UNBUDGETED_REF_NUMBER;
					}
				} else {
					allAfeIds.push(id);
					supplimentaryAFENumbers = projectReferenceNumber;

					if (budgetReferenceNumbers.length) {
						budgetReferenceNumbers.forEach((budgetReferenceNumber, key) => {
							budgetRefNumbers =
								budgetRefNumbers +
								(budgetRefNumbers && !key ? '|' : '') +
								(key ? ',' : '') +
								budgetReferenceNumber;
						});
					} else {
						budgetRefNumbers =
							budgetRefNumbers +
							(budgetRefNumbers ? '|' : '') +
							ORACLE_FUSION_DATA.UNBUDGETED_REF_NUMBER;
					}
				}

				fusionIntegrationData.id = id;
				fusionIntegrationData.fusionIntegrationResponse = fusionIntegrationResponse
					? fusionIntegrationResponse
					: [];
				fusionIntegrationData.submitterId = submitterId;
				fusionIntegrationData.projectLeaderEmail = projectLeaderEmail;

				if (glCodes.length) {

					if (afeRequestTypeId === 2) {
						const responseDataOpex = await this.getOpexProjectDetail({
							glCodes,
							additionalCurrencyAmount,
							afeRequestType,
							afeRequestTypeId,
							category,
							createdOn,
							supplementalData,
							projectTitleRefNumber,
							projectReferenceNumber,
							projectLeaderEmail,
							submitterId,
							workflowYear,
							entityCode,
							isNotProd,
							costCenterCode,
							supplimentaryAFENumbers,
							createdOnDateObj,
						});

						console.log(responseDataOpex);

						fusionIntegrationData.Projects = responseDataOpex.projectDetail as any[];

						fusionIntegrationData.Tasks = responseDataOpex.Tasks as any[];
					} else {
						fusionIntegrationData.Projects = await this.getProjectDetail({
							glCodes,
							additionalCurrencyAmount,
							afeRequestType,
							afeRequestTypeId,
							category,
							createdOn,
							supplementalData,
							projectTitleRefNumber,
							projectReferenceNumber,
							projectLeaderEmail,
							submitterId,
							workflowYear,
							entityCode,
							isNotProd,
							costCenterCode,
							supplimentaryAFENumbers,
							createdOnDateObj,
						});
					}


					console.dir('Project ---- ');
					console.dir(fusionIntegrationData.Projects, { depth: 10 });

					const todayDate = new Date();
					const currentYear = todayDate.getFullYear();
					let currentMonth = todayDate.getMonth() + 1;
					let currentDate = todayDate.getDate();

					const parentEntityDetails = await this.adminApiClient.getParentsOfEntity(entityId);

					const financialEntityList = [
						'GCC-LG',
						'GCC-PNT',
						'HO',
						'MEARO-C1',
						'MEARO-LG',
						'DIGI-FZE',
						'DPWDSLTD-C1',
						'DTE'
					];

					const overridenEntityList = [
						'UAE-JEDPT',
						'UAE-JEDLG'
					];

					let financialPlanType = '';

					if (parentEntityDetails?.length) {
						const mainEntityDetail = parentEntityDetails.find(parentEntity => {
							return financialEntityList.includes(parentEntity.code);
						});

						const overridenEntityDetail = parentEntityDetails.find(parentEntity => {
							return overridenEntityList.includes(parentEntity.code);
						});

						const entityDetail = overridenEntityDetail ? overridenEntityDetail : mainEntityDetail;

						if (entityDetail) {
							switch (entityDetail.code) {
								case 'GCC-LG':
								case 'GCC-PNT':
									financialPlanType = 'UAER Cost Only';

									fusionIntegrationData.ProjectClassifications.push({
										ProjectName: projectTitleRefNumber,
										ClassCategory: ORACLE_FUSION_DATA.CLASS_CATEGORY.TECHNICAL_DEPARTMENT_PROJECT,
										ClassCode: 'Yes',
									});

									fusionIntegrationData.isUAEEntity = true;
									break;
								case 'HO':
									financialPlanType = 'HO Cost Only';
									break;
								case 'DTE':
									financialPlanType = 'UAER Cost Only';
									break;
								case 'MEARO-C1':
								case 'MEARO-LG':
									financialPlanType = 'RO Cost Only';
									break;
								case 'DIGI-FZE':
									financialPlanType = 'DPW Digital Cost Only';
									break;
								case 'UAE-JEDPT':
								case 'UAE-JEDLG':
									financialPlanType = 'JED Cost Budget Non Periodic & Absolute Control';

									fusionIntegrationData.ProjectClassifications.push({
										ProjectName: projectTitleRefNumber,
										ClassCategory: ORACLE_FUSION_DATA.CLASS_CATEGORY.TECHNICAL_DEPARTMENT_PROJECT,
										ClassCode: 'Yes',
									});

									fusionIntegrationData.isUAEEntity = true;
									break;
								case 'DPWDSLTD-C1':
									financialPlanType = 'DAR Cost Budget Non Periodic & Absolute Control';
									break;
								default:
									financialPlanType = '';
							}
						}
					}

					fusionIntegrationData.Budgets.push({
						FinancialPlanType: financialPlanType,
						PlanVersionName:
							ORACLE_FUSION_DATA.PLAN_VERSION_SUFFFIX +
							(currentMonth + '_' + currentDate + '_' + currentYear),
						ProjectNumber: projectReferenceNumber,
						PlanVersionStatus: ORACLE_FUSION_DATA.PLAN_VERSION_STATUS,
						BudgetCreationMethod: ORACLE_FUSION_DATA.BUDGET_CREATION_METHOD,
						TaskNumber: projectReferenceNumber,
						ResourceName: ORACLE_FUSION_DATA.RESOURCE_NAME,
						Currency: additionalCurrencyAmount.currency,
						RawCostAmounts: additionalCurrencyAmount.amount,
						SourceBudgetLineReference:
							afeRequestTypeId === 1 || afeRequestTypeId === 2
								? budgetRefNumbers
									? budgetRefNumbers
									: null
								: null,
					});

					fusionIntegrationData.ProjectClassifications.push({
						ProjectName: projectTitleRefNumber,
						ClassCategory: ORACLE_FUSION_DATA.CLASS_CATEGORY.AFE_TYPE,
						ClassCode: afeRequestType.title,
					});

					if (budgetType) {
						fusionIntegrationData.ProjectClassifications.push({
							ProjectName: projectTitleRefNumber,
							ClassCategory: ORACLE_FUSION_DATA.CLASS_CATEGORY.BUDGET_TYPE,
							ClassCode: BUDGET_TYPE_TITLE_FOR_FUSION[budgetType],
						});
					}

					projectComponentList.forEach(projectComponent => {
						fusionIntegrationData.ProjectClassifications.push({
							ProjectName: projectTitleRefNumber,
							ClassCategory: ORACLE_FUSION_DATA.CLASS_CATEGORY.PROJECT_COMPONENT_TYPE,
							ClassCode: projectComponent,
						});
					});

					fusionIntegrationAfeList.push(fusionIntegrationData);
				}
			}

			let successCount = 0;
			let failedCount = 0;

			let returnResponse = [];

			for (let i = 0; i < fusionIntegrationAfeList.length; i++) {
				const { id, fusionIntegrationResponse, isUAEEntity, submitterId, sendEmailToSubmitter, sendEmailToLeader, projectLeaderEmail, ...fusionPayload } =
					fusionIntegrationAfeList[i];
				let data;

				try {
					if (isUAEEntity) {
						// FOR UAE
						data = await this.fussionUaeApi.sendData(fusionPayload, isNotProd);
					} else {
						data = await this.fussionApi.sendData(fusionPayload, isNotProd);
					}

					if (!data) {
						data = {
							'Error Message': 'No response from fusion!'
						}
					}

					returnResponse.push({
						requestPayload: fusionPayload,
						data,
						isUAEEntity,
						isNotProd,
					});
				} catch (error) {
					await this.sendEmailToITSupportOnFailure(id, businessHierarchy.id, error);
					data = {
						'Error Message': error?.message || 'Something went wrong',
					};
				}

				data.createdOn = new Date();

				let newData = [];

				if (fusionIntegrationResponse.length) {
					fusionIntegrationResponse.forEach(fusionIntegration => {
						newData.push(fusionIntegration);
					});
				}

				newData.push({
					...data,
					fusionPayload
				});

				await this.afeProposalRepository.updateAfeProposalByIdWOUser(id, {
					fusionIntegrationStatus: data.Status === 'SUCCESS' ? 2 : 1,
					retryFusionIntegration: this.sequlizeOperator.sequelizeLiteral(
						'retry_fusion_integration + 1',
					),
					fusionIntegrationResponse: newData,
				});

				if (data.Status === 'SUCCESS') {
					successCount = successCount + 1;
					// const submitterDetail = await this.mSGraphApiClient.getUserDetails(submitterId);

					if (sendEmailToSubmitter) {
						let emailUser: any = { to: [submitterId] }

						if (sendEmailToLeader && projectLeaderEmail) {
							emailUser.cc = [projectLeaderEmail];
						}

						await this.sharedNotificationService.sendNotificationForAfeProposal(
							id,
							id,
							NOTIFICATION_ENTITY_TYPE.FUSION_NOTIFICATION,
							emailUser,
							'AFE.EMAIL.FUSION.INTEGRATION.SUCCESSFUL',
						);
					}
				} else {
					failedCount = failedCount + 1;
					await this.sendEmailToITSupportOnFailure(id, businessHierarchy.id, JSON.stringify({
						requestPayload: fusionPayload,
						fusionResponse: data
					}));
				}
			}

			return {
				message:
					fusionIntegrationAfeList.length +
					' AFE data sent for integration. Total Success: ' +
					successCount +
					' & Failed: ' +
					failedCount +
					'.',
				data: returnResponse,
			};
		} else {
			return { message: 'No AFE available to send data for oracle fusion integration.' };
		}
	}

	private async getUsersEmailByRoleAndEntityId(role: string, entityId: number): Promise<string[]> {
		const userEmails: string[] = [];
		const users = await this.adminApiClient.getUsersByRoleOfAnEntity(role, entityId);
		const userIds = users?.map(user => user.user_name.toLowerCase());
		const userAdDetails = userIds.length
			? await this.mSGraphApiClient.getUsersDetails(userIds)
			: [];
		userEmails.push(...userAdDetails.map(user => user.mail));
		return userEmails;
	}

	private async sendEmailToITSupportOnFailure(proposalId: number, groupEntityId: number, error) {
		const itSupportEmails = await this.getUsersEmailByRoleAndEntityId('FusionFailure', groupEntityId);
		await this.sharedNotificationService.sendNotificationForAfeProposal(
			proposalId,
			proposalId,
			NOTIFICATION_ENTITY_TYPE.FUSION_NOTIFICATION,
			{ to: [...itSupportEmails] },
			'AFE.EMAIL.FUSION.INTEGRATION.FAILED',
			false,
			{ error },
		);
	}


	// For Capex & EQ Type
	private async getProjectDetail(payload: any) {

		const { glCodes, additionalCurrencyAmount, afeRequestType, afeRequestTypeId, category, createdOn, supplementalData, projectTitleRefNumber, projectReferenceNumber, projectLeaderEmail, submitterId, workflowYear, entityCode, isNotProd, costCenterCode, supplimentaryAFENumbers, createdOnDateObj } = payload;

		let finalProjectDetail = [];


		glCodes.forEach(glCode => {
			const segments = glCode.code.split('-');
			const segmentData = {
				Segment1: segments[0],
				Segment2: segments[1],
				Segment3: segments[2],
				Segment4: segments[3],
				Segment5: segments[4],
				Segment6: segments[5],
				Segment7: segments[6],
				Segment8: segments[7],
			};

			const companyCode = segments[0];

			let projectEndDate;
			if (afeRequestTypeId !== 2) {
				projectEndDate = new Date(
					createdOnDateObj.setFullYear(createdOnDateObj.getFullYear() + 2),
				);
			} else {
				if (entityCode === 'HOBU') {
					projectEndDate = new Date((workflowYear + 1) + '-12-31T23:59:59.000Z');
				} else {
					projectEndDate = new Date(workflowYear + '-12-31T23:59:59.000Z');
				}
			}

			let devEmailId = null;
			if (isNotProd) {
				devEmailId = '<EMAIL>';
			}

			let projectDetail: any = {
				ProjectCurrencyCode: additionalCurrencyAmount.currency,
				ProjectDescription: projectTitleRefNumber,
				ProjectStartDate: createdOn,
				ProjectEndDate: projectEndDate,
				ProjectName: projectTitleRefNumber,
				ProjectNumber: projectReferenceNumber,
				ProjectManagerEmail: devEmailId ? devEmailId : projectLeaderEmail,
				PersonEmail: devEmailId ? devEmailId : submitterId,
				IsSupplementary: category === AFE_CATEGORY.SUPPLEMENTAL,
				ParentAFENumber:
					category === AFE_CATEGORY.SUPPLEMENTAL ? supplementalData.parentAfeNo : null,
				CompanyCode: companyCode,
				AFEType: afeRequestType.title,
				...segmentData,
				Attribute1: null,
				Attribute2: null,
				Attribute3: null,
				Attribute4: null,
				Attribute5: null,
				AFE_SupplimentaryAFENumbers: supplimentaryAFENumbers,
			};

			if (afeRequestTypeId === 1 || afeRequestTypeId === 2) {
				projectDetail = {
					...projectDetail,
					originalCapexCostCenter: costCenterCode ? costCenterCode : null, // Add cost center number
				};
			}

			if (afeRequestTypeId === 3) {
				projectDetail = {
					...projectDetail,
					originalCapexCostCenter: '1101', // Default for all Equity Infusion
				};
			}

			finalProjectDetail.push(projectDetail);
		});

		return finalProjectDetail;
	}

	//For Opex New Implementation
	private async getOpexProjectDetail(payload: any) {

		const { glCodes, additionalCurrencyAmount, afeRequestType, afeRequestTypeId, category, createdOn, supplementalData, projectTitleRefNumber, projectReferenceNumber, projectLeaderEmail, submitterId, workflowYear, entityCode, isNotProd, costCenterCode, supplimentaryAFENumbers, createdOnDateObj } = payload;

		if (!glCodes.length) {
			return [];
		}

		let finalSegments = [];

		let projectEndDate;
		if (afeRequestTypeId !== 2) {
			projectEndDate = new Date(
				createdOnDateObj.setFullYear(createdOnDateObj.getFullYear() + 2),
			);
		} else {
			if (entityCode === 'HOBU') {
				projectEndDate = new Date((workflowYear + 1) + '-12-31T23:59:59.000Z');
			} else {
				projectEndDate = new Date(workflowYear + '-12-31T23:59:59.000Z');
			}
		}

		let devEmailId = null;
		if (isNotProd) {
			devEmailId = '<EMAIL>';
		}

		let projectDetail: any = {
			ProjectCurrencyCode: additionalCurrencyAmount.currency,
			ProjectDescription: projectTitleRefNumber,
			ProjectStartDate: createdOn,
			ProjectEndDate: projectEndDate,
			ProjectName: projectTitleRefNumber,
			ProjectNumber: projectReferenceNumber,
			ProjectManagerEmail: devEmailId ? devEmailId : projectLeaderEmail,
			PersonEmail: devEmailId ? devEmailId : submitterId,
			IsSupplementary: category === AFE_CATEGORY.SUPPLEMENTAL,
			ParentAFENumber:
				category === AFE_CATEGORY.SUPPLEMENTAL ? supplementalData.parentAfeNo : null,
			AFEType: afeRequestType.title,
			Attribute1: null,
			Attribute2: null,
			Attribute3: null,
			Attribute4: null,
			Attribute5: null,
			AFE_SupplimentaryAFENumbers: supplimentaryAFENumbers
		};

		if (afeRequestTypeId === 3) {
			projectDetail = {
				...projectDetail,
				originalCapexCostCenter: '1101', // Default for all Equity Infusion
			};
		}

		glCodes.forEach(glCode => {
			console.log('------------------- glCode S -----------------');
			console.dir(glCode, { depth: 10 });
			console.log('------------------- glCode E -----------------');


			const segments = glCode.code.split('-');

			const companyCode = segments[0];

			if (afeRequestTypeId === 1 || afeRequestTypeId === 2) {
				projectDetail = {
					...projectDetail,
					CompanyCode: companyCode,
					originalCapexCostCenter: costCenterCode ? costCenterCode : null, // Add cost center number
				};
			}

			finalSegments.push({
				Segment1: segments[0],
				Segment2: segments[1],
				Segment3: segments[2],
				Segment4: segments[3],
				Segment5: segments[4],
				Segment6: segments[5],
				Segment7: segments[6],
				Segment8: segments[7],
				TaskAmount: toNumber(glCode?.additionalCurrencyAmount?.amount || additionalCurrencyAmount?.amount || 0),
				TaskName: segments[2]
			});
		});

		return { projectDetail, finalSegments };
	}
}
