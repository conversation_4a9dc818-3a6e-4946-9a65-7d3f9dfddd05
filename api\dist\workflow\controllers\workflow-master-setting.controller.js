"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowMasterSetingController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const enums_1 = require("../../shared/enums");
const dtos_2 = require("../dtos");
const clone_workflow_request_dto_1 = require("../dtos/request/clone-workflow-request.dto");
const list_master_workflow_request_dto_1 = require("../dtos/request/list-master-workflow-request.dto");
const get_history_response_dto_1 = require("../dtos/response/get-history-response.dto");
const services_1 = require("../services");
let WorkflowMasterSetingController = class WorkflowMasterSetingController {
    constructor(workflowMasterSettingService) {
        this.workflowMasterSettingService = workflowMasterSettingService;
    }
    getAllMasterSettingList(query, listMasterWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const _a = Object.assign({}, query), { limit = null, page = null } = _a, filter = __rest(_a, ["limit", "page"]);
            return this.workflowMasterSettingService.getAllMasterSettingList(limit, page, filter, listMasterWorkflowRequestDto);
        });
    }
    getAllMasterSetting(query) {
        return __awaiter(this, void 0, void 0, function* () {
            const _a = Object.assign({}, query), { limit = null, page = null } = _a, filter = __rest(_a, ["limit", "page"]);
            return this.workflowMasterSettingService.getAllMasterSetting(limit, page, filter);
        });
    }
    getOverrideAllSettingsDetail(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.getOverrideAllSettingsDetail(workflowSettingId);
        });
    }
    getAllAssignedUniqueRolesForWorkflowSetting(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.getAllAssignedUniqueRolesForWorkflowSetting(workflowSettingId);
        });
    }
    getAllUnpublishedOverriden(parentId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.getAllUnpublishedOverriden(parentId);
        });
    }
    addNewMasterWorkflowSetting(request, newMasterWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.addNewMasterWorkflowSetting(newMasterWorkflowRequestDto, request.currentContext);
        });
    }
    getWorkflowDetailByWorkflowSettingId(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.getWorkflowDetailByWorkflowSettingId(workflowSettingId);
        });
    }
    getPolicyDetailByWorkflowSettingId(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.getPolicyDetailByWorkflowSettingId(workflowSettingId);
        });
    }
    downloadPolicyDetailByWorkflowSettingId(res, workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { report, filename } = yield this.workflowMasterSettingService.downloadPolicyDetailByWorkflowSettingId(workflowSettingId);
            res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
            res.header('Access-Control-Expose-Headers', 'Content-Disposition');
            res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            return res.send(report);
        });
    }
    getWorkflowSettingHistory(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.getWorkflowSettingHistory(workflowSettingId);
        });
    }
    publishWorkflowSetting(request, workflowSettingId, unpublishedVersionRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.publishWorkflow(workflowSettingId, (unpublishedVersionRequestDto === null || unpublishedVersionRequestDto === void 0 ? void 0 : unpublishedVersionRequestDto.unpublishedVersion) || false, request.currentContext);
        });
    }
    validateOverridesEntity(request, workflowSettingId, overrideWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.validateOverrideWorkflow(workflowSettingId, overrideWorkflowRequestDto);
        });
    }
    overrideMasterWorkflowSetting(request, workflowSettingId, overrideWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.overrideWorkflow(workflowSettingId, overrideWorkflowRequestDto, request.currentContext);
        });
    }
    cloneWorkflowSettingSteps(request, workflowSettingId, CloneWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.cloneWorkflowStep(workflowSettingId, CloneWorkflowRequestDto, request.currentContext);
        });
    }
    deleteWorkflowSetting(request, workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.deleteWorkflow(workflowSettingId, request.currentContext);
        });
    }
    deleteMultipleOverridenWorkflowSetting(request, overridenWorkflowIds) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.deleteMultipleOverridenWorkflowSetting(overridenWorkflowIds, request.currentContext);
        });
    }
    deleteUnpublishedNewVersionWorkflowSetting(request, workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.deleteUnpublishedNewVersionWorkflow(workflowSettingId, request.currentContext);
        });
    }
    deleteOverriddenWorkflowSetting(request, workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.deleteOverriddenWorkflow(workflowSettingId, request.currentContext);
        });
    }
    deleteUnpublishedOverriddenWorkflowSetting(request, workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.deleteUnpublishedOverriddenWorkflowSetting(workflowSettingId, request.currentContext);
        });
    }
    createNewVersionWorkflowSetting(request, newVersionWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const workflowSettingId = newVersionWorkflowRequestDto.workflowSettingId;
            return this.workflowMasterSettingService.createNewVersionWorkflow(workflowSettingId, request.currentContext);
        });
    }
    publishOverriddenWorkflowSetting(request, workflowMasterSettingId, publishedOverriddenWorkflowRequest) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.publishOverriddenWorkflowSetting(workflowMasterSettingId, publishedOverriddenWorkflowRequest, request.currentContext);
        });
    }
    getUnpublishedVersionWorkflowDetailBySettingId(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.workflowMasterSettingService.getUnpublishedVersionWorkflowDetailBySettingId(workflowSettingId);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        type: Number,
        description: 'Number of records in response.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        type: Number,
        description: 'Page number of the paginated record.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'parentId',
        type: Number,
        description: 'ParentId filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'published',
        type: Boolean,
        description: 'Published filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'requestTypeId',
        type: Number,
        description: 'RequestTypeId filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'year',
        type: Number,
        description: 'Year filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'budgetType',
        type: String,
        description: 'BudgetType filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'id',
        type: Number,
        description: 'Id filter for Workflow Listing.',
        required: false
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'EntityId filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get All workflow settings.',
        type: dtos_2.PaginatedGetMasterSettingResponseDTO,
    }),
    (0, common_1.Post)('/list'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, list_master_workflow_request_dto_1.ListMasterWorkflowRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getAllMasterSettingList", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        type: Number,
        description: 'Number of records in response.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        type: Number,
        description: 'Page number of the paginated record.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'parentId',
        type: Number,
        description: 'ParentId filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'published',
        type: Boolean,
        description: 'Published filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'requestTypeId',
        type: Number,
        description: 'RequestTypeId filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'year',
        type: Number,
        description: 'Year filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'budgetType',
        type: String,
        description: 'BudgetType filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'id',
        type: Number,
        description: 'Id filter for Workflow Listing.',
        required: false
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'EntityId filters for Workflow Listing.',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get All workflow settings.',
        type: dtos_2.PaginatedGetMasterSettingResponseDTO,
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getAllMasterSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.Get)('/override-workflow-setting/:workflowSettingId'),
    __param(0, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getOverrideAllSettingsDetail", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.Get)('/get-all-unique-assigned-roles/:workflowSettingId'),
    __param(0, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getAllAssignedUniqueRolesForWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, common_1.Get)('/unpublished-overridden-settings/:parentId'),
    __param(0, (0, common_1.Param)('parentId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getAllUnpublishedOverriden", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add New Master Workflow Setting.',
        type: dtos_2.NewMasterWorkflowResponseDto,
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.NewMasterWorkflowRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "addNewMasterWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get workflow complete details by workflow id.',
        type: dtos_2.WorkflowDetailResponseDTO,
    }),
    (0, common_1.Get)('/:workflowSettingId'),
    __param(0, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getWorkflowDetailByWorkflowSettingId", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get policy workflow complete details by workflow id.',
        type: dtos_2.WorkflowDetailResponseDTO,
    }),
    (0, common_1.Get)('/:workflowSettingId/policy'),
    __param(0, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getPolicyDetailByWorkflowSettingId", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Download policy workflow complete details by workflow id.',
    }),
    (0, common_1.Get)('/:workflowSettingId/policy/download'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "downloadPolicyDetailByWorkflowSettingId", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get workflow setting history by workflow id.',
        type: [get_history_response_dto_1.GetHistoryResponseDTO],
    }),
    (0, common_1.Get)('/:workflowSettingId/history'),
    __param(0, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getWorkflowSettingHistory", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Publish Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Put)('/:workflowSettingId/publish'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_2.UnpublishedVersionRequest]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "publishWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Override Master Workflow Setting.',
        type: [dtos_2.NewMasterWorkflowResponseDto],
    }),
    (0, common_1.Post)('/:workflowSettingId/validate-override'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_2.OverrideWorkflowRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "validateOverridesEntity", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Override Master Workflow Setting.',
        type: dtos_2.NewMasterWorkflowResponseDto,
    }),
    (0, common_1.Post)('/:workflowSettingId/override'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_2.OverrideWorkflowRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "overrideMasterWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Clone Other Workflow Setting Steps.',
        type: dtos_2.WorkflowDetailResponseDTO,
    }),
    (0, common_1.Post)('/:workflowSettingId/clone'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, clone_workflow_request_dto_1.CloneWorkflowRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "cloneWorkflowSettingSteps", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)('/:workflowSettingId'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "deleteWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete Multiple Overriden Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/delete-selected-overriden-workflow'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)('overridenWorkflowIds')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Array]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "deleteMultipleOverridenWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete Unpublished New Version Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)('/:workflowSettingId/unpublished-version'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "deleteUnpublishedNewVersionWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete All Overridden Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)('/:workflowSettingId/override'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "deleteOverriddenWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete All Unpublished Overridden Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)('/:workflowSettingId/unpublished-override-version'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "deleteUnpublishedOverriddenWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Create New Version Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/new-version'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, clone_workflow_request_dto_1.NewVersionWorkflowRequestDto]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "createNewVersionWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Publish Selected Overridden Workflow Setting.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/:workflowMasterSettingId/publish-overridden-workflow'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('workflowMasterSettingId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, dtos_2.PublishedOverriddenWorkflowRequest]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "publishOverriddenWorkflowSetting", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get unpublished version workflow complete details by workflow id.',
        type: dtos_2.WorkflowDetailResponseDTO,
    }),
    (0, common_1.Get)('/:workflowSettingId/unpublished-version'),
    __param(0, (0, common_1.Param)('workflowSettingId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowMasterSetingController.prototype, "getUnpublishedVersionWorkflowDetailBySettingId", null);
WorkflowMasterSetingController = __decorate([
    (0, swagger_1.ApiTags)('Master Workflow Setting APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('workflow-setting'),
    __metadata("design:paramtypes", [services_1.WorkflowMasterSettingService])
], WorkflowMasterSetingController);
exports.WorkflowMasterSetingController = WorkflowMasterSetingController;
//# sourceMappingURL=workflow-master-setting.controller.js.map