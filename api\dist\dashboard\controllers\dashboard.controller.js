"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const enums_1 = require("../../shared/enums");
const request_type_wise_afe_response_dto_1 = require("../dtos/response/request-type-wise-afe-response.dto");
const status_wise_afe_response_dto_1 = require("../dtos/response/status-wise-afe-response.dto");
const services_1 = require("../services");
let DashboardController = class DashboardController {
    constructor(dashboardService) {
        this.dashboardService = dashboardService;
    }
    getStatusWiseCount(request, year = null) {
        return this.dashboardService.getStatusWiseCount(request.currentContext, year);
    }
    getRequestTypeWiseCount(request, year, entityId = null) {
        return this.dashboardService.getRequestWiseMonthlyCount(request.currentContext, year, entityId);
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_VIEW),
    (0, swagger_1.ApiQuery)({
        name: 'year',
        type: Number,
        description: 'Year to fetch the detail.',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return status wise count - Total AFE Submitted, Approved, Rejected, In-Process.',
        type: status_wise_afe_response_dto_1.StatusWiseAFEResponseDto,
    }),
    (0, common_1.Get)('/status-wise-count'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('year')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getStatusWiseCount", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_VIEW),
    (0, swagger_1.ApiQuery)({
        name: 'year',
        type: Number,
        description: 'Year to fetch the detail.',
        required: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Business entity id to fetch the detail.',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return monthly request type wise count - Capex, opex, etc',
        type: [request_type_wise_afe_response_dto_1.RequestTypeWiseAfeResponseDto],
    }),
    (0, common_1.Get)('/request-type-wise-count'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('year')),
    __param(2, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getRequestTypeWiseCount", null);
DashboardController = __decorate([
    (0, swagger_1.ApiTags)('Dashboard APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('dashboard'),
    __metadata("design:paramtypes", [services_1.DashboardService])
], DashboardController);
exports.DashboardController = DashboardController;
//# sourceMappingURL=dashboard.controller.js.map