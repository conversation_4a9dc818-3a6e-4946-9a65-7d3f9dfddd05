"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskDetailResponseDto = exports.TaskAdditionalInfoResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class TaskAdditionalInfoResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'proposalId', required: true }),
    (0, class_transformer_1.Expose)({ name: 'proposalId' }),
    __metadata("design:type", Number)
], TaskAdditionalInfoResponseDto.prototype, "proposal_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'proposalProjectReferenceNumber', required: true }),
    (0, class_transformer_1.Expose)({ name: 'proposalProjectReferenceNumber' }),
    __metadata("design:type", Number)
], TaskAdditionalInfoResponseDto.prototype, "proposal_project_reference_number", void 0);
exports.TaskAdditionalInfoResponseDto = TaskAdditionalInfoResponseDto;
class TaskDetailResponseDto {
    constructor(partial = {}) {
        Object.assign(this, partial);
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'id', required: false }),
    (0, class_transformer_1.Expose)({ name: 'id' }),
    __metadata("design:type", Number)
], TaskDetailResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'tennantId', required: false }),
    (0, class_transformer_1.Expose)({ name: 'tennantId' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "tennant_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'applicationId', required: false }),
    (0, class_transformer_1.Expose)({ name: 'applicationId' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "application_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'businessEntityId', required: false }),
    (0, class_transformer_1.Expose)({ name: 'businessEntityId' }),
    __metadata("design:type", Number)
], TaskDetailResponseDto.prototype, "business_entity_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'businessEntityId', required: false }),
    (0, class_transformer_1.Expose)({ name: 'businessEntityId' }),
    __metadata("design:type", Number)
], TaskDetailResponseDto.prototype, "entity_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'entityType', required: false }),
    (0, class_transformer_1.Expose)({ name: 'entityType' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "entity_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'title', required: false }),
    (0, class_transformer_1.Expose)({ name: 'title' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'details', required: false }),
    (0, class_transformer_1.Expose)({ name: 'details' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "details", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'assignedTo', required: false }),
    (0, class_transformer_1.Expose)({ name: 'assignedTo' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "assigned_to", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'isGroupAssignment', required: false }),
    (0, class_transformer_1.Expose)({ name: 'isGroupAssignment' }),
    __metadata("design:type", Boolean)
], TaskDetailResponseDto.prototype, "is_group_assignment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'dueDate', required: false }),
    (0, class_transformer_1.Expose)({ name: 'dueDate' }),
    __metadata("design:type", Date)
], TaskDetailResponseDto.prototype, "due_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'taskBaseUrl', required: false }),
    (0, class_transformer_1.Expose)({ name: 'taskBaseUrl' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "task_base_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'taskRelUrl', required: false }),
    (0, class_transformer_1.Expose)({ name: 'taskRelUrl' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "task_rel_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'taskStatus', required: false }),
    (0, class_transformer_1.Expose)({ name: 'taskStatus' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "task_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'taskOutcome', required: false }),
    (0, class_transformer_1.Expose)({ name: 'taskOutcome' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "task_outcome", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'taskCompletionDate', required: false }),
    (0, class_transformer_1.Expose)({ name: 'taskCompletionDate' }),
    __metadata("design:type", Date)
], TaskDetailResponseDto.prototype, "task_completion_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'taskCompletionComments', required: false }),
    (0, class_transformer_1.Expose)({ name: 'taskCompletionComments' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "task_completion_comments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'additionalInfo', required: false, type: TaskAdditionalInfoResponseDto }),
    (0, class_transformer_1.Expose)({ name: 'additionalInfo' }),
    (0, class_transformer_1.Type)(() => TaskAdditionalInfoResponseDto),
    __metadata("design:type", TaskAdditionalInfoResponseDto)
], TaskDetailResponseDto.prototype, "additional_info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'createdBy', required: false }),
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "created_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'createdOn', required: false }),
    (0, class_transformer_1.Expose)({ name: 'createdOn' }),
    __metadata("design:type", Date)
], TaskDetailResponseDto.prototype, "created_on", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'modifiedBy', required: false }),
    (0, class_transformer_1.Expose)({ name: 'modifiedBy' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "modified_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'modifiedOn', required: false }),
    (0, class_transformer_1.Expose)({ name: 'modifiedOn' }),
    __metadata("design:type", Date)
], TaskDetailResponseDto.prototype, "modified_on", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'completedBy', required: false }),
    (0, class_transformer_1.Expose)({ name: 'completedBy' }),
    __metadata("design:type", String)
], TaskDetailResponseDto.prototype, "completed_by", void 0);
exports.TaskDetailResponseDto = TaskDetailResponseDto;
//# sourceMappingURL=task-detail-response.dto.js.map