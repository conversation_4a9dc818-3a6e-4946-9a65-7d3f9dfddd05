import { AfeProposalApproverService, AfeProposalReadService, AfeProposalService } from '../services';
import { AfeProposalListingResponseDto, SubmitAfeProposalRequestDto, CreateAfeProposalResposeDto, AfeProposalResposeDto, AfeProposalAmountSplitResponseDto, ResubmitAfeProposalRequestDto, SearchAfeByProjectRefernceNoResponse, RelatedAfesInfoResponse, CheckAfeOrItsSupplementalInProgress, LatestAfeVersionInfoResponseDto, UpdateAfeDetailRequestDTO, UpdateApproversListRequestDto, WithdrawAfeProposalRequestDto, AddNewReadersRequestDto, SendBackAfeProposalRequestDto, ReopenAfeProposalRequestDto, UploadEvidenceRequestDto, UpdateApproverUserRequestDTO } from '../dtos';
import { RequestContext } from 'src/shared/types';
import { Pagination } from 'src/core/pagination';
import { AfeProposalApproverResponseDto } from '../dtos/response/afe-proposal-approver-response';
import { TOGGLE_ON_OFF } from 'src/shared/enums';
import { AfeListingFilterRequestDto } from '../dtos/request/afe-listing-filter-request.dto';
import { Response } from 'express';
import { MessageResponseDto } from 'src/shared/dtos';
export declare class AfeProposalController {
    private readonly afeProposalService;
    private readonly afeProposalReadService;
    private readonly afeProposalApproverService;
    constructor(afeProposalService: AfeProposalService, afeProposalReadService: AfeProposalReadService, afeProposalApproverService: AfeProposalApproverService);
    createAfeProposal(request: RequestContext, submitAfeProposalRequestDto: SubmitAfeProposalRequestDto): Promise<CreateAfeProposalResposeDto>;
    getAfeProposalById(request: RequestContext, id: number, taskId?: number): Promise<AfeProposalResposeDto>;
    getAfeListing(request: RequestContext, limit: number, page: number, forApprovalHistory: boolean, filter: AfeListingFilterRequestDto): Promise<Pagination<AfeProposalListingResponseDto>>;
    getApproversListOfAfePoposal(afeProposalId: number, request: RequestContext, taskId?: number): Promise<AfeProposalApproverResponseDto>;
    getAmountSplitsOfAfeProposal(afeProposalId: number, request: RequestContext, taskId?: number): Promise<AfeProposalAmountSplitResponseDto>;
    getAfeProposalActionHistory(afeProposalId: number, request: RequestContext, taskId?: number): Promise<Record<string, any>>;
    resubmitAfeProposal(request: RequestContext, resubmitAfeProposalRequestDto: ResubmitAfeProposalRequestDto): Promise<CreateAfeProposalResposeDto>;
    searchAfeListByProjectRefernceNumber(request: RequestContext, projectReferenceNumber: string, afeRequestTypeIds: string, entityId?: number): Promise<SearchAfeByProjectRefernceNoResponse[]>;
    getAllRelatedSupplementalAfes(request: RequestContext, afeProposalId: number, taskId?: number): Promise<RelatedAfesInfoResponse[]>;
    isAfeOrItsSupplementalInProgress(request: RequestContext, afeProposalId: number): Promise<CheckAfeOrItsSupplementalInProgress>;
    getLatestVersionAfeInfoByParentAfeRefNumber(request: RequestContext, afeRefNumber: string): Promise<LatestAfeVersionInfoResponseDto>;
    toggleAfeNotificationSubscription(request: RequestContext, afeProposalId: number, toggleValue: TOGGLE_ON_OFF): Promise<void>;
    updateProposalDetail(request: RequestContext, updateAfeDetailRequestDTO: UpdateAfeDetailRequestDTO): Promise<{
        message: string;
    }>;
    generateAfeProposalPdf(request: RequestContext, afeProposalId: number, res: Response): Promise<void>;
    updateApproversList(request: RequestContext, afeProposalId: number, updateApproversListRequestDto: UpdateApproversListRequestDto): Promise<MessageResponseDto>;
    addNewReaders(request: RequestContext, afeProposalId: number, addNewReadersRequestDto: AddNewReadersRequestDto): Promise<{
        message: string;
    }>;
    withdrawAfeProposal(request: RequestContext, withdrawAfeProposalRequestDto: WithdrawAfeProposalRequestDto): Promise<{
        message: string;
    }>;
    updateParentAfeSupplementalsDeltaAmounts(parentId: number): Promise<{
        message: string;
    }>;
    sendBackAfeProposal(request: RequestContext, sendBackAfeProposalRequestDto: SendBackAfeProposalRequestDto): Promise<{
        message: string;
    }>;
    reopenAfeProposal(request: RequestContext, reopenAfeProposalRequestDto: ReopenAfeProposalRequestDto): Promise<{
        message: string;
    }>;
    revertSentBackAction(request: RequestContext, reopenAfeProposalRequestDto: ReopenAfeProposalRequestDto): Promise<{
        message: string;
    }>;
    uploadEvidence(request: RequestContext, afeProposalId: number, evidences: UploadEvidenceRequestDto): Promise<{
        message: string;
    }>;
    updateApproverUser(request: RequestContext, afeProposalId: number, updateApproverUserRequestDTO: UpdateApproverUserRequestDTO): Promise<{
        message: string;
    }>;
}
