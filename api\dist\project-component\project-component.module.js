"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectComponentModule = void 0;
const common_1 = require("@nestjs/common");
const length_of_commitment_repository_1 = require("../afe-config/repositories/length-of-commitment.repository");
const clients_1 = require("../shared/clients");
const services_1 = require("../shared/services");
const controllers_1 = require("./controllers");
const repositories_1 = require("./repositories");
const services_2 = require("./services");
const repositories = [repositories_1.ProjectComponentRepository, length_of_commitment_repository_1.LengthOfCommitmentRepository];
let ProjectComponentModule = class ProjectComponentModule {
};
ProjectComponentModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.ProjectComponentController],
        providers: [services_2.ProjectComponentService, services_1.EntityService, clients_1.AdminApiClient, ...repositories],
    })
], ProjectComponentModule);
exports.ProjectComponentModule = ProjectComponentModule;
//# sourceMappingURL=project-component.module.js.map