"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowSharedBucketController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const enums_1 = require("../../shared/enums");
const new_shared_bucket_limit_request_dto_1 = require("../dtos/request/new-shared-bucket-limit-request.dto");
const get_bucket_shared_limit_response_dto_1 = require("../dtos/response/get-bucket-shared-limit-response.dto");
const workflow_shared_bucket_service_1 = require("../services/workflow-shared-bucket.service");
let WorkflowSharedBucketController = class WorkflowSharedBucketController {
    constructor(workflowSharedBucketService) {
        this.workflowSharedBucketService = workflowSharedBucketService;
    }
    getSharedBucketLimitList(stepId) {
        return this.workflowSharedBucketService.getSharedBucketLimitList(stepId);
    }
    addNewBucketEntity(stepId, newSharedBucketLimitRequestDTO, request) {
        return this.workflowSharedBucketService.addNewBucketEntity(stepId, newSharedBucketLimitRequestDTO, request.currentContext);
    }
    deleteBucketEntity(id, request) {
        return this.workflowSharedBucketService.deleteBucketLimit(id, request.currentContext);
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get shared bucket limit list by step id with step and setting detail.',
        type: get_bucket_shared_limit_response_dto_1.GetBucketSharedLimitWithStepResponseDTO,
    }),
    (0, common_1.Get)('/:stepId'),
    __param(0, (0, common_1.Param)('stepId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WorkflowSharedBucketController.prototype, "getSharedBucketLimitList", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get shared bucket limit list by step id with step and setting detail.',
        type: get_bucket_shared_limit_response_dto_1.GetBucketSharedLimitWithStepResponseDTO,
    }),
    (0, common_1.Post)('/:stepId'),
    __param(0, (0, common_1.Param)('stepId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, new_shared_bucket_limit_request_dto_1.NewSharedBucketLimitRequestDTO, Object]),
    __metadata("design:returntype", Promise)
], WorkflowSharedBucketController.prototype, "addNewBucketEntity", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete shared bucket limit.',
        type: get_bucket_shared_limit_response_dto_1.GetBucketSharedLimitWithStepResponseDTO,
    }),
    (0, common_1.Delete)('/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], WorkflowSharedBucketController.prototype, "deleteBucketEntity", null);
WorkflowSharedBucketController = __decorate([
    (0, swagger_1.ApiTags)('Workflow Shared Bucket Limit'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('workflow-shared-bucket'),
    __metadata("design:paramtypes", [workflow_shared_bucket_service_1.WorkflowSharedBucketService])
], WorkflowSharedBucketController);
exports.WorkflowSharedBucketController = WorkflowSharedBucketController;
//# sourceMappingURL=workflow-shared-bucket.controller.js.map