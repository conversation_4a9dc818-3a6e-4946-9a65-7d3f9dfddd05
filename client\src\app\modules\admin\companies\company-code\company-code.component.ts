import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { HistoryResponse } from '@core/interfaces/history-response';
import { ModalComponent, ModalConfig } from '@core/modules/partials';
import { SpinnerService } from '@core/services/common/spinner.service';
import { TranslateService } from '@ngx-translate/core';
import { toNumber } from 'lodash';
import { finalize } from 'rxjs';
import Swal from 'sweetalert2';
import { CompanyCodeResponse } from '../../core/models';
import { CompaniesService } from '../../core/services';
import { RequestTypeData } from '@core/data/request-type';
import { Attachment } from '@core/models';

@Component({
  selector: 'app-company-code',
  templateUrl: './company-code.component.html',
  styleUrls: ['./company-code.component.scss'],
})
export class CompanyCodeComponent implements OnChanges, OnInit {
  @Input('selectedEntity') selectedEntity: {
    entityId: number;
    entityName: string;
    entityCode: string;
  };
  @Input('selectedCompanyCode') selectedCompanyCode: CompanyCodeResponse | null;

  @Output() onCompanyChange = new EventEmitter<boolean>();

  public isEditMode: boolean = false;
  public companyCodeFormGroup: FormGroup;
  public isSubmitted: boolean = false;
  public companyEditorModalTitle: string =
    this.translateService.instant('MENU.ADD_COMPANY');
  public modalDismissButtonLabel: string = this.translateService.instant(
    'FORM.BUTTON.SAVE_BUTTON'
  );

  public fusionEnableModalTitle: string = this.translateService.instant(
    'MENU.CHOOSE_REQUEST_TYPES_FOR_FUSION_INTEGRATION'
  );

  evidenceError: boolean = false;
  evidenceFormReady: boolean = false;
  evidenceDocuments: Attachment[] = [];
  deletedEvidenceDocuments: Attachment[] = [];

  enableMultiNatualAccount: boolean = false;

  @ViewChild('uploadEvidence') private uploadEvidenceModelComponent: ModalComponent;

  @ViewChild('companyCodeEditorModal')
  private companyCodeEditorComponent: ModalComponent;

  historyModalConfig: ModalConfig = {
    modalTitle: this.translateService.instant(
      'FORM.BUTTON.COMPANY_HISTORY_BUTTON'
    ),
    hideCloseButton() {
      return true;
    },
    hideDismissButton() {
      return true;
    },
    modalDialogConfig: {
      backdrop: 'static',
      size: 'lg',
      keyboard: false,
      centered: false,
    },
  };

  companyHistory: HistoryResponse[] = [];
  @ViewChild('historyModal') private historyModalComponent: ModalComponent;

  public companyCodeEditorModalConfig: ModalConfig = {
    modalTitle: this.companyEditorModalTitle,
    dismissButtonLabel: this.modalDismissButtonLabel,
    closeButtonLabel: this.translateService.instant(
      'FORM.BUTTON.CANCEL_BUTTON'
    ),
    onDismiss: () => {
      this.companyCodeFormGroup.reset();
      return true;
    },
    shouldClose: () => {
      this.companyCodeFormGroup.reset();
      return true;
    },
    shouldDismiss: () => {
      this.isSubmitted = true;
      if (!this.companyCodeFormGroup.valid) {
        return false;
      }
      this.addNewCompany();
      return false;
    },
    modalDialogConfig: {
      backdrop: 'static',
      size: 'm',
      keyboard: false,
      centered: true,
    },
  };

  public fusionIntegrationModalConfig: ModalConfig = {
    modalTitle: this.fusionEnableModalTitle,
    dismissButtonLabel: this.modalDismissButtonLabel,
    closeButtonLabel: this.translateService.instant(
      'FORM.BUTTON.CANCEL_BUTTON'
    ),
    onDismiss: () => {
      return true;
    },
    shouldClose: () => {
      return true;
    },
    shouldDismiss: () => {
      this.updateFussionIntegration();
      return false;
    },
    modalDialogConfig: {
      backdrop: 'static',
      size: 'lg',
      keyboard: false,
      centered: true,
    },
  };
  @ViewChild('fusionIntegrationModal')
  private fusionIntegrationModal: ModalComponent;
  public fusionIntegrationFormGroup: FormGroup;
  public fusionIntegrationColumns: { id: number; col: string }[] = [];
  public fusionIntegrationEnableFor: (string | undefined)[] = [];

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly spinnerService: SpinnerService,
    private readonly companiesService: CompaniesService,
    private readonly translateService: TranslateService,
    private readonly requestTypeData: RequestTypeData,
    private readonly fb: FormBuilder
  ) {
    this.fusionIntegrationColumns = this.requestTypeData.data.map((d) => ({
      id: d.id,
      col: d.title,
    }));
    // Create a FormControl for each available colunm, initialize them as unchecked, and put them in an array
    const formControls = this.fusionIntegrationColumns.map(
      (col) =>
        new FormControl(
          this.selectedCompanyCode?.fusionIntegrationForRequestTypeIds?.includes(
            col.id
          )
        )
    );

    // Create a FormControl for the select/unselect all checkbox
    const selectAllControl = new FormControl();
    this.fusionIntegrationFormGroup = this.fb.group({
      requestTypes: new FormArray(formControls),
      selectAll: selectAllControl,
    });

  }

  ngOnInit() {
    this.companyCodeFormGroup = new FormGroup({
      companyCode: new FormControl('', Validators.required),
      companyName: new FormControl(
        this.selectedEntity?.entityName || '',
        Validators.required
      ),
    });

    // Subscribe to changes on the selectAll checkbox
    this.fusionIntegrationFormGroup
      ?.get('selectAll')
      ?.valueChanges.subscribe((bool) => {
        this.fusionIntegrationFormGroup
          ?.get('requestTypes')
          ?.patchValue(Array(this.fusionIntegrationColumns.length).fill(bool), {
            emitEvent: false,
          });
      });

    // Subscribe to changes on the colunm name checkboxes
    this.fusionIntegrationFormGroup
      .get('requestTypes')
      ?.valueChanges.subscribe((val) => {
        const allSelected = val.every((bool: any) => bool);
        if (
          this.fusionIntegrationFormGroup.get('selectAll')?.value !==
          allSelected
        ) {
          this.fusionIntegrationFormGroup
            .get('selectAll')
            ?.patchValue(allSelected, { emitEvent: false });
        }
      });

    this.setFusionIntegrationEnabledFor();
    console.log(this.selectedCompanyCode);
    this.enableMultiNatualAccount = this.selectedCompanyCode?.enableMultiNaturalAccount || false;
  }

  ngOnChanges(): void { }

  private setFusionIntegrationEnabledFor() {
    this.fusionIntegrationEnableFor =
      this.selectedCompanyCode?.fusionIntegrationForRequestTypeIds
        ?.map((id) => this.requestTypeData.data.find((d) => d.id === id)?.title)
        ?.filter((title) => !!title) || [];
  }

  public getRequestTypesControls() {
    return (this.fusionIntegrationFormGroup.get('requestTypes') as FormArray)
      .controls;
  }

  public isAnyRequestTypeSelected() {
    return this.getRequestTypesControls().some((control: any) => control.value);
  }

  public updateMultiNaturalAccount() {
    this.spinnerService.startSpinner();

    this.companiesService.updateMultiNaturalAccount(
      toNumber(this.selectedCompanyCode?.id),
      !this.selectedCompanyCode?.enableMultiNaturalAccount,
    ).subscribe({
      next: () => {
        Swal.fire(
          this.translateService.instant('SWAL.SUCCESS'),
          this.translateService.instant('SWAL.UPDATE_MULTI_NATURAL_ACCOUNT_SUCCESS'),
          'success'
        );
        this.cdr.detectChanges();
        this.spinnerService.stopSpinner();
      },
      error: (error: any) => {
        Swal.fire({
          icon: 'error',
          title: this.translateService.instant('SWAL.ERROR'),
          text: error.message,
        });
        setTimeout(() => {
          this.enableMultiNatualAccount = !this.enableMultiNatualAccount;
          this.cdr.detectChanges();
        }, 1000)
        this.spinnerService.stopSpinner();
      },
    });

    this.cdr.detectChanges();
  }

  public isAllRequestTypeSelected() {
    return this.getRequestTypesControls().every(
      (control: any) => control.value
    );
  }

  public isRequiredError(FContorl: string): boolean | undefined {
    return (
      this.companyCodeFormGroup.get(FContorl)?.hasError('required') &&
      (this.companyCodeFormGroup.get(FContorl)?.touched || this.isSubmitted)
    );
  }

  public isPatternError(FContorl: string): boolean | undefined {
    return (
      this.companyCodeFormGroup.get(FContorl)?.hasError('pattern') &&
      (this.companyCodeFormGroup.get(FContorl)?.touched || this.isSubmitted)
    );
  }

  /**
   * Get the company code form controller reference.
   */
  get companyCodeFormController() {
    return this.companyCodeFormGroup.get('companyCode');
  }

  /**
   * Open company code editor.
   * @returns
   */
  public async openCompanyCodeEditorModal() {
    this.isSubmitted = false;
    return await this.companyCodeEditorComponent.open();
  }

  /**
   * Add new company in the business unit.
   */
  private addNewCompany() {
    this.isSubmitted = false;
    if (this.companyCodeFormGroup?.valid) {
      const requestPayload = {
        code: this.companyCodeFormGroup.get('companyCode')?.value,
        name: this.companyCodeFormGroup.get('companyName')?.value,
        entityId: this.selectedEntity.entityId,
      };
      this.spinnerService.startSpinner();
      this.companiesService
        .createCompanyCode(requestPayload)
        .pipe(
          finalize(() => {
            this.spinnerService.stopSpinner();
          })
        )
        .subscribe({
          next: () => {
            Swal.fire(
              this.translateService.instant('SWAL.SUCCESS'),
              this.translateService.instant(
                'SWAL.ADD_NEW_COMPANY_CODE_SUCCESS'
              ),
              'success'
            );
            this.onCompanyChange.emit(true);
            this.companyCodeFormGroup.reset();
            this.companyCodeEditorComponent.close();
            this.isSubmitted = true;
          },
          error: (error) => {
            Swal.fire({
              icon: 'error',
              title: this.translateService.instant(
                'SWAL.ADD_NEW_COMPANY_CODE_ERROR'
              ),
              text: error.message,
            });
          },
        });
    }
  }

  /**
   * Deactivate current active company code for the business unit entity.
   */
  public deactivateCompanyCode() {
    Swal.fire({
      title: this.translateService.instant('SWAL.CONFIRMATION'),
      text: this.translateService.instant('SWAL.COMPANY_CODE_INACTIVE_CONFIRM'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: this.translateService.instant(
        'SWAL.DEACTIVATE_BUTTON'
      ),
    }).then((response) => {
      if (response.isConfirmed && this.selectedCompanyCode) {
        this.spinnerService.startSpinner();
        this.companiesService
          .deactivateCompanyCode(this.selectedCompanyCode?.id)
          .pipe(
            finalize(() => {
              this.spinnerService.stopSpinner();
            })
          )
          .subscribe({
            next: () => {
              Swal.fire(
                this.translateService.instant('SWAL.SUCCESS'),
                this.translateService.instant(
                  'SWAL.MARK_COMPANY_CODE_INACTIVE'
                ),
                'success'
              );
              this.onCompanyChange.emit(true);
            },
            error: (error) => {
              Swal.fire({
                icon: 'error',
                title: this.translateService.instant(
                  'SWAL.DEACTIVATE_COMPANY_CODE_ERROR'
                ),
                text: error.message,
              });
            },
          });
      }
      this.cdr.detectChanges();
    });
  }

  openHistoryModel() {
    this.spinnerService.startSpinner();

    this.companiesService
      .getCompanyHistory(this.selectedEntity.entityId)
      .subscribe({
        next: (response) => {
          this.companyHistory = response as HistoryResponse[];
          this.cdr.detectChanges();
          this.historyModalComponent.open();
          this.spinnerService.stopSpinner();
        },
        error: (_) => {
          this.companyHistory = [] as HistoryResponse[];
          this.cdr.detectChanges();
          this.historyModalComponent.open();
          this.spinnerService.stopSpinner();

          Swal.fire({
            icon: 'error',
            title: this.translateService.instant('SWAL.ERROR'),
            text: this.translateService.instant('SWAL.ERROR_HISTORY_FETCH'),
          });
        },
      });
  }

  /**
   * Open fusion integration update modal.
   */
  public openFusionIntegrationModal() {
    const selectedValues = this.fusionIntegrationColumns.map((value) => {
      return this.selectedCompanyCode?.fusionIntegrationForRequestTypeIds?.includes(
        value.id
      );
    });

    const allValuesSelected = selectedValues.every((value) => value);

    this.fusionIntegrationFormGroup
      ?.get('requestTypes')
      ?.patchValue(selectedValues, { emitEvent: false });

    this.fusionIntegrationFormGroup
      .get('selectAll')
      ?.patchValue(allValuesSelected, { emitEvent: false });

    this.fusionIntegrationModal.open();
  }

  /**
   * Update the fusion integration enablement for the AFE request types
   */
  public updateFussionIntegration() {
    const selectedRequestTypes =
      this.fusionIntegrationFormGroup.value.requestTypes
        .map((checked: any, index: any) =>
          checked ? this.fusionIntegrationColumns[index].id : null
        )
        .filter((value: any) => !!value);

    if (this.selectedCompanyCode) {
      this.spinnerService.startSpinner();
      this.companiesService
        .updateFusionIntegration({
          requestTypeIds: selectedRequestTypes,
          id: toNumber(this.selectedCompanyCode.id),
        })
        .subscribe({
          next: (_) => {
            if (this.selectedCompanyCode) {
              this.selectedCompanyCode.fusionIntegrationForRequestTypeIds =
                selectedRequestTypes;
            }
            this.setFusionIntegrationEnabledFor();
            this.cdr.detectChanges();
            Swal.fire(
              this.translateService.instant('SWAL.SUCCESS'),
              this.translateService.instant(
                'SWAL.FUSION_INTEGRATION_UPDATE_SUCCESS'
              ),
              'success'
            );
            this.spinnerService.stopSpinner();
          },
          error: (err) => {
            Swal.fire({
              icon: 'error',
              title: this.translateService.instant(
                'SWAL.FUSION_INTEGRATION_UPDATE_ERROR'
              ),
              text: err.message,
            });
            this.spinnerService.stopSpinner();
          },
        });
    }
  }

  openEvidenceModel() {
    this.evidenceFormReady = false;
    this.evidenceError = false;
    this.evidenceDocuments = [];
    this.deletedEvidenceDocuments = [];
    this.cdr.detectChanges();
    this.uploadEvidenceModelComponent.open();

    setTimeout(() => {
      this.evidenceFormReady = true;
      this.cdr.detectChanges();
    }, 100);
  }

  public uploadEvidenceConfig: ModalConfig = {
    modalTitle: this.translateService.instant('FORM.BUTTON.UPLOAD_EVIDENCE_BUTTON'),
    dismissButtonLabel: this.translateService.instant('FORM.BUTTON.UPLOAD_BUTTON'),
    closeButtonLabel: this.translateService.instant(
      'FORM.BUTTON.CANCEL_BUTTON'
    ),
    onDismiss: () => {
      this.evidenceFormReady = false;
      this.evidenceDocuments = [];
      this.deletedEvidenceDocuments = [];
      this.cdr.detectChanges();
      return true;
    },
    shouldClose: () => {
      this.evidenceFormReady = false;
      this.evidenceDocuments = [];
      this.deletedEvidenceDocuments = [];
      this.cdr.detectChanges();
      return true;
    },
    shouldDismiss: () => {
      this.uploadNewEvidence();
      return false;
    },
    modalDialogConfig: {
      backdrop: 'static',
      size: 'lg',
      keyboard: false,
      centered: false,
    },
  };

  onAttachmentEdited(attachments: Attachment[]) {
    this.evidenceDocuments = attachments as Attachment[];
    this.cdr.detectChanges();
  }

  onAttachmentDelete(attachment: Attachment) {
    if (!attachment.IsNew) {
      this.deletedEvidenceDocuments.push(attachment);
      this.cdr.detectChanges();
    }

    console.log(this.evidenceDocuments);
    this.evidenceDocuments = this.evidenceDocuments.filter((x: any) => x !== attachment) as Attachment[];
    console.log(this.evidenceDocuments);
    this.cdr.detectChanges();
  }

  uploadNewEvidence() {
    this.evidenceError = false;

    console.log(this.evidenceDocuments);

    if (!this.evidenceDocuments?.length) {
      this.evidenceError = true;
      this.cdr.detectChanges();
      return;
    }

    if (this?.selectedCompanyCode?.id) {
      this.spinnerService.startSpinner();

      this.companiesService.uploadEvidence(this.selectedCompanyCode.id, this.evidenceDocuments).subscribe({
        next: (response: any) => {
          Swal.fire(
            this.translateService.instant('SWAL.SUCCESS'),
            this.translateService.instant('SWAL.UPLOAD_EVIDENCE_SUCCESS'),
            'success'
          );
          this.evidenceDocuments = [];
          this.deletedEvidenceDocuments = [];
          this.evidenceFormReady = false;
          this.cdr.detectChanges();
          this.uploadEvidenceModelComponent.close();
          this.spinnerService.stopSpinner();
        },
        error: (error: any) => {
          Swal.fire({
            icon: 'error',
            title: this.translateService.instant('SWAL.ERROR'),
            text: error.message,
          });
          this.spinnerService.stopSpinner();
        },
      });
    }

  }
}
