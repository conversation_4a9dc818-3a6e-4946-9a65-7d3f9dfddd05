"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../business-entity/repositories");
const clients_1 = require("../shared/clients");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
const repositories_2 = require("./repositories");
const services_2 = require("../shared/services");
const helpers_1 = require("../shared/helpers");
const services_3 = require("../report/services");
const repositories_3 = require("../afe-proposal/repositories");
const services_4 = require("../business-entity/services");
const repositories_4 = require("../workflow/repositories");
const repositories = [
    repositories_2.NaturalAccountNumberRepository,
    repositories_2.CostCenterRepository,
    repositories_2.CurrencyTypeRepository,
    repositories_1.EntitySetupRepository,
    repositories_2.AnalysisCodeRepository,
    repositories_2.CompanyCodeRepository,
    repositories_3.UserProjectComponentMappingRepository,
    repositories_3.UserCostCenterMappingRepository,
    repositories_3.AfeProposalRepository,
    repositories_4.WorkflowMasterSettingRepository
];
let FinanceModule = class FinanceModule {
};
FinanceModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.FinanceController, controllers_1.FinanceAdminController],
        providers: [
            services_1.FinanceService,
            services_1.FinanceAdminService,
            services_2.SharedPermissionService,
            services_3.ReportService,
            services_2.ExcelSheetService,
            services_2.ConditionCreatorService,
            clients_1.AdminApiClient,
            clients_1.MSGraphApiClient,
            clients_1.HistoryApiClient,
            helpers_1.DatabaseHelper,
            helpers_1.SequlizeOperator,
            services_4.BusinessEntityService,
            ...repositories
        ],
    })
], FinanceModule);
exports.FinanceModule = FinanceModule;
//# sourceMappingURL=finance.module.js.map