"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const dtos_1 = require("../../shared/dtos");
const enums_1 = require("../../shared/enums");
const dtos_2 = require("../dtos");
const services_1 = require("../services");
let TaskController = class TaskController {
    constructor(taskService) {
        this.taskService = taskService;
    }
    performActionOnAfeAproposalTask(actionType, performActionOnAfeProposalRequestDto, request) {
        return this.taskService.performActionOnAfeAproposalTask(actionType, performActionOnAfeProposalRequestDto, request.currentContext);
    }
    getAllPendingUserTasks(request, assignedTo) {
        return this.taskService.getAllPendingUserTasks(request.currentContext, assignedTo);
    }
    getTaskDetailById(id) {
        return this.taskService.getTaskDetailById(id);
    }
    getCurrentTaskOfUserByAfeId(id, request, taskId) {
        return this.taskService.getCurrentTaskOfUserByAfeId(id, request.currentContext, taskId);
    }
    sendReminder(approverId, request) {
        return this.taskService.sendReminder(approverId, request.currentContext);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perform approval action on the afe proposal task',
        type: dtos_1.MessageResponseDto
    }),
    (0, swagger_1.ApiParam)({
        name: 'actionType',
        required: true,
        description: 'Task approval action type',
        enum: enums_1.TASK_ACTION,
    }),
    (0, common_1.Post)('/action/:actionType'),
    __param(0, (0, common_1.Param)('actionType')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dtos_2.PerformActionOnAfeProposalRequestDto, Object]),
    __metadata("design:returntype", void 0)
], TaskController.prototype, "performActionOnAfeAproposalTask", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all the pending tasks of an user.',
        type: [dtos_2.TaskDetailResponseDto],
    }),
    (0, common_1.Get)('/pending'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('assignedTo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], TaskController.prototype, "getAllPendingUserTasks", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get task details by its id.',
        type: dtos_2.TaskDetailResponseDto,
    }),
    (0, common_1.Get)('/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], TaskController.prototype, "getTaskDetailById", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get current task step of an user.',
        type: dtos_2.CurrentTaskOfUserResponseDto,
    }),
    (0, swagger_1.ApiQuery)({ name: 'taskId', required: false, type: Number }),
    (0, common_1.Get)('afe-proposal/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Number]),
    __metadata("design:returntype", void 0)
], TaskController.prototype, "getCurrentTaskOfUserByAfeId", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Send reminder notification by approver Id.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Get)('send-reminder/:approverId'),
    __param(0, (0, common_1.Param)('approverId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", void 0)
], TaskController.prototype, "sendReminder", null);
TaskController = __decorate([
    (0, swagger_1.ApiTags)('Task APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('tasks'),
    __metadata("design:paramtypes", [services_1.TaskService])
], TaskController);
exports.TaskController = TaskController;
//# sourceMappingURL=task.controller.js.map