"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetRepresentativeResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class GetRepresentativeResponseDto {
    constructor(partial = {}) {
        Object.assign(this, partial);
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetRepresentativeResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'representativeFor' }),
    __metadata("design:type", String)
], GetRepresentativeResponseDto.prototype, "delegate_for_username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'representative' }),
    __metadata("design:type", String)
], GetRepresentativeResponseDto.prototype, "delegate_to_username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'fromDate' }),
    __metadata("design:type", Date)
], GetRepresentativeResponseDto.prototype, "delegate_from_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'toDate' }),
    __metadata("design:type", Date)
], GetRepresentativeResponseDto.prototype, "delegate_to_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'createdOn' }),
    __metadata("design:type", Date)
], GetRepresentativeResponseDto.prototype, "created_on", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    __metadata("design:type", String)
], GetRepresentativeResponseDto.prototype, "created_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'modifiedBy' }),
    __metadata("design:type", String)
], GetRepresentativeResponseDto.prototype, "modified_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'modifiedOn' }),
    __metadata("design:type", Date)
], GetRepresentativeResponseDto.prototype, "modified_on", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)({ name: 'additionalInfo' }),
    __metadata("design:type", Object)
], GetRepresentativeResponseDto.prototype, "additional_info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)({ name: 'businessEntityId' }),
    __metadata("design:type", Number)
], GetRepresentativeResponseDto.prototype, "business_entity_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'comments' }),
    __metadata("design:type", String)
], GetRepresentativeResponseDto.prototype, "delegate_comments", void 0);
exports.GetRepresentativeResponseDto = GetRepresentativeResponseDto;
//# sourceMappingURL=get-representative-response.dto.js.map