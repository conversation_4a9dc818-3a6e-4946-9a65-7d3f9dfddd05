{"version": 3, "file": "ms-graph-api.client.js", "sourceRoot": "", "sources": ["../../../src/shared/clients/ms-graph-api.client.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,uDAAyC;AAEzC,4CAA4C;AAC5C,gEAA0D;AAC1D,0CAA0C;AAI1C,IAAa,gBAAgB,GAA7B,MAAa,gBAAgB;IAI5B,YAEkB,kBAAsD,EAEtD,qBAAkC,EACX,aAA4B;QAHnD,uBAAkB,GAAlB,kBAAkB,CAAoC;QAEtD,0BAAqB,GAArB,qBAAqB,CAAa;QACX,kBAAa,GAAb,aAAa,CAAe;QAEpE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC;QAC/D,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG;YACnB,MAAM,EAAE,CAAC,GAAG,WAAW,WAAW,CAAC;SACnC,CAAC;IACH,CAAC;IAEa,QAAQ;;YACrB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxF,CAAC;KAAA;IAEa,UAAU;;YACvB,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9C,OAAO,EAAE,aAAa,EAAE,UAAU,WAAW,EAAE,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC;QACjF,CAAC;KAAA;IAEY,WAAW,CAAC,UAAkB,EAAE,OAAe,EAAE,KAAc;;YAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,kHAAkH,UAAU,cAAc,UAAU,2BAA2B,UAAU,cAAc,OAAO,WAAW,KAAK,0HAA0H,CACxV,CAAC;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,eAAC,OAAA,CAAC,CAAA,MAAA,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,iBAAiB,0CAAE,WAAW,EAAE,0CAAE,QAAQ,CAAC,gBAAgB,CAAC,CAAA,CAAA,EAAA,CAAC,CAAC;YACtG,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAEY,0BAA0B,CAAC,MAAc;;YACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,8GAA8G,MAAM,8BAA8B,MAAM,GAAG,CAC3J,CAAC;YACH,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAEY,iCAAiC,CAAC,GAAW;;YACzD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,+FAA+F,GAAG,8BAA8B,GAAG,GAAG,CACtI,CAAC;YACH,OAAO,IAAI,CAAC;QACb,CAAC;KAAA;IAEY,cAAc,CAAC,MAAc;;YACzC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;YAC3D,OAAO,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAI,IAAI,CAAC;QACnD,CAAC;KAAA;IAEY,qBAAqB,CAAC,KAAa;;YAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,qBAAqB;iBAC/C,WAAW,CAAC,OAAO,CAAC;iBACpB,GAAG,CACH,2GAA2G,KAAK,GAAG,CACnH,CAAC;YACH,OAAO,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAI,IAAI,CAAC;QACnD,CAAC;KAAA;IAEY,qBAAqB,CAAC,OAAiB;;;YACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACnD,MAAM,OAAO,GAAoB,EAAE,CAAC;YACpC,IAAI,QAAQ,GAEF,+FAA+F,GAAG,8BAA8B,GAAG,GAAG,CAAC;YACjJ,OAAO,QAAQ,EAAE;gBAChB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB;qBAC/C,WAAW,CAAC,OAAO,CAAC;qBACpB,GAAG,CACH,+FAA+F,GAAG,8BAA8B,GAAG,GAAG,CACtI,CAAC;gBACH,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5B,QAAQ;oBACP,CAAA,MAAA,IAAI,CAAC,iBAAiB,CAAC,0CAAE,OAAO,CAC/B,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,EACzE,EAAE,CACF,KAAI,IAAI,CAAC;aACX;YACD,OAAO,OAAO,CAAC;;KACf;IAEY,eAAe,CAAC,GAAa;;YACzC,MAAM,UAAU,GAAG,CAAC,CAAC;YACrB,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAoB,EAAE,CAAC;YAEnC,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,eAAe,EAAE,UAAU,EAAE,EAAE;gBAEpE,MAAM,KAAK,GAAG,UAAU,GAAG,UAAU,CAAC;gBACtC,MAAM,GAAG,GAAG,KAAK,GAAG,UAAU,CAAC;gBAC/B,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC3C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;gBACpE,MAAM,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;aAC7B;YACD,OAAO,MAAM,CAAC;QACf,CAAC;KAAA;CACD,CAAA;AAhHY,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAMV,WAAA,IAAA,eAAM,EAAC,wBAAY,CAAC,qBAAqB,CAAC,CAAA;IAE1C,WAAA,IAAA,eAAM,EAAC,wBAAY,CAAC,8BAA8B,CAAC,CAAA;IAEnD,WAAA,IAAA,eAAM,EAAC,8BAAa,CAAC,CAAA;qCAHe,IAAI,CAAC,6BAA6B,EAE/B,sBAAW;QACI,8BAAa;GATzD,gBAAgB,CAgH5B;AAhHY,4CAAgB"}