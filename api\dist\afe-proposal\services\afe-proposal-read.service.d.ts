/// <reference types="node" />
import { AfeBudgetTypeRepository } from 'src/afe-config/repositories';
import { LengthOfCommitmentRepository } from 'src/afe-config/repositories/length-of-commitment.repository';
import { Pagination } from 'src/core/pagination';
import { LoggerService } from 'src/core/services';
import { AnalysisCodeRepository, CostCenterRepository, CurrencyTypeRepository, NaturalAccountNumberRepository } from 'src/finance/repositories';
import { PdfGeneratorService } from 'src/pdf-generator/pdf-generator.service';
import { AttachmentApiClient, HistoryApiClient, TaskApiClient } from 'src/shared/clients';
import { CurrentContext } from 'src/shared/types';
import { AfeProposalValidator } from 'src/shared/validators';
import { TaskService } from 'src/task/services';
import { AfeProposalAmountSplitResponseDto, AfeProposalListingResponseDto, AfeProposalResposeDto, CheckAfeOrItsSupplementalInProgress, LatestAfeVersionInfoResponseDto, RelatedAfesInfoResponse, SearchAfeByProjectRefernceNoResponse } from '../dtos';
import { AfeListingFilterRequestDto } from '../dtos/request/afe-listing-filter-request.dto';
import { AfeProposalApproverResponseDto } from '../dtos/response/afe-proposal-approver-response';
import { AfeProposalAmountSplitRepository, AfeProposalApproverRepository, AfeProposalRepository } from '../repositories';
import { BusinessEntityService } from 'src/business-entity/services';
import { SharedPermissionService } from 'src/shared/services';
import { ConfigService } from 'src/config/config.service';
export declare class AfeProposalReadService {
    private readonly afeProposalRepository;
    private readonly afeProposalApproverRepository;
    private readonly afeProposalAmountSplitRepository;
    private readonly historyApiClient;
    private readonly analysisCodeRepository;
    private readonly naturalAccountNumberRepository;
    private readonly costCenterRepository;
    private readonly afeBudgetTypeRepository;
    private readonly currencyTypeRepository;
    private readonly taskService;
    private readonly taskApiClient;
    private readonly afeProposalValidator;
    private readonly loggerService;
    private readonly pdfGeneratorService;
    private readonly attachmentApiClient;
    private readonly lengthOfCommitmentRepository;
    private readonly businessEntityService;
    private readonly permissionService;
    private readonly configService;
    constructor(afeProposalRepository: AfeProposalRepository, afeProposalApproverRepository: AfeProposalApproverRepository, afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository, historyApiClient: HistoryApiClient, analysisCodeRepository: AnalysisCodeRepository, naturalAccountNumberRepository: NaturalAccountNumberRepository, costCenterRepository: CostCenterRepository, afeBudgetTypeRepository: AfeBudgetTypeRepository, currencyTypeRepository: CurrencyTypeRepository, taskService: TaskService, taskApiClient: TaskApiClient, afeProposalValidator: AfeProposalValidator, loggerService: LoggerService, pdfGeneratorService: PdfGeneratorService, attachmentApiClient: AttachmentApiClient, lengthOfCommitmentRepository: LengthOfCommitmentRepository, businessEntityService: BusinessEntityService, permissionService: SharedPermissionService, configService: ConfigService);
    getAfeListing(currentContext: CurrentContext, limit: number, page: number, forApprovalHistory: boolean, filters: AfeListingFilterRequestDto): Promise<Pagination<AfeProposalListingResponseDto>>;
    getAfeProposalById(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<AfeProposalResposeDto>;
    getApproversListOfAfePoposal(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<AfeProposalApproverResponseDto>;
    getAmountSplitsOfAfeProposal(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<AfeProposalAmountSplitResponseDto>;
    private getAfeProposalAmountSplitData;
    getAfeProposalActionHistory(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<Record<string, any>>;
    searchAfeListByProjectReferenceNumber(projectReferenceNumber: string, afeRequestTypeIds: number[], currentContext: CurrentContext, entityId: number): Promise<SearchAfeByProjectRefernceNoResponse[]>;
    getAllRelatedSupplementalAfes(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<RelatedAfesInfoResponse[]>;
    isAfeOrItsSupplementalInProgress(afeProposalId: number, currentContext: CurrentContext): Promise<CheckAfeOrItsSupplementalInProgress>;
    getLatestVersionAfeInfoByParentAfeRefNumber(afeProposalRefNumber: string, currentContext: CurrentContext): Promise<LatestAfeVersionInfoResponseDto>;
    generateAfePdf(afeProposalId: number, currentContext: CurrentContext, taskId?: number): Promise<Buffer>;
}
