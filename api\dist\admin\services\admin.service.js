"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../afe-draft/repositories");
const helpers_1 = require("../../shared/helpers");
const repositories_2 = require("../../afe-proposal/repositories");
const repositories_3 = require("../../finance/repositories");
const repositories_4 = require("../../workflow/repositories");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const repositories_5 = require("../../notification/repositories");
const sequelize_1 = require("sequelize");
let AdminService = class AdminService {
    constructor(draftRepository, databaseHelper, afeProposalRepository, costCenterRepository, workflowMasterStepRepository, afeProposalApproverRepository, notificationRepository, sequlizeOperator) {
        this.draftRepository = draftRepository;
        this.databaseHelper = databaseHelper;
        this.afeProposalRepository = afeProposalRepository;
        this.costCenterRepository = costCenterRepository;
        this.workflowMasterStepRepository = workflowMasterStepRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.notificationRepository = notificationRepository;
        this.sequlizeOperator = sequlizeOperator;
    }
    replaceUserEmail(replaceUserRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            let { sourceLoginId, targetLoginId } = replaceUserRequestDto;
            sourceLoginId = sourceLoginId.toLowerCase();
            targetLoginId = targetLoginId.toLowerCase();
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.draftRepository.updateDraftByCondition({
                    createdBy: sequelize_1.Sequelize.where(sequelize_1.Sequelize.literal('LOWER("created_by")'), { [sequelize_1.Op.eq]: sourceLoginId })
                }, { createdBy: targetLoginId });
                yield this.afeProposalRepository.updateProposalByConditionWoUser({
                    submitterId: sequelize_1.Sequelize.where(sequelize_1.Sequelize.literal('LOWER("submitter_id")'), { [sequelize_1.Op.eq]: sourceLoginId })
                }, { submitterId: targetLoginId });
                yield this.afeProposalRepository.updateProposalByConditionWoUser({
                    'data.submitterDetails.loginId': {
                        [sequelize_1.Op.iLike]: sourceLoginId
                    }
                }, {
                    data: this.sequlizeOperator.sequelizeLiteral(`jsonb_set(data, '{submitterDetails, loginId}', '"${targetLoginId}"', true)`)
                });
                yield this.afeProposalRepository.updateProposalByConditionWoUser({
                    'data.projectDetails.projectLeader.loginId': {
                        [sequelize_1.Op.iLike]: sourceLoginId
                    }
                }, {
                    data: this.sequlizeOperator.sequelizeLiteral(`jsonb_set(data, '{projectDetails,projectLeader,loginId}', '"${targetLoginId}"', true)`),
                });
                yield this.afeProposalRepository.updateProposalByConditionWoUser({
                    userStatus: {
                        [sequelize_1.Op.iLike]: `%Pending with ${sourceLoginId}%`
                    }
                }, {
                    userStatus: `Pending with ${targetLoginId}`,
                });
                let sourceInSubscribers = yield this.afeProposalRepository.getSubscribersForAllAvailableAFE(sourceLoginId);
                if (sourceInSubscribers.length) {
                    yield Promise.all(sourceInSubscribers.map((afeDetail) => __awaiter(this, void 0, void 0, function* () {
                        const otherSubscriber = afeDetail.subscribers.filter(subscriber => (subscriber === null || subscriber === void 0 ? void 0 : subscriber.toLowerCase()) !== (sourceLoginId === null || sourceLoginId === void 0 ? void 0 : sourceLoginId.toLowerCase()));
                        otherSubscriber.push(targetLoginId);
                        afeDetail.subscribers = otherSubscriber;
                        yield this.afeProposalRepository.updateAfeProposalByIdWOUser(afeDetail.id, { subscribers: otherSubscriber });
                    })));
                }
                const sourceInReaders = yield this.afeProposalRepository.getReadersForAllAvailableAFE(sourceLoginId);
                if (sourceInReaders.length) {
                    yield Promise.all(sourceInReaders.map((afeDetail) => __awaiter(this, void 0, void 0, function* () {
                        const matchingReader = afeDetail.readers.find(reader => { var _a; return (((_a = reader === null || reader === void 0 ? void 0 : reader.loginId) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === sourceLoginId.toLowerCase()); });
                        const otherReaders = afeDetail.readers.filter(reader => { var _a; return (((_a = reader === null || reader === void 0 ? void 0 : reader.loginId) === null || _a === void 0 ? void 0 : _a.toLowerCase()) !== sourceLoginId.toLowerCase()); });
                        otherReaders.push(Object.assign(Object.assign({}, matchingReader), { loginId: targetLoginId }));
                        afeDetail.readers = otherReaders;
                        yield this.afeProposalRepository.updateAfeProposalByIdWOUser(afeDetail.id, { readers: otherReaders });
                    })));
                }
                yield this.costCenterRepository.updateCostCenterByConditionWoUser({
                    'departmentHead.loginId': {
                        [sequelize_1.Op.iLike]: sourceLoginId
                    }
                }, {
                    departmentHead: this.sequlizeOperator.sequelizeLiteral(`jsonb_set(department_head, '{loginId}', '"${targetLoginId}"', true)`),
                });
                yield this.costCenterRepository.updateCostCenterByConditionWoUser({
                    'sectionHead.loginId': {
                        [sequelize_1.Op.iLike]: sourceLoginId
                    }
                }, {
                    sectionHead: this.sequlizeOperator.sequelizeLiteral(`jsonb_set(section_head, '{loginId}', '"${targetLoginId}"', true)`),
                });
                yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                    associateType: associated_type_enum_1.ASSOCIATED_TYPE.USER,
                    associatedUser: sequelize_1.Sequelize.where(sequelize_1.Sequelize.literal('LOWER("associated_user")'), { [sequelize_1.Op.eq]: sourceLoginId })
                }, { associatedUser: targetLoginId }, currentContext);
                yield this.afeProposalApproverRepository.updateApproverByConditionWoUser({
                    assginedType: this.sequlizeOperator.inOperator([enums_1.ASSIGNED_TYPE.USER, enums_1.ASSIGNED_TYPE.COST_CENTER]),
                    assignedTo: sequelize_1.Sequelize.where(sequelize_1.Sequelize.literal('LOWER("assigned_to")'), { [sequelize_1.Op.eq]: sourceLoginId })
                }, { assignedTo: targetLoginId });
                yield this.afeProposalApproverRepository.updateApproverByConditionWoUser({
                    actionBy: sequelize_1.Sequelize.where(sequelize_1.Sequelize.literal('LOWER("action_by")'), { [sequelize_1.Op.eq]: sourceLoginId })
                }, { actionBy: targetLoginId });
                yield this.afeProposalApproverRepository.updateApproverByConditionWoUser({
                    'userDetail.loginId': {
                        [sequelize_1.Op.iLike]: sourceLoginId
                    }
                }, {
                    userDetail: this.sequlizeOperator.sequelizeLiteral(`jsonb_set(user_detail, '{loginId}', '"${targetLoginId}"', true)`),
                });
                let notificationUser = yield this.notificationRepository.getAllMatchingSubscriberOrViewer(sourceLoginId);
                if (notificationUser.length) {
                    yield Promise.all(notificationUser.map((notificationDetail) => __awaiter(this, void 0, void 0, function* () {
                        var _a, _b;
                        let otherSubscriber = (notificationDetail === null || notificationDetail === void 0 ? void 0 : notificationDetail.subscribers) || [];
                        if ((_a = notificationDetail === null || notificationDetail === void 0 ? void 0 : notificationDetail.subscribers) === null || _a === void 0 ? void 0 : _a.length) {
                            const isSubAvailable = notificationDetail.subscribers.find(subscriber => subscriber.toLowerCase() === sourceLoginId.toLowerCase());
                            if (isSubAvailable) {
                                otherSubscriber = notificationDetail.subscribers.filter(subscriber => subscriber.toLowerCase() !== sourceLoginId.toLowerCase());
                                otherSubscriber.push(targetLoginId);
                            }
                        }
                        let otherViewer = (notificationDetail === null || notificationDetail === void 0 ? void 0 : notificationDetail.viewedBy) || [];
                        if ((_b = notificationDetail === null || notificationDetail === void 0 ? void 0 : notificationDetail.viewedBy) === null || _b === void 0 ? void 0 : _b.length) {
                            const isViewAvailable = notificationDetail.viewedBy.find(viewedBy => viewedBy.toLowerCase() === sourceLoginId.toLowerCase());
                            if (isViewAvailable) {
                                otherViewer = notificationDetail.viewedBy.filter(viewedBy => viewedBy.toLowerCase() !== sourceLoginId.toLowerCase());
                                otherViewer.push(targetLoginId);
                            }
                        }
                        yield this.notificationRepository.updateNotificationByConditionWoUser({
                            id: notificationDetail.id
                        }, {
                            subscribers: otherSubscriber,
                            viewedBy: otherViewer
                        });
                    })));
                }
                return { message: 'Login id has been updated successfully.' };
            }));
        });
    }
};
AdminService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.DraftAfeRepository,
        helpers_1.DatabaseHelper,
        repositories_2.AfeProposalRepository,
        repositories_3.CostCenterRepository,
        repositories_4.WorkflowMasterStepRepository,
        repositories_2.AfeProposalApproverRepository,
        repositories_5.NotificationRepository,
        helpers_1.SequlizeOperator])
], AdminService);
exports.AdminService = AdminService;
//# sourceMappingURL=admin.service.js.map