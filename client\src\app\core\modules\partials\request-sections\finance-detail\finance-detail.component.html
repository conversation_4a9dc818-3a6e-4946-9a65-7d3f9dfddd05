<section class="mt-5">
  <div class="w-100 div-item p-5 bg-body rounded">
    <div class="w-100 border p-5 bg-body rounded">
      <div
        class="row mb-5 border-bottom d-flex justify-content-between pb-1 pb-lg-1"
      >
        <div class="col-lg-6 col-md-6 col-sm-12">
          <h3
            class="fw-bolder"
            translate="AFE_MODULE.SECTION_TITLE.FINANCE_DETAILS"
          ></h3>
        </div>

        <!-- (FinanceDetail?.internalStatus &&
              FinanceDetail.internalStatus === 'IN_PROGRESS') || -->
        <!-- <button
          *ngIf="
            FinanceDetail.isSupplemental &&
            FinanceDetail?.parentAfeLatestProjectComponentInfo?.length &&
            (FinanceDetail?.internalStatus || FinanceDetail?.draftId)
          "
          type="button"
          (click)="openAmountBreakdownModal()"
          class="btn btn-sm mb-3 editBtn btnRounded fw-bold ps-4 pe-4 py-1 mx-2"
          translate="FORM.BUTTON.AMOUNT_DIFFERENCE_BUTTON"
        ></button> -->

        <div
          *ngIf="
            FinanceDetail?.id && FinanceDetail.isSupplemental && isCapexOpex()
          "
          class="col-lg-6 col-md-6 col-sm-12"
        >
          <div class="d-flex justify-content-end fw-bold">
            {{ "FORM.LABEL.SECTION_VIEW" | translate }}
            <div class="form-switch form-check ms-2">
              <input
                (click)="updateView()"
                [(ngModel)]="amountSplitTableView"
                type="checkbox"
                class="form-check-input"
                id="site_state"
              />
            </div>
            <label
              for="site_state"
              class="form-check-label"
              translate="FORM.LABEL.TABULAR_VIEW"
            ></label>
          </div>
        </div>
      </div>

      <ng-container *ngIf="isAmountBreakupReady && amountSplitTableView">
        <app-supplimental-amount-breakup
          [currencyList]="currencyList"
          [prevBudgetBasedProjectSplit]="prevBudgetBasedProjectSplit"
          [newBudgetBasedProjectSplit]="newBudgetBasedProjectSplit"
        ></app-supplimental-amount-breakup>
      </ng-container>

      <ng-container *ngIf="!amountSplitTableView">
        <!-- Total Expenditure [DONE] -->
        <app-total-expenditure
          [showHeaderTitle]="true"
          [currencyDetail]="FinanceDetail.currencyDetail"
          [totalAmount]="FinanceDetail.totalAmount"
          [totalMarketValue]="
            FinanceDetail.totalMarketValue !== undefined
              ? FinanceDetail.totalMarketValue
              : null
          "
          [inputView]="false"
          [requestTypeId]="requestTypeId"
          [supplementalAmount]="supplementalAmount"
          [supplementalDeltaAmounts]="
            supplementalDeltaAmounts ? supplementalDeltaAmounts : null
          "
        ></app-total-expenditure>

        <!-- Budget Type Split [DONE] -->
        <div
          class="w-100 border p-5 bg-body rounded my-3"
          *ngIf="
            FinanceDetail?.budgetTypeSplit?.length &&
            FinanceDetail.budgetTypeSplit.length > 1
          "
        >
          <!-- && FinanceDetail?.budgetType?.id === 3 -->
          <h5 class="fw-bolder pb-3 d-flex align-items-center">
            {{ "FORM.LABEL.BUDGET" | translate }}
            {{ "FORM.LABEL.TYPE" | translate }}
            {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
          </h5>

          <div class="row">
            <div
              *ngFor="
                let budgetTypeSplitDetail of FinanceDetail.budgetTypeSplit
              "
              class="col-12 col-lg-6 col-md-6 col-sm-6 mb-3"
            >
              <label class="form-label fw-bold text-gray-600">
                {{
                  FinanceDetail.budgetType.title
                    ? getBudgetTypeTitle(budgetTypeSplitDetail.title)
                    : (noDataText | titlecase)
                }}
              </label>
              <div class="fw-bold">
                <app-amount-representation
                  [currentAmount]="{
                    amount: budgetTypeSplitDetail?.amount || 0,
                    currency: FinanceDetail.currencyDetail.currency,
                    conversionRate: FinanceDetail.currencyDetail.conversionRate
                  }"
                  [supplementalDeltaAmounts]="
                    budgetTypeSplitDetail?.deltaAdditionalCurrencyAmount
                      ? {
                          amount:
                            budgetTypeSplitDetail?.deltaAdditionalCurrencyAmount
                              ?.amount || 0,
                          currency:
                            budgetTypeSplitDetail?.deltaAdditionalCurrencyAmount
                              ?.currency || 'USD'
                        }
                      : null
                  "
                ></app-amount-representation>
              </div>
            </div>
          </div>
        </div>

        <!-- Budget Based Project Split -->
        <div
          class="w-100 border p-5 bg-body rounded my-3"
          *ngIf="
            FinanceDetail?.budgetBasedProjectSplit?.length &&
            FinanceDetail.budgetBasedProjectSplit.length > 1
          "
        >
          <h5 class="fw-bolder pb-3 d-flex align-items-center">
            {{ "COMMON.BUDGET_BASED_PROJECT_SPLIT" | translate }}
            {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
          </h5>

          <div class="row">
            <div
              *ngFor="
                let budgetBasedProjectSplit of FinanceDetail.budgetBasedProjectSplit;
                let i = index
              "
              class="col-12 col-12 mb-3"
            >
              <div *ngIf="i" class="separator mt-5 mb-3 opacity-75"></div>

              <label class="form-label fw-bolder text-gray-800">
                {{ budgetBasedProjectSplit.title }}
              </label>

              <div class="row">
                <!-- Budgeted Amount -->
                <div class="col-6">
                  <label class="form-label fw-bold pb-1">
                    {{ "COMMON.BUDGETED" | translate }} (
                    {{ "COMMON.AMOUNT" | translate }}
                    {{ "COMMON.IN" | translate }}
                    {{ FinanceDetail.currencyDetail.currency }} )
                  </label>
                  <div class="fw-bold">
                    <app-amount-representation
                      [currentAmount]="{
                        amount: budgetBasedProjectSplit?.budgetedAmount || 0,
                        currency: FinanceDetail.currencyDetail.currency,
                        conversionRate:
                          FinanceDetail.currencyDetail.conversionRate
                      }"
                      [supplementalDeltaAmounts]="
                        budgetBasedProjectSplit?.deltaAdditionalCurrencyAmount
                          ? {
                              amount:
                                budgetBasedProjectSplit
                                  ?.deltaAdditionalCurrencyAmount
                                  ?.budgetedAmount || 0,
                              currency:
                                budgetBasedProjectSplit
                                  ?.deltaAdditionalCurrencyAmount?.currency ||
                                'USD'
                            }
                          : null
                      "
                    ></app-amount-representation>
                  </div>
                </div>

                <!-- Unbudgeted Amount -->
                <div class="col-6">
                  <label class="form-label fw-bold pb-1">
                    {{ "COMMON.UNBUDGETED" | translate }} (
                    {{ "COMMON.AMOUNT" | translate }}
                    {{ "COMMON.IN" | translate }}
                    {{ FinanceDetail.currencyDetail.currency }} )
                  </label>
                  <div class="fw-bold">
                    <app-amount-representation
                      [currentAmount]="{
                        amount: budgetBasedProjectSplit?.unbudgetedAmount || 0,
                        currency: FinanceDetail.currencyDetail.currency,
                        conversionRate:
                          FinanceDetail.currencyDetail.conversionRate
                      }"
                      [supplementalDeltaAmounts]="
                        budgetBasedProjectSplit?.deltaAdditionalCurrencyAmount
                          ? {
                              amount:
                                budgetBasedProjectSplit
                                  ?.deltaAdditionalCurrencyAmount
                                  ?.unbudgetedAmount || 0,
                              currency:
                                budgetBasedProjectSplit
                                  ?.deltaAdditionalCurrencyAmount?.currency ||
                                'USD'
                            }
                          : null
                      "
                    ></app-amount-representation>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-container>

      <!-- Cost Center Split -->
      <div
        class="w-100 border p-5 bg-body rounded my-3"
        *ngIf="FinanceDetail?.costCenterSplit?.length"
      >
        <h5
          *ngIf="costCenterAvailable"
          class="fw-bolder pb-3 d-flex align-items-center"
        >
          {{ "FORM.LABEL.COST_CENTER" | translate }}
          {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
        </h5>

        <h5
          *ngIf="!costCenterAvailable"
          class="fw-bolder pb-3 d-flex align-items-center"
        >
          {{ "FORM.LABEL.BUDGET_REFERENCE_NUMBER" | translate }}
          {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
        </h5>

        <div class="row">
          <div
            *ngFor="
              let costCenterSplitDetail of FinanceDetail.costCenterSplit;
              let costCenterSeq = index
            "
            class="col-12 mb-2 p-5 pb-0 border border-gray-300 rounded"
          >
            <ng-container *ngIf="costCenterAvailable">
              <div class="row">
                <div
                  [class]="
                    costCenterSplitDetail?.section ? 'col-lg-6' : 'col-lg-12'
                  "
                >
                  {{ costCenterSeq + 1 }}.
                  <label
                    class="form-label fw-bold text-gray-600"
                    translate="FORM.LABEL.COST_CENTER_TITLE"
                  ></label>
                  <div class="fw-bold">
                    {{ costCenterSplitDetail.title }}
                    <span *ngIf="costCenterSplitDetail.code">
                      - ({{ costCenterSplitDetail.code }})</span
                    >
                  </div>
                </div>
                <div class="col-lg-6" *ngIf="costCenterSplitDetail?.section">
                  <label
                    class="form-label fw-bold text-gray-600"
                    translate="FORM.LABEL.SECTION"
                  ></label>
                  <div class="fw-bold">
                    {{ costCenterSplitDetail.section }}
                  </div>
                </div>
              </div>

              <div class="row">
                <div
                  class="col-lg-6 mt-3"
                  *ngIf="costCenterSplitDetail?.naturalAccount?.title"
                >
                  <label
                    class="form-label fw-bold text-gray-600"
                    translate="FORM.LABEL.NATURAL_ACCOUNT"
                  ></label>
                  <div class="fw-bold">
                    {{ costCenterSplitDetail.naturalAccount.title }} - ({{
                      costCenterSplitDetail?.naturalAccount?.number
                    }})
                  </div>
                </div>
                <div
                  class="col-lg-6 mt-3"
                  *ngIf="costCenterSplitDetail?.analysisCode?.title"
                >
                  <label
                    class="form-label fw-bold text-gray-600"
                    translate="FORM.LABEL.ANALYSIS_CODE"
                  ></label>
                  <div class="fw-bold">
                    {{ costCenterSplitDetail.analysisCode.title }} - ({{
                      costCenterSplitDetail?.analysisCode?.code
                    }})
                  </div>
                </div>
              </div>
            </ng-container>

            <div
              *ngIf="costCenterAvailable"
              class="separator separator-dashed separator-border-1 my-5"
            ></div>

            <div
              *ngIf="costCenterSplitDetail?.budgetReferenceNumberSplit?.length"
            >
              <h6 *ngIf="costCenterAvailable" class="fw-bolder mb-2">
                {{ "FORM.LABEL.BUDGET_REFERENCE_NUMBER" | translate }}
                {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
              </h6>

              <div class="row">
                <div
                  *ngFor="
                    let budgetReferenceNumberSplit of costCenterSplitDetail.budgetReferenceNumberSplit;
                    let budgetRefSeq = index
                  "
                  class="row"
                >
                  <div class="col-lg-6 col-12 mb-3">
                    {{ costCenterSeq + 1 }}.{{ budgetRefSeq + 1 }}.
                    <label
                      class="form-label fw-bold text-gray-600"
                      translate="FORM.LABEL.BUDGET_REFERENCE_NUMBER"
                    ></label>
                    <div class="fw-bold">
                      {{
                        budgetReferenceNumberSplit.number
                          ? budgetReferenceNumberSplit.number
                          : "N/A"
                      }}
                    </div>
                  </div>
                  <div class="col-lg-6 col-12 mb-3">
                    <label class="form-label fw-bold text-gray-600">
                      {{ "FORM.LABEL.TOTAL" | translate }}
                      {{ "COMMON.AMOUNT" | translate }}
                      in {{ FinanceDetail.currencyDetail.currency }}
                    </label>
                    <div class="fw-bold">
                      {{
                        budgetReferenceNumberSplit.amount
                          | currency
                            : FinanceDetail.currencyDetail.currency
                            : "symbol"
                            : "1.2-2"
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- New Natural A/C  Split -->
      <div
        class="w-100 border p-5 bg-body rounded mt-3"
        *ngIf="FinanceDetail?.naturalAccountNumberSplit?.length"
      >
        <h5
          class="fw-bolder pb-3 d-flex align-items-center border-bottom mb-4"
          translate="FORM.LABEL.NATURAL_ACCOUNTS"
        ></h5>

        <div class="row">
          <div
            class="col-12 col-lg-6 col-md-6 col-sm-12 mb-4"
            *ngFor="
              let naturalAccountNumberSplitDetail of FinanceDetail.naturalAccountNumberSplit;
              let i = index
            "
          >
            <div class="card border-light-subtle shadow-sm h-100">
              <div class="card-body p-4">
                <div class="mb-3">
                  <label
                    class="form-label fw-bold text-gray-600 mb-2"
                    translate="FORM.LABEL.ACCOUNT_NUMBER"
                  ></label>
                  <div class="fw-bold text-dark fs-6">
                    {{ naturalAccountNumberSplitDetail.title }} - ({{
                      naturalAccountNumberSplitDetail.number
                    }})
                  </div>
                </div>

                <div class="separator mb-3 opacity-25"></div>

                <div>
                  <label class="form-label fw-bold text-gray-600 mb-2">
                    {{ "COMMON.AMOUNT" | translate }}
                    {{ "COMMON.IN" | translate }}
                    {{ FinanceDetail.currencyDetail.currency }}
                  </label>
                  <div class="fw-bold text-primary fs-5">
                    {{
                      naturalAccountNumberSplitDetail.amount
                        | currency
                          : FinanceDetail.currencyDetail.currency
                          : "symbol"
                          : "1.2-2"
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chart Of account split -->
      <div
        class="w-100 border p-2 bg-body rounded my-3"
        *ngIf="FinanceDetail?.chartOfAccounts?.length"
      >
        <div class="rounded p-3">
          <h5 class="fw-bolder mb-2" translate="FORM.LABEL.CHARGE_AMMOUNT"></h5>

          <ng-container
            *ngFor="
              let chartOfAccount of FinanceDetail.chartOfAccounts;
              let key = index
            "
          >
            <ng-container
              *ngIf="
                getTypeOfVariable(chartOfAccount.segments) === 'object';
                else stringOfCOA
              "
            >
              <div class="row pt-3 px-3">
                <div class="col-12 mb-4 p-0">
                  <label
                    class="fw-bold text-gray-600"
                    translate="FORM.LABEL.CHART_OF_ACCOUNT"
                  ></label>
                  <div class="fw-bold">
                    {{ getChartOfAccountString(chartOfAccount.segments) }}
                  </div>
                </div>

                <div class="col-12 mb-4 p-0">
                  <label class="fw-bold text-gray-600">
                    {{ "FORM.LABEL.CHARGE_AMMOUNT" | translate }} in
                    {{ FinanceDetail.currencyDetail.currency }}
                  </label>
                  <div class="fw-bold">
                    {{
                      chartOfAccount.amount
                        | currency
                          : FinanceDetail.currencyDetail.currency
                          : "symbol"
                          : "1.2-2"
                    }}
                  </div>
                </div>
              </div>
            </ng-container>

            <ng-template #stringOfCOA>
              <div class="my-2">
                <label
                  class="fw-bold text-gray-600 mb-2"
                  translate="FORM.LABEL.CHART_OF_ACCOUNT"
                ></label>
                <div class="row">
                  <div class="fw-bold text-gray-600">
                    {{ chartOfAccount.segments }}
                  </div>
                </div>
              </div>

              <div class="my-2">
                <label
                  class="fw-bold text-gray-600 mb-2"
                  translate="FORM.LABEL.CHARGE_AMMOUNT"
                ></label>
                <div class="row">
                  <div class="fw-bold">
                    {{
                      FinanceDetail.totalAmount
                        | currency
                          : FinanceDetail.currencyDetail.currency
                          : "symbol"
                          : "1.2-2"
                    }}
                  </div>
                </div>
              </div>
            </ng-template>

            <div
              class="separator opacity-75"
              *ngIf="
                FinanceDetail?.chartOfAccounts?.length &&
                FinanceDetail.chartOfAccounts.length > 1
              "
            ></div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- && FinanceDetail.internalStatus === 'IN_PROGRESS' -->
<ng-container
  *ngIf="
    supplimentalModalReady &&
    FinanceDetail.isSupplemental &&
    (FinanceDetail?.internalStatus || FinanceDetail?.draftId)
  "
>
  <app-modal
    #supplimentalAmountBreakup
    [modalConfig]="supplimentalAmountBreakupConfig"
  >
    <app-supplimental-amount-breakup
      [prevBudgetBasedProjectSplit]="prevBudgetBasedProjectSplit"
      [newBudgetBasedProjectSplit]="newBudgetBasedProjectSplit"
    ></app-supplimental-amount-breakup>
  </app-modal>
</ng-container>
