<ng-container *ngIf="formReady; else loadingPage">
  <div
    class="w-100 bg-body rounded"
    [ngClass]="{'px-5 py-5': !isDryRun}"
    *ngIf="totalProjectComponentLeft()"
  >
    <div
      *ngIf="!isDryRun && currencyDetail && !currencyDetail.primary"
      class="d-flex justify-content-end w-100 px-0 pb-2"
    >
      <span class="fw-bold"
        >{{ "COMMON.EXCHANGE_RATE" | translate }} &nbsp;</span
      >
      <span class="ml-1 badge badge-light-primary fs-8 fw-bolder">
        1 USD - {{ currencyDetail.conversionRate }}
        {{ currencyDetail.currency }}
      </span>
    </div>

    <div *ngIf="!defaultValues?.isSupplemental" class="col-12 border rounded p-5 mb-5">
      <div class="pb-1 pb-lg-1 border-bottom mb-5">
        <h3
          class="fw-bolder d-flex align-items-center"
          translate="AFE_MODULE.SECTION_TITLE.PROJECT_COMPONENT"
        ></h3>
      </div>

      <label class="form-label fw-bold required">
        {{ "FORM.PLACEHOLDER.SELECT" | translate }}
        {{ "FORM.LABEL.PROJECT_COMPONENT" | translate }}
      </label>
      <select
        [(ngModel)]="projectComponentSelected"
        (change)="onAddProjectComponent()"
        name="projectComponent"
        class="form-select form-select-lg form-select-solid"
      >
        <option value="">
          {{ "FORM.PLACEHOLDER.SELECT" | translate }}
          {{ "FORM.LABEL.PROJECT_COMPONENT" | translate }}
        </option>
        <ng-container *ngFor="let projectComponent of projectComponentList">
          <option
            *ngIf="projectComponent.active"
            [value]="+projectComponent.id"
          >
            {{ projectComponent.title }}
          </option>
        </ng-container>
      </select>

      <ng-container *ngIf="isRequiredError()">
        <app-input-error-message
          errorMessage=" {{
            'FORM.VALIDATION.MIN_PROJECT_COMPONENT' | translate
          }}"
        >
        </app-input-error-message>
      </ng-container>
    </div>
  </div>

  <div *ngIf="projectComponentList.length" [formGroup]="form">
    <div
      *ngIf="
        projectComponentSplit.controls.length && isLengthOfCommitmentEnabled
      "
      class="w-100 bg-body rounded mb-5 mt-5"
      [ngClass]="{'px-5 py-5': !isDryRun}"
    >
      <div class="col-12 border rounded p-5 mb-5">
        <div class="pb-1 pb-lg-1 border-bottom mb-5">
          <h3
            class="fw-bolder d-flex align-items-center"
            translate="LIST.LENGTH_OF_COMMITMENT"
          ></h3>
        </div>

        <label class="text-gray-600 fw-bold required">
          {{ "LIST.LENGTH_OF_COMMITMENT" | translate }}
        </label>

        <select
          name="lengthOfCommitment"
          formControlName="lengthOfCommitment"
          class="form-select form-select-lg form-select-solid"
          id="lengthOfCommitment"
        >
          <option value="">
            {{ "FORM.PLACEHOLDER.SELECT" | translate }}
            {{ "LIST.LENGTH_OF_COMMITMENT" | translate }}
          </option>
          <option
            *ngFor="let lengthOfCommitment of lengthOfCommitments"
            [value]="lengthOfCommitment.year"
          >
            {{ lengthOfCommitment.title }}
          </option>
        </select>

        <ng-container
          *ngIf="
            form.get('lengthOfCommitment')?.hasError('min') &&
            (isSubmitted || form.get('lengthOfCommitment')?.touched)
          "
        >
          <app-input-error-message
            errorMessage="{{ 'FORM.VALIDATION.MIN_LOC' | translate }}"
          >
          </app-input-error-message>
        </ng-container>

        <ng-container
          *ngIf="
            form.get('lengthOfCommitment')?.hasError('required') &&
            (isSubmitted || form.get('lengthOfCommitment')?.touched)
          "
        >
          <app-input-error-message
            errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
          >
          </app-input-error-message>
        </ng-container>
      </div>
    </div>

    <div
      *ngIf="projectComponentSplit.controls.length"
      formArrayName="projectComponentSplit"
      class="w-100 bg-body rounded mb-5 mt-5"
      [ngClass]="{'px-5 py-5': !isDryRun}"
    >
      <div class="col-12 border rounded p-5 mb-5">
        <div class="pb-1 pb-lg-1 border-bottom mb-5 d-flex justify-content-between">
          <h3
            class="fw-bolder d-flex align-items-center"
            translate="AFE_MODULE.SECTION_TITLE.PROJECT_COMPONENT_AMOUNT"
          ></h3>

          <button
            *ngIf="excludedChildProjects?.length && !defaultValues?.isSupplemental"
            (click)="addAllRemovedProjects()"
            type="button"
            class="btn btn-sm mb-3 editBtn btnRounded fw-bold ps-4 pe-4 py-1 mx-2"
            translate="FORM.BUTTON.ADD_REMOVED_PROJECT"
        ></button>
        </div>

        <div
          *ngFor="
            let projectComponent of projectComponentSplit.controls;
            let i = index
          "
          [formGroupName]="i"
        >
          <div *ngIf="i" class="separator mb-3 opacity-75"></div>

          <div class="mr-5 w-100">
            <div class="d-flex flex-row justify-content-between pb-1">
              <div>
                <label class="text-gray-600 fw-bold required">
                  {{ projectComponent.value.title }}
                  ({{ "COMMON.AMOUNT" | translate }}
                  {{ "COMMON.IN" | translate }} {{ currencyDetail.currency }})
                </label>
              </div>
            </div>

            <!-- <a (click)="changeProjectComponentStatus(projectComponent.value, i)">
            <i class="mx-1 fas fa-times-circle text-dark"></i>
          </a> -->

            <div class="d-flex flex-row justify-content-between pb-1">
              <div class="w-100">
                <app-input-amount 
                  type="currency" 
                  formControlName="amount" 
                  placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{ 'COMMON.AMOUNT' | translate }} {{ 'COMMON.IN' | translate }} {{ currencyDetail.currency }}">
                </app-input-amount>

                <ng-container
                  *ngIf="
                    projectComponent.get('amount')?.hasError('min') &&
                    (isSubmitted || projectComponent.get('amount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.MIN_AMOUNT' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>

                <ng-container
                  *ngIf="
                    projectComponent.get('amount')?.hasError('max') &&
                    (isSubmitted || projectComponent.get('amount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.MAX_AMOUNT' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>

                <ng-container
                  *ngIf="
                    projectComponent.get('amount')?.hasError('required') &&
                    (isSubmitted || projectComponent.get('amount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>

              <div
                *ngIf="!defaultValues?.isSupplemental"
                class="pt-3 cursor-pointer  px-2"
                (click)="
                  changeProjectComponentStatus(projectComponent.value, i)
                "
              >
                <img
                  width="32px"
                  src="./assets/media/svg/icons/dpw-icons/cross2.png"
                  alt="Remove"
                />
              </div>
            </div>
          </div>
        </div>

        <div
          *ngIf="defaultValues?.isSupplemental"
          class="notice d-flex justify-content-between bg-light-warning rounded border-warning border border-dashed p-4 mt-5"
        >
          <div class="text-gray-600" translate="WARNING.OPEX_PROJECT_COMPONENT_RESTRICTION"></div>
        </div>
        
        <ng-container *ngIf="parentProjectComponentInfo?.length">
          <div class="separator mt-5 separator-dashed separator-border-1"></div>
          <div class="pb-3 pb-lg-2 mt-5">
            <h3
              class="fw-bold d-flex align-items-center"
              translate="AFE_MODULE.SECTION_TITLE.AFE_APPROVED_VALUE"
            ></h3>
          </div>
          <div class="fv-row mb-2" *ngIf="parentProjectComponentInfo?.length">
            <div
              class="row"
              *ngFor="
                let projectComponent of parentProjectComponentInfo;
                let i = index
              "
            >
              <div class="col-lg-6 col-sm-6 mb-2 mb-lg-2">
                <label class="text-gray-600 fw-bold">
                  {{ projectComponent.title }}
                  ({{ "COMMON.AMOUNT" | translate }}
                  {{ "COMMON.IN" | translate }} {{ projectComponent.currency }})
                </label>
                <div class="h4 text-gray-800">
                  {{
                    projectComponent.amount
                      | currency: projectComponent.currency:"symbol":"1.2-2"
                  }}
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #loadingPage>
  <app-form-skeleton-loader></app-form-skeleton-loader>
</ng-template>
