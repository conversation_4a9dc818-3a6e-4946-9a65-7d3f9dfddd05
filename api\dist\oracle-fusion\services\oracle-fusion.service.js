"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OracleFusionService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const repositories_1 = require("../../afe-proposal/repositories");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const constants_1 = require("../constants");
const services_1 = require("../../finance/services");
const clients_1 = require("../../shared/clients");
const repositories_2 = require("../../finance/repositories");
const services_2 = require("../../shared/services");
let OracleFusionService = class OracleFusionService {
    constructor(afeProposalRepository, afeProposalAmountSplitRepository, fussionApi, fussionUaeApi, sequlizeOperator, financeAdminService, adminApiClient, costCenterRepository, mSGraphApiClient, sharedNotificationService) {
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalAmountSplitRepository = afeProposalAmountSplitRepository;
        this.fussionApi = fussionApi;
        this.fussionUaeApi = fussionUaeApi;
        this.sequlizeOperator = sequlizeOperator;
        this.financeAdminService = financeAdminService;
        this.adminApiClient = adminApiClient;
        this.costCenterRepository = costCenterRepository;
        this.mSGraphApiClient = mSGraphApiClient;
        this.sharedNotificationService = sharedNotificationService;
    }
    sendFusionEnabledApprovedAfe() {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function* () {
            console.log('sendFusionEnabledApprovedAfe');
            const businessHierarchy = yield this.adminApiClient.getAllBusinessHierarchy();
            console.log('sendFusionEnabledApprovedAfe 1');
            const fusionReadyAfeList = yield this.afeProposalRepository.getFusionReadyAfeProposals();
            console.log('sendFusionEnabledApprovedAfe 2', fusionReadyAfeList.length);
            let fusionIntegrationAfeList = [];
            if (fusionReadyAfeList.length) {
                const isNotProd = process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'staging';
                for (let i = 0; i < fusionReadyAfeList.length; i++) {
                    const result = fusionReadyAfeList[i];
                    let afeProposalId = (0, lodash_1.toNumber)(result.id);
                    const proposalAmountSplit = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndTypes(afeProposalId, [
                        enums_1.AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT,
                        enums_1.AMOUNT_SPLIT.GL_CODE_SPLIT,
                        enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT,
                        enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT,
                    ], ['objectId', 'objectTitle', 'type', 'amount', 'currency', 'additionalCurrencyAmount']);
                    let projectComponentList = [];
                    let budgetReferenceNumbers = [];
                    let glCodes = [];
                    let costCenterCode = '';
                    let fusionIntegrationData = {
                        id: null,
                        isUAEEntity: false,
                        Projects: [],
                        Budgets: [],
                        ProjectClassifications: [],
                        fusionIntegrationResponse: [],
                        submitterId: null,
                        projectLeaderEmail: null,
                        sendEmailToSubmitter: false,
                        sendEmailToLeader: false,
                    };
                    for (let i = 0; i < proposalAmountSplit.length; i++) {
                        const proposalAmount = proposalAmountSplit[i];
                        if (proposalAmount.type === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT) {
                            const costCenterId = proposalAmount.objectId;
                            const costCenterDetail = yield this.costCenterRepository.getCostCenterById(costCenterId);
                            if (costCenterDetail === null || costCenterDetail === void 0 ? void 0 : costCenterDetail.code) {
                                costCenterCode = costCenterDetail.code;
                            }
                            if ((_b = (_a = proposalAmount === null || proposalAmount === void 0 ? void 0 : proposalAmount.additionalInfo) === null || _a === void 0 ? void 0 : _a.budgetReferenceNumberSplit) === null || _b === void 0 ? void 0 : _b.length) {
                                proposalAmount.additionalInfo.budgetReferenceNumberSplit.forEach(budgetReferenceNumberSplit => {
                                    budgetReferenceNumbers.push(budgetReferenceNumberSplit.number);
                                });
                            }
                        }
                        if (proposalAmount.type === enums_1.AMOUNT_SPLIT.GL_CODE_SPLIT) {
                            glCodes.push({
                                code: proposalAmount.objectTitle,
                                additionalCurrencyAmount: proposalAmount.additionalCurrencyAmount,
                            });
                        }
                        if (proposalAmount.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT) {
                            projectComponentList.push(proposalAmount.objectTitle);
                        }
                    }
                    let { id, afeRequestTypeId, category, createdOn, data, parentAfeId, budgetType, projectReferenceNumber, supplementalData, additionalCurrencyAmount, workflowYear, name: projectTitle, afeRequestType, fusionIntegrationResponse, entityId, submitterId, entityCode } = result;
                    const submitterDetail = yield this.mSGraphApiClient.getUserDetails(submitterId.toLowerCase());
                    if (submitterDetail) {
                        submitterId = (submitterDetail === null || submitterDetail === void 0 ? void 0 : submitterDetail.mail) || (submitterDetail === null || submitterDetail === void 0 ? void 0 : submitterDetail.userPrincipalName) || submitterId;
                        fusionIntegrationData.sendEmailToSubmitter = !!(submitterDetail === null || submitterDetail === void 0 ? void 0 : submitterDetail.mail);
                    }
                    let projectLeaderEmail = data.projectDetails.projectLeader.mail;
                    if ((_d = (_c = data === null || data === void 0 ? void 0 : data.projectDetails) === null || _c === void 0 ? void 0 : _c.projectLeader) === null || _d === void 0 ? void 0 : _d.mail) {
                        const projectLeaderDetail = yield this.mSGraphApiClient.getUserDetails(data.projectDetails.projectLeader.mail.toLowerCase());
                        projectLeaderEmail = (projectLeaderDetail === null || projectLeaderDetail === void 0 ? void 0 : projectLeaderDetail.mail) || (projectLeaderDetail === null || projectLeaderDetail === void 0 ? void 0 : projectLeaderDetail.userPrincipalName) || submitterId;
                        fusionIntegrationData.sendEmailToLeader = !!(projectLeaderDetail === null || projectLeaderDetail === void 0 ? void 0 : projectLeaderDetail.mail);
                    }
                    if (afeRequestTypeId === 3) {
                        const companyDetail = yield this.financeAdminService.getEntityActiveCompanyCodeDetails(entityId, false);
                        if (companyDetail === null || companyDetail === void 0 ? void 0 : companyDetail.code) {
                            const naturalAccountList = yield this.financeAdminService.getCapexNaturalAccountNumbersByCompanyCodeId(companyDetail.id);
                            let EiNaturalAccount = '';
                            if (naturalAccountList === null || naturalAccountList === void 0 ? void 0 : naturalAccountList.length) {
                                EiNaturalAccount = naturalAccountList[0].number;
                            }
                            const EiGlCode = companyDetail.code +
                                '-0000-' +
                                (EiNaturalAccount || '********') +
                                '-0000-000000-000000-********-0000';
                            glCodes.push({
                                code: EiGlCode,
                                additionalCurrencyAmount
                            });
                        }
                    }
                    const projectTitleRefNumber = projectTitle + ' ' + projectReferenceNumber;
                    const createdOnDateObj = new Date(createdOn);
                    let supplimentaryAFENumbers = '';
                    let budgetRefNumbers = '';
                    let allAfeIds = [];
                    if (category === enums_1.AFE_CATEGORY.SUPPLEMENTAL) {
                        const afeNumbers = yield this.afeProposalRepository.getApprovedAfeAndItsChildern(parentAfeId);
                        supplimentaryAFENumbers = '';
                        afeNumbers.forEach((afeNumber, key) => {
                            supplimentaryAFENumbers =
                                supplimentaryAFENumbers + (key ? '|' : '') + afeNumber.projectReferenceNumber;
                            allAfeIds.push((0, lodash_1.toNumber)(afeNumber.id));
                        });
                        const budgetRefAfes = allAfeIds.filter(afeId => {
                            if ((0, lodash_1.toNumber)(afeId) !== (0, lodash_1.toNumber)(afeProposalId)) {
                                return afeId;
                            }
                        });
                        const proposalBudgetRefSplit = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdsAndTypes(budgetRefAfes, [enums_1.AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT]);
                        const budgetRefClubbed = [];
                        proposalBudgetRefSplit.forEach(proposalBudgetRef => {
                            if (budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId]) {
                                budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId].push(proposalBudgetRef);
                            }
                            else {
                                budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId] = [];
                                budgetRefClubbed['BR' + proposalBudgetRef.afeProposalId].push(proposalBudgetRef);
                            }
                        });
                        budgetRefAfes.forEach((afeNumber, mainKey) => {
                            if (budgetRefClubbed['BR' + afeNumber]) {
                                budgetRefNumbers = budgetRefNumbers + (mainKey ? '|' : '');
                                budgetRefClubbed['BR' + afeNumber].forEach((budgetRef, childKey) => {
                                    budgetRefNumbers = budgetRefNumbers + (childKey ? ',' : '') + budgetRef.objectTitle;
                                });
                            }
                            else {
                                budgetRefNumbers =
                                    budgetRefNumbers +
                                        (budgetRefNumbers ? '|' : '') +
                                        constants_1.ORACLE_FUSION_DATA.UNBUDGETED_REF_NUMBER;
                            }
                        });
                        if (budgetReferenceNumbers.length) {
                            budgetReferenceNumbers.forEach((budgetReferenceNumber, key) => {
                                budgetRefNumbers =
                                    budgetRefNumbers +
                                        (budgetRefNumbers && !key ? '|' : '') +
                                        (key ? ',' : '') +
                                        budgetReferenceNumber;
                            });
                        }
                        else {
                            budgetRefNumbers =
                                budgetRefNumbers +
                                    (budgetRefNumbers ? '|' : '') +
                                    constants_1.ORACLE_FUSION_DATA.UNBUDGETED_REF_NUMBER;
                        }
                    }
                    else {
                        allAfeIds.push(id);
                        supplimentaryAFENumbers = projectReferenceNumber;
                        if (budgetReferenceNumbers.length) {
                            budgetReferenceNumbers.forEach((budgetReferenceNumber, key) => {
                                budgetRefNumbers =
                                    budgetRefNumbers +
                                        (budgetRefNumbers && !key ? '|' : '') +
                                        (key ? ',' : '') +
                                        budgetReferenceNumber;
                            });
                        }
                        else {
                            budgetRefNumbers =
                                budgetRefNumbers +
                                    (budgetRefNumbers ? '|' : '') +
                                    constants_1.ORACLE_FUSION_DATA.UNBUDGETED_REF_NUMBER;
                        }
                    }
                    fusionIntegrationData.id = id;
                    fusionIntegrationData.fusionIntegrationResponse = fusionIntegrationResponse
                        ? fusionIntegrationResponse
                        : [];
                    fusionIntegrationData.submitterId = submitterId;
                    fusionIntegrationData.projectLeaderEmail = projectLeaderEmail;
                    if (glCodes.length) {
                        if (afeRequestTypeId === 2) {
                            fusionIntegrationData.Projects = yield this.getOpexProjectDetail({
                                glCodes,
                                additionalCurrencyAmount,
                                afeRequestType,
                                afeRequestTypeId,
                                category,
                                createdOn,
                                supplementalData,
                                projectTitleRefNumber,
                                projectReferenceNumber,
                                projectLeaderEmail,
                                submitterId,
                                workflowYear,
                                entityCode,
                                isNotProd,
                                costCenterCode,
                                supplimentaryAFENumbers,
                                createdOnDateObj,
                            });
                        }
                        else {
                            fusionIntegrationData.Projects = yield this.getProjectDetail({
                                glCodes,
                                additionalCurrencyAmount,
                                afeRequestType,
                                afeRequestTypeId,
                                category,
                                createdOn,
                                supplementalData,
                                projectTitleRefNumber,
                                projectReferenceNumber,
                                projectLeaderEmail,
                                submitterId,
                                workflowYear,
                                entityCode,
                                isNotProd,
                                costCenterCode,
                                supplimentaryAFENumbers,
                                createdOnDateObj,
                            });
                        }
                        console.dir('Project ---- ');
                        console.dir(fusionIntegrationData.Projects, { depth: 10 });
                        const todayDate = new Date();
                        const currentYear = todayDate.getFullYear();
                        let currentMonth = todayDate.getMonth() + 1;
                        let currentDate = todayDate.getDate();
                        const parentEntityDetails = yield this.adminApiClient.getParentsOfEntity(entityId);
                        const financialEntityList = [
                            'GCC-LG',
                            'GCC-PNT',
                            'HO',
                            'MEARO-C1',
                            'MEARO-LG',
                            'DIGI-FZE',
                            'DPWDSLTD-C1',
                            'DTE'
                        ];
                        const overridenEntityList = [
                            'UAE-JEDPT',
                            'UAE-JEDLG'
                        ];
                        let financialPlanType = '';
                        if (parentEntityDetails === null || parentEntityDetails === void 0 ? void 0 : parentEntityDetails.length) {
                            const mainEntityDetail = parentEntityDetails.find(parentEntity => {
                                return financialEntityList.includes(parentEntity.code);
                            });
                            const overridenEntityDetail = parentEntityDetails.find(parentEntity => {
                                return overridenEntityList.includes(parentEntity.code);
                            });
                            const entityDetail = overridenEntityDetail ? overridenEntityDetail : mainEntityDetail;
                            if (entityDetail) {
                                switch (entityDetail.code) {
                                    case 'GCC-LG':
                                    case 'GCC-PNT':
                                        financialPlanType = 'UAER Cost Only';
                                        fusionIntegrationData.ProjectClassifications.push({
                                            ProjectName: projectTitleRefNumber,
                                            ClassCategory: constants_1.ORACLE_FUSION_DATA.CLASS_CATEGORY.TECHNICAL_DEPARTMENT_PROJECT,
                                            ClassCode: 'Yes',
                                        });
                                        fusionIntegrationData.isUAEEntity = true;
                                        break;
                                    case 'HO':
                                        financialPlanType = 'HO Cost Only';
                                        break;
                                    case 'DTE':
                                        financialPlanType = 'UAER Cost Only';
                                        break;
                                    case 'MEARO-C1':
                                    case 'MEARO-LG':
                                        financialPlanType = 'RO Cost Only';
                                        break;
                                    case 'DIGI-FZE':
                                        financialPlanType = 'DPW Digital Cost Only';
                                        break;
                                    case 'UAE-JEDPT':
                                    case 'UAE-JEDLG':
                                        financialPlanType = 'JED Cost Budget Non Periodic & Absolute Control';
                                        fusionIntegrationData.ProjectClassifications.push({
                                            ProjectName: projectTitleRefNumber,
                                            ClassCategory: constants_1.ORACLE_FUSION_DATA.CLASS_CATEGORY.TECHNICAL_DEPARTMENT_PROJECT,
                                            ClassCode: 'Yes',
                                        });
                                        fusionIntegrationData.isUAEEntity = true;
                                        break;
                                    case 'DPWDSLTD-C1':
                                        financialPlanType = 'DAR Cost Budget Non Periodic & Absolute Control';
                                        break;
                                    default:
                                        financialPlanType = '';
                                }
                            }
                        }
                        fusionIntegrationData.Budgets.push({
                            FinancialPlanType: financialPlanType,
                            PlanVersionName: constants_1.ORACLE_FUSION_DATA.PLAN_VERSION_SUFFFIX +
                                (currentMonth + '_' + currentDate + '_' + currentYear),
                            ProjectNumber: projectReferenceNumber,
                            PlanVersionStatus: constants_1.ORACLE_FUSION_DATA.PLAN_VERSION_STATUS,
                            BudgetCreationMethod: constants_1.ORACLE_FUSION_DATA.BUDGET_CREATION_METHOD,
                            TaskNumber: projectReferenceNumber,
                            ResourceName: constants_1.ORACLE_FUSION_DATA.RESOURCE_NAME,
                            Currency: additionalCurrencyAmount.currency,
                            RawCostAmounts: additionalCurrencyAmount.amount,
                            SourceBudgetLineReference: afeRequestTypeId === 1 || afeRequestTypeId === 2
                                ? budgetRefNumbers
                                    ? budgetRefNumbers
                                    : null
                                : null,
                        });
                        fusionIntegrationData.ProjectClassifications.push({
                            ProjectName: projectTitleRefNumber,
                            ClassCategory: constants_1.ORACLE_FUSION_DATA.CLASS_CATEGORY.AFE_TYPE,
                            ClassCode: afeRequestType.title,
                        });
                        if (budgetType) {
                            fusionIntegrationData.ProjectClassifications.push({
                                ProjectName: projectTitleRefNumber,
                                ClassCategory: constants_1.ORACLE_FUSION_DATA.CLASS_CATEGORY.BUDGET_TYPE,
                                ClassCode: enums_1.BUDGET_TYPE_TITLE_FOR_FUSION[budgetType],
                            });
                        }
                        projectComponentList.forEach(projectComponent => {
                            fusionIntegrationData.ProjectClassifications.push({
                                ProjectName: projectTitleRefNumber,
                                ClassCategory: constants_1.ORACLE_FUSION_DATA.CLASS_CATEGORY.PROJECT_COMPONENT_TYPE,
                                ClassCode: projectComponent,
                            });
                        });
                        fusionIntegrationAfeList.push(fusionIntegrationData);
                    }
                }
                let successCount = 0;
                let failedCount = 0;
                let returnResponse = [];
                for (let i = 0; i < fusionIntegrationAfeList.length; i++) {
                    const _e = fusionIntegrationAfeList[i], { id, fusionIntegrationResponse, isUAEEntity, submitterId, sendEmailToSubmitter, sendEmailToLeader, projectLeaderEmail } = _e, fusionPayload = __rest(_e, ["id", "fusionIntegrationResponse", "isUAEEntity", "submitterId", "sendEmailToSubmitter", "sendEmailToLeader", "projectLeaderEmail"]);
                    let data;
                    try {
                        if (isUAEEntity) {
                            data = yield this.fussionUaeApi.sendData(fusionPayload, isNotProd);
                        }
                        else {
                            data = yield this.fussionApi.sendData(fusionPayload, isNotProd);
                        }
                        if (!data) {
                            data = {
                                'Error Message': 'No response from fusion!'
                            };
                        }
                        returnResponse.push({
                            requestPayload: fusionPayload,
                            data,
                            isUAEEntity,
                            isNotProd,
                        });
                    }
                    catch (error) {
                        yield this.sendEmailToITSupportOnFailure(id, businessHierarchy.id, error);
                        data = {
                            'Error Message': (error === null || error === void 0 ? void 0 : error.message) || 'Something went wrong',
                        };
                    }
                    data.createdOn = new Date();
                    let newData = [];
                    if (fusionIntegrationResponse.length) {
                        fusionIntegrationResponse.forEach(fusionIntegration => {
                            newData.push(fusionIntegration);
                        });
                    }
                    newData.push(Object.assign(Object.assign({}, data), { fusionPayload }));
                    yield this.afeProposalRepository.updateAfeProposalByIdWOUser(id, {
                        fusionIntegrationStatus: data.Status === 'SUCCESS' ? 2 : 1,
                        retryFusionIntegration: this.sequlizeOperator.sequelizeLiteral('retry_fusion_integration + 1'),
                        fusionIntegrationResponse: newData,
                    });
                    if (data.Status === 'SUCCESS') {
                        successCount = successCount + 1;
                        if (sendEmailToSubmitter) {
                            let emailUser = { to: [submitterId] };
                            if (sendEmailToLeader && projectLeaderEmail) {
                                emailUser.cc = [projectLeaderEmail];
                            }
                            yield this.sharedNotificationService.sendNotificationForAfeProposal(id, id, enums_1.NOTIFICATION_ENTITY_TYPE.FUSION_NOTIFICATION, emailUser, 'AFE.EMAIL.FUSION.INTEGRATION.SUCCESSFUL');
                        }
                    }
                    else {
                        failedCount = failedCount + 1;
                        yield this.sendEmailToITSupportOnFailure(id, businessHierarchy.id, JSON.stringify({
                            requestPayload: fusionPayload,
                            fusionResponse: data
                        }));
                    }
                }
                return {
                    message: fusionIntegrationAfeList.length +
                        ' AFE data sent for integration. Total Success: ' +
                        successCount +
                        ' & Failed: ' +
                        failedCount +
                        '.',
                    data: returnResponse,
                };
            }
            else {
                return { message: 'No AFE available to send data for oracle fusion integration.' };
            }
        });
    }
    getUsersEmailByRoleAndEntityId(role, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const userEmails = [];
            const users = yield this.adminApiClient.getUsersByRoleOfAnEntity(role, entityId);
            const userIds = users === null || users === void 0 ? void 0 : users.map(user => user.user_name.toLowerCase());
            const userAdDetails = userIds.length
                ? yield this.mSGraphApiClient.getUsersDetails(userIds)
                : [];
            userEmails.push(...userAdDetails.map(user => user.mail));
            return userEmails;
        });
    }
    sendEmailToITSupportOnFailure(proposalId, groupEntityId, error) {
        return __awaiter(this, void 0, void 0, function* () {
            const itSupportEmails = yield this.getUsersEmailByRoleAndEntityId('FusionFailure', groupEntityId);
            yield this.sharedNotificationService.sendNotificationForAfeProposal(proposalId, proposalId, enums_1.NOTIFICATION_ENTITY_TYPE.FUSION_NOTIFICATION, { to: [...itSupportEmails] }, 'AFE.EMAIL.FUSION.INTEGRATION.FAILED', false, { error });
        });
    }
    getProjectDetail(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const { glCodes, additionalCurrencyAmount, afeRequestType, afeRequestTypeId, category, createdOn, supplementalData, projectTitleRefNumber, projectReferenceNumber, projectLeaderEmail, submitterId, workflowYear, entityCode, isNotProd, costCenterCode, supplimentaryAFENumbers, createdOnDateObj } = payload;
            let finalProjectDetail = [];
            glCodes.forEach(glCode => {
                const segments = glCode.code.split('-');
                const segmentData = {
                    Segment1: segments[0],
                    Segment2: segments[1],
                    Segment3: segments[2],
                    Segment4: segments[3],
                    Segment5: segments[4],
                    Segment6: segments[5],
                    Segment7: segments[6],
                    Segment8: segments[7],
                };
                const companyCode = segments[0];
                let projectEndDate;
                if (afeRequestTypeId !== 2) {
                    projectEndDate = new Date(createdOnDateObj.setFullYear(createdOnDateObj.getFullYear() + 2));
                }
                else {
                    if (entityCode === 'HOBU') {
                        projectEndDate = new Date((workflowYear + 1) + '-12-31T23:59:59.000Z');
                    }
                    else {
                        projectEndDate = new Date(workflowYear + '-12-31T23:59:59.000Z');
                    }
                }
                let devEmailId = null;
                if (isNotProd) {
                    devEmailId = '<EMAIL>';
                }
                let projectDetail = Object.assign(Object.assign({ ProjectCurrencyCode: additionalCurrencyAmount.currency, ProjectDescription: projectTitleRefNumber, ProjectStartDate: createdOn, ProjectEndDate: projectEndDate, ProjectName: projectTitleRefNumber, ProjectNumber: projectReferenceNumber, ProjectManagerEmail: devEmailId ? devEmailId : projectLeaderEmail, PersonEmail: devEmailId ? devEmailId : submitterId, IsSupplementary: category === enums_1.AFE_CATEGORY.SUPPLEMENTAL, ParentAFENumber: category === enums_1.AFE_CATEGORY.SUPPLEMENTAL ? supplementalData.parentAfeNo : null, CompanyCode: companyCode, AFEType: afeRequestType.title }, segmentData), { Attribute1: null, Attribute2: null, Attribute3: null, Attribute4: null, Attribute5: null, AFE_SupplimentaryAFENumbers: supplimentaryAFENumbers });
                if (afeRequestTypeId === 1 || afeRequestTypeId === 2) {
                    projectDetail = Object.assign(Object.assign({}, projectDetail), { originalCapexCostCenter: costCenterCode ? costCenterCode : null });
                }
                if (afeRequestTypeId === 3) {
                    projectDetail = Object.assign(Object.assign({}, projectDetail), { originalCapexCostCenter: '1101' });
                }
                finalProjectDetail.push(projectDetail);
            });
            return finalProjectDetail;
        });
    }
    getOpexProjectDetail(payload) {
        return __awaiter(this, void 0, void 0, function* () {
            const { glCodes, additionalCurrencyAmount, afeRequestType, afeRequestTypeId, category, createdOn, supplementalData, projectTitleRefNumber, projectReferenceNumber, projectLeaderEmail, submitterId, workflowYear, entityCode, isNotProd, costCenterCode, supplimentaryAFENumbers, createdOnDateObj } = payload;
            if (!glCodes.length) {
                return [];
            }
            let finalSegments = [];
            let projectEndDate;
            if (afeRequestTypeId !== 2) {
                projectEndDate = new Date(createdOnDateObj.setFullYear(createdOnDateObj.getFullYear() + 2));
            }
            else {
                if (entityCode === 'HOBU') {
                    projectEndDate = new Date((workflowYear + 1) + '-12-31T23:59:59.000Z');
                }
                else {
                    projectEndDate = new Date(workflowYear + '-12-31T23:59:59.000Z');
                }
            }
            let devEmailId = null;
            if (isNotProd) {
                devEmailId = '<EMAIL>';
            }
            let projectDetail = {
                ProjectCurrencyCode: additionalCurrencyAmount.currency,
                ProjectDescription: projectTitleRefNumber,
                ProjectStartDate: createdOn,
                ProjectEndDate: projectEndDate,
                ProjectName: projectTitleRefNumber,
                ProjectNumber: projectReferenceNumber,
                ProjectManagerEmail: devEmailId ? devEmailId : projectLeaderEmail,
                PersonEmail: devEmailId ? devEmailId : submitterId,
                IsSupplementary: category === enums_1.AFE_CATEGORY.SUPPLEMENTAL,
                ParentAFENumber: category === enums_1.AFE_CATEGORY.SUPPLEMENTAL ? supplementalData.parentAfeNo : null,
                AFEType: afeRequestType.title,
                Attribute1: null,
                Attribute2: null,
                Attribute3: null,
                Attribute4: null,
                Attribute5: null,
                AFE_SupplimentaryAFENumbers: supplimentaryAFENumbers
            };
            if (afeRequestTypeId === 3) {
                projectDetail = Object.assign(Object.assign({}, projectDetail), { originalCapexCostCenter: '1101' });
            }
            glCodes.forEach(glCode => {
                var _a;
                console.log('------------------- glCode S -----------------');
                console.dir(glCode, { depth: 10 });
                console.log('------------------- glCode E -----------------');
                const segments = glCode.code.split('-');
                const companyCode = segments[0];
                if (afeRequestTypeId === 1 || afeRequestTypeId === 2) {
                    projectDetail = Object.assign(Object.assign({}, projectDetail), { CompanyCode: companyCode, originalCapexCostCenter: costCenterCode ? costCenterCode : null });
                }
                finalSegments.push({
                    Segment1: segments[0],
                    Segment2: segments[1],
                    Segment3: segments[2],
                    Segment4: segments[3],
                    Segment5: segments[4],
                    Segment6: segments[5],
                    Segment7: segments[6],
                    Segment8: segments[7],
                    TaskAmount: (0, lodash_1.toNumber)(((_a = glCode === null || glCode === void 0 ? void 0 : glCode.additionalCurrencyAmount) === null || _a === void 0 ? void 0 : _a.amount) || (additionalCurrencyAmount === null || additionalCurrencyAmount === void 0 ? void 0 : additionalCurrencyAmount.amount) || 0),
                    TaskName: segments[2]
                });
            });
            return [Object.assign(Object.assign({}, projectDetail), { Tasks: finalSegments })];
        });
    }
};
OracleFusionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.AfeProposalRepository,
        repositories_1.AfeProposalAmountSplitRepository,
        clients_1.FusionApiClient,
        clients_1.FusionUaeApiClient,
        helpers_1.SequlizeOperator,
        services_1.FinanceAdminService,
        clients_1.AdminApiClient,
        repositories_2.CostCenterRepository,
        clients_1.MSGraphApiClient,
        services_2.SharedNotificationService])
], OracleFusionService);
exports.OracleFusionService = OracleFusionService;
//# sourceMappingURL=oracle-fusion.service.js.map