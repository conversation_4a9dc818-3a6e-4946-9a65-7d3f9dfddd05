"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const repositories_1 = require("../../afe-proposal/repositories");
const repositories_2 = require("../../finance/repositories");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const mappings_1 = require("../../shared/mappings");
const services_1 = require("../../shared/services");
const dtos_1 = require("../dtos");
const cost_center_report_reponse_dto_1 = require("../dtos/response/cost-center-report-reponse.dto");
const services_2 = require("../../business-entity/services");
const clients_1 = require("../../shared/clients");
const exceptions_1 = require("../../shared/exceptions");
const repositories_3 = require("../../workflow/repositories");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
let ReportService = class ReportService {
    constructor(excelSheetService, afeProposalRepository, analysisCodeRepository, costCenterRepository, naturalAccountNumberRepository, businessEntityService, permissionService, adminApiClient, workflowSettingRepository, sequlizeOperator) {
        this.excelSheetService = excelSheetService;
        this.afeProposalRepository = afeProposalRepository;
        this.analysisCodeRepository = analysisCodeRepository;
        this.costCenterRepository = costCenterRepository;
        this.naturalAccountNumberRepository = naturalAccountNumberRepository;
        this.businessEntityService = businessEntityService;
        this.permissionService = permissionService;
        this.adminApiClient = adminApiClient;
        this.workflowSettingRepository = workflowSettingRepository;
        this.sequlizeOperator = sequlizeOperator;
    }
    downloadSubmittedAfesReport(exportSubmittedAfeRequestDto, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const { filters, selectedColumns } = exportSubmittedAfeRequestDto;
            if ((_a = filters === null || filters === void 0 ? void 0 : filters.businessEntities) === null || _a === void 0 ? void 0 : _a.length) {
                filters.businessEntities = yield this.businessEntityService.getBusinessEntitiesChildIds(filters.businessEntities);
            }
            const SHEET_NAME = 'AFE List';
            const { user } = currentContext;
            const locations = yield this.permissionService.getAllLocationIdForGivenPermission(user.unique_name, enums_1.PERMISSIONS.AFE_VIEW);
            const extraPermissions = yield this.permissionService.getExtraPermissionObject(user.username);
            let reportData = yield this.afeProposalRepository.getSubmittedAfesReportData(filters, user.username, (locations === null || locations === void 0 ? void 0 : locations.length) ? locations : [], extraPermissions);
            console.log('Report pre >>>>>>>> ');
            console.dir(reportData, { depth: 10 });
            console.log('selectedColumns >>>>>>>> ');
            console.dir(selectedColumns, { depth: 10 });
            if (selectedColumns === null || selectedColumns === void 0 ? void 0 : selectedColumns.length) {
                reportData = reportData.map((item) => {
                    return Object.fromEntries(Object.entries(item).filter(([key]) => selectedColumns.includes(key)));
                });
            }
            console.log('Report post >>>>>>>> ');
            console.dir(reportData, { depth: 10 });
            const report = yield this.excelSheetService.createExcelSheet(reportData, SHEET_NAME);
            return { report, filename: SHEET_NAME };
        });
    }
    downloadAfeRequestTypeLimitsReport() {
        return __awaiter(this, void 0, void 0, function* () {
        });
    }
    downloadBuLevelWorkflowReport(filter, currentContext) {
        var _a, _b, _c, _d, _e, _f;
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId, workflowMasterSettingId } = filter;
            const { username } = currentContext.user;
            const masterWorkflowDetail = yield this.workflowSettingRepository.getMasterSettingWithoutStepById(workflowMasterSettingId);
            if (!masterWorkflowDetail) {
                throw new exceptions_1.HttpException('Workflow setting is not available.', enums_1.HttpStatus.NOT_FOUND);
            }
            if (masterWorkflowDetail.parentId) {
                throw new exceptions_1.HttpException('The master workflow can only be selected.', enums_1.HttpStatus.NOT_FOUND);
            }
            const buChildList = yield this.adminApiClient.getAllBusinessHierarchyByUserAndPermission(username, enums_1.PERMISSIONS.AFE_REPORTS, entityId, null);
            if (!buChildList) {
                throw new exceptions_1.HttpException('No Business Unit available for selected entity.', enums_1.HttpStatus.NOT_FOUND);
            }
            let businessUnitList = [];
            let isBuSelected = false;
            if ((_a = buChildList === null || buChildList === void 0 ? void 0 : buChildList.children) === null || _a === void 0 ? void 0 : _a.length) {
                buChildList.children.forEach((childrenDetail) => {
                    var _a;
                    if ((_a = childrenDetail === null || childrenDetail === void 0 ? void 0 : childrenDetail.children) === null || _a === void 0 ? void 0 : _a.length) {
                        throw new exceptions_1.HttpException('It is not permitted to select an entity other than a region or a business unit.', enums_1.HttpStatus.BAD_REQUEST);
                    }
                });
                businessUnitList = buChildList.children;
            }
            else {
                isBuSelected = true;
                businessUnitList.push(buChildList);
            }
            const buEntityIds = businessUnitList.map(businessUnit => (0, lodash_1.toNumber)(businessUnit.id));
            let businessUnitWorkflows = yield this.workflowSettingRepository.getMasterFlowSettingWithStepsByCondition({
                entityId: this.sequlizeOperator.inOperator(buEntityIds),
                parentId: workflowMasterSettingId
            });
            let parentWorkflow = null;
            let parentEntityIds = yield this.adminApiClient.getParentIdsOfEntity(entityId);
            if (businessUnitWorkflows.length !== buEntityIds.length) {
                if (isBuSelected) {
                    parentEntityIds = parentEntityIds.filter(parentId => ((0, lodash_1.toNumber)(entityId) !== (0, lodash_1.toNumber)(parentId)));
                }
                parentEntityIds = parentEntityIds.reverse();
                const settingCondition = this.sequlizeOperator.orOperator({
                    id: workflowMasterSettingId,
                    parentId: workflowMasterSettingId
                });
                const parentWorkflows = yield this.workflowSettingRepository.getMasterFlowSettingWithStepsByCondition(Object.assign({ entityId: this.sequlizeOperator.inOperator(parentEntityIds) }, settingCondition));
                if (parentWorkflows === null || parentWorkflows === void 0 ? void 0 : parentWorkflows.length) {
                    const immediateParentWorkflowEntityId = parentEntityIds.find((parentEntityId) => {
                        return parentWorkflows.find((parentWorkflowDetail) => {
                            return (0, lodash_1.toNumber)(parentWorkflowDetail.entityId) === parentEntityId;
                        });
                    });
                    if (immediateParentWorkflowEntityId) {
                        parentWorkflow = parentWorkflows.find((parentWorkflowDetail) => {
                            return (0, lodash_1.toNumber)(parentWorkflowDetail.entityId) === immediateParentWorkflowEntityId;
                        });
                    }
                    if (parentWorkflow) {
                        businessUnitList.forEach((businessUnitDetail) => {
                            const isEntityOverriddenAvailable = businessUnitWorkflows.find((businessUnitWorkflow) => {
                                return ((0, lodash_1.toNumber)(businessUnitWorkflow.entityId) === (0, lodash_1.toNumber)(businessUnitDetail.id));
                            });
                            if (!isEntityOverriddenAvailable) {
                                businessUnitWorkflows.push(Object.assign(Object.assign({}, parentWorkflow), { entityCode: (businessUnitDetail === null || businessUnitDetail === void 0 ? void 0 : businessUnitDetail.code) || '', entityTitle: (businessUnitDetail === null || businessUnitDetail === void 0 ? void 0 : businessUnitDetail.full_name) || (businessUnitDetail === null || businessUnitDetail === void 0 ? void 0 : businessUnitDetail.short_name) || '', entityType: (businessUnitDetail === null || businessUnitDetail === void 0 ? void 0 : businessUnitDetail.entity_type) || '', entityId: (0, lodash_1.toNumber)(businessUnitDetail.id), isParentWorkflow: true, immediateParentEntity: {
                                        entityCode: parentWorkflow.entityCode,
                                        entityTitle: parentWorkflow.entityTitle,
                                        entityId: parentWorkflow.entityId,
                                        entityType: parentWorkflow.entityType
                                    } }));
                            }
                        });
                    }
                }
            }
            if (!businessUnitWorkflows.length) {
                throw new exceptions_1.HttpException('No workflow available for selected entity and setting.', enums_1.HttpStatus.NOT_FOUND);
            }
            businessUnitWorkflows = businessUnitWorkflows.map((businessUnitWorkflow) => {
                const buEntityParentIds = parentEntityIds.filter(parentId => ((0, lodash_1.toNumber)(businessUnitWorkflow.entityId) !== (0, lodash_1.toNumber)(parentId)));
                return Object.assign(Object.assign({}, businessUnitWorkflow), { buEntityParentIds });
            });
            let uniqueRoles = [];
            let allEntityIdWithCostCenterStep = [];
            businessUnitWorkflows.forEach((businessUnitWorkflow) => {
                businessUnitWorkflow.workflowMasterStep.forEach((workflowMasterStep) => {
                    if (workflowMasterStep.associateType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE) {
                        uniqueRoles.push(workflowMasterStep.associateRole);
                    }
                    if (workflowMasterStep.associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER) {
                        allEntityIdWithCostCenterStep.push(businessUnitWorkflow.entityId);
                    }
                });
            });
            let allRoleUsers = [];
            if (uniqueRoles.length) {
                uniqueRoles = Array.from(new Set(uniqueRoles));
                const roleUserListsPromises = uniqueRoles.map(role => this.adminApiClient.getRoleUserListByRoleName(role));
                const roleUsersArrays = yield Promise.all(roleUserListsPromises);
                allRoleUsers = roleUsersArrays.flat();
            }
            let allWorkflowWithUserDetail = businessUnitWorkflows.map((businessUnitWorkflow) => {
                const workflowSteps = businessUnitWorkflow.workflowMasterStep.map((workflowMasterStep) => {
                    if (workflowMasterStep.associateType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE) {
                        const allEntityIds = [businessUnitWorkflow.entityId, ...businessUnitWorkflow.buEntityParentIds];
                        const roleUsers = allRoleUsers.filter((roleUserDetail) => allEntityIds.includes((0, lodash_1.toNumber)(roleUserDetail.business_entity_id)) && roleUserDetail.role === workflowMasterStep.associateRole);
                        return Object.assign({ roleUsers }, workflowMasterStep);
                    }
                    else {
                        return workflowMasterStep;
                    }
                });
                return Object.assign(Object.assign({}, businessUnitWorkflow), { workflowMasterStep: workflowSteps });
            });
            const FILE_NAME = (((_b = masterWorkflowDetail === null || masterWorkflowDetail === void 0 ? void 0 : masterWorkflowDetail.requestType) === null || _b === void 0 ? void 0 : _b.id) ? (mappings_1.AFE_REQUEST_TYPE_ID_WITH_FILE_NAME_MAPPING[masterWorkflowDetail.requestType.id] + ' - ') : '') + ((masterWorkflowDetail === null || masterWorkflowDetail === void 0 ? void 0 : masterWorkflowDetail.budgetType) ? mappings_1.BUDGET_TYPE_NAME_MAPPING[masterWorkflowDetail.budgetType] + ' - ' : '') + (((_c = masterWorkflowDetail === null || masterWorkflowDetail === void 0 ? void 0 : masterWorkflowDetail.projectComponent) === null || _c === void 0 ? void 0 : _c.title) ? ((_d = masterWorkflowDetail === null || masterWorkflowDetail === void 0 ? void 0 : masterWorkflowDetail.projectComponent) === null || _d === void 0 ? void 0 : _d.title) + ' - ' : '') + buChildList.full_name + ' (' + buChildList.entity_type + ' Level)';
            const workflowColumns = [
                'Sr. No.',
                'Business Unit Code',
                'Business Unit Name',
                'Step Title',
                'Step Role',
                'Step Type',
                'Current Approvers',
                'Rules',
                'Single Limit'
            ];
            if (masterWorkflowDetail.isAggregateLimitApplicable) {
                workflowColumns.push('Aggregate Limit');
            }
            let headers = [];
            headers.push('Report For - ' + ((_e = masterWorkflowDetail.requestType) === null || _e === void 0 ? void 0 : _e.title) + ' ' + ((masterWorkflowDetail === null || masterWorkflowDetail === void 0 ? void 0 : masterWorkflowDetail.budgetType) ? mappings_1.BUDGET_TYPE_NAME_MAPPING[masterWorkflowDetail.budgetType] : '') + ' ' + (((_f = masterWorkflowDetail === null || masterWorkflowDetail === void 0 ? void 0 : masterWorkflowDetail.projectComponent) === null || _f === void 0 ? void 0 : _f.title) || ' '));
            headers.push('\nEntity Selected - ' + buChildList.full_name + ' (' + buChildList.entity_type + ')');
            headers.push('\nWorkflow Year - ' + masterWorkflowDetail.year);
            headers.push('\nReport Date - ' + (0, helpers_1.currentDateInDDMMYYYY)());
            let allCostCenters = [];
            if (allEntityIdWithCostCenterStep && allEntityIdWithCostCenterStep.length) {
                const uniqueEnittyWithCostCenter = Array.from(new Set(allEntityIdWithCostCenterStep));
                allCostCenters = yield this.costCenterRepository.getCostCentersWithCompanyCodeByEntityIds(uniqueEnittyWithCostCenter);
            }
            const report = yield this.excelSheetService.downloadBuLevelWorkflow(workflowColumns, allWorkflowWithUserDetail, allCostCenters, headers);
            return { report, filename: FILE_NAME };
        });
    }
    downloadAnalysisCode(companyCodeId) {
        return __awaiter(this, void 0, void 0, function* () {
            const SHEET_NAME = 'Analysis Code';
            const FILE_NAME = 'analysis_code_' + (companyCodeId ? companyCodeId : 'format');
            const analysisCodes = yield this.analysisCodeRepository.getAllAnalysisCodesListByCompanyCodeId(companyCodeId);
            const analysisCodeList = analysisCodes.map(d => (0, helpers_1.instanceToPlain)(new dtos_1.AnalysisCodeReportResponseDto(d), { excludeExtraneousValues: true }));
            let finalList = [];
            analysisCodeList.forEach((analysisCode) => {
                let requestTypes = (analysisCode['Request Type'].length == 2) ? 'Both' : analysisCode['Request Type'].map((requestId) => {
                    return mappings_1.AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[requestId];
                }).join(',');
                finalList.push(Object.assign(Object.assign({}, analysisCode), { 'Request Type': requestTypes, 'Is Deleted?': false }));
            });
            const dataValidations = [{
                    column: 'C',
                    dataList: [mappings_1.AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[1], mappings_1.AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[2], 'Both'],
                    allowBlank: false,
                    lastRow: 10000
                }, {
                    column: 'D',
                    dataList: [true, false],
                    allowBlank: false,
                    lastRow: 10000
                }];
            if (!finalList.length) {
                finalList.push({
                    'Title': '',
                    'Analysis Code': '',
                    'Request Type': '',
                    'Is Deleted?': ''
                });
            }
            const report = yield this.excelSheetService.createExcelSheet(finalList, SHEET_NAME, dataValidations);
            return { report, filename: FILE_NAME };
        });
    }
    downloadCostCenters(companyCodeId) {
        return __awaiter(this, void 0, void 0, function* () {
            const SHEET_NAME = 'Cost Center';
            const FILE_NAME = 'cost_center_' + (companyCodeId ? companyCodeId : 'format');
            const costCenters = yield this.costCenterRepository.getAllCostCentersListByCompanyCodeId(companyCodeId);
            const costCentersList = costCenters.map((costCenter) => {
                var _a;
                const costCenterDetail = Object.assign(Object.assign({}, costCenter), { departmentHead: (costCenter.departmentHead.userType === enums_1.AD_USER_TYPE.GUEST) ? costCenter.departmentHead.mail.toLowerCase() : costCenter.departmentHead.userPrincipalName.toLowerCase(), sectionHead: ((_a = costCenter === null || costCenter === void 0 ? void 0 : costCenter.sectionHead) === null || _a === void 0 ? void 0 : _a.length) ? costCenter === null || costCenter === void 0 ? void 0 : costCenter.sectionHead.map((entry) => `${entry.title}#${(entry.user.userType === enums_1.AD_USER_TYPE.GUEST) ? entry.user.mail.toLowerCase() : entry.user.userPrincipalName.toLowerCase()}`).join(";") : '' });
                return (0, helpers_1.instanceToPlain)(new cost_center_report_reponse_dto_1.CostCenterReportResponseDto(costCenterDetail), { excludeExtraneousValues: true });
            });
            let finalList = [];
            costCentersList.forEach((costCenter) => {
                finalList.push(Object.assign(Object.assign({}, costCenter), { 'Is Deleted?': false }));
            });
            const dataValidations = [{
                    column: 'C',
                    dataList: [true, false],
                    allowBlank: false,
                    lastRow: 10000
                }, {
                    column: 'F',
                    dataList: [true, false],
                    allowBlank: false,
                    lastRow: 10000
                }];
            if (!finalList.length) {
                finalList.push({
                    'Cost Center Code': '',
                    'Cost Center Name': '',
                    'Is Operating?': '',
                    'Department Head Email Id': '',
                    'Sections': '',
                    'Is Deleted?': ''
                });
            }
            const report = yield this.excelSheetService.createExcelSheet(finalList, SHEET_NAME, dataValidations);
            return { report, filename: FILE_NAME };
        });
    }
    downloadNaturalAccounts(companyCodeId) {
        return __awaiter(this, void 0, void 0, function* () {
            const SHEET_NAME = 'Natural Account';
            const FILE_NAME = 'natural_accounts_' + (companyCodeId ? companyCodeId : 'format');
            const naturalAccounts = yield this.naturalAccountNumberRepository.getAllNaturalAccountNumbersListByCompanyCodeId(companyCodeId);
            const naturalAccountsList = naturalAccounts.map((naturalAccount) => {
                const naturalAccountDetail = Object.assign({}, naturalAccount);
                return (0, helpers_1.instanceToPlain)(new dtos_1.NaturalAccountReportResponseDto(naturalAccountDetail), { excludeExtraneousValues: true });
            });
            let finalList = [];
            naturalAccountsList.forEach((naturalAccount) => {
                let requestTypes = (naturalAccount['Request Type'].length == 2) ? 'Both' : naturalAccount['Request Type'].map((requestId) => {
                    return mappings_1.AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[requestId];
                }).join(',');
                finalList.push(Object.assign(Object.assign({}, naturalAccount), { 'Request Type': requestTypes, 'Is Deleted?': false }));
            });
            const dataValidations = [{
                    column: 'C',
                    dataList: [mappings_1.AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[1], mappings_1.AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[2], 'Both'],
                    allowBlank: false,
                    lastRow: 10000
                }, {
                    column: 'D',
                    dataList: [true, false],
                    allowBlank: false,
                    lastRow: 10000
                }];
            if (!finalList.length) {
                finalList.push({
                    'Title': '',
                    'Account Number': '',
                    'Request Type': '',
                    'Is Deleted?': ''
                });
            }
            const report = yield this.excelSheetService.createExcelSheet(finalList, SHEET_NAME, dataValidations);
            return { report, filename: FILE_NAME };
        });
    }
};
ReportService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [services_1.ExcelSheetService,
        repositories_1.AfeProposalRepository,
        repositories_2.AnalysisCodeRepository,
        repositories_2.CostCenterRepository,
        repositories_2.NaturalAccountNumberRepository,
        services_2.BusinessEntityService,
        services_1.SharedPermissionService,
        clients_1.AdminApiClient,
        repositories_3.WorkflowMasterSettingRepository,
        helpers_1.SequlizeOperator])
], ReportService);
exports.ReportService = ReportService;
//# sourceMappingURL=report.service.js.map