"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowMasterStepService = void 0;
const common_1 = require("@nestjs/common");
const class_transformer_1 = require("class-transformer");
const lodash_1 = __importStar(require("lodash"));
const repositories_1 = require("../../afe-proposal/repositories");
const business_entity_level_dto_1 = require("../../business-entity/dtos/response/business-entity-level.dto");
const pagination_1 = require("../../core/pagination");
const services_1 = require("../../finance/services");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const dtos_1 = require("../dtos");
const get_aggregate_limit_balance_dto_1 = require("../dtos/response/get-aggregate-limit-balance.dto");
const get_child_shared_limit_response_dto_1 = require("../dtos/response/get-child-shared-limit-response.dto");
const get_exception_steps_response_dto_1 = require("../dtos/response/get-exception-steps-response.dto");
const get_history_response_dto_1 = require("../dtos/response/get-history-response.dto");
const get_role_based_steps_response_dto_1 = require("../dtos/response/get-role-based-steps-response.dto");
const repositories_2 = require("../repositories");
const types_1 = require("../types");
const workflow_shared_child_limit_service_1 = require("./workflow-shared-child-limit.service");
let WorkflowMasterStepService = class WorkflowMasterStepService {
    constructor(workflowMasterSettingRepository, workflowMasterStepRepository, adminApiClient, limitDeductionRepository, workflowSharedChildLimitRepository, afeProposalLimitDeductionRepository, workflowSharedChildLimitService, workflowSharedBucketLimitRepository, databaseHelper, sequlizeOperator, financeService, historyApiClient) {
        this.workflowMasterSettingRepository = workflowMasterSettingRepository;
        this.workflowMasterStepRepository = workflowMasterStepRepository;
        this.adminApiClient = adminApiClient;
        this.limitDeductionRepository = limitDeductionRepository;
        this.workflowSharedChildLimitRepository = workflowSharedChildLimitRepository;
        this.afeProposalLimitDeductionRepository = afeProposalLimitDeductionRepository;
        this.workflowSharedChildLimitService = workflowSharedChildLimitService;
        this.workflowSharedBucketLimitRepository = workflowSharedBucketLimitRepository;
        this.databaseHelper = databaseHelper;
        this.sequlizeOperator = sequlizeOperator;
        this.financeService = financeService;
        this.historyApiClient = historyApiClient;
    }
    addNewWorkflowStep(newMasterStepRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            let workflowDetail;
            let isUnpublishedVersion = false;
            if (!newMasterStepRequestDto.hasOwnProperty('unpublishedVersion') ||
                (newMasterStepRequestDto.hasOwnProperty('unpublishedVersion') && !newMasterStepRequestDto.unpublishedVersion)) {
                isUnpublishedVersion = false;
            }
            else {
                isUnpublishedVersion = true;
            }
            if (!isUnpublishedVersion) {
                workflowDetail = yield this.workflowMasterSettingRepository.getWorkflowSettingById(newMasterStepRequestDto.workflowMasterSettingId);
            }
            else {
                workflowDetail = yield this.workflowMasterSettingRepository.getUnpublishedVersionWorkflowDetailById(newMasterStepRequestDto.workflowMasterSettingId);
            }
            if (workflowDetail) {
                const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(dtos_1.WorkflowDetailResponseDTO, workflowDetail);
                if (!isUnpublishedVersion) {
                    if (masterSettingDetail.published) {
                        throw new exceptions_1.HttpException('Unable to add step as workflow is already published.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                if (masterSettingDetail.parentId) {
                    newMasterStepRequestDto.singleLimit = 0;
                    newMasterStepRequestDto.aggregateLimit = 0;
                    newMasterStepRequestDto.lengthOfCommitment = null;
                    newMasterStepRequestDto.canShareLimitToChild = false;
                    newMasterStepRequestDto.skipLimitRuleId = null;
                }
                newMasterStepRequestDto = yield this.validateAndUpdatePayload(newMasterStepRequestDto, masterSettingDetail);
                let existingSteps;
                if (!isUnpublishedVersion) {
                    existingSteps = yield this.workflowMasterStepRepository.getOnlyWorkflowStepsBySettingId(newMasterStepRequestDto.workflowMasterSettingId);
                }
                else {
                    existingSteps = yield this.workflowMasterStepRepository.getUnpublishedVersionSteps(newMasterStepRequestDto.workflowMasterSettingId, ((0, lodash_1.toNumber)(masterSettingDetail.unpublishedVersion)));
                }
                const returnPayload = Object.assign(Object.assign({}, newMasterStepRequestDto), { inherited: false, approvalSequence: existingSteps ? (existingSteps.length + 1) : 1, version: isUnpublishedVersion ? ((0, lodash_1.toNumber)(masterSettingDetail.unpublishedVersion)) : 1, active: isUnpublishedVersion ? false : true, includeBelowSteps: newMasterStepRequestDto.isMandatory ? newMasterStepRequestDto.includeBelowSteps : false });
                yield this.validateLimitWithParentChild(newMasterStepRequestDto, returnPayload, existingSteps, isUnpublishedVersion);
                const returnData = yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    const result = yield this.workflowMasterStepRepository.addNewWorkflowStep(returnPayload, currentContext);
                    const returnData = (0, helpers_1.singleObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, result);
                    yield this.workflowMasterStepRepository.updateWorkflowStep(returnData.id, { workflowMasterStepId: returnData.id }, currentContext, (isUnpublishedVersion ? {
                        includeDeleted: false,
                        includeInactive: true,
                        throwException: true,
                    } : {
                        includeDeleted: false,
                        includeInactive: false,
                        throwException: true,
                    }));
                    return Object.assign(Object.assign({}, returnData), { workflowMasterStepId: returnData.id });
                }));
                let historyRequest = [];
                historyRequest.push({
                    created_by: currentContext.user.username,
                    entity_id: returnData.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                    comments: returnData.title + ' step has been added' + ((returnData.version > 1) ? (' for version ' + returnData.version + ' workflow setting') : '') + '.',
                    additional_info: {
                        stepDetail: returnData
                    },
                }, {
                    created_by: currentContext.user.username,
                    entity_id: newMasterStepRequestDto.workflowMasterSettingId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                    comments: returnData.title + ' step has been added.',
                    additional_info: {
                        stepDetail: returnData
                    },
                });
                yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                return returnData;
            }
            else {
                if (!isUnpublishedVersion) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
                }
                else {
                    throw new exceptions_1.HttpException('Unpublished workflow setting version is unavailable.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
        });
    }
    copyStepToOverridden(copyStepRequestDto, currentContext) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            let { copyToAllOverridens, position, referenceStep, selectedOveriddenWorkflows = [], stepId } = copyStepRequestDto;
            const stepDetailToCopy = yield this.workflowMasterStepRepository.getUnpublishedVersionStepDetailById(stepId);
            if (!stepDetailToCopy) {
                throw new exceptions_1.HttpException('Step not found to copy.', enums_1.HttpStatus.NOT_FOUND);
            }
            if ((stepDetailToCopy === null || stepDetailToCopy === void 0 ? void 0 : stepDetailToCopy.version) !== ((_a = stepDetailToCopy === null || stepDetailToCopy === void 0 ? void 0 : stepDetailToCopy.workflowMasterSetting) === null || _a === void 0 ? void 0 : _a.unpublishedVersion)) {
                throw new exceptions_1.HttpException('Invalid step request.', enums_1.HttpStatus.NOT_FOUND);
            }
            const workflowMasterSettingId = ((_b = stepDetailToCopy === null || stepDetailToCopy === void 0 ? void 0 : stepDetailToCopy.workflowMasterSetting) === null || _b === void 0 ? void 0 : _b.parentId) ? stepDetailToCopy.workflowMasterSetting.parentId : stepDetailToCopy.workflowMasterSetting.id;
            selectedOveriddenWorkflows = selectedOveriddenWorkflows.filter((selectedOveriddenWorkflow) => {
                return ((0, lodash_1.toNumber)(selectedOveriddenWorkflow) !== (0, lodash_1.toNumber)(stepDetailToCopy.workflowMasterSetting.id));
            });
            if ((copyToAllOverridens && (selectedOveriddenWorkflows === null || selectedOveriddenWorkflows === void 0 ? void 0 : selectedOveriddenWorkflows.length)) || (!copyToAllOverridens && !(selectedOveriddenWorkflows === null || selectedOveriddenWorkflows === void 0 ? void 0 : selectedOveriddenWorkflows.length))) {
                throw new exceptions_1.HttpException('Invalid request.', enums_1.HttpStatus.BAD_REQUEST);
            }
            let overriddenCondition = null;
            if (copyToAllOverridens) {
                overriddenCondition = {
                    parentId: workflowMasterSettingId,
                    unpublishedVersion: this.sequlizeOperator.notEqualOperator(null),
                    id: this.sequlizeOperator.notEqualOperator(stepDetailToCopy.workflowMasterSetting.id)
                };
            }
            else {
                overriddenCondition = {
                    parentId: workflowMasterSettingId,
                    unpublishedVersion: this.sequlizeOperator.notEqualOperator(null),
                    id: this.sequlizeOperator.inOperator(selectedOveriddenWorkflows)
                };
            }
            const overriddenWorkflows = yield this.workflowMasterSettingRepository.getUnpublishedVersionWorkflowsByCondition(overriddenCondition);
            let stepAdditionDetails = [];
            let overiddenWorkflowIds = [];
            overriddenWorkflows.forEach((overriddenWorkflow) => {
                const isRoleAvailable = overriddenWorkflow.workflowMasterStep.find((workflowMasterStep) => {
                    if ((referenceStep === 'SECTION_HEAD' || referenceStep === 'DEPARTMENT_HEAD')) {
                        return (workflowMasterStep.associatedColumn === referenceStep);
                    }
                    else {
                        return (workflowMasterStep.associateRole === referenceStep);
                    }
                });
                if (isRoleAvailable) {
                    overiddenWorkflowIds.push(overriddenWorkflow.id);
                    stepAdditionDetails.push({
                        title: stepDetailToCopy.title,
                        workflowMasterSettingId: overriddenWorkflow.id,
                        associateLevel: stepDetailToCopy.associateLevel,
                        associateRole: stepDetailToCopy.associateRole,
                        associateType: stepDetailToCopy.associateType,
                        associatedColumn: stepDetailToCopy.associatedColumn,
                        singleLimit: stepDetailToCopy.singleLimit,
                        aggregateLimit: stepDetailToCopy.aggregateLimit,
                        lengthOfCommitment: stepDetailToCopy.lengthOfCommitment,
                        canWorkflowStart: stepDetailToCopy.canWorkflowStart,
                        canRemovedAtChildLevel: stepDetailToCopy.canRemovedAtChildLevel,
                        canShareLimitToChild: stepDetailToCopy.canShareLimitToChild,
                        sharedLimitMasterStepChildId: stepDetailToCopy.sharedLimitMasterStepChildId,
                        approvalSequence: 999,
                        ruleId: stepDetailToCopy.ruleId,
                        createdBy: currentContext.user.username,
                        updatedBy: currentContext.user.username,
                        deleted: stepDetailToCopy.deleted,
                        active: stepDetailToCopy.active,
                        associatedUser: stepDetailToCopy.associatedUser,
                        inherited: true,
                        workflowMasterStepId: stepDetailToCopy.workflowMasterStepId,
                        skipLimitRuleId: stepDetailToCopy.skipLimitRuleId,
                        parallelIdentifier: stepDetailToCopy.parallelIdentifier,
                        isMandatory: stepDetailToCopy.isMandatory,
                        includeBelowSteps: stepDetailToCopy.includeBelowSteps,
                        version: overriddenWorkflow.unpublishedVersion,
                        removeChildLimit: stepDetailToCopy.removeChildLimit
                    });
                }
            });
            const updateOtherApprovalSeqIds = [];
            const updateCopyStepVersion = [];
            overriddenWorkflows.forEach((workflowSetting) => {
                const isRoleAvailable = workflowSetting.workflowMasterStep.find((workflowMasterStep) => {
                    if ((referenceStep === 'SECTION_HEAD' || referenceStep === 'DEPARTMENT_HEAD')) {
                        return (workflowMasterStep.associatedColumn === referenceStep);
                    }
                    else {
                        return (workflowMasterStep.associateRole === referenceStep);
                    }
                });
                if (isRoleAvailable) {
                    const referenceStepSeq = isRoleAvailable.approvalSequence;
                    if (position === dtos_1.Position.AFTER) {
                        updateCopyStepVersion.push({
                            workflowMasterSettingId: workflowSetting.id,
                            associateRole: stepDetailToCopy.associateRole,
                            version: (0, lodash_1.toNumber)(workflowSetting.unpublishedVersion),
                            approvalSequence: (referenceStepSeq + 1),
                        });
                    }
                    if (position === dtos_1.Position.BEFORE) {
                        updateCopyStepVersion.push({
                            workflowMasterSettingId: workflowSetting.id,
                            associateRole: stepDetailToCopy.associateRole,
                            version: (0, lodash_1.toNumber)(workflowSetting.unpublishedVersion),
                            approvalSequence: (referenceStepSeq),
                        });
                    }
                    workflowSetting.workflowMasterStep.forEach((workflowMasterStep) => {
                        if ((position === dtos_1.Position.AFTER) && ((0, lodash_1.toNumber)(workflowMasterStep.approvalSequence) > (0, lodash_1.toNumber)(referenceStepSeq))) {
                            updateOtherApprovalSeqIds.push(workflowMasterStep.id);
                        }
                        if ((position === dtos_1.Position.BEFORE) && ((0, lodash_1.toNumber)(workflowMasterStep.approvalSequence) >= (0, lodash_1.toNumber)(referenceStepSeq))) {
                            updateOtherApprovalSeqIds.push(workflowMasterStep.id);
                        }
                    });
                }
            });
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.workflowMasterStepRepository.addBulkWorkflowSteps(stepAdditionDetails, currentContext);
                let params = {
                    includeDeleted: false,
                    includeInactive: true,
                    throwException: true,
                };
                yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                    id: this.sequlizeOperator.inOperator(updateOtherApprovalSeqIds),
                    active: false,
                    deleted: false
                }, {
                    approvalSequence: this.workflowMasterStepRepository.sequelizeLiteral('approval_sequence + 1'),
                }, currentContext, params);
                yield Promise.all(updateCopyStepVersion.map((copyStepDetail) => __awaiter(this, void 0, void 0, function* () {
                    yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                        workflowMasterSettingId: copyStepDetail.workflowMasterSettingId,
                        approvalSequence: 999
                    }, {
                        approvalSequence: this.workflowMasterStepRepository.sequelizeLiteral(copyStepDetail.approvalSequence)
                    }, currentContext, params);
                })));
                return { message: 'Step has been copied successfully.' };
            }));
        });
    }
    deleteStepFromOverridden(deleteStepRequestDto, currentContext) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            let { copyToAllOverridens, selectedOveriddenWorkflows = [], stepId } = deleteStepRequestDto;
            const stepDetailToDelete = yield this.workflowMasterStepRepository.getUnpublishedVersionStepDetailById(stepId);
            if (!stepDetailToDelete) {
                throw new exceptions_1.HttpException('Step not found to copy.', enums_1.HttpStatus.NOT_FOUND);
            }
            if ((stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.version) !== ((_a = stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.workflowMasterSetting) === null || _a === void 0 ? void 0 : _a.unpublishedVersion)) {
                throw new exceptions_1.HttpException('Invalid step request.', enums_1.HttpStatus.NOT_FOUND);
            }
            const workflowMasterSettingId = ((_b = stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.workflowMasterSetting) === null || _b === void 0 ? void 0 : _b.parentId) ? stepDetailToDelete.workflowMasterSetting.parentId : stepDetailToDelete.workflowMasterSetting.id;
            if ((copyToAllOverridens && (selectedOveriddenWorkflows === null || selectedOveriddenWorkflows === void 0 ? void 0 : selectedOveriddenWorkflows.length)) || (!copyToAllOverridens && !(selectedOveriddenWorkflows === null || selectedOveriddenWorkflows === void 0 ? void 0 : selectedOveriddenWorkflows.length))) {
                throw new exceptions_1.HttpException('Invalid request.', enums_1.HttpStatus.BAD_REQUEST);
            }
            let overriddenCondition = null;
            if (copyToAllOverridens) {
                overriddenCondition = {
                    parentId: workflowMasterSettingId,
                    unpublishedVersion: this.sequlizeOperator.notEqualOperator(null),
                };
            }
            else {
                overriddenCondition = {
                    parentId: workflowMasterSettingId,
                    unpublishedVersion: this.sequlizeOperator.notEqualOperator(null),
                    id: this.sequlizeOperator.inOperator(selectedOveriddenWorkflows)
                };
            }
            const overriddenWorkflows = yield this.workflowMasterSettingRepository.getUnpublishedVersionWorkflowsByCondition(overriddenCondition);
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                let overiddenWorkflowIds = [];
                let allStepsToDelete = [];
                let stepsIdsToDelete = [];
                overriddenWorkflows.forEach((overriddenWorkflow) => {
                    const isRoleAvailable = overriddenWorkflow.workflowMasterStep.find((workflowMasterStep) => {
                        var _a, _b, _c, _d, _e, _f;
                        if ((workflowMasterStep === null || workflowMasterStep === void 0 ? void 0 : workflowMasterStep.canRemovedAtChildLevel) && (workflowMasterStep === null || workflowMasterStep === void 0 ? void 0 : workflowMasterStep.inherited)) {
                            if ((stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.associateType) === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER) {
                                return (((_a = workflowMasterStep === null || workflowMasterStep === void 0 ? void 0 : workflowMasterStep.associatedColumn) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === ((_b = stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.associatedColumn) === null || _b === void 0 ? void 0 : _b.toLowerCase()));
                            }
                            else if ((stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.associateType) === associated_type_enum_1.ASSOCIATED_TYPE.USER) {
                                return (((_c = workflowMasterStep === null || workflowMasterStep === void 0 ? void 0 : workflowMasterStep.associatedUser) === null || _c === void 0 ? void 0 : _c.toLowerCase()) === ((_d = stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.associatedUser) === null || _d === void 0 ? void 0 : _d.toLowerCase()));
                            }
                            else {
                                return (((_e = workflowMasterStep === null || workflowMasterStep === void 0 ? void 0 : workflowMasterStep.associateRole) === null || _e === void 0 ? void 0 : _e.toLowerCase()) === ((_f = stepDetailToDelete === null || stepDetailToDelete === void 0 ? void 0 : stepDetailToDelete.associateRole) === null || _f === void 0 ? void 0 : _f.toLowerCase()));
                            }
                        }
                    });
                    if (isRoleAvailable) {
                        allStepsToDelete.push({
                            workflowMasterSettingId: isRoleAvailable.workflowMasterSettingId,
                            approvalSequence: isRoleAvailable.approvalSequence,
                            id: isRoleAvailable.id
                        });
                        stepsIdsToDelete.push(isRoleAvailable.id);
                        overiddenWorkflowIds.push(overriddenWorkflow.id);
                    }
                });
                if (!(overiddenWorkflowIds === null || overiddenWorkflowIds === void 0 ? void 0 : overiddenWorkflowIds.length)) {
                    throw new exceptions_1.HttpException('There is no overridden workflow to remove the step where it is inherited and we are allowed to remove it from the child.', enums_1.HttpStatus.NOT_FOUND);
                }
                let params = {
                    includeDeleted: false,
                    includeInactive: true,
                    throwException: true,
                };
                let condition = [];
                allStepsToDelete.forEach((workflowStep) => {
                    condition.push({
                        workflow_master_setting_id: workflowStep.workflowMasterSettingId,
                        approval_sequence: this.sequlizeOperator.greaterThanOperator(workflowStep.approvalSequence),
                        active: false,
                        deleted: false
                    });
                });
                if (condition.length) {
                    condition = this.sequlizeOperator.orOperator(condition);
                }
                if (condition) {
                    yield this.workflowMasterStepRepository.updateWorkflowByCondition(condition, {
                        approvalSequence: this.workflowMasterStepRepository.sequelizeLiteral('approval_sequence - 1')
                    }, currentContext, params);
                }
                if (stepsIdsToDelete.length) {
                    yield this.workflowMasterStepRepository.deleteWorkflowStepByCondition({
                        id: this.sequlizeOperator.inOperator(stepsIdsToDelete)
                    }, currentContext, params);
                    let removeLimitLinkCondition = {
                        workflowMasterSettingId: this.sequlizeOperator.inOperator(overiddenWorkflowIds),
                        sharedLimitMasterStepChildId: stepDetailToDelete.workflowMasterStepId,
                        active: false,
                        deleted: false
                    };
                    yield this.workflowMasterStepRepository.updateWorkflowByCondition(removeLimitLinkCondition, {
                        sharedLimitMasterStepChildId: null,
                        canShareLimitToChild: false
                    }, currentContext, params);
                }
                return { message: 'Steps has been deleted successfully.', overiddenWorkflowIds };
            }));
        });
    }
    updateWorkflowStep(updateWorkflowStepRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            let isUnpublishedVersion = false;
            if (!updateWorkflowStepRequestDto.hasOwnProperty('unpublishedVersion') ||
                (updateWorkflowStepRequestDto.hasOwnProperty('unpublishedVersion') && !updateWorkflowStepRequestDto.unpublishedVersion)) {
                isUnpublishedVersion = false;
            }
            else {
                isUnpublishedVersion = true;
            }
            const stepId = updateWorkflowStepRequestDto.stepId;
            let masterStepId = updateWorkflowStepRequestDto.stepId;
            let stepDetail;
            if (isUnpublishedVersion) {
                stepDetail = yield this.workflowMasterStepRepository.getUnpublishedVersionStepDetailById(stepId);
                if (stepDetail && (stepDetail.version !== stepDetail.workflowMasterSetting.unpublishedVersion)) {
                    throw new exceptions_1.HttpException('Workflow step is unavailable in unpublished version.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(stepId);
            }
            if (stepDetail) {
                masterStepId = stepDetail.workflowMasterStepId;
                if (!stepDetail.workflowMasterSetting ||
                    (stepDetail.workflowMasterSetting &&
                        (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!isUnpublishedVersion) {
                    if (stepDetail.workflowMasterSetting && stepDetail.workflowMasterSetting.published) {
                        throw new exceptions_1.HttpException('Workflow setting is already published for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                if (stepDetail.inherited &&
                    (stepDetail.associateType === associated_type_enum_1.ASSOCIATED_TYPE.USER) &&
                    (updateWorkflowStepRequestDto.singleLimit ||
                        updateWorkflowStepRequestDto.aggregateLimit)) {
                    throw new exceptions_1.HttpException('You cannot change the overridden step limit for users.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (updateWorkflowStepRequestDto.singleLimit || updateWorkflowStepRequestDto.aggregateLimit) {
                    const stepLimitSharedByParent = yield this.workflowMasterStepRepository.getWorkflowStepDetailByCondition({
                        sharedLimitMasterStepChildId: stepDetail.workflowMasterStepId
                    });
                    if (stepLimitSharedByParent) {
                        throw new exceptions_1.HttpException('It is not possible to set a limit for this step since it will be decided by ' + stepLimitSharedByParent.title + '.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                const workflowEntityLevel = stepDetail.workflowMasterSetting.entityType;
                const stepEntityLevel = stepDetail.associateLevel;
                if (stepDetail.inherited && stepEntityLevel) {
                    yield this.stepUpdateAllowedBasedOnLevel(workflowEntityLevel, stepEntityLevel);
                }
                let newUpdateWorkflowStepRequestDto;
                if (stepDetail.inherited) {
                    const allowedKey = ['singleLimit', 'aggregateLimit', 'lengthOfCommitment', 'title'];
                    newUpdateWorkflowStepRequestDto = Object.keys(updateWorkflowStepRequestDto).reduce((accumulator, key) => {
                        if (allowedKey.includes(key)) {
                            let value = updateWorkflowStepRequestDto[key];
                            if (key === 'lengthOfCommitment') {
                                if (stepDetail.workflowMasterSetting.isCommitmentLengthApplicable) {
                                    accumulator[key] = value;
                                }
                            }
                            else {
                                if (key === 'singleLimit') {
                                    value = (stepDetail.singleLimit <= 0) ? stepDetail.singleLimit : value;
                                }
                                if (key === 'aggregateLimit') {
                                    value = (stepDetail.aggregateLimit <= 0) ? stepDetail.aggregateLimit : value;
                                }
                                accumulator[key] = value;
                            }
                        }
                        return accumulator;
                    }, {});
                }
                else {
                    newUpdateWorkflowStepRequestDto = yield this.validateAndUpdatePayload(updateWorkflowStepRequestDto, stepDetail.workflowMasterSetting);
                }
                if (!newUpdateWorkflowStepRequestDto || lodash_1.default.isEmpty(newUpdateWorkflowStepRequestDto)) {
                    throw new exceptions_1.HttpException('Update is not possible for given data.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                yield this.validateLimitWithParentChild(newUpdateWorkflowStepRequestDto, stepDetail, null, isUnpublishedVersion);
                let historyRequest = [];
                yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    if (!stepDetail.inherited &&
                        stepDetail.canShareLimitToChild &&
                        (((newUpdateWorkflowStepRequestDto.singleLimit !== undefined && newUpdateWorkflowStepRequestDto.singleLimit === 0) ||
                            (newUpdateWorkflowStepRequestDto.aggregateLimit !== undefined && newUpdateWorkflowStepRequestDto.aggregateLimit === 0))
                            ||
                                ((stepDetail.singleLimit === -1 &&
                                    newUpdateWorkflowStepRequestDto.singleLimit !== undefined &&
                                    newUpdateWorkflowStepRequestDto.singleLimit >= 0) ||
                                    (stepDetail.aggregateLimit === -1 &&
                                        newUpdateWorkflowStepRequestDto.aggregateLimit !== undefined &&
                                        newUpdateWorkflowStepRequestDto.aggregateLimit >= 0)) ||
                            ((stepDetail.singleLimit > 0 &&
                                newUpdateWorkflowStepRequestDto.singleLimit !== undefined &&
                                (!Boolean(newUpdateWorkflowStepRequestDto.singleLimit) ||
                                    (newUpdateWorkflowStepRequestDto.singleLimit !== -1 &&
                                        newUpdateWorkflowStepRequestDto.singleLimit < stepDetail.singleLimit))) ||
                                (stepDetail.aggregateLimit > 0 &&
                                    newUpdateWorkflowStepRequestDto.aggregateLimit !== undefined &&
                                    (!Boolean(newUpdateWorkflowStepRequestDto.aggregateLimit) ||
                                        (newUpdateWorkflowStepRequestDto.aggregateLimit !== -1 &&
                                            newUpdateWorkflowStepRequestDto.aggregateLimit < stepDetail.aggregateLimit)))))) {
                        newUpdateWorkflowStepRequestDto.canShareLimitToChild = false;
                        newUpdateWorkflowStepRequestDto.sharedLimitMasterStepChildId = null;
                        if (!isUnpublishedVersion) {
                            yield this.workflowSharedChildLimitRepository.deleteSharedLimitByCondition({
                                workflowMasterParentStepId: stepDetail.workflowMasterStepId
                            }, currentContext);
                        }
                        else {
                            newUpdateWorkflowStepRequestDto.removeChildLimit = true;
                        }
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: stepDetail.id,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_REMOVED,
                            comments: stepDetail.title + ' child limit sharring has been removed due to changes in the limit.',
                            additional_info: {
                                beforeUpdate: stepDetail,
                                newUpdateRequest: newUpdateWorkflowStepRequestDto
                            },
                        });
                    }
                    let params = {
                        includeDeleted: false,
                        includeInactive: isUnpublishedVersion,
                        throwException: true,
                    };
                    if (newUpdateWorkflowStepRequestDto.hasOwnProperty('isMandatory')) {
                        if (!newUpdateWorkflowStepRequestDto.isMandatory) {
                            newUpdateWorkflowStepRequestDto.includeBelowSteps = false;
                        }
                    }
                    const updatedData = yield this.workflowMasterStepRepository.updateWorkflowStep(stepId, newUpdateWorkflowStepRequestDto, currentContext, params);
                    historyRequest.push({
                        created_by: currentContext.user.username,
                        entity_id: stepId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        comments: ((newUpdateWorkflowStepRequestDto === null || newUpdateWorkflowStepRequestDto === void 0 ? void 0 : newUpdateWorkflowStepRequestDto.title) || stepDetail.title) + ' step has been updated.',
                        additional_info: {
                            beforeUpdate: stepDetail,
                            newUpdateRequest: newUpdateWorkflowStepRequestDto
                        },
                    });
                    if (!stepDetail.inherited) {
                        const _a = Object.assign({}, newUpdateWorkflowStepRequestDto), { singleLimit = 0, aggregateLimit = 0, lengthOfCommitment = 0 } = _a, childUpdatePayload = __rest(_a, ["singleLimit", "aggregateLimit", "lengthOfCommitment"]);
                        if (!lodash_1.default.isEmpty(childUpdatePayload)) {
                            yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                                workflow_master_step_id: masterStepId,
                                inherited: true,
                                active: !isUnpublishedVersion
                            }, childUpdatePayload, currentContext, params);
                            const updateStepList = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                                workflow_master_step_id: masterStepId,
                                inherited: true,
                                active: !isUnpublishedVersion
                            }, false, isUnpublishedVersion);
                            if (updateStepList.length) {
                                for (let i = 0; i < updateStepList.length; i++) {
                                    historyRequest.push({
                                        created_by: currentContext.user.username,
                                        entity_id: updateStepList[i].id,
                                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                        comments: updateStepList[i].title + ' step has been updated.',
                                        additional_info: {
                                            beforeUpdate: updateStepList[i],
                                            newUpdateRequest: childUpdatePayload
                                        }
                                    });
                                }
                            }
                        }
                        if (newUpdateWorkflowStepRequestDto.hasOwnProperty('singleLimit')) {
                            const updateStepList = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                                workflowMasterStepId: masterStepId,
                                inherited: true,
                                singleLimit: stepDetail.singleLimit,
                                active: !isUnpublishedVersion
                            }, false, isUnpublishedVersion);
                            yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                                workflowMasterStepId: masterStepId,
                                inherited: true,
                                singleLimit: stepDetail.singleLimit,
                                active: !isUnpublishedVersion
                            }, { singleLimit }, currentContext, params);
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: stepDetail.id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                comments: ((newUpdateWorkflowStepRequestDto === null || newUpdateWorkflowStepRequestDto === void 0 ? void 0 : newUpdateWorkflowStepRequestDto.title) || stepDetail.title) + ' single limit has been updated.',
                                additional_info: {
                                    beforeUpdate: stepDetail,
                                    newUpdateRequest: { singleLimit }
                                }
                            });
                            if (updateStepList.length) {
                                for (let i = 0; i < updateStepList.length; i++) {
                                    historyRequest.push({
                                        created_by: currentContext.user.username,
                                        entity_id: updateStepList[i].id,
                                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                        comments: updateStepList[i].title + ' single limit has been updated.',
                                        additional_info: {
                                            beforeUpdate: updateStepList[i],
                                            newUpdateRequest: { singleLimit }
                                        }
                                    });
                                }
                            }
                        }
                        if (newUpdateWorkflowStepRequestDto.hasOwnProperty('aggregateLimit')) {
                            const updateStepList = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                                workflowMasterStepId: masterStepId,
                                inherited: true,
                                aggregateLimit: stepDetail.aggregateLimit,
                                active: !isUnpublishedVersion
                            }, false, isUnpublishedVersion);
                            yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                                workflowMasterStepId: masterStepId,
                                inherited: true,
                                aggregateLimit: stepDetail.aggregateLimit,
                                active: !isUnpublishedVersion
                            }, { aggregateLimit }, currentContext, params);
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: stepDetail.id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                comments: ((newUpdateWorkflowStepRequestDto === null || newUpdateWorkflowStepRequestDto === void 0 ? void 0 : newUpdateWorkflowStepRequestDto.title) || stepDetail.title) + ' aggregate limit has been updated.',
                                additional_info: {
                                    beforeUpdate: stepDetail,
                                    newUpdateRequest: { aggregateLimit }
                                }
                            });
                            if (updateStepList.length) {
                                for (let i = 0; i < updateStepList.length; i++) {
                                    historyRequest.push({
                                        created_by: currentContext.user.username,
                                        entity_id: updateStepList[i].id,
                                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                        comments: updateStepList[i].title + ' aggregate limit has been updated.',
                                        additional_info: {
                                            beforeUpdate: updateStepList[i],
                                            newUpdateRequest: { aggregateLimit }
                                        }
                                    });
                                }
                            }
                        }
                        if (newUpdateWorkflowStepRequestDto.hasOwnProperty('lengthOfCommitment')) {
                            const updateStepList = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                                workflowMasterStepId: masterStepId,
                                inherited: true,
                                lengthOfCommitment: stepDetail.lengthOfCommitment,
                                active: !isUnpublishedVersion
                            }, false, isUnpublishedVersion);
                            yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                                workflowMasterStepId: masterStepId,
                                inherited: true,
                                lengthOfCommitment: stepDetail.lengthOfCommitment,
                                active: !isUnpublishedVersion
                            }, { lengthOfCommitment }, currentContext, params);
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: stepDetail.id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                comments: ((newUpdateWorkflowStepRequestDto === null || newUpdateWorkflowStepRequestDto === void 0 ? void 0 : newUpdateWorkflowStepRequestDto.title) || stepDetail.title) + ' length of commitment has been updated.',
                                additional_info: {
                                    beforeUpdate: stepDetail,
                                    newUpdateRequest: { lengthOfCommitment }
                                }
                            });
                            if (updateStepList.length) {
                                for (let i = 0; i < updateStepList.length; i++) {
                                    historyRequest.push({
                                        created_by: currentContext.user.username,
                                        entity_id: updateStepList[i].id,
                                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                        comments: updateStepList[i].title + ' length of commitment has been updated.',
                                        additional_info: {
                                            beforeUpdate: updateStepList[i],
                                            newUpdateRequest: { lengthOfCommitment }
                                        }
                                    });
                                }
                            }
                        }
                    }
                }));
                if (historyRequest.length) {
                    yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                }
                return { message: 'Step data has been successfully updated.' };
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    deleteStep(stepId, currentContext, isUnpublishedVersion = false) {
        return __awaiter(this, void 0, void 0, function* () {
            let stepDetail;
            if (isUnpublishedVersion) {
                stepDetail = yield this.workflowMasterStepRepository.getUnpublishedVersionStepDetailById(stepId);
                if (stepDetail && (stepDetail.version !== stepDetail.workflowMasterSetting.unpublishedVersion)) {
                    throw new exceptions_1.HttpException('Workflow step is unavailable in unpublished version.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(stepId);
            }
            if (stepDetail) {
                if (stepDetail.inherited && !stepDetail.canRemovedAtChildLevel) {
                    throw new exceptions_1.HttpException(stepDetail.title + ' cannot be deleted due to master setting step restrictions.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!stepDetail.workflowMasterSetting ||
                    (stepDetail.workflowMasterSetting &&
                        (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!isUnpublishedVersion) {
                    if (stepDetail.workflowMasterSetting && stepDetail.workflowMasterSetting.published) {
                        throw new exceptions_1.HttpException('Workflow setting is already published for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                if (!stepDetail.inherited) {
                    const limitDeduction = yield this.limitDeductionRepository.getMasterSettingLimitDeductionCount(null, stepDetail.workflowMasterStepId);
                    if (limitDeduction > 0) {
                        throw new exceptions_1.HttpException('Unable to delete as limit got deducted for this step before.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                let shareChildLimitCheckCondition;
                let errorMessage = '';
                if (stepDetail.canShareLimitToChild) {
                    shareChildLimitCheckCondition = {
                        workflowMasterParentStepId: stepDetail.workflowMasterStepId
                    };
                    errorMessage = 'Can\'t delete this step as child is sharing the same limit.';
                }
                else {
                    if (!stepDetail.inherited) {
                        shareChildLimitCheckCondition = {
                            workflowMasterStepId: stepDetail.workflowMasterStepId
                        };
                        errorMessage = 'Can\'t delete this step as parent limit is shared with this step.';
                    }
                }
                if (shareChildLimitCheckCondition) {
                    const shareLimitMappingExist = yield this.workflowSharedChildLimitRepository.findChildSharedLimitByCondition(shareChildLimitCheckCondition);
                    if (shareLimitMappingExist) {
                        throw new exceptions_1.HttpException(errorMessage, enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                let historyRequest = [];
                yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    let condition;
                    let params = {
                        includeDeleted: false,
                        includeInactive: isUnpublishedVersion,
                        throwException: true,
                    };
                    if (stepDetail.inherited) {
                        condition = {
                            id: stepDetail.id,
                            active: !isUnpublishedVersion
                        };
                    }
                    else {
                        condition = {
                            workflowMasterStepId: stepDetail.workflowMasterStepId,
                            active: !isUnpublishedVersion
                        };
                    }
                    const allDeletedSteps = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition(condition, false, isUnpublishedVersion);
                    const deleteRes = yield this.workflowMasterStepRepository.deleteWorkflowStepByCondition(condition, currentContext, params);
                    if (deleteRes && allDeletedSteps.length) {
                        for (let i = 0; i < allDeletedSteps.length; i++) {
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: allDeletedSteps[i].id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                                comments: stepDetail.title + ' step has been deleted.',
                            }, {
                                created_by: currentContext.user.username,
                                entity_id: allDeletedSteps[i].workflowMasterSettingId,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                                comments: stepDetail.title + ' step has been deleted.',
                            });
                        }
                    }
                    if (deleteRes) {
                        let condition;
                        if (stepDetail.inherited || stepDetail.workflowMasterSetting.parentId) {
                            condition = {
                                approvalSequence: this.sequlizeOperator.greaterThanOperator(stepDetail.approvalSequence),
                                workflowMasterSettingId: stepDetail.workflowMasterSettingId,
                                active: !isUnpublishedVersion
                            };
                        }
                        else {
                            const allSteps = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                                workflowMasterStepId: stepDetail.workflowMasterStepId,
                                active: !isUnpublishedVersion
                            }, true, isUnpublishedVersion);
                            condition = [];
                            allSteps.forEach((workflowStep) => {
                                condition.push({
                                    workflow_master_setting_id: workflowStep.workflowMasterSettingId,
                                    approval_sequence: this.sequlizeOperator.greaterThanOperator(workflowStep.approvalSequence),
                                    active: !isUnpublishedVersion
                                });
                            });
                            if (condition.length) {
                                condition = this.sequlizeOperator.orOperator(condition);
                            }
                        }
                        if (condition) {
                            yield this.workflowMasterStepRepository.updateWorkflowByCondition(condition, {
                                approvalSequence: this.workflowMasterStepRepository.sequelizeLiteral('approval_sequence - 1')
                            }, currentContext, params);
                        }
                        let removeLimitLinkCondition;
                        if (stepDetail.inherited) {
                            removeLimitLinkCondition = {
                                sharedLimitMasterStepChildId: stepDetail.workflowMasterStepId,
                                workflowMasterSettingId: stepDetail.workflowMasterSettingId,
                                active: !isUnpublishedVersion
                            };
                        }
                        else {
                            removeLimitLinkCondition = {
                                sharedLimitMasterStepChildId: stepDetail.workflowMasterStepId,
                                active: !isUnpublishedVersion
                            };
                        }
                        yield this.workflowMasterStepRepository.updateWorkflowByCondition(removeLimitLinkCondition, {
                            sharedLimitMasterStepChildId: null,
                            canShareLimitToChild: false
                        }, currentContext, params);
                    }
                    else {
                        throw new exceptions_1.HttpException('Unable to delete the workflow step.', enums_1.HttpStatus.BAD_REQUEST);
                    }
                }));
                if (historyRequest.length) {
                    yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                }
                return { message: 'Workflow step has been deleted successfully.' };
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    stepMovement(stepId, stepMovementRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            let isUnpublishedVersion = false;
            if (!stepMovementRequestDto.hasOwnProperty('unpublishedVersion') ||
                (stepMovementRequestDto.hasOwnProperty('unpublishedVersion') && !stepMovementRequestDto.unpublishedVersion)) {
                isUnpublishedVersion = false;
            }
            else {
                isUnpublishedVersion = true;
            }
            let stepDetail;
            if (isUnpublishedVersion) {
                stepDetail = yield this.workflowMasterStepRepository.getUnpublishedVersionStepDetailById(stepId);
                if (stepDetail && (stepDetail.version !== stepDetail.workflowMasterSetting.unpublishedVersion)) {
                    throw new exceptions_1.HttpException('Workflow step is unavailable in unpublished version.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(stepId);
            }
            if (stepDetail) {
                if (stepDetail.inherited) {
                    throw new exceptions_1.HttpException('Can\'t change the position for overridden step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!stepDetail.workflowMasterSetting ||
                    (stepDetail.workflowMasterSetting &&
                        (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!isUnpublishedVersion) {
                    if (stepDetail.workflowMasterSetting && stepDetail.workflowMasterSetting.published) {
                        throw new exceptions_1.HttpException('Workflow setting is already published for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                let response = false;
                if (stepMovementRequestDto.position === types_1.StepPositionActionEnum.MOVE_UP) {
                    response = yield this.moveStepUp(stepDetail, currentContext, isUnpublishedVersion);
                }
                if (stepMovementRequestDto.position === types_1.StepPositionActionEnum.MOVE_DOWN) {
                    response = yield this.moveStepDown(stepDetail, currentContext, isUnpublishedVersion);
                }
                if (response) {
                    yield this.historyApiClient.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: stepId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                        action_performed: ((stepMovementRequestDto.position === types_1.StepPositionActionEnum.MOVE_UP) ? enums_1.HISTORY_ACTION_TYPE.STEP_MOVED_UP : enums_1.HISTORY_ACTION_TYPE.STEP_MOVED_DOWN),
                        comments: stepDetail.title + ' has been moved one step ' + ((stepMovementRequestDto.position === types_1.StepPositionActionEnum.MOVE_UP) ? 'up' : 'down') + '.',
                    });
                    return { message: 'Workflow step sequence changed successfully.' };
                }
                throw new exceptions_1.HttpException('Unable to change the sequence for this step.', enums_1.HttpStatus.BAD_REQUEST);
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    moveStepUp(stepDetail, currentContext, isUnpublishedVersion = false) {
        return __awaiter(this, void 0, void 0, function* () {
            if (stepDetail.approvalSequence === 1) {
                throw new exceptions_1.HttpException('Step can\'t be moved up, step is already at the top.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            let params = {
                includeDeleted: false,
                includeInactive: isUnpublishedVersion,
                throwException: true,
            };
            if (stepDetail.singleLimit > 0 || stepDetail.aggregateLimit > 0 || stepDetail.lengthOfCommitment > 0) {
                yield this.validateLimitWithParentChildMovement(stepDetail, types_1.StepPositionActionEnum.MOVE_UP, isUnpublishedVersion);
            }
            else {
                yield this.validateParentChildShareMovement(stepDetail, types_1.StepPositionActionEnum.MOVE_UP, isUnpublishedVersion);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                    id: stepDetail.id,
                    deleted: false,
                    active: !isUnpublishedVersion
                }, {
                    approvalSequence: this.workflowMasterStepRepository.sequelizeLiteral('approval_sequence - 1')
                }, currentContext, params);
                yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                    workflowMasterSettingId: stepDetail.workflowMasterSettingId,
                    id: this.sequlizeOperator.notEqualOperator(stepDetail.id),
                    approvalSequence: (stepDetail.approvalSequence - 1),
                    deleted: false,
                    active: !isUnpublishedVersion
                }, {
                    approvalSequence: (stepDetail.approvalSequence)
                }, currentContext, params);
                return true;
            }));
        });
    }
    moveStepDown(stepDetail, currentContext, isUnpublishedVersion = false) {
        return __awaiter(this, void 0, void 0, function* () {
            let params = {
                includeDeleted: false,
                includeInactive: isUnpublishedVersion,
                throwException: true,
            };
            let totalSteps = 0;
            if (isUnpublishedVersion) {
                totalSteps = yield this.workflowMasterStepRepository.getWorkflowUnpublishedVersionMasterStepsCount(stepDetail.workflowMasterSettingId);
            }
            else {
                totalSteps = yield this.workflowMasterStepRepository.getWorkflowMasterStepsCount(stepDetail.workflowMasterSettingId);
            }
            if (stepDetail.approvalSequence === totalSteps) {
                throw new exceptions_1.HttpException('Step can\'t be moved down, step is already at the bottom.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (stepDetail.singleLimit > 0 || stepDetail.aggregateLimit > 0 || stepDetail.lengthOfCommitment > 0) {
                yield this.validateLimitWithParentChildMovement(stepDetail, types_1.StepPositionActionEnum.MOVE_DOWN, isUnpublishedVersion);
            }
            else {
                yield this.validateParentChildShareMovement(stepDetail, types_1.StepPositionActionEnum.MOVE_DOWN, isUnpublishedVersion);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                    id: stepDetail.id,
                    deleted: false,
                    active: !isUnpublishedVersion
                }, {
                    approvalSequence: this.workflowMasterStepRepository.sequelizeLiteral('approval_sequence + 1')
                }, currentContext, params);
                yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                    workflowMasterSettingId: stepDetail.workflowMasterSettingId,
                    id: this.sequlizeOperator.notEqualOperator(stepDetail.id),
                    approvalSequence: (stepDetail.approvalSequence + 1),
                    deleted: false,
                    active: !isUnpublishedVersion
                }, {
                    approvalSequence: (stepDetail.approvalSequence)
                }, currentContext, params);
                return true;
            }));
        });
    }
    stepUpdateAllowedBasedOnLevel(workflowEntityLevel, stepEntityLevel) {
        return __awaiter(this, void 0, void 0, function* () {
            let entityLevelList = yield this.adminApiClient.getBusinessEntityLevels();
            entityLevelList = entityLevelList.map(d => (0, class_transformer_1.instanceToPlain)(new business_entity_level_dto_1.BusinessEntityLevelResponseDto(d)));
            const settingLevelDetail = entityLevelList.find((entityLevel) => {
                return entityLevel.levelName === workflowEntityLevel;
            });
            const stepLevelDetail = entityLevelList.find((entityLevel) => {
                return entityLevel.levelName === stepEntityLevel;
            });
            if (!(settingLevelDetail && stepLevelDetail && settingLevelDetail.orderNumber <= stepLevelDetail.orderNumber)) {
                throw new exceptions_1.HttpException('Workflow step can\'t be changed for parent level entity.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            return true;
        });
    }
    validateLimitWithParentChild(newUpdateWorkflowStepRequestDto, stepDetail, workflowParentStepList = null, isUnpublishedVersion = false) {
        return __awaiter(this, void 0, void 0, function* () {
            if ((newUpdateWorkflowStepRequestDto.hasOwnProperty('singleLimit')
                && Boolean(newUpdateWorkflowStepRequestDto.singleLimit))
                || (newUpdateWorkflowStepRequestDto.hasOwnProperty('aggregateLimit')
                    && Boolean(newUpdateWorkflowStepRequestDto.aggregateLimit))
                || (newUpdateWorkflowStepRequestDto.hasOwnProperty('lengthOfCommitment')
                    && Boolean(newUpdateWorkflowStepRequestDto.lengthOfCommitment))) {
                let workflowParentSteps;
                if (workflowParentStepList === null) {
                    workflowParentSteps = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                        workflowMasterSettingId: stepDetail.workflowMasterSettingId,
                        id: this.sequlizeOperator.notEqualOperator(stepDetail.id),
                        active: !isUnpublishedVersion,
                        version: stepDetail.version
                    }, false, isUnpublishedVersion);
                }
                else {
                    workflowParentSteps = workflowParentStepList;
                }
                workflowParentSteps.forEach((workflowParentStep) => {
                    if (newUpdateWorkflowStepRequestDto.singleLimit &&
                        (((newUpdateWorkflowStepRequestDto.singleLimit > 0
                            && workflowParentStep.singleLimit !== -1
                            && workflowParentStep.singleLimit !== 0)
                            &&
                                ((stepDetail.approvalSequence > workflowParentStep.approvalSequence
                                    &&
                                        newUpdateWorkflowStepRequestDto.singleLimit >= workflowParentStep.singleLimit)
                                    ||
                                        (stepDetail.approvalSequence < workflowParentStep.approvalSequence
                                            &&
                                                newUpdateWorkflowStepRequestDto.singleLimit <= workflowParentStep.singleLimit))))) {
                        throw new exceptions_1.HttpException((newUpdateWorkflowStepRequestDto.singleLimit === -1)
                            ? 'For single limit unlimited limit has already been defined for ' + workflowParentStep.title
                            : 'Single limit can\'t be ' + (stepDetail.approvalSequence > workflowParentStep.approvalSequence ? 'greater' : 'less') + ' than or equal to ' + workflowParentStep.title + ' single limit.', enums_1.HttpStatus.NOT_FOUND);
                    }
                    if (newUpdateWorkflowStepRequestDto.aggregateLimit &&
                        (((newUpdateWorkflowStepRequestDto.aggregateLimit > 0
                            && workflowParentStep.aggregateLimit !== -1
                            && workflowParentStep.aggregateLimit !== 0)
                            &&
                                ((stepDetail.approvalSequence > workflowParentStep.approvalSequence
                                    &&
                                        newUpdateWorkflowStepRequestDto.aggregateLimit >= workflowParentStep.aggregateLimit)
                                    ||
                                        (stepDetail.approvalSequence < workflowParentStep.approvalSequence
                                            &&
                                                newUpdateWorkflowStepRequestDto.aggregateLimit <= workflowParentStep.aggregateLimit))))) {
                        throw new exceptions_1.HttpException((newUpdateWorkflowStepRequestDto.aggregateLimit === -1)
                            ? 'For agrregate limit unlimited limit has already been defined for ' + workflowParentStep.title
                            : 'Agrregate limit can\'t be ' + (stepDetail.approvalSequence > workflowParentStep.approvalSequence ? 'greater' : 'less') + ' than or equal to ' + workflowParentStep.title + ' agrregate limit.', enums_1.HttpStatus.NOT_FOUND);
                    }
                    if ((newUpdateWorkflowStepRequestDto.lengthOfCommitment &&
                        workflowParentStep.lengthOfCommitment)
                        &&
                            ((stepDetail.approvalSequence > workflowParentStep.approvalSequence
                                &&
                                    newUpdateWorkflowStepRequestDto.lengthOfCommitment > workflowParentStep.lengthOfCommitment)
                                ||
                                    (stepDetail.approvalSequence < workflowParentStep.approvalSequence
                                        &&
                                            newUpdateWorkflowStepRequestDto.lengthOfCommitment < workflowParentStep.lengthOfCommitment))) {
                        throw new exceptions_1.HttpException('Length of commitment can\'t be ' + (stepDetail.approvalSequence > workflowParentStep.approvalSequence ? 'greater' : 'less') + ' than ' + workflowParentStep.title + ' length of commitment.', enums_1.HttpStatus.NOT_FOUND);
                    }
                });
            }
            return true;
        });
    }
    validateParentChildShareMovement(stepDetail, position, isUnpublishedVersion = false) {
        return __awaiter(this, void 0, void 0, function* () {
            const workflowParentSteps = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                workflowMasterSettingId: stepDetail.workflowMasterSettingId,
                approvalSequence: (position === types_1.StepPositionActionEnum.MOVE_UP ? (stepDetail.approvalSequence - 1) : (stepDetail.approvalSequence + 1)),
                deleted: false,
                active: !isUnpublishedVersion
            }, false, isUnpublishedVersion);
            workflowParentSteps.forEach((workflowParentStep) => {
                if (position === types_1.StepPositionActionEnum.MOVE_UP) {
                    if (workflowParentStep.sharedLimitMasterStepChildId === stepDetail.workflowMasterStepId) {
                        const parentSequenceNumber = workflowParentStep.approvalSequence;
                        if (((0, lodash_1.toNumber)(stepDetail.approvalSequence) - 1) === parentSequenceNumber) {
                            throw new exceptions_1.HttpException('Step can\'t be above parent step who is sharing the limit.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                    }
                }
                if ((position === types_1.StepPositionActionEnum.MOVE_DOWN)
                    && stepDetail.sharedLimitMasterStepChildId) {
                    if (workflowParentStep.workflowMasterStepId === stepDetail.sharedLimitMasterStepChildId) {
                        const stepSequenceNumber = stepDetail.approvalSequence;
                        if (((0, lodash_1.toNumber)(stepSequenceNumber) + 1) === workflowParentStep.approvalSequence) {
                            throw new exceptions_1.HttpException('Step can\'t be below child step whom it is sharing the limit.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                    }
                }
            });
        });
    }
    validateLimitWithParentChildMovement(stepDetail, position, isUnpublishedVersion = false) {
        return __awaiter(this, void 0, void 0, function* () {
            if (stepDetail.singleLimit > 0 ||
                stepDetail.aggregateLimit > 0 ||
                stepDetail.lengthOfCommitment > 0) {
                const workflowParentSteps = yield this.workflowMasterStepRepository.getWorkflowStepsByCondition({
                    workflowMasterSettingId: stepDetail.workflowMasterSettingId,
                    approvalSequence: (position === types_1.StepPositionActionEnum.MOVE_UP ? (stepDetail.approvalSequence - 1) : (stepDetail.approvalSequence + 1)),
                    deleted: false,
                    active: !isUnpublishedVersion
                }, false, isUnpublishedVersion);
                workflowParentSteps.forEach((workflowParentStep) => {
                    if (position === types_1.StepPositionActionEnum.MOVE_UP) {
                        if (workflowParentStep.sharedLimitMasterStepChildId === stepDetail.workflowMasterStepId) {
                            const parentSequenceNumber = workflowParentStep.approvalSequence;
                            if (((0, lodash_1.toNumber)(stepDetail.approvalSequence) - 1) === parentSequenceNumber) {
                                throw new exceptions_1.HttpException('Step can\'t be above parent step who is sharing the limit.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                            }
                        }
                    }
                    if ((position === types_1.StepPositionActionEnum.MOVE_DOWN)
                        && stepDetail.sharedLimitMasterStepChildId) {
                        if (workflowParentStep.workflowMasterStepId === stepDetail.sharedLimitMasterStepChildId) {
                            const stepSequenceNumber = stepDetail.approvalSequence;
                            if (((0, lodash_1.toNumber)(stepSequenceNumber) + 1) === workflowParentStep.approvalSequence) {
                                throw new exceptions_1.HttpException('Step can\'t be below child step whom it is sharing the limit.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                            }
                        }
                    }
                    if (stepDetail.singleLimit && stepDetail.singleLimit > 0) {
                        if (position === types_1.StepPositionActionEnum.MOVE_UP && stepDetail.singleLimit < workflowParentStep.singleLimit) {
                            throw new exceptions_1.HttpException('It is not possible to move this step above ' + workflowParentStep.title + '. The single limit can\'t be lower than ' + workflowParentStep.title + '.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                        if (position === types_1.StepPositionActionEnum.MOVE_DOWN && (stepDetail.singleLimit > workflowParentStep.singleLimit && workflowParentStep.singleLimit > 0)) {
                            throw new exceptions_1.HttpException('It is not possible to move this step below ' + workflowParentStep.title + '. The single limit can\'t be greater than ' + workflowParentStep.title + '.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                    }
                    if (stepDetail.aggregateLimit && stepDetail.aggregateLimit > 0) {
                        if (position === types_1.StepPositionActionEnum.MOVE_UP && stepDetail.aggregateLimit < workflowParentStep.aggregateLimit) {
                            throw new exceptions_1.HttpException('It is not possible to move this step above ' + workflowParentStep.title + ', The aggregate limit can\'t be lower than ' + workflowParentStep.title + '.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                        if (position === types_1.StepPositionActionEnum.MOVE_DOWN && (stepDetail.aggregateLimit > workflowParentStep.aggregateLimit && workflowParentStep.aggregateLimit > 0)) {
                            throw new exceptions_1.HttpException('It is not possible to move this step below ' + workflowParentStep.title + '. The aggregate limit can\'t be greater than ' + workflowParentStep.title + '.' + '', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                    }
                    if (stepDetail.lengthOfCommitment && stepDetail.lengthOfCommitment > 0) {
                        if (position === types_1.StepPositionActionEnum.MOVE_UP && stepDetail.lengthOfCommitment < workflowParentStep.lengthOfCommitment) {
                            throw new exceptions_1.HttpException('It is not possible to move this step above ' + workflowParentStep.title + '. The length of commitment can\'t be lower than ' + workflowParentStep.title + '.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                        if (position === types_1.StepPositionActionEnum.MOVE_DOWN && (stepDetail.lengthOfCommitment > workflowParentStep.lengthOfCommitment && workflowParentStep.lengthOfCommitment > 0)) {
                            throw new exceptions_1.HttpException('It is not possible to move this step below ' + workflowParentStep.title + '. The length of commitment can\'t be greater than ' + workflowParentStep.title + '.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                    }
                });
            }
            return true;
        });
    }
    validateAndUpdatePayload(requestPayload, workflowMasterSetting) {
        return __awaiter(this, void 0, void 0, function* () {
            if (requestPayload.hasOwnProperty('lengthOfCommitment')
                && !workflowMasterSetting.isCommitmentLengthApplicable) {
                requestPayload.lengthOfCommitment = null;
            }
            if (requestPayload.hasOwnProperty('aggregateLimit')
                && !workflowMasterSetting.isAggregateLimitApplicable) {
                requestPayload.aggregateLimit = 0;
            }
            if (requestPayload.hasOwnProperty('associateType')) {
                if (requestPayload.associateType === 'ROLE') {
                    requestPayload.associatedColumn = null;
                    requestPayload.associatedUser = null;
                }
                if (requestPayload.associateType === 'USER') {
                    requestPayload.associatedColumn = null;
                    requestPayload.associateRole = null;
                    requestPayload.associateLevel = null;
                }
                if (requestPayload.associateType === 'COST_CENTER') {
                    requestPayload.associatedUser = null;
                    requestPayload.associateRole = null;
                    requestPayload.associateLevel = null;
                }
            }
            return requestPayload;
        });
    }
    childLimitShareStep(stepId, updateStepLimitShareRequestDTO, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            let isUnpublishedVersion = false;
            if (!updateStepLimitShareRequestDTO.hasOwnProperty('unpublishedVersion') ||
                (updateStepLimitShareRequestDTO.hasOwnProperty('unpublishedVersion') && !updateStepLimitShareRequestDTO.unpublishedVersion)) {
                isUnpublishedVersion = false;
            }
            else {
                isUnpublishedVersion = true;
            }
            if (updateStepLimitShareRequestDTO.canShareLimitToChild && !updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId) {
                throw new exceptions_1.HttpException('sharedLimitMasterStepChildId is missing in the request.', enums_1.HttpStatus.BAD_REQUEST);
            }
            if (!updateStepLimitShareRequestDTO.canShareLimitToChild && updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId) {
                throw new exceptions_1.HttpException('Invalid Request Payload.', enums_1.HttpStatus.BAD_REQUEST);
            }
            let stepDetail;
            let masterStepId = stepId;
            if (isUnpublishedVersion) {
                stepDetail = yield this.workflowMasterStepRepository.getUnpublishedVersionStepDetailById(stepId);
                if (stepDetail && (stepDetail.version !== stepDetail.workflowMasterSetting.unpublishedVersion)) {
                    throw new exceptions_1.HttpException('Workflow step is unavailable in unpublished version.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailById(stepId);
            }
            masterStepId = stepDetail.workflowMasterStepId;
            if (stepDetail) {
                let childStepDetail = null;
                if (stepDetail.singleLimit === 0 && stepDetail.aggregateLimit === 0) {
                    throw new exceptions_1.HttpException('Step don\'t have limit to share.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (stepDetail.associateType !== associated_type_enum_1.ASSOCIATED_TYPE.ROLE) {
                    throw new exceptions_1.HttpException('Step is not allowed to share the limit.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (stepDetail.inherited) {
                    throw new exceptions_1.HttpException('Overridden step can\'t update child limit sharing.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!stepDetail.workflowMasterSetting ||
                    (stepDetail.workflowMasterSetting &&
                        (stepDetail.workflowMasterSetting.deleted || !stepDetail.workflowMasterSetting.active))) {
                    throw new exceptions_1.HttpException('Workflow setting is unavailable for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                if (!isUnpublishedVersion) {
                    if (stepDetail.workflowMasterSetting && stepDetail.workflowMasterSetting.published) {
                        throw new exceptions_1.HttpException('Workflow setting is already published for this step.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                let parms = {
                    throwException: false,
                    includeDeleted: false,
                    includeInactive: isUnpublishedVersion,
                };
                if (updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId) {
                    childStepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailByCondition({
                        workflowMasterStepId: updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId,
                        active: !isUnpublishedVersion
                    }, parms);
                    if (!childStepDetail) {
                        throw new exceptions_1.HttpException('Child step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
                    }
                    if (childStepDetail.singleLimit !== 0 || childStepDetail.aggregateLimit !== 0) {
                        throw new exceptions_1.HttpException('There is already a limit set on this child, so it can\'t be shared.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                    const childAlreadyLinked = yield this.workflowMasterStepRepository.getWorkflowStepDetailByCondition({
                        sharedLimitMasterStepChildId: childStepDetail.workflowMasterStepId,
                        workflowMasterStepId: this.sequlizeOperator.notEqualOperator(masterStepId),
                        active: !isUnpublishedVersion,
                        version: childStepDetail.version
                    }, parms);
                    if (childAlreadyLinked) {
                        throw new exceptions_1.HttpException('Child has already been linked for ' + childAlreadyLinked.title + '.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                if (stepDetail.canShareLimitToChild === updateStepLimitShareRequestDTO.canShareLimitToChild &&
                    (!updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId ||
                        (stepDetail.sharedLimitMasterStepChildId == childStepDetail.workflowMasterStepId))) {
                    throw new exceptions_1.HttpException('Limit share is already updated.', enums_1.HttpStatus.BAD_REQUEST);
                }
                updateStepLimitShareRequestDTO = Object.assign(Object.assign({}, updateStepLimitShareRequestDTO), { sharedLimitMasterStepChildId: !updateStepLimitShareRequestDTO.canShareLimitToChild ? null : childStepDetail.workflowMasterStepId });
                const allWorkflowParenAndInherritedChilStep = yield this.workflowMasterStepRepository.getAllMasterSettingIdsOfStepByCondition({
                    workflowMasterStepId: updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId ? childStepDetail.workflowMasterStepId : stepDetail.sharedLimitMasterStepChildId,
                    active: !isUnpublishedVersion
                }, false, isUnpublishedVersion);
                let historyRequest = [];
                yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                        workflowMasterStepId: masterStepId,
                        workflowMasterSettingId: this.sequlizeOperator.inOperator(allWorkflowParenAndInherritedChilStep),
                        active: !isUnpublishedVersion
                    }, Object.assign(Object.assign({}, updateStepLimitShareRequestDTO), { removeChildLimit: isUnpublishedVersion }), currentContext, parms);
                    historyRequest.push({
                        created_by: currentContext.user.username,
                        entity_id: stepId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        comments: 'Child limit setting has been updated for this step.',
                        additional_info: {
                            requestPayload: updateStepLimitShareRequestDTO,
                        }
                    });
                    if (updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId) {
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: updateStepLimitShareRequestDTO.sharedLimitMasterStepChildId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                            comments: stepDetail.title + ' is now linked with this step to share the limit.',
                            additional_info: {
                                requestPayload: updateStepLimitShareRequestDTO,
                            }
                        });
                    }
                    else {
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: stepDetail.sharedLimitMasterStepChildId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                            comments: stepDetail.title + ' is now removed with this step to share the limit.',
                            additional_info: {
                                requestPayload: updateStepLimitShareRequestDTO,
                            }
                        });
                    }
                    if (!isUnpublishedVersion) {
                        yield this.workflowSharedChildLimitRepository.deleteSharedLimitByCondition({
                            workflowMasterParentStepId: masterStepId
                        }, currentContext);
                    }
                }));
                if (historyRequest.length) {
                    yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                }
                return { message: 'Limit sharing has been successfully updated.', data: childStepDetail };
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getStepDetailWithId(stepId) {
        return __awaiter(this, void 0, void 0, function* () {
            const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailWIthChildShareById(stepId);
            if (stepDetail) {
                return (0, helpers_1.singleObjectToInstance)(get_child_shared_limit_response_dto_1.GetChildSharedLimitWithParentDetailDTO, stepDetail);
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getRoleBasedSteps(currentContext, limit = 10, page = 1, filter) {
        return __awaiter(this, void 0, void 0, function* () {
            const userRoles = yield this.adminApiClient.getUserRoles(currentContext.user.username);
            let roles = [];
            if (userRoles && userRoles.length) {
                userRoles.forEach((userRole) => {
                    roles.push(userRole.roleName);
                });
            }
            const stepDetails = yield this.workflowMasterStepRepository.getActiveSettingStepsByCondition({
                associateRole: this.sequlizeOperator.inOperator(roles),
                canShareLimitToChild: true,
                inherited: false
            }, limit, page, filter);
            if (stepDetails) {
                const records = (0, helpers_1.multiObjectToInstance)(get_role_based_steps_response_dto_1.GetRoleBasedStepsResponse, stepDetails.rows);
                return new pagination_1.Pagination({ records, total: stepDetails.count });
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getStepBalance(stepId, entityId = null, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepDetailWIthChildShareByCondition({
                workflowMasterStepId: stepId,
                inherited: false
            });
            if (stepDetail) {
                if (!((_a = stepDetail === null || stepDetail === void 0 ? void 0 : stepDetail.workflowMasterSetting) === null || _a === void 0 ? void 0 : _a.published)) {
                    throw new exceptions_1.HttpException('Workflow is unpublished.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                let stepDetailMaster = stepDetail;
                if (stepDetail.workflowMasterStepId !== stepId) {
                    stepDetailMaster = yield this.workflowMasterStepRepository.getWorkflowStepDetailWIthChildShareByCondition({
                        workflowMasterStepId: stepDetail.workflowMasterStepId,
                        inherited: false
                    });
                }
                yield this.workflowSharedChildLimitService.doBasicValidationCheck(stepDetailMaster, {}, true);
                if (stepDetailMaster.aggregateLimit > 0) {
                    if ((0, lodash_1.toNumber)(entityId)) {
                        if (stepDetailMaster.associateType === 'COST_CENTER') {
                            return yield this.getCostCenterBasedBalance((0, lodash_1.toNumber)(entityId), stepDetailMaster, currentContext);
                        }
                        else {
                            return yield this.getEntityBasedBalance((0, lodash_1.toNumber)(entityId), stepDetailMaster, currentContext);
                        }
                    }
                    else {
                        const deductionAmount = yield this.afeProposalLimitDeductionRepository.totalDeductionByStepId(stepDetailMaster.workflowMasterStepId);
                        let totalDeduction = 0;
                        let deductions = [];
                        deductionAmount.forEach((deduction) => {
                            totalDeduction = (0, lodash_1.toNumber)(totalDeduction) + (0, lodash_1.toNumber)(deduction.amount);
                            deductions.push((0, helpers_1.singleObjectToInstance)(get_aggregate_limit_balance_dto_1.DeductionDto, deduction));
                        });
                        const entityFinalStepDetail = Object.assign({ limitDeduction: totalDeduction, deductions: deductions.length ? (deductions.sort((a, b) => b.updatedOn - a.updatedOn)) : [] }, stepDetailMaster);
                        return [(0, helpers_1.singleObjectToInstance)(get_aggregate_limit_balance_dto_1.GetAggregateLimitbalanceDTO, entityFinalStepDetail)];
                    }
                }
                else {
                    const childEntities = yield this.adminApiClient.getAllBusinessHierarchyByUserAndPermission(currentContext.user.username, enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT, (0, lodash_1.toNumber)(entityId), null);
                    let allChildEntityId = [];
                    if (childEntities && childEntities.children && childEntities.children.length) {
                        for (const childEntity of childEntities.children) {
                            allChildEntityId.push(childEntity.id);
                        }
                    }
                    else {
                        throw new exceptions_1.HttpException('Invalid Parent Entity Selected.', enums_1.HttpStatus.NOT_FOUND);
                    }
                    let allSharedLimits = yield this.workflowSharedChildLimitRepository.findChildSharedLimitsByCondition({
                        workflowMasterStepId: stepDetailMaster.workflowMasterStepId,
                        aggregateLimit: this.sequlizeOperator.greaterThanOperator(0),
                        entityId: this.sequlizeOperator.inOperator(allChildEntityId)
                    });
                    if (allChildEntityId && allChildEntityId.length) {
                        let allChildEntityId = [];
                        for (const childEntity of childEntities.children) {
                            allChildEntityId.push(childEntity.id);
                        }
                        const deductionAmount = yield this.afeProposalLimitDeductionRepository.totalDeductionByEntitiesOnStep(allChildEntityId, stepDetailMaster.workflowMasterStepId);
                        let finalDeductionDetail = (0, helpers_1.multiObjectToInstance)(get_aggregate_limit_balance_dto_1.GetAggregateLimitbalanceDTO, allSharedLimits);
                        finalDeductionDetail = finalDeductionDetail.map((sharedLimitDetail) => {
                            sharedLimitDetail = Object.assign(Object.assign({}, sharedLimitDetail), { limitDeduction: 0, deductions: [] });
                            if (deductionAmount && deductionAmount.length) {
                                deductionAmount.forEach((deduction) => {
                                    if (deduction.entityId === sharedLimitDetail.entityId) {
                                        sharedLimitDetail.limitDeduction = sharedLimitDetail.limitDeduction + deduction.amount;
                                        sharedLimitDetail.deductions.push((0, helpers_1.singleObjectToInstance)(get_aggregate_limit_balance_dto_1.DeductionDto, deduction));
                                    }
                                });
                            }
                            return sharedLimitDetail;
                        });
                        return finalDeductionDetail;
                    }
                    throw new exceptions_1.HttpException('No aggregate limit available for this step.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                throw new exceptions_1.HttpException('Workflow step is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getEntityBasedBalance(entityId, stepDetailMaster, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const childEntities = yield this.adminApiClient.getAllBusinessHierarchyByUserAndPermission(currentContext.user.username, enums_1.PERMISSIONS.AFE_LIMIT_MANAGEMENT, (0, lodash_1.toNumber)(entityId), null);
            if (childEntities && childEntities.children && childEntities.children.length) {
                let allEntityFinalStepList = [];
                for (const childEntity of childEntities.children) {
                    const { finalStepDetail, entitySharingSameBucket } = yield this.workflowSharedChildLimitService.getActualStepDetail(stepDetailMaster, {
                        entityId: (0, lodash_1.toNumber)(childEntity.id),
                        aggregateLimit: stepDetailMaster.aggregateLimit
                    }, true);
                    const { children } = childEntity, entityDetail = __rest(childEntity, ["children"]);
                    allEntityFinalStepList.push({
                        finalStepDetail,
                        entitySharingSameBucket,
                        entityDetail: entityDetail
                    });
                }
                const balanceDeductionEntity = [];
                for (const stepEntityDetail of allEntityFinalStepList) {
                    balanceDeductionEntity.push(...stepEntityDetail.entitySharingSameBucket);
                }
                if (balanceDeductionEntity.length) {
                    const deductionDetail = yield this.afeProposalLimitDeductionRepository.totalDeductionByEntitiesOnStep(balanceDeductionEntity, stepDetailMaster.workflowMasterStepId);
                    allEntityFinalStepList = allEntityFinalStepList.map((entityFinalStepDetail) => {
                        if (!entityFinalStepDetail.hasOwnProperty('limitDeduction')) {
                            entityFinalStepDetail.limitDeduction = 0;
                            entityFinalStepDetail.deductions = [];
                        }
                        entityFinalStepDetail.entitySharingSameBucket.forEach((bucketEntity) => {
                            deductionDetail.forEach((deduction) => {
                                if (deduction.entityId === bucketEntity) {
                                    entityFinalStepDetail.limitDeduction = (entityFinalStepDetail.limitDeduction + deduction.amount);
                                    entityFinalStepDetail.deductions.push((0, helpers_1.singleObjectToInstance)(get_aggregate_limit_balance_dto_1.DeductionDto, deduction));
                                }
                            });
                        });
                        entityFinalStepDetail = Object.assign(Object.assign(Object.assign({ limitDeduction: entityFinalStepDetail.limitDeduction, deductions: (entityFinalStepDetail === null || entityFinalStepDetail === void 0 ? void 0 : entityFinalStepDetail.deductions) ? (entityFinalStepDetail === null || entityFinalStepDetail === void 0 ? void 0 : entityFinalStepDetail.deductions.sort((a, b) => b.updatedOn - a.updatedOn)) : [] }, entityFinalStepDetail.finalStepDetail), entityFinalStepDetail.entityDetail), { entityId: entityFinalStepDetail.entityDetail.id, entityTitle: entityFinalStepDetail.entityDetail.full_name, entityShortName: entityFinalStepDetail.entityDetail.short_name, entityCode: entityFinalStepDetail.entityDetail.code, entityType: entityFinalStepDetail.entityDetail.entity_type });
                        return (0, helpers_1.singleObjectToInstance)(get_aggregate_limit_balance_dto_1.GetAggregateLimitbalanceDTO, entityFinalStepDetail);
                    });
                    return (0, helpers_1.multiObjectToInstance)(get_aggregate_limit_balance_dto_1.GetAggregateLimitbalanceDTO, allEntityFinalStepList);
                }
                else {
                    throw new exceptions_1.HttpException('Invalid Parent Entity Selected.', enums_1.HttpStatus.NOT_FOUND);
                }
            }
            else {
                throw new exceptions_1.HttpException('Invalid Parent Entity Selected.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getCostCenterBasedBalance(entityId, stepDetailMaster, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const costCenterList = yield this.financeService.getCostCentersWithCompanyCodeByEntity(entityId);
            const costCenterId = [];
            costCenterList.forEach((costCenter) => {
                costCenterId.push((0, lodash_1.toNumber)(costCenter.id));
            });
            const deductionDetail = yield this.afeProposalLimitDeductionRepository.totalDeductionByCostCenterOnStep(costCenterId, stepDetailMaster.workflowMasterStepId);
            const { finalStepDetail, entitySharingSameBucket } = yield this.workflowSharedChildLimitService.getActualStepDetail(stepDetailMaster, {
                entityId: (0, lodash_1.toNumber)(entityId),
                aggregateLimit: stepDetailMaster.aggregateLimit
            }, true);
            const costCenterPayload = [];
            costCenterList.forEach((costCenterDetail) => {
                let totalDeduction = 0;
                let deductions = [];
                deductionDetail.forEach((deduction) => {
                    if ((0, lodash_1.toNumber)(deduction.costCenterId) === (0, lodash_1.toNumber)(costCenterDetail.id)) {
                        totalDeduction = (0, lodash_1.toNumber)(totalDeduction) + (0, lodash_1.toNumber)(deduction.amount);
                        deductions.push((0, helpers_1.singleObjectToInstance)(get_aggregate_limit_balance_dto_1.DeductionDto, deduction));
                    }
                });
                const stepsDetails = Object.assign(Object.assign(Object.assign({}, costCenterDetail), finalStepDetail), { title: costCenterDetail.name, limitDeduction: totalDeduction, deductions: deductions.length ? (deductions.sort((a, b) => b.updatedOn - a.updatedOn)) : [] });
                costCenterPayload.push(stepsDetails);
            });
            return (0, helpers_1.multiObjectToInstance)(get_aggregate_limit_balance_dto_1.GetAggregateLimitbalanceDTO, costCenterPayload);
        });
    }
    getWorkflowStepHistory(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.historyApiClient.getRequestHistory(id, enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP);
            return result.map(d => (0, class_transformer_1.instanceToPlain)(new get_history_response_dto_1.GetHistoryResponseDTO(d)));
        });
    }
    getExceptionalOverriddenSteps(stepId) {
        return __awaiter(this, void 0, void 0, function* () {
            const stepDetail = yield this.workflowMasterStepRepository.getWorkflowStepById(stepId);
            if (stepDetail && !stepDetail.inherited) {
                const overridenIdsList = yield this.workflowMasterSettingRepository.getOverridenWorkflowIdsByParentId(stepDetail.workflowMasterSettingId);
                if (overridenIdsList.length) {
                    const overridenStepList = yield this.workflowMasterStepRepository.getWorkflowStepDetailsByCondition({
                        inherited: true,
                        workflowMasterStepId: stepDetail.workflowMasterStepId,
                        workflowMasterSettingId: this.sequlizeOperator.inOperator(overridenIdsList),
                    });
                    let finalExceptionList = [];
                    let sharedBucketEntityIds = [];
                    let allBusinessEntities;
                    if (overridenStepList.length) {
                        sharedBucketEntityIds = yield this.workflowSharedBucketLimitRepository.getAllBucketSharedEntityIdByStepId(stepDetail.workflowMasterStepId);
                        allBusinessEntities = yield this.adminApiClient.getAllBusinessHierarchy();
                    }
                    for (let i = 0; i < overridenStepList.length; i++) {
                        if ((overridenStepList[i].singleLimit !== stepDetail.singleLimit ||
                            overridenStepList[i].aggregateLimit !== stepDetail.aggregateLimit ||
                            overridenStepList[i].lengthOfCommitment !== stepDetail.lengthOfCommitment)
                            &&
                                !sharedBucketEntityIds.includes((0, lodash_1.toNumber)(overridenStepList[i].workflowMasterSetting.entityId))
                            && this.validateParentEntityHasSameLimit(allBusinessEntities, overridenStepList[i].workflowMasterSetting.entityId, overridenStepList[i], overridenStepList)) {
                            const _a = overridenStepList[i], { workflowMasterSetting } = _a, workflowSteps = __rest(_a, ["workflowMasterSetting"]);
                            finalExceptionList.push(Object.assign(Object.assign({}, workflowSteps), { entityTitle: workflowMasterSetting.entityTitle, entityCode: workflowMasterSetting.entityCode }));
                        }
                    }
                    return (0, helpers_1.multiObjectToInstance)(get_exception_steps_response_dto_1.GetExceptionStepsResponseDTO, finalExceptionList);
                }
            }
            return [];
        });
    }
    validateParentEntityHasSameLimit(allBusinessEntities, entityId, currentWorkflowStep, allOverridenWorkflowSteps) {
        var _a;
        let parentIds = ((_a = this.adminApiClient.getPath([allBusinessEntities], `${entityId}`)) === null || _a === void 0 ? void 0 : _a.map(e => Number(e.id))) || [];
        parentIds = parentIds.filter((parentEntityId) => {
            return (parentEntityId !== entityId);
        });
        let differParentLimit = true;
        allOverridenWorkflowSteps.forEach((workflowStep) => {
            if ((currentWorkflowStep.singleLimit === workflowStep.singleLimit &&
                currentWorkflowStep.aggregateLimit === workflowStep.aggregateLimit &&
                currentWorkflowStep.lengthOfCommitment === workflowStep.lengthOfCommitment) &&
                parentIds.includes((0, lodash_1.toNumber)(workflowStep.workflowMasterSetting.entityId))) {
                differParentLimit = false;
                return;
            }
        });
        return differParentLimit;
    }
};
WorkflowMasterStepService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_2.WorkflowMasterSettingRepository,
        repositories_2.WorkflowMasterStepRepository,
        clients_1.AdminApiClient,
        repositories_1.AfeProposalLimitDeductionRepository,
        repositories_2.WorkflowSharedChildLimitRepository,
        repositories_1.AfeProposalLimitDeductionRepository,
        workflow_shared_child_limit_service_1.WorkflowSharedChildLimitService,
        repositories_2.WorkflowSharedBucketLimitRepository,
        helpers_1.DatabaseHelper,
        helpers_1.SequlizeOperator,
        services_1.FinanceService,
        clients_1.HistoryApiClient])
], WorkflowMasterStepService);
exports.WorkflowMasterStepService = WorkflowMasterStepService;
//# sourceMappingURL=workflow-master-step.service.js.map