import { SCHEDULER_TYPE } from 'src/shared/enums';
import { QUEUE_LOG_ACTION } from 'src/shared/enums/queue-log-action-type.enum';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { Scheduler } from '../models';
export declare class SchedulerRepository extends BaseRepository<Scheduler> {
    constructor();
    getSchedulerByType(type: SCHEDULER_TYPE): Promise<Scheduler[]>;
    getSchedulerByTypeAndParameters(type: SCHEDULER_TYPE, parameters: {
        entityId?: number;
        action?: QUEUE_LOG_ACTION;
        finalApproval: boolean;
    }): Promise<Scheduler[]>;
    updateLastRunAt(id: number, date: Date, currentContext: CurrentContext): Promise<number>;
}
