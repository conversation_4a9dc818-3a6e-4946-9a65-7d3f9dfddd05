{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/MyWorkspace/Projects/DpWorld/AFE_Revamp/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { FormArray, FormControl } from '@angular/forms';\nimport { toNumber } from 'lodash';\nimport { BehaviorSubject, Subject, takeUntil } from 'rxjs';\nimport { PAGINATION_DEFAULT_LIMIT } from 'src/app/core/constants/contants';\nimport { GlobalUrls, ReplaceUrlVariable } from 'src/app/core/constants/urls.constants';\nimport { BudgetTypeLabel } from 'src/app/core/enums/Metadata';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/api/proposal.service\";\nimport * as i2 from \"src/app/core/services/common/common.service\";\nimport * as i3 from \"src/app/core/services/common/spinner.service\";\nimport * as i4 from \"src/app/core/services/common/permission.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"@core/services/common/localStorage.service\";\nimport * as i8 from \"@core/services/api\";\nimport * as i9 from \"@core/data/request-type\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"../modals/modal/modal.component\";\nimport * as i13 from \"ng-inline-svg-2\";\nimport * as i14 from \"ngx-pagination\";\nimport * as i15 from \"../filter-elements/filter-badges/filter-badges.component\";\nimport * as i16 from \"../filter-elements/afe-filter-modal/afe-filter-modal.component\";\nimport * as i17 from \"../no-data/no-data.component\";\nimport * as i18 from \"../skeleton-loader/list-skeleton-loader/list-skeleton-loader.component\";\nconst _c0 = [\"filterModal\"];\nconst _c1 = [\"exportModal\"];\n\nfunction AfeListComponent_ng_container_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"a\", 20);\n    i0.ɵɵlistener(\"click\", function AfeListComponent_ng_container_2_div_13_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.openExportModal());\n    });\n    i0.ɵɵelement(2, \"span\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen005.svg\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"FORM.BUTTON.EXPORT\"), \" \");\n  }\n}\n\nfunction AfeListComponent_ng_container_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"app-filter-badges\", 29);\n    i0.ɵɵlistener(\"resetEventTrigger\", function AfeListComponent_ng_container_2_div_14_Template_app_filter_badges_resetEventTrigger_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.resetEventTrigger());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"localStorageKey\", ctx_r8.filterLocalStorageKey);\n  }\n}\n\nfunction AfeListComponent_ng_container_2_div_15_div_1_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const afeDetail_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" ( \", afeDetail_r17.location.title, \" ) \");\n  }\n}\n\nfunction AfeListComponent_ng_container_2_div_15_div_1_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 45)(2, \"div\", 46)(3, \"span\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"span\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const afeDetail_r17 = i0.ɵɵnextContext().$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"LIST.BUDGET_TYPE\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r20.getBudgetTypeTitle(afeDetail_r17.budgetType));\n  }\n}\n\nfunction AfeListComponent_ng_container_2_div_15_div_1_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 45)(2, \"div\", 46)(3, \"span\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"span\", 49);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const afeDetail_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, \"LIST.APPROVAL_PENDING_WITH\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(afeDetail_r17.approvalPendingWith.join(\", \"));\n  }\n}\n\nfunction AfeListComponent_ng_container_2_div_15_div_1_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54)(2, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function AfeListComponent_ng_container_2_div_15_div_1_div_71_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const afeDetail_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.nagivateToTaskAction(afeDetail_r17.task));\n    });\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"title\", i0.ɵɵpipeBind1(3, 2, \"FORM.BUTTON.REVIEW\"));\n    i0.ɵɵpropertyInterpolate(\"translate\", i0.ɵɵpipeBind1(4, 4, \"FORM.BUTTON.REVIEW\"));\n  }\n}\n\nconst _c2 = function (a2) {\n  return [\"/afe\", \"afe-detail\", a2];\n};\n\nfunction AfeListComponent_ng_container_2_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38)(3, \"div\", 39);\n    i0.ɵɵelement(4, \"img\", 40);\n    i0.ɵɵelementStart(5, \"a\", 41)(6, \"span\", 42);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 43)(9, \"span\", 44);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 39)(12, \"div\", 45)(13, \"div\", 46)(14, \"span\", 47);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 48)(18, \"span\", 49);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 39)(21, \"div\", 45)(22, \"div\", 46)(23, \"span\", 47);\n    i0.ɵɵtext(24);\n    i0.ɵɵpipe(25, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 48)(27, \"span\", 49);\n    i0.ɵɵtext(28);\n    i0.ɵɵtemplate(29, AfeListComponent_ng_container_2_div_15_div_1_ng_container_29_Template, 2, 1, \"ng-container\", 6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"div\", 39)(31, \"div\", 45)(32, \"div\", 46)(33, \"span\", 47);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 48)(37, \"span\", 49);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(39, AfeListComponent_ng_container_2_div_15_div_1_div_39_Template, 9, 4, \"div\", 50);\n    i0.ɵɵelementStart(40, \"div\", 39)(41, \"div\", 45)(42, \"div\", 46)(43, \"span\", 47);\n    i0.ɵɵtext(44);\n    i0.ɵɵpipe(45, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 48)(47, \"span\", 49);\n    i0.ɵɵtext(48);\n    i0.ɵɵpipe(49, \"currency\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(50, \"div\", 39)(51, \"div\", 45)(52, \"div\", 46)(53, \"span\", 47);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 48)(57, \"span\", 49);\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(60, \"div\", 39)(61, \"div\", 45)(62, \"div\", 46)(63, \"span\", 47);\n    i0.ɵɵtext(64);\n    i0.ɵɵpipe(65, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 48)(67, \"span\", 49);\n    i0.ɵɵtext(68);\n    i0.ɵɵpipe(69, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(70, AfeListComponent_ng_container_2_div_15_div_1_div_70_Template, 9, 4, \"div\", 51);\n    i0.ɵɵtemplate(71, AfeListComponent_ng_container_2_div_15_div_1_div_71_Template, 5, 6, \"div\", 52);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const afeDetail_r17 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(42, _c2, afeDetail_r17.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", afeDetail_r17.projectReferenceNumber, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", afeDetail_r17.userStatus, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 19, \"LIST.PROJECT_NAME\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(afeDetail_r17.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(25, 21, \"LIST.ENTITY_NAME\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", afeDetail_r17.entityTitle, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", afeDetail_r17 == null ? null : afeDetail_r17.location == null ? null : afeDetail_r17.location.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(35, 23, \"LIST.REQUEST_TYPE\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(afeDetail_r17.afeRequestType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", afeDetail_r17.budgetType);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(45, 25, \"LIST.EXPENSE_AMOUNT\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(+afeDetail_r17.totalAmount ? i0.ɵɵpipeBind4(49, 27, afeDetail_r17.totalAmount, afeDetail_r17.currencyType, \"symbol\", \"1.2-2\") : 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(55, 32, \"LIST.CREATED_AT\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(59, 34, afeDetail_r17.createdOn, \"medium\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(65, 37, \"LIST.UPDATED_AT\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(69, 39, afeDetail_r17.updatedOn, \"medium\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", afeDetail_r17 == null ? null : afeDetail_r17.approvalPendingWith == null ? null : afeDetail_r17.approvalPendingWith.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", afeDetail_r17.task);\n  }\n}\n\nconst _c3 = function (a0, a1, a2) {\n  return {\n    itemsPerPage: a0,\n    currentPage: a1,\n    totalItems: a2\n  };\n};\n\nfunction AfeListComponent_ng_container_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵtemplate(1, AfeListComponent_ng_container_2_div_15_div_1_Template, 72, 44, \"div\", 30);\n    i0.ɵɵpipe(2, \"paginate\");\n    i0.ɵɵelementStart(3, \"div\", 31)(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"translate\");\n    i0.ɵɵelementStart(8, \"span\", 34);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"lowercase\");\n    i0.ɵɵpipe(11, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 34);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"pagination-controls\", 35);\n    i0.ɵɵlistener(\"pageChange\", function AfeListComponent_ng_container_2_div_15_Template_pagination_controls_pageChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.handlePageChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 8, ctx_r9.afeList, i0.ɵɵpureFunction3(19, _c3, ctx_r9.pagination.limit, ctx_r9.pagination.page, ctx_r9.totalRecord)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \", ctx_r9.pagination.page === 1 ? 1 : (ctx_r9.pagination.page - 1) * ctx_r9.pagination.limit + 1, \" \", i0.ɵɵpipeBind1(7, 11, \"COMMON.TO\"), \" \", ctx_r9.pagination.limit * ctx_r9.pagination.page <= ctx_r9.totalRecord ? ctx_r9.pagination.limit * ctx_r9.pagination.page : ctx_r9.totalRecord, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(10, 13, i0.ɵɵpipeBind1(11, 15, \"COMMON.RECORD\")), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(14, 17, \"COMMON.OF\"), \" \", ctx_r9.totalRecord, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"responsive\", true);\n  }\n}\n\nfunction AfeListComponent_ng_container_2_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-no-data\", 56);\n    i0.ɵɵpipe(1, \"translate\");\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"message\", i0.ɵɵpipeBind1(1, 1, \"ERROR.NO_RECORD_FOUND\"));\n  }\n}\n\nfunction AfeListComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"div\", 16)(3, \"div\")(4, \"h3\", 17)(5, \"span\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 19)(8, \"div\")(9, \"a\", 20);\n    i0.ɵɵlistener(\"click\", function AfeListComponent_ng_container_2_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.openFilterModal());\n    });\n    i0.ɵɵelement(10, \"span\", 21);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, AfeListComponent_ng_container_2_div_13_Template, 5, 4, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, AfeListComponent_ng_container_2_div_14_Template, 2, 1, \"div\", 23);\n    i0.ɵɵtemplate(15, AfeListComponent_ng_container_2_div_15_Template, 16, 23, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 25);\n    i0.ɵɵtemplate(17, AfeListComponent_ng_container_2_ng_template_17_Template, 2, 3, \"ng-template\", null, 26, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r10 = i0.ɵɵreference(18);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.listTitle, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen031.svg\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(12, 7, \"FORM.BUTTON.FILTER\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showExport && ctx_r0.afeList.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.storedFilterData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.afeList.length)(\"ngIfElse\", _r10);\n  }\n}\n\nfunction AfeListComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-list-skeleton-loader\", 57);\n  }\n\n  if (rf & 2) {\n    i0.ɵɵproperty(\"loaderCount\", 4);\n  }\n}\n\nfunction AfeListComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-afe-filter-modal\", 58);\n    i0.ɵɵlistener(\"filterPayloadEvent\", function AfeListComponent_ng_container_7_Template_app_afe_filter_modal_filterPayloadEvent_1_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.getFilterPayload($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"localStorageKey\", ctx_r4.filterLocalStorageKey);\n  }\n}\n\nfunction AfeListComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60);\n    i0.ɵɵelement(2, \"input\", 61);\n    i0.ɵɵelementStart(3, \"label\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const i_r36 = ctx.index;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControlName\", i_r36);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.exportColumns[i_r36].col, \" \");\n  }\n}\n\nexport class AfeListComponent {\n  constructor(proposalService, commonService, spinnerService, permissionService, cdr, router, route, translateService, localStorageService, reportService, requestTypeData, fb) {\n    this.proposalService = proposalService;\n    this.commonService = commonService;\n    this.spinnerService = spinnerService;\n    this.permissionService = permissionService;\n    this.cdr = cdr;\n    this.router = router;\n    this.route = route;\n    this.translateService = translateService;\n    this.localStorageService = localStorageService;\n    this.reportService = reportService;\n    this.requestTypeData = requestTypeData;\n    this.fb = fb;\n    this.afeList = [];\n    this.loading = true;\n    this.destroy$ = new Subject();\n    this.pagination = {\n      limit: PAGINATION_DEFAULT_LIMIT,\n      offset: 0,\n      page: 1\n    };\n    this.totalRecord = 0;\n    this.filterModalTitle = this.translateService.instant('MENU.FILTER');\n    this.modalDismissButtonLabel = this.translateService.instant('FORM.BUTTON.APPLY');\n    this.exportToExcelTitle = this.translateService.instant('MENU.CHOOSE_COLUMNS_FOR_EXCEL_EXPORT');\n    this.downloadButtonLabel = this.translateService.instant('FORM.BUTTON.DOWNLOAD');\n    this.filterChangeEvent = new BehaviorSubject(true);\n    this.isFilterModalReady = false;\n    this.filterModalConfig = {\n      modalTitle: this.filterModalTitle,\n      dismissButtonLabel: this.modalDismissButtonLabel,\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.RESET'),\n      onDismiss: () => {\n        this.isFilterModalReady = false;\n        return true;\n      },\n      shouldClose: () => {\n        this.isFilterModalReady = false;\n        this.pagination.page = 1;\n        this.pagination.offset = 0; //this.cdr.detectChanges();\n\n        this.localStorageService.set(this.filterLocalStorageKey, '');\n        this.isFilterModalReady = true;\n        this.cdr.detectChanges();\n        return false;\n      },\n      shouldDismiss: () => {\n        this.pagination.page = 1;\n        this.pagination.offset = 0;\n        this.cdr.detectChanges();\n        this.localStorageService.set(this.filterLocalStorageKey, this.filtersData);\n        this.filterChangeEvent.next(true);\n        this.isFilterModalReady = false;\n        return true;\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'lg',\n        keyboard: false,\n        centered: true\n      }\n    };\n    this.exportModalConfig = {\n      modalTitle: this.exportToExcelTitle,\n      dismissButtonLabel: this.downloadButtonLabel,\n      closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),\n      onDismiss: () => {\n        return true;\n      },\n      shouldClose: () => {\n        return true;\n      },\n      shouldDismiss: () => {\n        this.exportToExcelSheet();\n        return false;\n      },\n      modalDialogConfig: {\n        backdrop: 'static',\n        size: 'md',\n        keyboard: false,\n        centered: true\n      }\n    };\n    this.exportColumns = [{\n      id: 'Business Unit Code',\n      col: 'Business Unit Code'\n    }, {\n      id: 'Business Unit Name',\n      col: 'Business Unit Name'\n    }, {\n      id: 'Proposal Type',\n      col: 'Proposal Type'\n    }, {\n      id: 'AFE Number',\n      col: 'AFE Number'\n    }, {\n      id: 'AFE Name',\n      col: 'AFE Name'\n    }, {\n      id: 'Budget Type',\n      col: 'Budget Type'\n    }, {\n      id: 'Type',\n      col: 'Type'\n    }, {\n      id: 'Total Expenditure (In USD)',\n      col: 'Total Expenditure'\n    }, {\n      id: 'Submitter',\n      col: 'Submitter'\n    }, {\n      id: 'Submission Date',\n      col: 'Submission Date'\n    }, {\n      id: 'Expense Summary',\n      col: 'Expense Summary'\n    }, {\n      id: 'Cost Centers',\n      col: 'Cost Centers'\n    }, {\n      id: 'GL Codes',\n      col: 'GL Codes'\n    }, {\n      id: 'Budget Reference Number',\n      col: 'Budget Reference Number'\n    }, {\n      id: 'Status',\n      col: 'Status'\n    }, {\n      id: 'Approved / Rejected On',\n      col: 'Approved / Rejected On'\n    }, {\n      id: 'Workflow Year',\n      col: 'Workflow Year'\n    }]; // Create a FormControl for each available colunm, initialize them as unchecked, and put them in an array\n\n    const formControls = this.exportColumns.map(_ => new FormControl(true)); // Create a FormControl for the select/unselect all checkbox\n\n    const selectAllControl = new FormControl(true);\n    this.exportColumnsForm = this.fb.group({\n      exportColumns: new FormArray(formControls),\n      selectAll: selectAllControl\n    });\n  }\n\n  ngOnInit() {\n    var _a, _b, _c;\n\n    this.spinnerService.startSpinner();\n    this.loading = true;\n    this.route.data.subscribe(data => {\n      if (data.permissionRequired) {\n        this.commonService.isPermissionFetched.subscribe(permissionReady => {\n          if (permissionReady) {\n            const permissionList = this.commonService.permissionAll;\n\n            if (permissionList.length) {\n              const ifPermission = this.permissionService.checkPermissionByName(data.permissionRequired);\n\n              if (ifPermission) {\n                this.localStorageService.watch(this.filterLocalStorageKey).pipe(takeUntil(this.destroy$)).subscribe({\n                  next: val => {\n                    this.forApprovalHistory = data.isApprovalHistory;\n                    this.storedFilterData = val;\n                    this.fetchList();\n                  }\n                });\n              } else {\n                this.spinnerService.stopSpinner();\n                this.router.navigate([GlobalUrls.ACCESS_DENIED]);\n              }\n            } else {\n              this.spinnerService.stopSpinner();\n              this.router.navigate([GlobalUrls.ACCESS_DENIED]);\n            }\n          }\n        });\n      } else {\n        this.localStorageService.watch(this.filterLocalStorageKey).pipe(takeUntil(this.destroy$)).subscribe({\n          next: val => {\n            this.forApprovalHistory = data.isApprovalHistory;\n            this.storedFilterData = val;\n            this.fetchList();\n          }\n        });\n      }\n    }); // Subscribe to changes on the selectAll checkbox\n\n    (_b = (_a = this.exportColumnsForm) === null || _a === void 0 ? void 0 : _a.get('selectAll')) === null || _b === void 0 ? void 0 : _b.valueChanges.subscribe(bool => {\n      var _a, _b;\n\n      (_b = (_a = this.exportColumnsForm) === null || _a === void 0 ? void 0 : _a.get('exportColumns')) === null || _b === void 0 ? void 0 : _b.patchValue(Array(this.exportColumns.length).fill(bool), {\n        emitEvent: false\n      });\n    }); // Subscribe to changes on the colunm name checkboxes\n\n    (_c = this.exportColumnsForm.get('exportColumns')) === null || _c === void 0 ? void 0 : _c.valueChanges.subscribe(val => {\n      var _a, _b;\n\n      const allSelected = val.every(bool => bool);\n\n      if (((_a = this.exportColumnsForm.get('selectAll')) === null || _a === void 0 ? void 0 : _a.value) !== allSelected) {\n        (_b = this.exportColumnsForm.get('selectAll')) === null || _b === void 0 ? void 0 : _b.patchValue(allSelected, {\n          emitEvent: false\n        });\n      }\n    });\n  }\n\n  getExportColumnsControls() {\n    return this.exportColumnsForm.get('exportColumns').controls;\n  }\n\n  isAnyColunmSelected() {\n    return this.getExportColumnsControls().some(control => control.value);\n  }\n  /**\r\n   * Creating body payload for afe list filter.\r\n   * @param filterSelection\r\n   * @returns\r\n   */\n\n\n  filterPayload(filterSelection) {\n    var _a, _b, _c, _d, _e, _f;\n\n    if (filterSelection) {\n      const {\n        obj: filterObject\n      } = filterSelection;\n      let filterRequest = {};\n\n      for (let property in filterObject) {\n        if (filterObject[property]) {\n          switch (property) {\n            case 'requestTypes':\n            case 'projectComponents':\n            case 'budgetTypes':\n            case 'statuses':\n            case 'businessEntities':\n            case 'submissionTypes':\n            case 'afeTypes':\n            case 'submittedBy':\n              if ((_a = filterObject[property]) === null || _a === void 0 ? void 0 : _a.length) {\n                filterRequest = Object.assign(Object.assign({}, filterRequest), {\n                  [property]: (_b = filterObject[property]) === null || _b === void 0 ? void 0 : _b.map(f => f.id)\n                });\n              }\n\n              break;\n\n            case 'lengthOfCommitments':\n              if ((_c = filterObject[property]) === null || _c === void 0 ? void 0 : _c.length) {\n                filterRequest = Object.assign(Object.assign({}, filterRequest), {\n                  [property]: (_d = filterObject[property]) === null || _d === void 0 ? void 0 : _d.map(f => f.title)\n                });\n              }\n\n              break;\n\n            case 'costCenters':\n            case 'additionalLocations':\n              if ((_e = filterObject[property]) === null || _e === void 0 ? void 0 : _e.length) {\n                filterRequest = Object.assign(Object.assign({}, filterRequest), {\n                  [property]: (_f = filterObject[property]) === null || _f === void 0 ? void 0 : _f.map(f => toNumber(f.id))\n                });\n              }\n\n              break;\n\n            case 'fromAmount':\n            case 'toAmount':\n            case 'projectName':\n            case 'afeReferenceNumber':\n              filterRequest = Object.assign(Object.assign({}, filterRequest), {\n                [property]: filterObject[property]\n              });\n              break;\n\n            case 'workflowYear':\n              if (filterObject[property]) {\n                filterRequest = Object.assign(Object.assign({}, filterRequest), {\n                  [property]: toNumber(filterObject[property])\n                });\n              }\n\n              break;\n\n            case 'fromSubmissionDate':\n            case 'toSubmissionDate':\n            case 'fromApprovalDate':\n            case 'toApprovalDate':\n              const {\n                year,\n                month,\n                day\n              } = filterObject[property];\n              filterRequest = Object.assign(Object.assign({}, filterRequest), {\n                [property]: `${year}-${month}-${day}`\n              });\n              break;\n          }\n        }\n      }\n\n      return filterRequest;\n    }\n\n    return null;\n  }\n\n  resetEventTrigger() {\n    this.pagination.page = 1;\n    this.pagination.offset = 0;\n    this.cdr.detectChanges();\n  }\n\n  fetchList() {\n    const filters = this.filterPayload(this.storedFilterData);\n    this.spinnerService.startSpinner();\n    console.log(filters);\n    this.proposalService.getSubmittedApprovalList(this.pagination, this.forApprovalHistory, filters).subscribe({\n      next: response => {\n        this.afeList = response.records.map(record => {\n          var _a;\n\n          return Object.assign(Object.assign({}, record), {\n            afeRequestType: (_a = this.requestTypeData.getRequestDetailFromId(record.afeRequestTypeId, true)) === null || _a === void 0 ? void 0 : _a.title\n          });\n        });\n        this.totalRecord = response.total;\n        this.spinnerService.stopSpinner();\n        this.loading = false;\n        this.cdr.detectChanges();\n      },\n      error: _ => {\n        Swal.fire({\n          icon: 'error',\n          title: this.translateService.instant('SWAL.OOPS'),\n          text: _.message\n        });\n        this.afeList = [];\n        this.loading = false;\n        this.spinnerService.stopSpinner();\n        this.cdr.detectChanges();\n      }\n    });\n  }\n\n  exportToExcelSheet() {\n    const selectedColumns = this.exportColumnsForm.value.exportColumns.map((checked, index) => checked ? this.exportColumns[index].id : null).filter(value => value !== null);\n\n    if (!this.isAnyColunmSelected()) {\n      Swal.fire({\n        icon: 'warning',\n        title: this.translateService.instant('SWAL.OOPS'),\n        text: this.translateService.instant('SWAL.SELECT_AT_LEAST_ONE_COLUMN')\n      });\n      return;\n    }\n\n    const filters = this.filterPayload(this.storedFilterData);\n    this.reportService.downloadAfeListExcelReport(filters, selectedColumns);\n  }\n\n  nagivateToTaskAction(task) {\n    const {\n      relUrl,\n      taskId\n    } = task;\n    this.router.navigateByUrl(ReplaceUrlVariable(`${relUrl}`, {\n      taskId\n    }));\n  }\n\n  getBudgetTypeTitle(budgetType) {\n    return BudgetTypeLabel[budgetType];\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.unsubscribe();\n  }\n\n  handlePageChange(event) {\n    this.spinnerService.startSpinner();\n    this.loading = true;\n    this.pagination.offset = +event - 1;\n    this.pagination.page = event;\n    this.fetchList();\n  }\n\n  getFilterPayload(event) {\n    this.filtersData = event;\n  }\n\n  openFilterModal() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.isFilterModalReady = true;\n\n      _this.cdr.detectChanges();\n\n      return yield _this.filterModal.open();\n    })();\n  }\n\n  openExportModal() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      _this2.cdr.detectChanges();\n\n      return yield _this2.exportModal.open();\n    })();\n  }\n\n}\n\nAfeListComponent.ɵfac = function AfeListComponent_Factory(t) {\n  return new (t || AfeListComponent)(i0.ɵɵdirectiveInject(i1.ProposalService), i0.ɵɵdirectiveInject(i2.CommonService), i0.ɵɵdirectiveInject(i3.SpinnerService), i0.ɵɵdirectiveInject(i4.PermissionService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.LocalStorageService), i0.ɵɵdirectiveInject(i8.ReportService), i0.ɵɵdirectiveInject(i9.RequestTypeData), i0.ɵɵdirectiveInject(i10.FormBuilder));\n};\n\nAfeListComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AfeListComponent,\n  selectors: [[\"app-afe-list\"]],\n  viewQuery: function AfeListComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterModal = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.exportModal = _t.first);\n    }\n  },\n  inputs: {\n    filterLocalStorageKey: \"filterLocalStorageKey\",\n    listTitle: \"listTitle\",\n    showExport: \"showExport\"\n  },\n  decls: 20,\n  vars: 7,\n  consts: [[1, \"d-flex\", \"flex-column\"], [1, \"div-item\", \"card\", \"mb-0\", \"mb-xl-0\", \"border\", \"rounded\"], [4, \"ngIf\", \"ngIfElse\"], [\"loadingPage\", \"\"], [3, \"modalConfig\"], [\"filterModal\", \"\"], [4, \"ngIf\"], [\"exportModal\", \"\"], [1, \"w-100\", \"p-5\", \"bg-body\", \"rounded\", 3, \"formGroup\"], [1, \"row\", \"p-1\", \"p-lg-3\", \"p-md-3\", \"p-sm-3\"], [1, \"col-12\", \"col-md-12\", \"p-0\", \"m-0\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"value\", \"\", \"formControlName\", \"selectAll\", 1, \"form-check-input\"], [1, \"form-label\", \"fw-bold\"], [\"class\", \"col-6 p-0 m-0\", \"formArrayName\", \"exportColumns\", 4, \"ngFor\", \"ngForOf\"], [1, \"card\"], [1, \"d-flex\", \"flex-lg-row\", \"flex-md-row\", \"flex-sm-row\", \"flex-column\", \"p-5\", \"py-3\", \"justify-content-between\", \"border-bottom\", \"border-gray-300\"], [1, \"card-title\", \"mt-3\"], [1, \"card-label\", \"fw-bolder\", \"fs-3\", \"mb-1\"], [1, \"d-flex\", \"justify-content-end\", \"py-1\"], [\"data-kt-menu-trigger\", \"click\", \"data-kt-menu-placement\", \"bottom-end\", \"data-kt-menu-flip\", \"top-end\", 1, \"btn\", \"btn-sm\", \"btn-flex\", \"btn-primary\", \"btn-active-primary\", \"fw-bold\", \"cursor-pointer\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-5\", \"svg-icon-gray-500\", \"me-1\", 3, \"inlineSVG\"], [\"class\", \"px-2\", 4, \"ngIf\"], [\"class\", \"mt-1 mx-8 my-2 py-3 align-middle\", 4, \"ngIf\"], [\"class\", \"d-flex flex-column\", 4, \"ngIf\", \"ngIfElse\"], [1, \"row\"], [\"noDataMessage\", \"\"], [1, \"px-2\"], [1, \"mt-1\", \"mx-8\", \"my-2\", \"py-3\", \"align-middle\"], [3, \"localStorageKey\", \"resetEventTrigger\"], [\"class\", \"row mx-5 my-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"px-2\", \"pt-3\", \"border-top\", \"border-gray-300\", \"mx-0\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"fw-bold\", \"text-gray-900\"], [1, \"totalRecord\"], [\"previousLabel\", \"Prev\", \"nextLabel\", \"Next\", 3, \"responsive\", \"pageChange\"], [1, \"row\", \"mx-5\", \"my-3\"], [1, \"div-item\"], [1, \"row\", \"p-1\", \"p-lg-3\", \"p-md-3\", \"p-sm-3\", \"pb-3\"], [1, \"col-12\", \"col-md-6\", \"p-0\", \"m-0\"], [\"width\", \"15px\", \"height\", \"20px\", \"src\", \"./assets/media/svg/icons/dpw-icons/nextArrow.png\", \"alt\", \"S.No\"], [1, \"d-value\", \"text-dark\", \"fw-bold\", \"text-hover-primary\", \"fs-6\", 3, \"routerLink\"], [1, \"px-1\", \"underline\"], [1, \"col-12\", \"col-md-6\", \"p-0\", \"m-0\", \"status-block\", \"text-right\"], [1, \"badge\", \"fw-bold\", \"me-auto\", \"px-2\", \"badge\", \"label-light-primary\", \"mx-1\", \"fs-8\", \"py-1\", \"ms-5\"], [1, \"d-flex\", \"justify-content-start\", \"pb-1\"], [1, \"d-flex\", \"flex-nowrap\", 2, \"min-width\", \"110px\"], [1, \"fw-bold\", \"text-gray-600\", \"px-6\", \"pe-2\"], [1, \"d-flex\", \"flex-wrap\", \"justify-content-start\"], [1, \"fw-bold\"], [\"class\", \"col-12 col-md-6 p-0 m-0\", 4, \"ngIf\"], [\"class\", \"col-12 col-md-12 p-0 m-0\", 4, \"ngIf\"], [\"class\", \"col-12 col-md-12 p-0 m-0 pt-3\", 4, \"ngIf\"], [1, \"col-12\", \"col-md-12\", \"p-0\", \"m-0\", \"pt-3\"], [1, \"d-flex\", \"flex-wrap\", \"justify-content-end\", \"px-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"editBtn\", \"btnRounded\", \"fw-bold\", \"ps-4\", \"pe-4\", \"py-1\", \"mx-2\", 3, \"title\", \"translate\", \"click\"], [3, \"message\"], [3, \"loaderCount\"], [3, \"localStorageKey\", \"filterPayloadEvent\"], [\"formArrayName\", \"exportColumns\", 1, \"col-6\", \"p-0\", \"m-0\"], [1, \"form-check\", \"mt-3\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"formControlName\"]],\n  template: function AfeListComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtemplate(2, AfeListComponent_ng_container_2_Template, 19, 9, \"ng-container\", 2);\n      i0.ɵɵtemplate(3, AfeListComponent_ng_template_3_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementStart(5, \"app-modal\", 4, 5);\n      i0.ɵɵtemplate(7, AfeListComponent_ng_container_7_Template, 2, 1, \"ng-container\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"app-modal\", 4, 7);\n      i0.ɵɵelementContainerStart(10);\n      i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"div\", 11);\n      i0.ɵɵelement(15, \"input\", 12);\n      i0.ɵɵelementStart(16, \"label\", 13);\n      i0.ɵɵtext(17, \" Select/Deselect all \");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(18, \"div\", 9);\n      i0.ɵɵtemplate(19, AfeListComponent_div_19_Template, 5, 2, \"div\", 14);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementContainerEnd();\n      i0.ɵɵelementEnd()()();\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(4);\n\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading)(\"ngIfElse\", _r1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"modalConfig\", ctx.filterModalConfig);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isFilterModalReady);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"modalConfig\", ctx.exportModalConfig);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"formGroup\", ctx.exportColumnsForm);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.getExportColumnsControls());\n    }\n  },\n  dependencies: [i11.NgForOf, i11.NgIf, i5.RouterLinkWithHref, i10.CheckboxControlValueAccessor, i10.NgControlStatus, i10.NgControlStatusGroup, i10.FormGroupDirective, i10.FormControlName, i10.FormArrayName, i6.TranslateDirective, i12.ModalComponent, i13.InlineSVGDirective, i14.PaginationControlsComponent, i15.FilterBadgesComponent, i16.AfeFilterModalComponent, i17.NoDataComponent, i18.ListSkeletonLoaderComponent, i11.LowerCasePipe, i11.CurrencyPipe, i11.DatePipe, i6.TranslatePipe, i14.PaginatePipe],\n  styles: [\"@media (max-width: 767px) {\\n  .status-block[_ngcontent-%COMP%] {\\n    text-align: left !important;\\n  }\\n}\\n@media only screen and (min-width: 280px) and (max-width: 360px) {\\n  .totalRecord[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .headingClass[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    line-height: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFmZS1saXN0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0U7SUFDSSwyQkFBQTtFQUFKO0FBQ0Y7QUFJQTtFQUNFO0lBQ0EsYUFBQTtFQUZBOztFQU1BO0lBQ0UsZUFBQTtJQUNBLGlCQUFBO0VBSEY7QUFDRiIsImZpbGUiOiJhZmUtbGlzdC5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIlxyXG5AbWVkaWEgKG1heC13aWR0aDogNzY3cHgpIHtcclxuICAuc3RhdHVzLWJsb2NrIHtcclxuICAgICAgdGV4dC1hbGlnbjogbGVmdCAhaW1wb3J0YW50O1xyXG4gIH1cclxufVxyXG5cclxuXHJcbkBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogMjgwcHgpIGFuZCAobWF4LXdpZHRoOiAzNjBweCl7XHJcbiAgLnRvdGFsUmVjb3Jke1xyXG4gIGRpc3BsYXk6IG5vbmU7XHJcbiAgfVxyXG5cclxuXHJcbiAgLmhlYWRpbmdDbGFzc3tcclxuICAgIGZvbnQtc2l6ZTogMTFweDtcclxuICAgIGxpbmUtaGVpZ2h0OiAxMnB4O1xyXG59XHJcblxyXG59XHJcbiAgXHJcblxyXG4iXX0= */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,SAAT,EAAiCC,WAAjC,QAA+D,gBAA/D;AAOA,SAASC,QAAT,QAAyB,QAAzB;AACA,SAASC,eAAT,EAAsCC,OAAtC,EAA+CC,SAA/C,QAAgE,MAAhE;AACA,SAASC,wBAAT,QAAyC,iCAAzC;AACA,SAASC,UAAT,EAAqBC,kBAArB,QAA+C,uCAA/C;AACA,SAAyBC,eAAzB,QAAgD,6BAAhD;AAOA,OAAOC,IAAP,MAAiB,aAAjB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICUYC,gCAAuD,CAAvD,EAAuD,GAAvD,EAAuD,EAAvD;IAIIA;MAAAA;MAAA;MAAA,OAASA,yCAAT;IAA0B,CAA1B;IACAA;IAGAA;;IACFA;;;;IAJQA;IAAAA;IAGNA;IAAAA;;;;;;;;IAURA,gCAAuE,CAAvE,EAAuE,mBAAvE,EAAuE,EAAvE;IACqBA;MAAAA;MAAA;MAAA,OAAqBA,2CAArB;IAAwC,CAAxC;IACnBA;;;;;IAD6DA;IAAAA;;;;;;IAqD/CA;IACEA;IACFA;;;;;IADEA;IAAAA;;;;;;IAsBVA,gCAAkE,CAAlE,EAAkE,KAAlE,EAAkE,EAAlE,EAAkE,CAAlE,EAAkE,KAAlE,EAAkE,EAAlE,EAAkE,CAAlE,EAAkE,MAAlE,EAAkE,EAAlE;IAGoDA;;IAE1CA;IAENA,gCAAoD,CAApD,EAAoD,MAApD,EAAoD,EAApD;IACwBA;IAElBA;;;;;;IAP0CA;IAAAA;IAKxBA;IAAAA;;;;;;IAuD5BA,gCAAqF,CAArF,EAAqF,KAArF,EAAqF,EAArF,EAAqF,CAArF,EAAqF,KAArF,EAAqF,EAArF,EAAqF,CAArF,EAAqF,MAArF,EAAqF,EAArF;IAGoDA;;IAE1CA;IAENA,gCAAoD,CAApD,EAAoD,MAApD,EAAoD,EAApD;IACwBA;IAElBA;;;;;IAP0CA;IAAAA;IAKxBA;IAAAA;;;;;;;;IAM5BA,gCAAkE,CAAlE,EAAkE,KAAlE,EAAkE,EAAlE,EAAkE,CAAlE,EAAkE,QAAlE,EAAkE,EAAlE;IAGMA;MAAAA;MAAA;MAAA;MAAA,OAASA,gEAAT;IAA6C,CAA7C;;;IAEmDA;;;;IAH/BA;IAAAA;IAGpBA;;;;;;;;;;IA1JZA,gCASI,CATJ,EASI,KATJ,EASI,EATJ,EASI,CATJ,EASI,KATJ,EASI,EATJ,EASI,CATJ,EASI,KATJ,EASI,EATJ;IAaQA;IACAA,8BACsD,CADtD,EACsD,MADtD,EACsD,EADtD;IAGIA;IAAsCA;IAG5CA,gCAA6D,CAA7D,EAA6D,MAA7D,EAA6D,EAA7D;IAEIA;IACFA;IAGFA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAGoDA;;IAE1CA;IAENA,iCAAoD,EAApD,EAAoD,MAApD,EAAoD,EAApD;IACwBA;IAAoBA;IAKhDA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAGoDA;;IAE1CA;IAENA,iCAAoD,EAApD,EAAoD,MAApD,EAAoD,EAApD;IAEIA;IACAA;IAGFA;IAMNA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAGoDA;;IAE1CA;IAENA,iCAAoD,EAApD,EAAoD,MAApD,EAAoD,EAApD;IACwBA;IAElBA;IAIVA;IAeAA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAGoDA;;IAE1CA;IAENA,iCAAoD,EAApD,EAAoD,MAApD,EAAoD,EAApD;IACwBA;;IAQlBA;IAIVA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAGoDA;;IAE1CA;IAENA,iCAAoD,EAApD,EAAoD,MAApD,EAAoD,EAApD;IACwBA;;IAElBA;IAIVA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAGoDA;;IAE1CA;IAENA,iCAAoD,EAApD,EAAoD,MAApD,EAAoD,EAApD;IACwBA;;IAElBA;IAIVA;IAcAA;IAQFA;;;;;IA9IMA;IAAAA;IAEEA;IAAAA;IAKFA;IAAAA;IAOgDA;IAAAA;IAKxBA;IAAAA;IAQwBA;IAAAA;IAM5CA;IAAAA;IACeA;IAAAA;IAY6BA;IAAAA;IAKxBA;IAAAA;IAMUA;IAAAA;IAkBcA;IAAAA;IAKxBA;IAAAA;IAewBA;IAAAA;IAKxBA;IAAAA;IASwBA;IAAAA;IAKxBA;IAAAA;IAMWA;IAAAA;IAcKA;IAAAA;;;;;;;;;;;;;;;;IAtJpDA;IACEA;;IAgKAA,gCAA4D,CAA5D,EAA4D,KAA5D,EAA4D,EAA5D,EAA4D,CAA5D,EAA4D,KAA5D,EAA4D,EAA5D;IAGMA;;IAQEA;IAA0BA;;;IAA8CA;IACxEA;IAA0BA;;IAA+CA;IAE7EA;IACEA;MAAAA;MAAA;MAAA,OAAcA,gDAAd;IAAsC,CAAtC;IAAwCA;;;;;IA7KrDA;IAAAA;IAiKaA;IAAAA;IAQ4BA;IAAAA;IACAA;IAAAA;IAE6BA;IAAAA;;;;;;IAQ/DA;;;;;IAAaA;;;;;;;;IAxOnBA;IACEA,gCAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,IAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,MAAlB,EAAkB,EAAlB;IAOUA;IACFA;IAIJA,gCAA8C,CAA9C,EAA8C,KAA9C,EAA8C,CAA9C,EAA8C,GAA9C,EAA8C,EAA9C;IAMMA;MAAAA;MAAA;MAAA,OAASA,yCAAT;IAA0B,CAA1B;IACAA;IAGAA;;IACFA;IAGFA;IAcFA;IAIFA;IAIAA;IAoLFA;IACAA;IACEA;IAGFA;IACFA;;;;;;;IAnOYA;IAAAA;IAYMA;IAAAA;IAGNA;IAAAA;IAIeA;IAAAA;IAkBjBA;IAAAA;IAIAA;IAAAA,6CAAsB,UAAtB,EAAsBC,IAAtB;;;;;;IA4LRD;;;;IAA0BA;;;;;;;;IAG1BA;IACEA;IAAsBA;MAAAA;MAAA;MAAA,OAAsBA,gDAAtB;IAA8C,CAA9C;IACtBA;IACFA;;;;;IAFwEA;IAAAA;;;;;;IAkBlEA,gCAAuH,CAAvH,EAAuH,KAAvH,EAAuH,EAAvH;IAEIA;IACAA;IACEA;IACFA;;;;;;IAHgDA;IAAAA;IAE9CA;IAAAA;;;;AD/OlB,OAAM,MAAOE,gBAAP,CAAuB;EAoG3BC,YACUC,eADV,EAEUC,aAFV,EAGUC,cAHV,EAIUC,iBAJV,EAKUC,GALV,EAMUC,MANV,EAOUC,KAPV,EAQUC,gBARV,EASmBC,mBATnB,EAUmBC,aAVnB,EAWmBC,eAXnB,EAYmBC,EAZnB,EAYkC;IAXxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACS;IACA;IACA;IACA;IA5GZ,eAAe,EAAf;IACA,eAAmB,IAAnB;IACC,gBAA6B,IAAItB,OAAJ,EAA7B;IAED,kBAA0B;MAC/BuB,KAAK,EAAErB,wBADwB;MAE/BsB,MAAM,EAAE,CAFuB;MAG/BC,IAAI,EAAE;IAHyB,CAA1B;IAMA,mBAAsB,CAAtB;IAEA,wBAA2B,KAAKP,gBAAL,CAAsBQ,OAAtB,CAA8B,aAA9B,CAA3B;IACA,+BAAkC,KAAKR,gBAAL,CAAsBQ,OAAtB,CAA8B,mBAA9B,CAAlC;IACA,0BAA6B,KAAKR,gBAAL,CAAsBQ,OAAtB,CAA8B,sCAA9B,CAA7B;IACA,2BAA8B,KAAKR,gBAAL,CAAsBQ,OAAtB,CAA8B,sBAA9B,CAA9B;IAGA,yBAAoB,IAAI3B,eAAJ,CAA6B,IAA7B,CAApB;IAQP,0BAA8B,KAA9B;IACO,yBAAiC;MACtC4B,UAAU,EAAE,KAAKC,gBADqB;MAEtCC,kBAAkB,EAAE,KAAKC,uBAFa;MAGtCC,gBAAgB,EAAE,KAAKb,gBAAL,CAAsBQ,OAAtB,CAA8B,mBAA9B,CAHoB;MAItCM,SAAS,EAAE,MAAK;QACd,KAAKC,kBAAL,GAA0B,KAA1B;QACA,OAAO,IAAP;MACD,CAPqC;MAQtCC,WAAW,EAAE,MAAK;QAChB,KAAKD,kBAAL,GAA0B,KAA1B;QACA,KAAKE,UAAL,CAAgBV,IAAhB,GAAuB,CAAvB;QACA,KAAKU,UAAL,CAAgBX,MAAhB,GAAyB,CAAzB,CAHgB,CAIhB;;QACA,KAAKL,mBAAL,CAAyBiB,GAAzB,CAA6B,KAAKC,qBAAlC,EAAyD,EAAzD;QACA,KAAKJ,kBAAL,GAA0B,IAA1B;QACA,KAAKlB,GAAL,CAASuB,aAAT;QACA,OAAO,KAAP;MACD,CAjBqC;MAkBtCC,aAAa,EAAE,MAAK;QAClB,KAAKJ,UAAL,CAAgBV,IAAhB,GAAuB,CAAvB;QACA,KAAKU,UAAL,CAAgBX,MAAhB,GAAyB,CAAzB;QACA,KAAKT,GAAL,CAASuB,aAAT;QACA,KAAKnB,mBAAL,CAAyBiB,GAAzB,CAA6B,KAAKC,qBAAlC,EAAyD,KAAKG,WAA9D;QACA,KAAKC,iBAAL,CAAuBC,IAAvB,CAA4B,IAA5B;QACA,KAAKT,kBAAL,GAA0B,KAA1B;QACA,OAAO,IAAP;MACD,CA1BqC;MA2BtCU,iBAAiB,EAAE;QAAEC,QAAQ,EAAE,QAAZ;QAAsBC,IAAI,EAAE,IAA5B;QAAkCC,QAAQ,EAAE,KAA5C;QAAmDC,QAAQ,EAAE;MAA7D;IA3BmB,CAAjC;IA+BA,yBAAiC;MACtCpB,UAAU,EAAE,KAAKqB,kBADqB;MAEtCnB,kBAAkB,EAAE,KAAKoB,mBAFa;MAGtClB,gBAAgB,EAAE,KAAKb,gBAAL,CAAsBQ,OAAtB,CAA8B,2BAA9B,CAHoB;MAItCM,SAAS,EAAE,MAAK;QACd,OAAO,IAAP;MACD,CANqC;MAOtCE,WAAW,EAAE,MAAK;QAChB,OAAO,IAAP;MACD,CATqC;MAUtCK,aAAa,EAAE,MAAK;QAClB,KAAKW,kBAAL;QACA,OAAO,KAAP;MACD,CAbqC;MActCP,iBAAiB,EAAE;QAAEC,QAAQ,EAAE,QAAZ;QAAsBC,IAAI,EAAE,IAA5B;QAAkCC,QAAQ,EAAE,KAA5C;QAAmDC,QAAQ,EAAE;MAA7D;IAdmB,CAAjC;IAkBA,qBAAgB,CACrB;MAAEI,EAAE,EAAE,oBAAN;MAA4BC,GAAG,EAAE;IAAjC,CADqB,EAErB;MAAED,EAAE,EAAE,oBAAN;MAA4BC,GAAG,EAAE;IAAjC,CAFqB,EAGrB;MAAED,EAAE,EAAE,eAAN;MAAuBC,GAAG,EAAE;IAA5B,CAHqB,EAIrB;MAAED,EAAE,EAAE,YAAN;MAAoBC,GAAG,EAAE;IAAzB,CAJqB,EAKrB;MAAED,EAAE,EAAE,UAAN;MAAkBC,GAAG,EAAE;IAAvB,CALqB,EAMrB;MAAED,EAAE,EAAE,aAAN;MAAqBC,GAAG,EAAE;IAA1B,CANqB,EAOrB;MAAED,EAAE,EAAE,MAAN;MAAcC,GAAG,EAAE;IAAnB,CAPqB,EAQrB;MAAED,EAAE,EAAE,4BAAN;MAAoCC,GAAG,EAAE;IAAzC,CARqB,EASrB;MAAED,EAAE,EAAE,WAAN;MAAmBC,GAAG,EAAE;IAAxB,CATqB,EAUrB;MAAED,EAAE,EAAE,iBAAN;MAAyBC,GAAG,EAAE;IAA9B,CAVqB,EAWrB;MAAED,EAAE,EAAE,iBAAN;MAAyBC,GAAG,EAAE;IAA9B,CAXqB,EAYrB;MAAED,EAAE,EAAE,cAAN;MAAsBC,GAAG,EAAE;IAA3B,CAZqB,EAarB;MAAED,EAAE,EAAE,UAAN;MAAkBC,GAAG,EAAE;IAAvB,CAbqB,EAcrB;MAAED,EAAE,EAAE,yBAAN;MAAiCC,GAAG,EAAE;IAAtC,CAdqB,EAerB;MAAED,EAAE,EAAE,QAAN;MAAgBC,GAAG,EAAE;IAArB,CAfqB,EAgBrB;MAAED,EAAE,EAAE,wBAAN;MAAgCC,GAAG,EAAE;IAArC,CAhBqB,EAiBrB;MAAED,EAAE,EAAE,eAAN;MAAuBC,GAAG,EAAE;IAA5B,CAjBqB,CAAhB,CAgC2B,CAEhC;;IACA,MAAMC,YAAY,GAAG,KAAKC,aAAL,CAAmBC,GAAnB,CAAuBC,CAAC,IAAI,IAAI3D,WAAJ,CAAgB,IAAhB,CAA5B,CAArB,CAHgC,CAKhC;;IACA,MAAM4D,gBAAgB,GAAG,IAAI5D,WAAJ,CAAgB,IAAhB,CAAzB;IACA,KAAK6D,iBAAL,GAAyB,KAAKpC,EAAL,CAAQqC,KAAR,CAAc;MACrCL,aAAa,EAAE,IAAI1D,SAAJ,CAAcyD,YAAd,CADsB;MAErCO,SAAS,EAAEH;IAF0B,CAAd,CAAzB;EAID;;EAEDI,QAAQ;;;IACN,KAAKhD,cAAL,CAAoBiD,YAApB;IACA,KAAKC,OAAL,GAAe,IAAf;IAEA,KAAK9C,KAAL,CAAW+C,IAAX,CAAgBC,SAAhB,CAA2BD,IAAD,IAAS;MACjC,IAAIA,IAAI,CAACE,kBAAT,EAA6B;QAC3B,KAAKtD,aAAL,CAAmBuD,mBAAnB,CAAuCF,SAAvC,CAAkDG,eAAD,IAAoB;UACnE,IAAIA,eAAJ,EAAqB;YACnB,MAAMC,cAAc,GAAG,KAAKzD,aAAL,CAAmB0D,aAA1C;;YACA,IAAID,cAAc,CAACE,MAAnB,EAA2B;cACzB,MAAMC,YAAY,GAAG,KAAK1D,iBAAL,CAAuB2D,qBAAvB,CAA6CT,IAAI,CAACE,kBAAlD,CAArB;;cACA,IAAIM,YAAJ,EAAkB;gBAChB,KAAKrD,mBAAL,CAAyBuD,KAAzB,CAA+B,KAAKrC,qBAApC,EACGsC,IADH,CACQ1E,SAAS,CAAC,KAAK2E,QAAN,CADjB,EAEGX,SAFH,CAEa;kBACTvB,IAAI,EAAGmC,GAAD,IAAQ;oBACZ,KAAKC,kBAAL,GAA0Bd,IAAI,CAACe,iBAA/B;oBACA,KAAKC,gBAAL,GAAwBH,GAAxB;oBACA,KAAKI,SAAL;kBACD;gBALQ,CAFb;cASD,CAVD,MAUO;gBACL,KAAKpE,cAAL,CAAoBqE,WAApB;gBACA,KAAKlE,MAAL,CAAYmE,QAAZ,CAAqB,CAAChF,UAAU,CAACiF,aAAZ,CAArB;cACD;YACF,CAhBD,MAgBO;cACL,KAAKvE,cAAL,CAAoBqE,WAApB;cACA,KAAKlE,MAAL,CAAYmE,QAAZ,CAAqB,CAAChF,UAAU,CAACiF,aAAZ,CAArB;YACD;UACF;QACF,CAxBD;MAyBD,CA1BD,MA0BO;QACL,KAAKjE,mBAAL,CAAyBuD,KAAzB,CAA+B,KAAKrC,qBAApC,EACGsC,IADH,CACQ1E,SAAS,CAAC,KAAK2E,QAAN,CADjB,EAEGX,SAFH,CAEa;UACTvB,IAAI,EAAGmC,GAAD,IAAQ;YACZ,KAAKC,kBAAL,GAA0Bd,IAAI,CAACe,iBAA/B;YACA,KAAKC,gBAAL,GAAwBH,GAAxB;YACA,KAAKI,SAAL;UACD;QALQ,CAFb;MASD;IACF,CAtCD,EAJM,CA4CN;;IACA,iBAAKvB,iBAAL,MAAsB,IAAtB,IAAsB2B,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEC,GAAF,CAAM,WAAN,CAAtB,MAAwC,IAAxC,IAAwCC,aAAxC,GAAwC,MAAxC,GAAwCA,GAAEC,YAAF,CAAevB,SAAf,CAAyBwB,IAAI,IAAG;;;MACtE,iBAAK/B,iBAAL,MAAsB,IAAtB,IAAsB2B,aAAtB,GAAsB,MAAtB,GAAsBA,GAClBC,GADkB,CACd,eADc,CAAtB,MACwB,IADxB,IACwBC,aADxB,GACwB,MADxB,GACwBA,GACpBG,UADoB,CACTC,KAAK,CAAC,KAAKrC,aAAL,CAAmBiB,MAApB,CAAL,CAAiCqB,IAAjC,CAAsCH,IAAtC,CADS,EACoC;QAAEI,SAAS,EAAE;MAAb,CADpC,CADxB;IAGD,CAJuC,CAAxC,CA7CM,CAmDN;;IACA,WAAKnC,iBAAL,CAAuB4B,GAAvB,CAA2B,eAA3B,OAA2C,IAA3C,IAA2CQ,aAA3C,GAA2C,MAA3C,GAA2CA,GAAEN,YAAF,CAAevB,SAAf,CAAyBY,GAAG,IAAG;;;MACxE,MAAMkB,WAAW,GAAGlB,GAAG,CAACmB,KAAJ,CAAWP,IAAD,IAAeA,IAAzB,CAApB;;MACA,IAAI,YAAK/B,iBAAL,CAAuB4B,GAAvB,CAA2B,WAA3B,OAAuC,IAAvC,IAAuCD,aAAvC,GAAuC,MAAvC,GAAuCA,GAAEY,KAAzC,MAAmDF,WAAvD,EAAoE;QAClE,WAAKrC,iBAAL,CAAuB4B,GAAvB,CAA2B,WAA3B,OAAuC,IAAvC,IAAuCC,aAAvC,GAAuC,MAAvC,GAAuCA,GAAEG,UAAF,CAAaK,WAAb,EAA0B;UAAEF,SAAS,EAAE;QAAb,CAA1B,CAAvC;MACD;IACF,CAL0C,CAA3C;EAMD;;EAEMK,wBAAwB;IAC7B,OAAQ,KAAKxC,iBAAL,CAAuB4B,GAAvB,CAA2B,eAA3B,EAA0Da,QAAlE;EACD;;EAEMC,mBAAmB;IACxB,OAAO,KAAKF,wBAAL,GAAgCG,IAAhC,CAAsCC,OAAD,IAAkBA,OAAO,CAACL,KAA/D,CAAP;EACD;EAED;;;;;;;EAKQM,aAAa,CAACC,eAAD,EAAqB;;;IACxC,IAAIA,eAAJ,EAAqB;MACnB,MAAM;QAAEC,GAAG,EAAEC;MAAP,IAAwBF,eAA9B;MACA,IAAIG,aAAa,GAAG,EAApB;;MACA,KAAK,IAAIC,QAAT,IAAqBF,YAArB,EAAmC;QACjC,IAAIA,YAAY,CAACE,QAAD,CAAhB,EAA4B;UAC1B,QAAQA,QAAR;YACE,KAAK,cAAL;YACA,KAAK,mBAAL;YACA,KAAK,aAAL;YACA,KAAK,UAAL;YACA,KAAK,kBAAL;YACA,KAAK,iBAAL;YACA,KAAK,UAAL;YACA,KAAK,aAAL;cACE,IAAI,kBAAY,CAACA,QAAD,CAAZ,MAAsB,IAAtB,IAAsBvB,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEd,MAA5B,EAAoC;gBAClCoC,aAAa,mCAAQA,aAAR,GAAqB;kBAAE,CAACC,QAAD,GAAY,kBAAY,CAACA,QAAD,CAAZ,MAAsB,IAAtB,IAAsBrB,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEhC,GAAF,CAAOsD,CAAD,IAAYA,CAAC,CAAC1D,EAApB;gBAApC,CAArB,CAAb;cACD;;cACD;;YAEF,KAAK,qBAAL;cACE,IAAI,kBAAY,CAACyD,QAAD,CAAZ,MAAsB,IAAtB,IAAsBd,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvB,MAA5B,EAAoC;gBAClCoC,aAAa,mCAAQA,aAAR,GAAqB;kBAAE,CAACC,QAAD,GAAY,kBAAY,CAACA,QAAD,CAAZ,MAAsB,IAAtB,IAAsBE,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEvD,GAAF,CAAOsD,CAAD,IAAYA,CAAC,CAACE,KAApB;gBAApC,CAArB,CAAb;cACD;;cACD;;YAEF,KAAK,aAAL;YACA,KAAK,qBAAL;cACE,IAAI,kBAAY,CAACH,QAAD,CAAZ,MAAsB,IAAtB,IAAsBI,aAAtB,GAAsB,MAAtB,GAAsBA,GAAEzC,MAA5B,EAAoC;gBAClCoC,aAAa,mCAAQA,aAAR,GAAqB;kBAAE,CAACC,QAAD,GAAY,kBAAY,CAACA,QAAD,CAAZ,MAAsB,IAAtB,IAAsBK,aAAtB,GAAsB,MAAtB,GAAsBA,GAAE1D,GAAF,CAAOsD,CAAD,IAAY/G,QAAQ,CAAC+G,CAAC,CAAC1D,EAAH,CAA1B;gBAApC,CAArB,CAAb;cACD;;cACD;;YAEF,KAAK,YAAL;YACA,KAAK,UAAL;YACA,KAAK,aAAL;YACA,KAAK,oBAAL;cAEEwD,aAAa,mCAAQA,aAAR,GAAqB;gBAAE,CAACC,QAAD,GAAYF,YAAY,CAACE,QAAD;cAA1B,CAArB,CAAb;cACA;;YAEF,KAAK,cAAL;cACE,IAAIF,YAAY,CAACE,QAAD,CAAhB,EAA4B;gBAC1BD,aAAa,mCAAQA,aAAR,GAAqB;kBAAE,CAACC,QAAD,GAAY9G,QAAQ,CAAC4G,YAAY,CAACE,QAAD,CAAb;gBAAtB,CAArB,CAAb;cACD;;cACD;;YAEF,KAAK,oBAAL;YACA,KAAK,kBAAL;YACA,KAAK,kBAAL;YACA,KAAK,gBAAL;cAEE,MAAM;gBAAEM,IAAF;gBAAQC,KAAR;gBAAeC;cAAf,IAAuBV,YAAY,CAACE,QAAD,CAAzC;cACAD,aAAa,mCAAQA,aAAR,GAAqB;gBAAE,CAACC,QAAD,GAAY,GAAGM,IAAI,IAAIC,KAAK,IAAIC,GAAG;cAArC,CAArB,CAAb;cACA;UAhDJ;QAkDD;MACF;;MACD,OAAOT,aAAP;IACD;;IACD,OAAO,IAAP;EACD;;EAEDU,iBAAiB;IACf,KAAKlF,UAAL,CAAgBV,IAAhB,GAAuB,CAAvB;IACA,KAAKU,UAAL,CAAgBX,MAAhB,GAAyB,CAAzB;IACA,KAAKT,GAAL,CAASuB,aAAT;EACD;;EAEM2C,SAAS;IACd,MAAMqC,OAAO,GAAG,KAAKf,aAAL,CAAmB,KAAKvB,gBAAxB,CAAhB;IACA,KAAKnE,cAAL,CAAoBiD,YAApB;IAEAyD,OAAO,CAACC,GAAR,CAAYF,OAAZ;IAEA,KAAK3G,eAAL,CAAqB8G,wBAArB,CAA8C,KAAKtF,UAAnD,EAA+D,KAAK2C,kBAApE,EAAwFwC,OAAxF,EACGrD,SADH,CACa;MACTvB,IAAI,EAAGgF,QAAD,IAAkB;QACtB,KAAKC,OAAL,GAAeD,QAAQ,CAACE,OAAT,CAAiBrE,GAAjB,CAAsBsE,MAAD,IAAgB;;;UAAC,uCAChDA,MADgD,GAC1C;YAAEC,cAAc,EAAE,WAAKzG,eAAL,CAAqB0G,sBAArB,CAA4CF,MAAM,CAACG,gBAAnD,EAAqE,IAArE,OAA0E,IAA1E,IAA0E3C,aAA1E,GAA0E,MAA1E,GAA0EA,GAAE0B;UAA9F,CAD0C;QAEnD,CAFa,CAAf;QAGA,KAAKkB,WAAL,GAAmBP,QAAQ,CAACQ,KAA5B;QACA,KAAKrH,cAAL,CAAoBqE,WAApB;QACA,KAAKnB,OAAL,GAAe,KAAf;QACA,KAAKhD,GAAL,CAASuB,aAAT;MACD,CATQ;MAUT6F,KAAK,EAAG3E,CAAD,IAAM;QACXlD,IAAI,CAAC8H,IAAL,CAAU;UACRC,IAAI,EAAE,OADE;UAERtB,KAAK,EAAE,KAAK7F,gBAAL,CAAsBQ,OAAtB,CAA8B,WAA9B,CAFC;UAGR4G,IAAI,EAAE9E,CAAC,CAAC+E;QAHA,CAAV;QAKA,KAAKZ,OAAL,GAAe,EAAf;QACA,KAAK5D,OAAL,GAAe,KAAf;QACA,KAAKlD,cAAL,CAAoBqE,WAApB;QACA,KAAKnE,GAAL,CAASuB,aAAT;MACD;IApBQ,CADb;EAuBD;;EAEMY,kBAAkB;IACvB,MAAMsF,eAAe,GAAG,KAAK9E,iBAAL,CAAuBuC,KAAvB,CAA6B3C,aAA7B,CACrBC,GADqB,CACjB,CAACkF,OAAD,EAAeC,KAAf,KAA8BD,OAAO,GAAG,KAAKnF,aAAL,CAAmBoF,KAAnB,EAA0BvF,EAA7B,GAAkC,IADtD,EAErBwF,MAFqB,CAEb1C,KAAD,IAAgBA,KAAK,KAAK,IAFZ,CAAxB;;IAIA,IAAI,CAAC,KAAKG,mBAAL,EAAL,EAAiC;MAC/B9F,IAAI,CAAC8H,IAAL,CAAU;QACRC,IAAI,EAAE,SADE;QAERtB,KAAK,EAAE,KAAK7F,gBAAL,CAAsBQ,OAAtB,CAA8B,WAA9B,CAFC;QAGR4G,IAAI,EAAE,KAAKpH,gBAAL,CAAsBQ,OAAtB,CAA8B,iCAA9B;MAHE,CAAV;MAKA;IACD;;IAED,MAAM4F,OAAO,GAAG,KAAKf,aAAL,CAAmB,KAAKvB,gBAAxB,CAAhB;IACA,KAAK5D,aAAL,CAAmBwH,0BAAnB,CAA8CtB,OAA9C,EAAuDkB,eAAvD;EACD;;EAEMK,oBAAoB,CAACC,IAAD,EAAU;IACnC,MAAM;MAAEC,MAAF;MAAUC;IAAV,IAAqBF,IAA3B;IACA,KAAK9H,MAAL,CAAYiI,aAAZ,CAA0B7I,kBAAkB,CAAC,GAAG2I,MAAM,EAAV,EAAc;MAAEC;IAAF,CAAd,CAA5C;EACD;;EAEME,kBAAkB,CAACC,UAAD,EAA2B;IAClD,OAAO9I,eAAe,CAAC8I,UAAD,CAAtB;EACD;;EAEDC,WAAW;IACT,KAAKxE,QAAL,CAAclC,IAAd,CAAmB,IAAnB;IACA,KAAKkC,QAAL,CAAcyE,WAAd;EACD;;EAEMC,gBAAgB,CAACC,KAAD,EAAc;IACnC,KAAK1I,cAAL,CAAoBiD,YAApB;IACA,KAAKC,OAAL,GAAe,IAAf;IACA,KAAK5B,UAAL,CAAgBX,MAAhB,GAAyB,CAAC+H,KAAD,GAAS,CAAlC;IACA,KAAKpH,UAAL,CAAgBV,IAAhB,GAAuB8H,KAAvB;IACA,KAAKtE,SAAL;EACD;;EAEMuE,gBAAgB,CAACD,KAAD,EAAW;IAChC,KAAK/G,WAAL,GAAmB+G,KAAnB;EACD;;EAEKE,eAAe;IAAA;;IAAA;MACnB,KAAI,CAACxH,kBAAL,GAA0B,IAA1B;;MACA,KAAI,CAAClB,GAAL,CAASuB,aAAT;;MACA,aAAa,KAAI,CAACoH,WAAL,CAAiBC,IAAjB,EAAb;IAHmB;EAIpB;;EAEKC,eAAe;IAAA;;IAAA;MACnB,MAAI,CAAC7I,GAAL,CAASuB,aAAT;;MACA,aAAa,MAAI,CAACuH,WAAL,CAAiBF,IAAjB,EAAb;IAFmB;EAGpB;;AA/V0B;;;mBAAhBlJ,kBAAgBF;AAAA;;;QAAhBE;EAAgBqJ;EAAAC;IAAA;;;;;;;;;;;;;;;;;;;;;;MC1B7BxJ,+BAAgC,CAAhC,EAAgC,KAAhC,EAAgC,CAAhC;MAEIA;MA4OAA;MAGAA;MACEA;MAIFA;MACAA;MACEA;MACEA,gCAAuE,EAAvE,EAAuE,KAAvE,EAAuE,CAAvE,EAAuE,EAAvE,EAAuE,KAAvE,EAAuE,EAAvE,EAAuE,EAAvE,EAAuE,KAAvE,EAAuE,EAAvE;MAIQA;MACAA;MACEA;MACFA;MAINA;MACEA;MAQFA;MAEJA;MACFA;;;;;;MA9QeA;MAAAA,oCAAgB,UAAhB,EAAgByJ,GAAhB;MA+OSzJ;MAAAA;MACPA;MAAAA;MAKOA;MAAAA;MAEmBA;MAAAA;MAYsCA;MAAAA", "names": ["FormArray", "FormControl", "toNumber", "BehaviorSubject", "Subject", "takeUntil", "PAGINATION_DEFAULT_LIMIT", "GlobalUrls", "ReplaceUrlVariable", "BudgetTypeLabel", "<PERSON><PERSON>", "i0", "_r10", "AfeListComponent", "constructor", "proposalService", "commonService", "spinnerService", "permissionService", "cdr", "router", "route", "translateService", "localStorageService", "reportService", "requestTypeData", "fb", "limit", "offset", "page", "instant", "modalTitle", "filterModalTitle", "dismissButtonLabel", "modalDismissButtonLabel", "closeButtonLabel", "on<PERSON><PERSON><PERSON>", "isFilterModalReady", "shouldClose", "pagination", "set", "filterLocalStorageKey", "detectChanges", "<PERSON><PERSON><PERSON><PERSON>", "filtersData", "filterChangeEvent", "next", "modalDialogConfig", "backdrop", "size", "keyboard", "centered", "exportToExcelTitle", "downloadButtonLabel", "exportToExcelSheet", "id", "col", "formControls", "exportColumns", "map", "_", "selectAllControl", "exportColumnsForm", "group", "selectAll", "ngOnInit", "startSpinner", "loading", "data", "subscribe", "permissionRequired", "isPermissionFetched", "permissionReady", "permissionList", "permissionAll", "length", "ifPermission", "checkPermissionByName", "watch", "pipe", "destroy$", "val", "forApprovalHistory", "isApprovalHistory", "storedFilterData", "fetchList", "stopSpinner", "navigate", "ACCESS_DENIED", "_a", "get", "_b", "valueChanges", "bool", "patchValue", "Array", "fill", "emitEvent", "_c", "allSelected", "every", "value", "getExportColumnsControls", "controls", "isAnyColunmSelected", "some", "control", "filterPayload", "filterSelection", "obj", "filterObject", "filterRequest", "property", "f", "_d", "title", "_e", "_f", "year", "month", "day", "resetEventTrigger", "filters", "console", "log", "getSubmittedApprovalList", "response", "afeList", "records", "record", "afeRequestType", "getRequestDetailFromId", "afeRequestTypeId", "totalRecord", "total", "error", "fire", "icon", "text", "message", "selectedColumns", "checked", "index", "filter", "downloadAfeListExcelReport", "nagivateToTaskAction", "task", "relUrl", "taskId", "navigateByUrl", "getBudgetTypeTitle", "budgetType", "ngOnDestroy", "unsubscribe", "handlePageChange", "event", "getFilterPayload", "openFilterModal", "filterModal", "open", "openExportModal", "exportModal", "selectors", "viewQuery", "_r1"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\core\\modules\\partials\\afe-list\\afe-list.component.ts", "C:\\Users\\<USER>\\MyWorkspace\\Projects\\DpWorld\\AFE_Revamp\\client\\src\\app\\core\\modules\\partials\\afe-list\\afe-list.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';\r\nimport { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RequestTypeData } from '@core/data/request-type';\r\nimport { ModalComponent, ModalConfig } from '@core/modules/partials';\r\nimport { ReportService } from '@core/services/api';\r\nimport { LocalStorageService } from '@core/services/common/localStorage.service';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { toNumber } from 'lodash';\r\nimport { BehaviorSubject, Observable, Subject, takeUntil } from 'rxjs';\r\nimport { PAGINATION_DEFAULT_LIMIT } from 'src/app/core/constants/contants';\r\nimport { GlobalUrls, ReplaceUrlVariable } from 'src/app/core/constants/urls.constants';\r\nimport { BudgetTypeEnum, BudgetTypeLabel } from 'src/app/core/enums/Metadata';\r\nimport { IPagination } from 'src/app/core/interfaces/api';\r\nimport { UserPermissionModel } from 'src/app/core/models/basic/userpermissions';\r\nimport { ProposalService } from 'src/app/core/services/api/proposal.service';\r\nimport { CommonService } from 'src/app/core/services/common/common.service';\r\nimport { PermissionService } from 'src/app/core/services/common/permission.service';\r\nimport { SpinnerService } from 'src/app/core/services/common/spinner.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-afe-list',\r\n  templateUrl: './afe-list.component.html',\r\n  styleUrls: ['./afe-list.component.scss']\r\n})\r\nexport class AfeListComponent implements OnInit, OnDestroy {\r\n  @Input('filterLocalStorageKey') filterLocalStorageKey: string;\r\n  @Input('listTitle') listTitle: string;\r\n\r\n  public afeList: any = [];\r\n  public loading: boolean = true;\r\n  private destroy$: Subject<boolean> = new Subject<boolean>();\r\n\r\n  public pagination: IPagination = {\r\n    limit: PAGINATION_DEFAULT_LIMIT,\r\n    offset: 0,\r\n    page: 1\r\n  };\r\n\r\n  public totalRecord: number = 0;\r\n\r\n  public filterModalTitle: string = this.translateService.instant('MENU.FILTER');\r\n  public modalDismissButtonLabel: string = this.translateService.instant('FORM.BUTTON.APPLY');\r\n  public exportToExcelTitle: string = this.translateService.instant('MENU.CHOOSE_COLUMNS_FOR_EXCEL_EXPORT');\r\n  public downloadButtonLabel: string = this.translateService.instant('FORM.BUTTON.DOWNLOAD');\r\n  public filtersData: any;\r\n  public storedFilterData: any | null;\r\n  public filterChangeEvent = new BehaviorSubject<boolean>(true);\r\n  public filterLocalStorage: Observable<any>;\r\n  private forApprovalHistory: boolean;\r\n  @Input() public showExport: boolean;\r\n\r\n  @ViewChild('filterModal') private filterModal: ModalComponent;\r\n  @ViewChild('exportModal') private exportModal: ModalComponent;\r\n\r\n  isFilterModalReady: boolean = false;\r\n  public filterModalConfig: ModalConfig = {\r\n    modalTitle: this.filterModalTitle,\r\n    dismissButtonLabel: this.modalDismissButtonLabel,\r\n    closeButtonLabel: this.translateService.instant('FORM.BUTTON.RESET'),\r\n    onDismiss: () => {\r\n      this.isFilterModalReady = false;\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      this.isFilterModalReady = false;\r\n      this.pagination.page = 1;\r\n      this.pagination.offset = 0;\r\n      //this.cdr.detectChanges();\r\n      this.localStorageService.set(this.filterLocalStorageKey, '');\r\n      this.isFilterModalReady = true;\r\n      this.cdr.detectChanges();\r\n      return false;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.pagination.page = 1;\r\n      this.pagination.offset = 0;\r\n      this.cdr.detectChanges();\r\n      this.localStorageService.set(this.filterLocalStorageKey, this.filtersData);\r\n      this.filterChangeEvent.next(true);\r\n      this.isFilterModalReady = false;\r\n      return true;\r\n    },\r\n    modalDialogConfig: { backdrop: 'static', size: 'lg', keyboard: false, centered: true }\r\n  };\r\n\r\n\r\n  public exportModalConfig: ModalConfig = {\r\n    modalTitle: this.exportToExcelTitle,\r\n    dismissButtonLabel: this.downloadButtonLabel,\r\n    closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),\r\n    onDismiss: () => {\r\n      return true;\r\n    },\r\n    shouldClose: () => {\r\n      return true;\r\n    },\r\n    shouldDismiss: () => {\r\n      this.exportToExcelSheet();\r\n      return false;\r\n    },\r\n    modalDialogConfig: { backdrop: 'static', size: 'md', keyboard: false, centered: true }\r\n  };\r\n\r\n  public exportColumnsForm: FormGroup;\r\n  public exportColumns = [\r\n    { id: 'Business Unit Code', col: 'Business Unit Code' },\r\n    { id: 'Business Unit Name', col: 'Business Unit Name' },\r\n    { id: 'Proposal Type', col: 'Proposal Type' },\r\n    { id: 'AFE Number', col: 'AFE Number' },\r\n    { id: 'AFE Name', col: 'AFE Name' },\r\n    { id: 'Budget Type', col: 'Budget Type' },\r\n    { id: 'Type', col: 'Type' },\r\n    { id: 'Total Expenditure (In USD)', col: 'Total Expenditure' },\r\n    { id: 'Submitter', col: 'Submitter' },\r\n    { id: 'Submission Date', col: 'Submission Date' },\r\n    { id: 'Expense Summary', col: 'Expense Summary' },\r\n    { id: 'Cost Centers', col: 'Cost Centers' },\r\n    { id: 'GL Codes', col: 'GL Codes' },\r\n    { id: 'Budget Reference Number', col: 'Budget Reference Number' },\r\n    { id: 'Status', col: 'Status' },\r\n    { id: 'Approved / Rejected On', col: 'Approved / Rejected On' },\r\n    { id: 'Workflow Year', col: 'Workflow Year' },\r\n  ];\r\n\r\n  constructor(\r\n    private proposalService: ProposalService,\r\n    private commonService: CommonService,\r\n    private spinnerService: SpinnerService,\r\n    private permissionService: PermissionService,\r\n    private cdr: ChangeDetectorRef,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private translateService: TranslateService,\r\n    private readonly localStorageService: LocalStorageService,\r\n    private readonly reportService: ReportService,\r\n    private readonly requestTypeData: RequestTypeData,\r\n    private readonly fb: FormBuilder\r\n  ) {\r\n    // Create a FormControl for each available colunm, initialize them as unchecked, and put them in an array\r\n    const formControls = this.exportColumns.map(_ => new FormControl(true));\r\n\r\n    // Create a FormControl for the select/unselect all checkbox\r\n    const selectAllControl = new FormControl(true);\r\n    this.exportColumnsForm = this.fb.group({\r\n      exportColumns: new FormArray(formControls),\r\n      selectAll: selectAllControl\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.spinnerService.startSpinner();\r\n    this.loading = true;\r\n\r\n    this.route.data.subscribe((data) => {\r\n      if (data.permissionRequired) {\r\n        this.commonService.isPermissionFetched.subscribe((permissionReady) => {\r\n          if (permissionReady) {\r\n            const permissionList = this.commonService.permissionAll as UserPermissionModel[];\r\n            if (permissionList.length) {\r\n              const ifPermission = this.permissionService.checkPermissionByName(data.permissionRequired)\r\n              if (ifPermission) {\r\n                this.localStorageService.watch(this.filterLocalStorageKey)\r\n                  .pipe(takeUntil(this.destroy$))\r\n                  .subscribe({\r\n                    next: (val) => {\r\n                      this.forApprovalHistory = data.isApprovalHistory;\r\n                      this.storedFilterData = val;\r\n                      this.fetchList();\r\n                    }\r\n                  });\r\n              } else {\r\n                this.spinnerService.stopSpinner();\r\n                this.router.navigate([GlobalUrls.ACCESS_DENIED]);\r\n              }\r\n            } else {\r\n              this.spinnerService.stopSpinner();\r\n              this.router.navigate([GlobalUrls.ACCESS_DENIED]);\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        this.localStorageService.watch(this.filterLocalStorageKey)\r\n          .pipe(takeUntil(this.destroy$))\r\n          .subscribe({\r\n            next: (val) => {\r\n              this.forApprovalHistory = data.isApprovalHistory;\r\n              this.storedFilterData = val;\r\n              this.fetchList();\r\n            }\r\n          });\r\n      }\r\n    });\r\n\r\n    // Subscribe to changes on the selectAll checkbox\r\n    this.exportColumnsForm?.get('selectAll')?.valueChanges.subscribe(bool => {\r\n      this.exportColumnsForm\r\n        ?.get('exportColumns')\r\n        ?.patchValue(Array(this.exportColumns.length).fill(bool), { emitEvent: false });\r\n    });\r\n\r\n    // Subscribe to changes on the colunm name checkboxes\r\n    this.exportColumnsForm.get('exportColumns')?.valueChanges.subscribe(val => {\r\n      const allSelected = val.every((bool: any) => bool);\r\n      if (this.exportColumnsForm.get('selectAll')?.value !== allSelected) {\r\n        this.exportColumnsForm.get('selectAll')?.patchValue(allSelected, { emitEvent: false });\r\n      }\r\n    });\r\n  }\r\n\r\n  public getExportColumnsControls() {\r\n    return (this.exportColumnsForm.get('exportColumns') as FormArray).controls;\r\n  }\r\n\r\n  public isAnyColunmSelected() {\r\n    return this.getExportColumnsControls().some((control: any) => control.value);\r\n  }\r\n\r\n  /**\r\n   * Creating body payload for afe list filter.\r\n   * @param filterSelection \r\n   * @returns \r\n   */\r\n  private filterPayload(filterSelection: any) {\r\n    if (filterSelection) {\r\n      const { obj: filterObject } = filterSelection;\r\n      let filterRequest = {};\r\n      for (let property in filterObject) {\r\n        if (filterObject[property]) {\r\n          switch (property) {\r\n            case 'requestTypes':\r\n            case 'projectComponents':\r\n            case 'budgetTypes':\r\n            case 'statuses':\r\n            case 'businessEntities':\r\n            case 'submissionTypes':\r\n            case 'afeTypes':\r\n            case 'submittedBy':\r\n              if (filterObject[property]?.length) {\r\n                filterRequest = { ...filterRequest, [property]: filterObject[property]?.map((f: any) => f.id) };\r\n              }\r\n              break;\r\n\r\n            case 'lengthOfCommitments':\r\n              if (filterObject[property]?.length) {\r\n                filterRequest = { ...filterRequest, [property]: filterObject[property]?.map((f: any) => f.title) };\r\n              }\r\n              break;\r\n\r\n            case 'costCenters':\r\n            case 'additionalLocations':\r\n              if (filterObject[property]?.length) {\r\n                filterRequest = { ...filterRequest, [property]: filterObject[property]?.map((f: any) => toNumber(f.id)) };\r\n              }\r\n              break;\r\n\r\n            case 'fromAmount':\r\n            case 'toAmount':\r\n            case 'projectName':\r\n            case 'afeReferenceNumber':\r\n\r\n              filterRequest = { ...filterRequest, [property]: filterObject[property] };\r\n              break;\r\n\r\n            case 'workflowYear':\r\n              if (filterObject[property]) {\r\n                filterRequest = { ...filterRequest, [property]: toNumber(filterObject[property]) };\r\n              }\r\n              break;\r\n\r\n            case 'fromSubmissionDate':\r\n            case 'toSubmissionDate':\r\n            case 'fromApprovalDate':\r\n            case 'toApprovalDate':\r\n\r\n              const { year, month, day } = filterObject[property];\r\n              filterRequest = { ...filterRequest, [property]: `${year}-${month}-${day}` };\r\n              break;\r\n          }\r\n        }\r\n      }\r\n      return filterRequest;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  resetEventTrigger() {\r\n    this.pagination.page = 1;\r\n    this.pagination.offset = 0;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  public fetchList() {\r\n    const filters = this.filterPayload(this.storedFilterData);\r\n    this.spinnerService.startSpinner();\r\n\r\n    console.log(filters);\r\n\r\n    this.proposalService.getSubmittedApprovalList(this.pagination, this.forApprovalHistory, filters)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.afeList = response.records.map((record: any) => ({\r\n            ...record, afeRequestType: this.requestTypeData.getRequestDetailFromId(record.afeRequestTypeId, true)?.title\r\n          }));\r\n          this.totalRecord = response.total;\r\n          this.spinnerService.stopSpinner();\r\n          this.loading = false;\r\n          this.cdr.detectChanges();\r\n        },\r\n        error: (_) => {\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: this.translateService.instant('SWAL.OOPS'),\r\n            text: _.message\r\n          });\r\n          this.afeList = [];\r\n          this.loading = false;\r\n          this.spinnerService.stopSpinner();\r\n          this.cdr.detectChanges();\r\n        }\r\n      })\r\n  }\r\n\r\n  public exportToExcelSheet() {\r\n    const selectedColumns = this.exportColumnsForm.value.exportColumns\r\n      .map((checked: any, index: any) => checked ? this.exportColumns[index].id : null)\r\n      .filter((value: any) => value !== null);\r\n\r\n    if (!this.isAnyColunmSelected()) {\r\n      Swal.fire({\r\n        icon: 'warning',\r\n        title: this.translateService.instant('SWAL.OOPS'),\r\n        text: this.translateService.instant('SWAL.SELECT_AT_LEAST_ONE_COLUMN')\r\n      });\r\n      return;\r\n    }\r\n\r\n    const filters = this.filterPayload(this.storedFilterData);\r\n    this.reportService.downloadAfeListExcelReport(filters, selectedColumns);\r\n  }\r\n\r\n  public nagivateToTaskAction(task: any) {\r\n    const { relUrl, taskId } = task;\r\n    this.router.navigateByUrl(ReplaceUrlVariable(`${relUrl}`, { taskId }));\r\n  }\r\n\r\n  public getBudgetTypeTitle(budgetType: BudgetTypeEnum) {\r\n    return BudgetTypeLabel[budgetType];\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next(true);\r\n    this.destroy$.unsubscribe();\r\n  }\r\n\r\n  public handlePageChange(event: number): void {\r\n    this.spinnerService.startSpinner();\r\n    this.loading = true;\r\n    this.pagination.offset = +event - 1;\r\n    this.pagination.page = event;\r\n    this.fetchList();\r\n  }\r\n\r\n  public getFilterPayload(event: any) {\r\n    this.filtersData = event;\r\n  }\r\n\r\n  async openFilterModal() {\r\n    this.isFilterModalReady = true;\r\n    this.cdr.detectChanges();\r\n    return await this.filterModal.open();\r\n  }\r\n\r\n  async openExportModal() {\r\n    this.cdr.detectChanges();\r\n    return await this.exportModal.open();\r\n  }\r\n}\r\n", "<div class=\"d-flex flex-column\">\r\n  <div class=\"div-item card mb-0 mb-xl-0 border rounded\">\r\n    <ng-container *ngIf=\"!loading; else loadingPage\">\r\n      <div class=\"card\">\r\n        <!-- begin::Header -->\r\n        <div\r\n          class=\"d-flex flex-lg-row flex-md-row flex-sm-row flex-column p-5 py-3 justify-content-between border-bottom border-gray-300 \">\r\n          <div>\r\n            <h3 class=\"card-title mt-3\">\r\n              <span class=\"card-label fw-bolder fs-3 mb-1\">\r\n                {{ listTitle }}\r\n              </span>\r\n            </h3>\r\n            <!-- begin::Actions  -->\r\n          </div>\r\n          <div class=\"d-flex justify-content-end py-1 \">\r\n            <!-- begin::Wrapper  -->\r\n            <div>\r\n              <!-- begin::Menu  -->\r\n              <a class=\"btn btn-sm btn-flex btn-primary btn-active-primary fw-bold cursor-pointer\"\r\n                data-kt-menu-trigger=\"click\" data-kt-menu-placement=\"bottom-end\" data-kt-menu-flip=\"top-end\"\r\n                (click)=\"openFilterModal()\">\r\n                <span [inlineSVG]=\"\r\n                    './assets/media/icons/duotune/general/gen031.svg'\r\n                  \" class=\"svg-icon svg-icon-5 svg-icon-gray-500 me-1\"></span>\r\n                {{ \"FORM.BUTTON.FILTER\" | translate }}\r\n              </a>\r\n              <!-- end::Menu  -->\r\n            </div>\r\n            <div class=\"px-2\" *ngIf=\"showExport && afeList.length\">\r\n              <!-- begin::Menu  -->\r\n              <a class=\"btn btn-sm btn-flex btn-primary btn-active-primary fw-bold cursor-pointer\"\r\n                data-kt-menu-trigger=\"click\" data-kt-menu-placement=\"bottom-end\" data-kt-menu-flip=\"top-end\"\r\n                (click)=\"openExportModal()\">\r\n                <span [inlineSVG]=\"\r\n                    './assets/media/icons/duotune/general/gen005.svg'\r\n                  \" class=\"svg-icon svg-icon-5 svg-icon-gray-500 me-1\"></span>\r\n                {{ \"FORM.BUTTON.EXPORT\" | translate }}\r\n              </a>\r\n              <!-- end::Menu  -->\r\n            </div>\r\n            <!-- end::Wrapper  -->\r\n            <!-- end::Button  -->\r\n          </div>\r\n        </div>\r\n        <!-- end::Header -->\r\n        <!-- begin::Body -->\r\n        <div *ngIf=\"storedFilterData\" class=\"mt-1 mx-8 my-2 py-3 align-middle\">\r\n          <app-filter-badges (resetEventTrigger)=\"resetEventTrigger()\" [localStorageKey]=\"filterLocalStorageKey\">\r\n          </app-filter-badges>\r\n        </div>\r\n        <div *ngIf=\"afeList.length; else noDataMessage\" class=\"d-flex flex-column\">\r\n          <div class=\"row mx-5 my-3\" *ngFor=\"\r\n              let afeDetail of afeList\r\n                | paginate\r\n                  : {\r\n                      itemsPerPage: pagination.limit,\r\n                      currentPage: pagination.page,\r\n                      totalItems: totalRecord\r\n                    };\r\n              let i = index\r\n            \">\r\n            <div class=\"div-item\">\r\n              <div class=\"row p-1 p-lg-3 p-md-3 p-sm-3 pb-3\">\r\n                <div class=\"col-12 col-md-6 p-0 m-0\">\r\n                  <img width=\"15px\" height=\"20px\" src=\"./assets/media/svg/icons/dpw-icons/nextArrow.png\" alt=\"S.No\" />\r\n                  <a class=\"d-value text-dark fw-bold text-hover-primary fs-6\"\r\n                    [routerLink]=\"['/afe', 'afe-detail', afeDetail.id]\">\r\n                    <span class=\"px-1 underline\">\r\n                      {{ afeDetail.projectReferenceNumber }}</span>\r\n                  </a>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 p-0 m-0 status-block text-right\">\r\n                  <span class=\"badge fw-bold me-auto px-2 badge label-light-primary mx-1 fs-8 py-1 ms-5\">\r\n                    {{ afeDetail.userStatus }}\r\n                  </span>\r\n                </div>\r\n\r\n                <div class=\"col-12 col-md-6 p-0 m-0\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.PROJECT_NAME\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">{{ afeDetail.name }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"col-12 col-md-6 p-0 m-0\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.ENTITY_NAME\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">\r\n                        {{ afeDetail.entityTitle }}\r\n                        <ng-container *ngIf=\"afeDetail?.location?.title\" >\r\n                          ( {{ afeDetail.location.title }} )\r\n                        </ng-container>\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n                <div class=\"col-12 col-md-6 p-0 m-0\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.REQUEST_TYPE\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">{{\r\n                        afeDetail.afeRequestType\r\n                        }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 p-0 m-0\" *ngIf=\"afeDetail.budgetType\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.BUDGET_TYPE\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">{{\r\n                        getBudgetTypeTitle(afeDetail.budgetType)\r\n                        }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"col-12 col-md-6 p-0 m-0\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.EXPENSE_AMOUNT\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">{{\r\n                        +afeDetail.totalAmount\r\n                        ? (afeDetail.totalAmount\r\n                        | currency\r\n                        : afeDetail.currencyType\r\n                        : \"symbol\"\r\n                        : \"1.2-2\")\r\n                        : 0\r\n                        }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 p-0 m-0\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.CREATED_AT\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">{{\r\n                        afeDetail.createdOn | date : \"medium\"\r\n                        }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-6 p-0 m-0\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.UPDATED_AT\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">{{\r\n                        afeDetail.updatedOn | date : \"medium\"\r\n                        }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-12 p-0 m-0\" *ngIf=\"afeDetail?.approvalPendingWith?.length\">\r\n                  <div class=\"d-flex justify-content-start pb-1\">\r\n                    <div class=\"d-flex flex-nowrap\" style=\"min-width: 110px\">\r\n                      <span class=\"fw-bold text-gray-600 px-6 pe-2\">{{\r\n                        \"LIST.APPROVAL_PENDING_WITH\" | translate\r\n                        }}</span>\r\n                    </div>\r\n                    <div class=\"d-flex flex-wrap justify-content-start\">\r\n                      <span class=\"fw-bold\">{{\r\n                        afeDetail.approvalPendingWith.join(\", \")\r\n                        }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-12 col-md-12 p-0 m-0 pt-3\" *ngIf=\"afeDetail.task\">\r\n                  <div class=\"d-flex flex-wrap justify-content-end px-0\">\r\n                    <button type=\"button\" title=\"{{ 'FORM.BUTTON.REVIEW' | translate }}\"\r\n                      (click)=\"nagivateToTaskAction(afeDetail.task)\"\r\n                      class=\"btn btn-sm editBtn btnRounded fw-bold ps-4 pe-4 py-1 mx-2\"\r\n                      translate=\"{{ 'FORM.BUTTON.REVIEW' | translate }}\"></button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row  px-2 pt-3 border-top border-gray-300 mx-0\">\r\n            <div class=\"d-flex justify-content-between\">\r\n              <div class=\"fw-bold text-gray-900\">\r\n                {{\r\n                pagination.page === 1\r\n                ? 1\r\n                : (pagination.page - 1) * pagination.limit + 1\r\n                }}\r\n                {{ \"COMMON.TO\" | translate }}\r\n                {{\r\n                pagination.limit * pagination.page <= totalRecord ? pagination.limit * pagination.page : totalRecord }}\r\n                  <span class=\"totalRecord\">{{ \"COMMON.RECORD\" | translate | lowercase }} </span>\r\n                  <span class=\"totalRecord\">{{ \"COMMON.OF\" | translate }} {{ totalRecord }}</span>\r\n              </div>\r\n              <pagination-controls previousLabel=\"Prev\" nextLabel=\"Next\" [responsive]=\"true\"\r\n                (pageChange)=\"handlePageChange($event)\"></pagination-controls>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <ng-template #noDataMessage>\r\n          <app-no-data [message]=\"'ERROR.NO_RECORD_FOUND' | translate\"></app-no-data>\r\n        </ng-template>\r\n      </div>\r\n    </ng-container>\r\n    <ng-template #loadingPage>\r\n      <app-list-skeleton-loader [loaderCount]=\"4\"></app-list-skeleton-loader>\r\n    </ng-template>\r\n    <app-modal #filterModal [modalConfig]=\"filterModalConfig\">\r\n      <ng-container *ngIf=\"isFilterModalReady\">\r\n        <app-afe-filter-modal (filterPayloadEvent)=\"getFilterPayload($event)\" [localStorageKey]=\"filterLocalStorageKey\">\r\n        </app-afe-filter-modal>\r\n      </ng-container>\r\n    </app-modal>\r\n    <app-modal #exportModal [modalConfig]=\"exportModalConfig\">\r\n      <ng-container>\r\n        <div class=\"w-100 p-5 bg-body rounded\" [formGroup]=\"exportColumnsForm\">\r\n          <div class=\"row p-1 p-lg-3 p-md-3 p-sm-3\">\r\n            <div class=\"col-12 col-md-12 p-0 m-0\">\r\n              <div class=\"form-check\">\r\n                <input class=\"form-check-input\" type=\"checkbox\" value=\"\" formControlName=\"selectAll\">\r\n                <label class=\"form-label fw-bold\">\r\n                  Select/Deselect all\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row p-1 p-lg-3 p-md-3 p-sm-3\">\r\n            <div class=\"col-6 p-0 m-0\" formArrayName=\"exportColumns\" *ngFor=\"let col of getExportColumnsControls(); let i = index\">\r\n              <div class=\"form-check mt-3\">\r\n                <input class=\"form-check-input\" type=\"checkbox\" [formControlName]=\"i\">\r\n                <label class=\"form-label fw-bold\">\r\n                  {{exportColumns[i].col}}\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n    </app-modal>\r\n  </div>\r\n</div>\r\n\r\n"]}, "metadata": {}, "sourceType": "module"}