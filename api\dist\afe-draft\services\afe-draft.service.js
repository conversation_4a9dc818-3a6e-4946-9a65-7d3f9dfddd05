"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeDraftService = void 0;
const common_1 = require("@nestjs/common");
const helpers_1 = require("../../shared/helpers");
const draft_afe_repository_1 = require("../repositories/draft-afe-repository");
const dtos_1 = require("../dtos");
const attachment_api_client_1 = require("../../shared/clients/attachment-api.client");
const attachment_response_dto_1 = require("../dtos/response/attachment-response.dto");
const services_1 = require("../../shared/services");
const constants_1 = require("../../shared/constants");
const enums_1 = require("../../shared/enums");
const pagination_1 = require("../../core/pagination");
const exceptions_1 = require("../../shared/exceptions");
let AfeDraftService = class AfeDraftService {
    constructor(draftAfeRepository, attachmentApiClient, sharedAttachmentService) {
        this.draftAfeRepository = draftAfeRepository;
        this.attachmentApiClient = attachmentApiClient;
        this.sharedAttachmentService = sharedAttachmentService;
    }
    getAllDraftsByActiveUser(currentContext, limit = 10, page = 1) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.draftAfeRepository.getAllDraftsByActiveUser(currentContext.user.username.toLowerCase(), limit, page);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.GetAfeDraftResponseDto, result.rows);
            return new pagination_1.Pagination({ records, total: result.count });
        });
    }
    getDraftsByIdAndActiveUser(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const draftDetail = yield this.draftAfeRepository.getDraftById(id);
            if (draftDetail &&
                draftDetail.createdBy.toLowerCase() !== currentContext.user.username.toLowerCase()) {
                throw new exceptions_1.HttpException('You are not authorized to fetch this draft.', enums_1.HttpStatus.FORBIDDEN);
            }
            if (draftDetail &&
                draftDetail.createdBy.toLowerCase() === currentContext.user.username.toLowerCase()) {
                const uploadedDocumentData = yield this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_DRAFT);
                const allAttachments = (0, helpers_1.multiObjectToInstance)(attachment_response_dto_1.AttachmentResponseDto, uploadedDocumentData);
                if (allAttachments.length) {
                    allAttachments.forEach(attachmentData => {
                        attachmentData.IsNew = false;
                    });
                    draftDetail.data['supportingDocuments'] = allAttachments;
                }
                return (0, helpers_1.singleObjectToInstance)(dtos_1.GetAfeDraftResponseDto, draftDetail);
            }
        });
    }
    saveDraft(draftAfeDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username: submitterId } = currentContext.user;
            const result = yield this.draftAfeRepository.saveNewDraft(Object.assign(Object.assign({}, draftAfeDto), { submitterId }), currentContext);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.AfeDraftResponseDto, result);
        });
    }
    updateDraftByIdAndActiveUser(updateDraftDto, currentContext) {
        var _a, _b, _c, _d;
        return __awaiter(this, void 0, void 0, function* () {
            const { id: draftId } = updateDraftDto;
            const afeDetail = yield this.draftAfeRepository.getDraftById(draftId);
            if (afeDetail &&
                afeDetail.createdBy.toLowerCase() === currentContext.user.username.toLowerCase()) {
                let uploadedDocumentList = [];
                if ((_b = (_a = updateDraftDto.data) === null || _a === void 0 ? void 0 : _a.supportingDocuments) === null || _b === void 0 ? void 0 : _b.length) {
                    uploadedDocumentList = yield this.sharedAttachmentService.supportingDocumentsActivity(updateDraftDto.data.supportingDocuments, draftId, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_DRAFT, constants_1.ATTACHMENT_REL_PATH.AFE_DRAFT, currentContext.user.username);
                }
                if ((_d = (_c = updateDraftDto.data) === null || _c === void 0 ? void 0 : _c.deletedSupportingDocuments) === null || _d === void 0 ? void 0 : _d.length) {
                    yield this.sharedAttachmentService.deleteSupportingDocumentsActivity(updateDraftDto.data.deletedSupportingDocuments);
                    delete updateDraftDto.data.deletedSupportingDocuments;
                }
                updateDraftDto.data.supportingDocuments ? delete updateDraftDto.data.supportingDocuments : '';
                yield this.draftAfeRepository.updateDraftByIdAndActiveUser(draftId, currentContext, {
                    data: updateDraftDto.data,
                    projectName: updateDraftDto.projectName,
                    entityName: updateDraftDto.entityName,
                });
                if (uploadedDocumentList.length) {
                    return { message: 'Draft updated successfully.', data: uploadedDocumentList };
                }
                else {
                    return { message: 'Draft updated successfully.' };
                }
            }
            if (afeDetail &&
                afeDetail.createdBy.toLowerCase() !== currentContext.user.username.toLowerCase()) {
                throw new exceptions_1.HttpException('You are not authorized to update this draft.', enums_1.HttpStatus.FORBIDDEN);
            }
        });
    }
    deleteDraftById(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const afeDetail = yield this.draftAfeRepository.getDraftById(id);
            if (afeDetail &&
                afeDetail.createdBy.toLowerCase() === currentContext.user.username.toLowerCase()) {
                const uploadedDocumentData = yield this.attachmentApiClient.getAllAttachments(id, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_DRAFT);
                let allAttachments = (0, helpers_1.multiObjectToInstance)(attachment_response_dto_1.AttachmentResponseDto, uploadedDocumentData);
                if (allAttachments.length) {
                    allAttachments.map(attachmentData => {
                        attachmentData.IsNew = false;
                    });
                }
                yield this.sharedAttachmentService.deleteSupportingDocumentsActivity(allAttachments);
                const response = yield this.draftAfeRepository.deleteDraftById(id, currentContext);
                if (response) {
                    return { message: 'AFE deleted successfully.' };
                }
            }
            if (afeDetail &&
                afeDetail.createdBy.toLowerCase() !== currentContext.user.username.toLowerCase()) {
                throw new exceptions_1.HttpException('You are not authorized to delete this draft.', enums_1.HttpStatus.FORBIDDEN);
            }
        });
    }
};
AfeDraftService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [draft_afe_repository_1.DraftAfeRepository,
        attachment_api_client_1.AttachmentApiClient,
        services_1.SharedAttachmentService])
], AfeDraftService);
exports.AfeDraftService = AfeDraftService;
//# sourceMappingURL=afe-draft.service.js.map