import { ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core'; import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { ModalComponent, ModalConfig } from '@core/modules/partials';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { lte, toNumber } from 'lodash';
import { debounceTime, forkJoin, Subscription } from 'rxjs';
import { DEBOUNCE_TIME } from 'src/app/core/constants/contants';
import { BudgetTypeIdMappingEnum } from 'src/app/core/enums/Metadata';
import { IBudgetBasedProjectSplitAmount, IBudgetReferenceSplit, IChartOfAccountList, IChartOfAccounts, ICostCenterSplitAmount, ISplitAmount } from 'src/app/core/interfaces/amount-split';
import { AnalysisObjectEnum, CostCenterObjectEnum, IAnalysisCode, ICostCenter, INaturalAccountNumber, NaturalObjectEnum } from 'src/app/core/interfaces/metadata';
import { Dropdown } from 'src/app/core/models/dropdown';
import { MetadataService } from 'src/app/core/services/api/metadata.service';
import { SpinnerService } from 'src/app/core/services/common/spinner.service';
import { ScrollTopComponent } from 'src/app/_metronic/components';

@Component({
  selector: 'app-finance-detail-form',
  templateUrl: './finance-detail-form.component.html',
})
export class FinanceDetailForm implements OnInit, OnDestroy {

  @Input('updateParentModel') updateParentModel:
    (part: Partial<any> | null, isFormValid: boolean) => void;

  @Input() defaultValues: Partial<any>;
  @Input() isSubmitted: Partial<boolean> = false;
  @Input() isMultiChartOfAccount: boolean = false;
  @Input() parentAfeTotalAmount: number | null;
  public supplementalAmount: number | null = null;

  naturalAccountNumberList: INaturalAccountNumber[] = []

  analysisCodeList: IAnalysisCode[] = [];
  costCenterList: ICostCenter[] = []

  totalBudgetedProjectError: boolean = false;
  totalUnbudgetedProjectError: boolean = false;
  totalBudgetProjectError: boolean = false;
  totalBudgetError: boolean = false;
  totalBudgetRefError: boolean = false;

  budgetedAmount: number = 0;
  unbudgetedAmount: number = 0;

  naturalAccountNumber: any;
  analysisCode: any;

  private unsubscribe: Subscription[] = [];
  form: FormGroup;
  formReady: boolean = false;
  items: Dropdown[] = [];
  public isCompanyCodeExists: boolean;
  supplimentalModalReady: boolean = false;
  newBudgetBasedProjectSplit: any = [];

  isCostCenterError: boolean = false;
  isCombinationAvailable: boolean = false;

  @ViewChild('supplimentalAmountBreakup') private sabModelComponent: ModalComponent;

  supplimentalAmountBreakupConfig: ModalConfig = {
    modalTitle: this.translateService.instant('FORM.LABEL.SUPPLEMENTAL_AMOUNT_DIFFERENCE'),
    hideDismissButton() {
      return true;
    },
    onDismiss: () => {
      this.supplimentalModalReady = false;
      this.cdr.detectChanges();
      return true;
    },
    shouldClose: () => {
      this.supplimentalModalReady = false;
      this.cdr.detectChanges();
      return true;
    },
    shouldDismiss: () => {
      return false;
    },
    modalDialogConfig: { backdrop: 'static', size: 'lg', keyboard: false, centered: false }
  };

  constructor(
    private metadataService: MetadataService,
    private cdr: ChangeDetectorRef,
    private spinnerService: SpinnerService,
    private translateService: TranslateService,
  ) { }

  ngOnInit() {
    this.cdr.detectChanges();

    ScrollTopComponent.goTop();
    this.spinnerService.startSpinner();

    this.metadataService.checkCompanyCodeExists(this.defaultValues.businessEntity.id).subscribe({
      next: (isExists) => {
        if (isExists) {
          this.isCompanyCodeExists = true;
          const forkSubs = forkJoin({
            naturalAccountNumber: this.metadataService.getNaturalAccountNumber(this.defaultValues.requestTypeId, this.defaultValues.businessEntity.id),
            getAnalysisCode: this.metadataService.getAnalysisCode(this.defaultValues.requestTypeId, this.defaultValues.businessEntity.id),
            costCenter: this.metadataService.getCostCenters(this.defaultValues.businessEntity.id)
          }).subscribe(
            {
              next: ({ naturalAccountNumber, costCenter, getAnalysisCode }) => {
                this.naturalAccountNumberList = naturalAccountNumber as unknown as INaturalAccountNumber[];
                this.analysisCodeList = getAnalysisCode as unknown as IAnalysisCode[];
                this.costCenterList = costCenter as unknown as ICostCenter[];
                this.cdr.detectChanges();
              },
              error: (_) => {
                this.initForm();
                this.formReady = true;
                this.cdr.detectChanges();
                this.spinnerService.stopSpinner();
              },
              complete: () => {
                this.initForm();
                this.formReady = true;
                this.cdr.detectChanges();
                this.defaultCostCenterLoading();
                this.spinnerService.stopSpinner();
              }
            }
          );
          this.unsubscribe.push(forkSubs);
        } else {
          this.isCompanyCodeExists = false;
          this.initForm();
          this.formReady = true;
          this.defaultCostCenterLoading();
          this.cdr.detectChanges();
          this.spinnerService.stopSpinner();
        }
      }
    });
  }

  initForm(): void {
    this.form = new FormGroup({
      budgetTypeSplit: new FormArray([]),
      // ...(this.isCompanyCodeExists && this.analysisCodeList.length && { analysisCodeSplit: new FormArray([this.getAnalysisCodeSplitControl()]) }),
      ...(this.isCompanyCodeExists && this.naturalAccountNumberList.length && { naturalAccountNumberSplit: this.initializeNaturalAccountSplitArray(), }),

      costCenterSplit: new FormArray((this.costCenterList.length) ? [] : [this.getCostCenterSplitControl(null, (!!this.defaultValues?.costCenterSplit?.length))], [Validators.required]),
      selectedCostCenter: new FormControl(''),
      selectedNaturalAccount: new FormControl(''),
      selectedAnalysisCode: new FormControl(''),
      budgetBasedProjectSplit: new FormArray([])
    });

    this.updateParentModel(null, this.form.valid);

    const formChangesSubscr = this.form.valueChanges
      .pipe(debounceTime(DEBOUNCE_TIME))
      .subscribe((val) => {

        if (this.costCenterList.length) {

          console.log(val);

          if (val?.costCenterSplit?.length) {
            val.costCenterSplit.map((costCenterDetail: any) => {

              if (toNumber(costCenterDetail?.naturalAccountId) >= 0) {
                costCenterDetail.naturalAccount = this.getNaturalAccountDetaulById(costCenterDetail.naturalAccountId);
                delete costCenterDetail.naturalAccountId;
              }

              if (toNumber(costCenterDetail?.analysisCodeId) >= 0) {
                costCenterDetail.analysisCode = this.getAnalysisDetailById(costCenterDetail.analysisCodeId);
                delete costCenterDetail.analysisCodeId;
              }
              return costCenterDetail;
            });
          }

          console.log(val);

          val.isChartOfAccount = true;
          val.chartOfAccounts = this.getChartOfAccountSegments();
        } else {
          val.costCenterSplit.map((costCenterDetail: any) => {
            delete costCenterDetail.analysisCodeId;
            delete costCenterDetail.naturalAccountId;
          });
          val.isChartOfAccount = false;
        }
        this.totalBudgetReferenceSplitValidate(val);
        this.totalSplitValidate([this.budgetTypeSplit]);

        if (toNumber(this.defaultValues.budgetType.id) === 3) {
          this.totalBudgetProjectSplitValidate();
        }

        if (this.naturalAccountNumberList.length) {
          // ADD NATURAL ACCOUNT SPLIT AMOUNT CHECK [TO DO]
          this.totalNaturalAccountAmountSplitValidate()

        }

        this.updateParentModel(val, this.form.valid);
      });

    this.onAddBudgetTypeSplit();
    this.onAddBudgetBasedProjectSplit();

    if (this.parentAfeTotalAmount) {
      this.supplementalAmount = this.defaultValues.totalAmount - this.parentAfeTotalAmount;
    }
    this.unsubscribe.push(formChangesSubscr);
  }

  openAmountBreakdownModal() {
    this.supplimentalModalReady = true;

    this.newBudgetBasedProjectSplit = this.budgetBasedProjectSplit?.value?.length ? _.cloneDeep(this.budgetBasedProjectSplit.value) : [];

    if (!this.newBudgetBasedProjectSplit?.length) {
      const budgetType = this.defaultValues?.budgetType || null;
      if (budgetType) {
        console.log(budgetType.id);
        const projectSplits = _.cloneDeep(this.defaultValues.projectComponentSplit);
        this.newBudgetBasedProjectSplit = [];
        projectSplits.forEach((projectSplit: any) => {
          this.newBudgetBasedProjectSplit.push({
            "id": projectSplit.id,
            "title": projectSplit.title,
            "totalAmount": projectSplit.amount,
            "budgetedAmount": ((toNumber(budgetType.id) === 1) ? projectSplit.amount : 0),
            "unbudgetedAmount": ((toNumber(budgetType.id) === 2) ? projectSplit.amount : 0),
            "currency": projectSplit.currency
          });
        });
      }
    }

    this.cdr.detectChanges();
    this.sabModelComponent.open();
  }

  onAddBudgetBasedProjectSplit(changeEvent: boolean = false, budgetId: number = 0) {

    console.log('onAddBudgetBasedProjectSplit', changeEvent, budgetId);
    setTimeout(() => {

      if (budgetId) {
        this.budgetTypeSplit.value.forEach((budgetTypeAmount: ISplitAmount, key: number) => {

          if (toNumber(budgetTypeAmount.id) === budgetId) {

            if (toNumber(budgetTypeAmount.id) === 1) {
              const newValue = toNumber((toNumber(this.defaultValues.totalAmount) - toNumber(budgetTypeAmount.amount)).toFixed(2));

              this.budgetTypeSplit.at(1).patchValue({
                amount: (newValue > 0) ? newValue : 0
              });
            }

            if (toNumber(budgetTypeAmount.id) === 2) {

              const newValue = toNumber((toNumber(this.defaultValues.totalAmount) - toNumber(budgetTypeAmount.amount)).toFixed(2));

              this.budgetTypeSplit.at(0).patchValue({
                amount: (newValue > 0) ? newValue : 0
              });
            }

            this.cdr.detectChanges();

          }
        });
      }

      this.budgetBasedProjectSplit.clear();
      this.cdr.detectChanges();
      if (
        (this.defaultValues.projectComponentSplit.length > 1)
        && (toNumber(this.defaultValues.budgetType.id) === 3)
      ) {
        let budgetedAmount = 0;
        let unbudgetedAmount = 0;
        let budgetPercentage: any;
        let unbudgetPercentage: any;
        let totalAmount = this.defaultValues.totalAmount;

        this.budgetTypeSplit.value.forEach((budgetTypeAmount: ISplitAmount) => {
          if (toNumber(budgetTypeAmount.id) === 1) {
            budgetedAmount = toNumber(budgetTypeAmount.amount);
          }
          if (toNumber(budgetTypeAmount.id) === 2) {
            unbudgetedAmount = toNumber(budgetTypeAmount.amount);
          }
        });

        budgetPercentage = (budgetedAmount / totalAmount).toFixed(2);
        unbudgetPercentage = (unbudgetedAmount / totalAmount).toFixed(2);

        if ((toNumber(budgetPercentage) + toNumber(unbudgetPercentage)) !== 1) {
          unbudgetPercentage = (1 - toNumber(budgetPercentage)).toFixed(2);
        }

        this.defaultValues.projectComponentSplit.forEach((projectComponentSplit: ISplitAmount) => {
          let defaultValue = null;
          if (!changeEvent) {
            defaultValue = this.getDefaultBudgetProjectValue(toNumber(projectComponentSplit.id));
          }

          console.log('defaultValue');
          console.log(defaultValue);

          let budgetAmount: any;
          let unbudgetedAmount: any;

          if (defaultValue) {
            budgetAmount = defaultValue.budgetedAmount;
            unbudgetedAmount = defaultValue.unbudgetedAmount;
          } else {
            budgetAmount = toNumber((toNumber(budgetPercentage) * toNumber(projectComponentSplit.amount)).toFixed(2));
            unbudgetedAmount = toNumber((toNumber(unbudgetPercentage) * toNumber(projectComponentSplit.amount)).toFixed(2));

            if ((budgetAmount + unbudgetedAmount) !== projectComponentSplit.amount) {
              unbudgetedAmount = toNumber((unbudgetedAmount + (toNumber(projectComponentSplit.amount) - (budgetAmount + unbudgetedAmount))).toFixed(2));
            }
          }

          const projectBudetAmount = new FormGroup({
            id: new FormControl(toNumber(projectComponentSplit.id)),
            title: new FormControl(projectComponentSplit.title),
            currency: new FormControl(projectComponentSplit.currency),
            budgetedAmount: new FormControl(budgetAmount),
            unbudgetedAmount: new FormControl(unbudgetedAmount),
            totalAmount: new FormControl(toNumber(projectComponentSplit.amount)),
          });

          this.budgetBasedProjectSplit.push(projectBudetAmount);
          this.cdr.detectChanges();
        });

        let validBudgetedAmount = 0;
        let validUnbudgetedAmount = 0;

        this.budgetBasedProjectSplit.value.forEach((budgetBasedProjectSplit: IBudgetBasedProjectSplitAmount) => {
          validBudgetedAmount = toNumber((toNumber(validBudgetedAmount) + toNumber(budgetBasedProjectSplit.budgetedAmount)).toFixed(2));

          validUnbudgetedAmount = toNumber((toNumber(validUnbudgetedAmount) + toNumber(budgetBasedProjectSplit.unbudgetedAmount)).toFixed(2));
        });

        if ((budgetedAmount !== validBudgetedAmount) || (unbudgetedAmount !== validUnbudgetedAmount)) {
          const splitList = this.budgetBasedProjectSplit.value;
          for (let i = 0; i < 1; i++) {

            if (budgetedAmount !== validBudgetedAmount) {
              const adjustmentAmount = toNumber((budgetedAmount - validBudgetedAmount).toFixed(2));

              const updatedAmount = toNumber((toNumber(splitList[i].budgetedAmount) + adjustmentAmount).toFixed(2));

              this.budgetBasedProjectSplit.at(i).patchValue({
                budgetedAmount: (updatedAmount > 0) ? updatedAmount : null
              });
              this.cdr.detectChanges();
            }

            if (unbudgetedAmount !== validUnbudgetedAmount) {
              const adjustmentAmount = toNumber((unbudgetedAmount - validUnbudgetedAmount).toFixed(2));

              const updatedAmount = toNumber((toNumber(splitList[i].unbudgetedAmount) + adjustmentAmount).toFixed(2));

              this.budgetBasedProjectSplit.at(i).patchValue({
                unbudgetedAmount: (updatedAmount > 0) ? updatedAmount : null
              });
              this.cdr.detectChanges();
            }
          }
        }

      }

      if (
        (this.defaultValues.projectComponentSplit.length === 1)
        && (toNumber(this.defaultValues.budgetType.id) === 3)
      ) {
        let budgetedAmount = 0;
        let unbudgetedAmount = 0;

        this.budgetTypeSplit.value.forEach((budgetTypeAmount: ISplitAmount) => {
          if (toNumber(budgetTypeAmount.id) === 1) {
            budgetedAmount = toNumber(budgetTypeAmount.amount);
          }
          if (toNumber(budgetTypeAmount.id) === 2) {
            unbudgetedAmount = toNumber(budgetTypeAmount.amount);
          }
        });

        this.defaultValues.projectComponentSplit.forEach((projectComponentSplit: ISplitAmount) => {
          const projectBudetAmount = new FormGroup({
            id: new FormControl(toNumber(projectComponentSplit.id)),
            title: new FormControl(projectComponentSplit.title),
            currency: new FormControl(projectComponentSplit.currency),
            budgetedAmount: new FormControl(toNumber(budgetedAmount)),
            unbudgetedAmount: new FormControl(toNumber(unbudgetedAmount)),
            totalAmount: new FormControl(toNumber(projectComponentSplit.amount)),
          });
          this.budgetBasedProjectSplit.push(projectBudetAmount);
          this.cdr.detectChanges();
        });
      }
    }, changeEvent ? 1000 : 100);
  }

  getDefaultBudgetProjectValue(projectId: number) {
    if (this.defaultValues && this.defaultValues.budgetBasedProjectSplit) {
      return this.defaultValues.budgetBasedProjectSplit.find((budgetBasedProjectSplit: IBudgetBasedProjectSplitAmount) => {
        if (toNumber(budgetBasedProjectSplit.id) === toNumber(projectId)) {
          return budgetBasedProjectSplit;
        }
      });
    }
    return null;
  }

  getChartOfAccountSegments() {
    let chartOfAccount: IChartOfAccounts[] = [];

    // this.costCenterSplit.controls.forEach((costCenterSplit: any, key) => {
    //   if (this.isMultiChartOfAccount || (!this.isMultiChartOfAccount && !key) && costCenterSplit?.value?.chartOfAccountSplits) {
    //     const chartOfAccountObj = {
    //       segments: {
    //         ...costCenterSplit.value.chartOfAccountSplits
    //       },
    //       amount: this.getChartOfAccountAmount(costCenterSplit, true),
    //       currency: this.defaultValues.currencyDetail.currency
    //     }
    //     delete chartOfAccountObj.segments.amount;
    //     chartOfAccount.push(chartOfAccountObj);
    //   }
    // })

    const chartOfAccountList = this.getUniqueChartOfAccountView();

    chartOfAccountList.forEach((chartOfAccountDetail: any) => {

      const { amount, ...segments } = chartOfAccountDetail;

      chartOfAccount.push({
        segments: {
          ...segments
        },
        amount,
        currency: this.defaultValues.currencyDetail.currency
      })
    });


    return chartOfAccount;
    // return this.form.controls.chartOfAccount.value;
  }

  totalBudgetReferenceSplitValidate(val: any): void {
    let totalBudgetReferenceAmount = 0;
    this.totalBudgetRefError = false;

    val?.costCenterSplit.forEach((costCenter: any) => {
      costCenter?.budgetReferenceNumberSplit.forEach((budgetReferenceNumber: any) => {
        totalBudgetReferenceAmount = toNumber((toNumber(totalBudgetReferenceAmount) + toNumber(budgetReferenceNumber.amount)).toFixed(2));
      });
    });

    if (val.costCenterSplit.length && (totalBudgetReferenceAmount !== toNumber(this.defaultValues.totalAmount.toFixed(2)))) {

      this.costCenterSplit.setErrors({
        totalBudgetReferenceAmountError: true
      })
      this.totalBudgetRefError = true;
    }
  }

  totalSplitValidate(formArrayList: FormArray[]): void {
    this.totalBudgetError = false;

    formArrayList.forEach((formArray) => {
      let totalSplitAmount = 0;
      formArray.value.forEach((valueDetail: any) => {
        totalSplitAmount = toNumber(totalSplitAmount) + toNumber(valueDetail.amount)
      })

      if (toNumber(this.defaultValues.totalAmount.toFixed(2)) !== toNumber(totalSplitAmount.toFixed(2))) {
        formArray.setErrors({
          totalAmountError: true
        })
        this.totalBudgetError = true
      }
    })
  }

  totalNaturalAccountAmountSplitValidate() {
    let totalSplitAmount = 0;
    this.naturalAccountNumberSplit.value.forEach((valueDetail: any) => {
      totalSplitAmount = toNumber(totalSplitAmount) + toNumber(valueDetail.amount)
    })

    if (toNumber(this.defaultValues.totalAmount.toFixed(2)) !== toNumber(totalSplitAmount.toFixed(2))) {
      this.naturalAccountNumberSplit.setErrors({
        totalAmountError: true
      })
    }
  }

  totalBudgetProjectSplitValidate(): void {
    this.totalBudgetProjectError = false;
    this.totalBudgetedProjectError = false;
    this.totalUnbudgetedProjectError = false;

    let totalSplitAmount = 0;
    let totalBudgtedSplitAmount = 0;
    let totalUnudgtedSplitAmount = 0;

    this.budgetedAmount = 0;
    this.unbudgetedAmount = 0;

    this.budgetTypeSplit.value.forEach((budgetTypeAmount: ISplitAmount) => {
      if (toNumber(budgetTypeAmount.id) === 1) {
        this.budgetedAmount = toNumber(toNumber(budgetTypeAmount.amount).toFixed(2));
      }
      if (toNumber(budgetTypeAmount.id) === 2) {
        this.unbudgetedAmount = toNumber(toNumber(budgetTypeAmount.amount).toFixed(2));
      }
    });

    console.log('----------------------- SPLIT DEBUG START -----------------------');

    console.log(this.budgetTypeSplit);
    console.log(this.budgetBasedProjectSplit);
    console.log('budgetedAmount - ', this.budgetedAmount)
    console.log('unbudgetedAmount - ', this.unbudgetedAmount)

    this.budgetBasedProjectSplit.value.forEach((valueDetail: any) => {
      if (valueDetail && valueDetail.totalAmount
        && valueDetail.budgetedAmount !== null && valueDetail.budgetedAmount !== undefined
        && valueDetail.unbudgetedAmount !== null && valueDetail.unbudgetedAmount !== undefined
      ) {
        totalSplitAmount = toNumber(totalSplitAmount) + toNumber((toNumber(valueDetail.budgetedAmount) + toNumber(valueDetail.unbudgetedAmount)).toFixed(2));

        totalBudgtedSplitAmount = toNumber((toNumber(totalBudgtedSplitAmount) + toNumber((valueDetail.budgetedAmount).toFixed(2))).toFixed(2));

        totalUnudgtedSplitAmount = toNumber((toNumber(totalUnudgtedSplitAmount) + toNumber((valueDetail.unbudgetedAmount).toFixed(2))).toFixed(2));

        console.log('valueDetail', valueDetail)
        console.log('totalSplitAmount', totalSplitAmount);
        console.log('totalBudgtedSplitAmount', totalBudgtedSplitAmount);
        console.log('totalUnudgtedSplitAmount', totalUnudgtedSplitAmount);

        if ((
          valueDetail.totalAmount !==
          this.toFixed(
            (
              valueDetail.budgetedAmount + valueDetail.unbudgetedAmount
            ), 2
          )
        )) {
          this.budgetBasedProjectSplit.setErrors({
            projectAmountError: true
          })
        }
      }
    })

    if (toNumber(this.defaultValues.totalAmount.toFixed(2)) != toNumber(totalSplitAmount)) {
      this.budgetBasedProjectSplit.setErrors({
        totalAmountError: true
      })
      this.totalBudgetProjectError = true
    }

    console.log('this.budgetedAmount !== totalBudgtedSplitAmount', this.budgetedAmount, totalBudgtedSplitAmount);

    if (this.budgetedAmount !== totalBudgtedSplitAmount) {
      this.budgetBasedProjectSplit.setErrors({
        totalBudgetedProjectError: true
      })
      this.totalBudgetedProjectError = true
    }

    console.log('this.unbudgetedAmount !== totalUnudgtedSplitAmount', this.unbudgetedAmount, totalUnudgtedSplitAmount);

    if (this.unbudgetedAmount != totalUnudgtedSplitAmount) {
      this.budgetBasedProjectSplit.setErrors({
        totalUnbudgetedProjectError: true
      })
      this.totalUnbudgetedProjectError = true
    }

    console.log('----------------------- SPLIT DEBUG END -----------------------');

  }

  getNaturalAccountSplitControl(): FormGroup {

    const naturalAccountGroup = new FormGroup({
      id: new FormControl(this.getNaturalAccountDefaultValue('id'), [Validators.required]),
      title: new FormControl(this.getNaturalAccountDefaultValue('title')),
      number: new FormControl(this.getNaturalAccountDefaultValue('number'), [Validators.required]),
      amount: new FormControl(this.naturalAccountNumberList.length <= 1 ? this.defaultValues.totalAmount : this.getNaturalAccountDefaultValue('amount'), [Validators.required, Validators.min(1), Validators.max(***************)]),
      currency: new FormControl(this.defaultValues.currencyDetail.currency, [Validators.required]),
    });

    return naturalAccountGroup;

  }

  /**
   * Initialize natural account split array with saved data or single control
   */
  initializeNaturalAccountSplitArray(): FormArray {
    const formArray = new FormArray<FormGroup>([]);

    if (this.defaultValues?.naturalAccountNumberSplit?.length) {
      // Load all saved natural account splits
      this.defaultValues.naturalAccountNumberSplit.forEach((splitData: any) => {
        const control = this.getNaturalAccountSplitControlWithData(splitData);
        formArray.push(control);
      });

      // After form is initialized, populate the title and number fields
      setTimeout(() => {
        this.populateNaturalAccountDetails();
      }, 100);
    } else {
      // Create single default control
      formArray.push(this.getNaturalAccountSplitControl());
    }

    return formArray;
  }

  /**
   * Populate natural account details for all loaded splits
   */
  populateNaturalAccountDetails(): void {
    if (this.naturalAccountNumberSplit && this.naturalAccountNumberSplit.controls) {
      this.naturalAccountNumberSplit.controls.forEach((control, index) => {
        const accountId = control.get('id')?.value;
        if (accountId) {
          this.setNaturalAccountNumber(index);
        }
      });

      // Set naturalAccountNumber variable for single account display
      if (this.naturalAccountNumberList.length === 1 && this.naturalAccountNumberSplit.controls.length > 0) {
        const firstControl = this.naturalAccountNumberSplit.controls[0];
        const accountNumber = firstControl.get('number')?.value;
        if (accountNumber) {
          this.naturalAccountNumber = accountNumber;
          this.cdr.detectChanges();
        }
      }
    }
  }

  /**
   * Create natural account split control with specific data
   */
  getNaturalAccountSplitControlWithData(splitData: any): FormGroup {
    // Find the natural account details from the list
    const accountDetails = this.naturalAccountNumberList.find(account =>
      toNumber(account.id) === toNumber(splitData.id)
    );

    return new FormGroup({
      id: new FormControl(splitData.id || '', [Validators.required]),
      title: new FormControl(accountDetails?.title || splitData.title || ''),
      number: new FormControl(accountDetails?.number || splitData.number || '', [Validators.required]),
      amount: new FormControl(splitData.amount || '', [Validators.required, Validators.min(1), Validators.max(***************)]),
      currency: new FormControl(this.defaultValues.currencyDetail.currency, [Validators.required]),
    });
  }

  getAnalysisCodeSplitControl(): FormGroup {
    const AnalysisCodeGroup = new FormGroup({
      id: new FormControl(this.getAnalysisCodeDefaultValue('id'), [Validators.required]),
      code: new FormControl(this.getAnalysisCodeDefaultValue('code'), [Validators.required]),
      title: new FormControl(this.getAnalysisCodeDefaultValue('title')),
      amount: new FormControl(this.defaultValues.totalAmount, [Validators.required, Validators.min(1), Validators.max(***************)]),
      currency: new FormControl(this.defaultValues.currencyDetail.currency, [Validators.required]),
    });

    return AnalysisCodeGroup;
  }

  defaultCostCenterLoading(): void {
    if (this.defaultValues?.costCenterSplit?.length) {
      this.defaultValues?.costCenterSplit.forEach((costCenter: ICostCenterSplitAmount, key: any) => {

        let costCenterAvailable = null;
        if (costCenter.id) {
          costCenterAvailable = this.costCenterList.find((costCenterDetail) => {
            return (toNumber(costCenterDetail.id) === toNumber(costCenter.id))
          });
        } else {
          costCenterAvailable = true;
        }

        if (costCenterAvailable) {

          // let naturalAccountId = costCenter?.naturalAccount?.id || 0;
          let analysisCodeId = costCenter?.analysisCode?.id || 0;

          if (
            // (naturalAccountId > 0 && !this.isNaturalAccountExistById(naturalAccountId)) ||
            (analysisCodeId > 0 && !this.isAnalysisExistById(analysisCodeId))
          ) {

          } else {
            if (this.costCenterList.length && costCenter.id) {
              this.addCostCenterControls(toNumber(costCenter.id), true, (costCenter?.section || ''), analysisCodeId);

              // this.changeCostCenterStatus({
              //   id: toNumber(costCenter.id),
              //   code: costCenter.code,
              //   budgetReferenceNumberSplit: []
              // });
            }
            this.costCenterSplit.controls.forEach((costCenterSplit) => {

              if (
                (toNumber(costCenter?.id || 0) === toNumber(costCenterSplit?.value?.id || 0)) &&
                (toNumber(costCenter?.analysisCode?.id || 0) === toNumber(costCenterSplit?.value?.analysisCodeId || 0)) 
                // && (toNumber(costCenter?.naturalAccount?.id || 0) === toNumber(costCenterSplit?.value?.naturalAccountId || 0))
              ) {
                if (costCenter?.budgetReferenceNumberSplit?.length) {
                  costCenter?.budgetReferenceNumberSplit.forEach((budgetReferenceNumberSplit, key) => {
                    if ((toNumber(this.defaultValues.budgetType.id) === 2) && !key) {
                      this.onBudgetReferenceNumberAdd(costCenterSplit, budgetReferenceNumberSplit)
                    }
                    if ((this.defaultValues.budgetType.id !== 2)) {
                      this.onBudgetReferenceNumberAdd(costCenterSplit, budgetReferenceNumberSplit)
                    }
                  });
                }
              }
            });
          }
        }
      });
    }

    this.setBudgetRefNumberSingleValue();
  }

  setBudgetRefNumberSingleValue() {
    let isSingleBudgetRef = false;

    console.log(this.costCenterSplit);

    if (this.costCenterSplit?.value?.length <= 1) {
      if (this.costCenterSplit.value[0]?.budgetReferenceNumberSplit?.length <= 1) {
        isSingleBudgetRef = true;
      }
    }

    if (isSingleBudgetRef) {
      this.costCenterSplit.controls.forEach((costCenterSplit) => {
        let costCenterSplitDetail = costCenterSplit as FormGroup;
        const budgetRefNumberSplit = costCenterSplitDetail.controls.budgetReferenceNumberSplit as FormArray;

        if (budgetRefNumberSplit?.length) {
          budgetRefNumberSplit.at(0).get('amount')?.patchValue(this.defaultValues.totalAmount);
          this.cdr.detectChanges();
        }
      });
    }
  }

  onCostCenterAdd(): void {

    console.log(this.form);

    this.isCombinationAvailable = false;
    this.cdr.detectChanges();

    if (this.form.value.selectedCostCenter &&
      // (
      //   (this.naturalAccountNumberList?.length && this.form.value.selectedNaturalAccount)
      //   ||
      //   (!this.naturalAccountNumberList?.length && !this.form.value.selectedNaturalAccount)
      // ) &&
      (
        (this.analysisCodeList?.length && this.form.value.selectedAnalysisCode)
        ||
        (!this.analysisCodeList?.length && !this.form.value.selectedAnalysisCode)
      )) {

      this.isCostCenterError = false;
      this.cdr.detectChanges();

      if (this.validateCombination()) {
        this.isCombinationAvailable = true;
        this.cdr.detectChanges();
        return;
      }

      this.addCostCenterControls(this.form.value.selectedCostCenter, false, '', this.form.value.selectedAnalysisCode);

      /*
        this.changeCostCenterStatus(
          {
            id: toNumber(this.form.value.selectedCostCenter),
            title: this.getCostCenterCodeById(this.form.value.selectedCostCenter, CostCenterObjectEnum.NAME),
            code: this.getCostCenterCodeById(this.form.value.selectedCostCenter, CostCenterObjectEnum.CODE),
            budgetReferenceNumberSplit: []
          }
        );
      */
      this.setBudgetRefNumberSingleValue();

      this.form.patchValue({
        selectedCostCenter: '',
        selectedAnalysisCode: '',
        // selectedNaturalAccount: ''
      });

      this.cdr.detectChanges();
    } else {
      this.isCostCenterError = true;
      this.cdr.detectChanges();
    }

  }

  validateCombination() {
    const { costCenterSplit, selectedCostCenter, selectedNaturalAccount, selectedAnalysisCode } = this.form.value;
    const combinationIds = [selectedCostCenter, selectedNaturalAccount, selectedAnalysisCode].map(toNumber).join('-');

    const isCombinationAvailable = costCenterSplit.some((costCenterDetail: any) => {
      const { id, naturalAccount, analysisCode } = costCenterDetail;
      const availableCombinationIds = [id, naturalAccount?.id, analysisCode?.id].map(toNumber).join('-');
      return availableCombinationIds === combinationIds;
    });

    return isCombinationAvailable;
  }

  getSection(costCenterId: number) {
    return this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.SECTION_HEAD);
  }

  getCostCenterCodeById(costCenterId: any, parameter: CostCenterObjectEnum): any {
    const costCenterData = this.costCenterList.find((costCenterDetail: any) => costCenterDetail.id == costCenterId)
    return costCenterData ? costCenterData[parameter] : ''
  }

  addCostCenterControls(costCenterId: any, isDefaultLoad: boolean, sectionTitle: string = '', analysisCode: any = null): void {
    const costCenterGroup = this.getCostCenterSplitControl(costCenterId, isDefaultLoad, sectionTitle, analysisCode);
    this.costCenterSplit.push(costCenterGroup)
  }

  getCostCenterSplitControl(costCenterId: any, isDefaultLoad: boolean, sectionTitle: string = '', analysisCode: any = null): FormGroup {
    const addDefaultBudgetRefNumber = (this.costCenterList.length == 0) && (this.defaultValues.costCenterSplit && this.defaultValues?.costCenterSplit.length && this.defaultValues?.costCenterSplit[0]?.id);

    // sectionList: new FormControl(this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.SECTION_HEAD)),

    const sections = this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.SECTION_HEAD);

    let sectionControl = {};

    if (sections?.length) {
      sectionControl = {
        section: new FormControl(sectionTitle, [Validators.required])
      }
    }

    return new FormGroup({
      id: new FormControl(toNumber(costCenterId)),
      title: new FormControl(this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.NAME)),
      code: new FormControl(this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.CODE)),
      budgetReferenceNumberSplit: new FormArray((addDefaultBudgetRefNumber || !isDefaultLoad) ? [this.getBudgetReferenceNumberSplitControl()] : [], [Validators.required]),
      // chartOfAccountSplits: this.getChartOfAccountNewSplitControl(costCenterId, toNumber(naturalAccount), toNumber(analysisCode)),
      ...sectionControl,
      // naturalAccountId: new FormControl(toNumber(naturalAccount)),
      analysisCodeId: new FormControl(toNumber(analysisCode)),
    });
  }

  getUniqueChartOfAccountView() {

    const uniqueSegments: any = {};

    this.costCenterSplit.controls.forEach((costCenter: any) => {

      const costCenterDetail = costCenter.value;

      if (costCenterDetail?.chartOfAccountSplits) {
        let totalAmount = 0;

        costCenterDetail?.budgetReferenceNumberSplit.forEach((budgetReferenceNumberSplit: any) => {
          totalAmount = toNumber(totalAmount) + toNumber(budgetReferenceNumberSplit.amount);
        })

        const { amount, ...coa } = costCenterDetail.chartOfAccountSplits;

        const key = JSON.stringify(coa); // Convert the segment object to a string
        if (uniqueSegments[key]) {
          uniqueSegments[key].amount += totalAmount; // If the segment exists, add the amount
        } else {
          uniqueSegments[key] = { ...coa, amount: totalAmount }; // If the segment doesn't exist, create a new entry
        }
      }

    });


    const uniqueCoaList = Object.values(uniqueSegments); // Get the values from the uniqueSegments object

    console.log(uniqueCoaList);
    return uniqueCoaList as IChartOfAccountList[];
  }

  sectionChange(control: any) {
    if (!control.value) {
      control.patchValue('');
      this.cdr.detectChanges();
    }

  }

  changeCostCenterStatus(CostCenterAdded: any, index: number | null = null): void {
    // this.costCenterList.map((costCenter: any) => {
    //   if (costCenter.id == CostCenterAdded.id) {
    //     costCenter.active = !costCenter.active;
    //   }
    // })

    if (index != null) {
      this.costCenterSplit.removeAt(index);
    }

  }

  totalCostCenterLeft(): number {
    return this.costCenterList.filter((costCenter: any) => costCenter.active).length;
  }

  getCostCenterSelectedValue(coseCenterId: any): string {
    const costCenterDetail = this.getCostCenterDetail(coseCenterId);
    return `${costCenterDetail?.code} - ( ${costCenterDetail?.name} )`
  }

  getCostCenterDetail(coseCenterId: any): (ICostCenter | undefined) {
    return this.costCenterList.find((costCenter: any) => costCenter.id == coseCenterId)
  }

  onBudgetReferenceNumberAdd(costCenterSplit: any, budgetReferenceNumberSplit: any | null = null): void {
    const budgetReferenceNumberSplitControl = this.getBudgetReferenceNumberSplitControl(budgetReferenceNumberSplit);
    this.getbudgetReferenceSplit(costCenterSplit).push(budgetReferenceNumberSplitControl)
  }

  getBudgetReferenceNumberSplitControl(budgetReferenceNumberSplit: IBudgetReferenceSplit | null = null): FormGroup {
    const formGroupBudgetReference = new FormGroup({
      number: new FormControl((budgetReferenceNumberSplit ? budgetReferenceNumberSplit.number : null), [Validators.required]),
      amount: new FormControl((budgetReferenceNumberSplit ? budgetReferenceNumberSplit.amount : null), [Validators.required, Validators.min(1), Validators.max(***************)]),
      currency: new FormControl(this.defaultValues.currencyDetail.currency, [Validators.required]),
    });

    if (this.defaultValues.budgetType.id == 2) {
      formGroupBudgetReference.get('number')?.patchValue('N/A');
      formGroupBudgetReference.get('number')?.disable();
    }

    return formGroupBudgetReference;
  }

  getChartOfAccountSplitControl(costCenterId: number): FormGroup {
    const companyCode = this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.COMPANY_CODE);
    const costCenterCode = (this.defaultValues.requestTypeId === 2) ? this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.CODE) : '';

    const naturalAccountNumber = this.getNaturalAccountDefaultValue('number');
    const analysisCode = (this.defaultValues.requestTypeId === 2) ? this.getAnalysisCodeDefaultValue('code') : '000000';

    let totalAmount = 0;
    if (this.defaultValues.requestTypeId === 1) {
      totalAmount = this.defaultValues.totalAmount;
    }

    if (this.defaultValues.requestTypeId === 2) {
      if (this.form && this.form.controls) {
        this.costCenterSplit.controls.forEach((costCenterSplit: any, key) => {
          if (costCenterSplit.value &&
            costCenterSplit.value.id &&
            (costCenterSplit.value.id === costCenterId)) {
            if (costCenterSplit.value && costCenterSplit.value?.budgetReferenceNumberSplit?.length) {
              costCenterSplit.value.budgetReferenceNumberSplit.forEach((budgetReferenceNumberSplit: any) => {
                totalAmount = totalAmount + budgetReferenceNumberSplit.amount
              })
            }
          }
        });
      }
    }

    return new FormGroup({
      segment1: new FormControl({ value: companyCode ? companyCode.code : '0000', disabled: false }),
      segment2: new FormControl({ value: costCenterCode ? costCenterCode : '0000', disabled: false }),
      segment3: new FormControl({ value: naturalAccountNumber ? naturalAccountNumber : '********', disabled: false }),
      segment4: new FormControl({ value: '0000', disabled: false }),
      segment5: new FormControl({ value: '000000', disabled: false }),
      segment6: new FormControl({ value: analysisCode ? analysisCode : '000000', disabled: false }),
      segment7: new FormControl({ value: '********', disabled: false }),
      segment8: new FormControl({ value: '0000', disabled: false }),
      amount: new FormControl({ value: totalAmount, disabled: false }),
    });
  }

  getChartOfAccountNewSplitControl(costCenterId: number, naturalAccountId: number, analysisCodeId: number): FormGroup {
    const companyCode = this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.COMPANY_CODE);
    const costCenterCode = (this.defaultValues.requestTypeId === 2) ? this.getCostCenterCodeById(costCenterId, CostCenterObjectEnum.CODE) : '';

    // const naturalAccountNumber = this.getNaturalAccountDefaultValue('number');

    const naturalAccountNumber = this.getNaturalAccountNumberById(naturalAccountId, NaturalObjectEnum.NUMBER);

    // const analysisCode = (this.defaultValues.requestTypeId === 2) ? this.getAnalysisCodeDefaultValue('code') : '000000';

    const analysisCode = (this.defaultValues.requestTypeId === 2) ? this.getAnalysisCodeById(analysisCodeId, AnalysisObjectEnum.CODE) : '000000';

    let totalAmount = 0;
    if (!this.isMultiChartOfAccount) {
      totalAmount = this.defaultValues.totalAmount;
    } else {
      if (this.form && this.form.controls) {
        this.costCenterSplit.controls.forEach((costCenterSplit: any, key) => {
          if (costCenterSplit.value &&
            costCenterSplit.value.id &&
            (costCenterSplit.value.id === costCenterId)) {
            if (costCenterSplit.value && costCenterSplit.value?.budgetReferenceNumberSplit?.length) {
              costCenterSplit.value.budgetReferenceNumberSplit.forEach((budgetReferenceNumberSplit: any) => {
                totalAmount = totalAmount + budgetReferenceNumberSplit.amount
              })
            }
          }
        });
      }
    }

    return new FormGroup({
      segment1: new FormControl({ value: companyCode ? companyCode.code : '0000', disabled: false }),
      segment2: new FormControl({ value: costCenterCode ? costCenterCode : '0000', disabled: false }),
      segment3: new FormControl({ value: naturalAccountNumber ? naturalAccountNumber : '********', disabled: false }),
      segment4: new FormControl({ value: '0000', disabled: false }),
      segment5: new FormControl({ value: '000000', disabled: false }),
      segment6: new FormControl({ value: analysisCode ? analysisCode : '000000', disabled: false }),
      segment7: new FormControl({ value: '********', disabled: false }),
      segment8: new FormControl({ value: '0000', disabled: false }),
      amount: new FormControl({ value: totalAmount, disabled: false }),
    });
  }

  getChartOfAccountAmount(costCenterSplitValue: any, isPayloadReq = false) {

    if (this.defaultValues.requestTypeId === 2) {
      let totalAmount = 0;

      if (isPayloadReq) {
        costCenterSplitValue = costCenterSplitValue.value;
      }

      if (costCenterSplitValue && costCenterSplitValue?.budgetReferenceNumberSplit?.length) {
        costCenterSplitValue?.budgetReferenceNumberSplit.forEach((budgetReferenceNumberSplit: IBudgetReferenceSplit) => {
          totalAmount = totalAmount + budgetReferenceNumberSplit.amount;
        });
      }
      return totalAmount;
    } else {
      return this.defaultValues.totalAmount;
    }

  }

  getNewChartOfAccountAmount(costCenterSplitValue: any, isPayloadReq = false) {

    if (this.isMultiChartOfAccount) {
      let totalAmount = 0;

      if (isPayloadReq) {
        costCenterSplitValue = costCenterSplitValue.value;
      }

      if (costCenterSplitValue && costCenterSplitValue?.budgetReferenceNumberSplit?.length) {
        costCenterSplitValue?.budgetReferenceNumberSplit.forEach((budgetReferenceNumberSplit: IBudgetReferenceSplit) => {
          totalAmount = totalAmount + budgetReferenceNumberSplit.amount;
        });
      }
      return totalAmount;
    } else {
      return this.defaultValues.totalAmount;
    }

  }

  onAddBudgetTypeSplit(): void {

    if (this.defaultValues.budgetType.id == 1 || this.defaultValues.budgetType.id == 3) {
      this.addBudgetTypeControl(1)
    }

    if (this.defaultValues.budgetType.id == 2 || this.defaultValues.budgetType.id == 3) {
      this.addBudgetTypeControl(2)
    }
  }

  addBudgetTypeControl(budgetTypeId: number): void {
    const budgetTypeGroup = new FormGroup({
      id: new FormControl(budgetTypeId, [Validators.required]),
      title: new FormControl(this.getBudgetTypeTitleById(budgetTypeId), [Validators.required]),
      amount: new FormControl(this.getBudgetTypeDefaultValue(budgetTypeId), [Validators.required, Validators.min(1), Validators.max(***************)]),
      currency: new FormControl(this.defaultValues.currencyDetail.currency, [Validators.required])
    });
    this.budgetTypeSplit.push(budgetTypeGroup);
  }

  getBudgetTypeDefaultValue(budgetTypeId: number): number | string {
    if (this.defaultValues.budgetType.id == budgetTypeId) {
      return this.defaultValues.totalAmount;
    } else {
      if (this.defaultValues.hasOwnProperty('budgetTypeSplit')) {
        const splitDetail = this.defaultValues.budgetTypeSplit.find((splitDetail: ISplitAmount) => {
          return splitDetail.id == budgetTypeId
        })
        return splitDetail?.amount;
      }

      return '';

    }
  }

  getBudgetTypeTitleById(budgetTypeId: number): string | undefined {
    // const budgetTypeData = this.defaultValues.budgetTypeList.find((budgetTypeDetail: any) => budgetTypeDetail.id == budgetTypeId)

    // return budgetTypeData?.title
    return BudgetTypeIdMappingEnum[(+budgetTypeId - 1)]
  }

  getNaturalAccountDefaultValue(parameter: any): any {
    if (this.defaultValues.hasOwnProperty('naturalAccountNumberSplit')) {

      const splitDetail = this.defaultValues.naturalAccountNumberSplit.find((naturalAccountNumber: INaturalAccountNumber) => {
        return naturalAccountNumber.number != null
      });

      let naturalAccountSelected = null;

      if (splitDetail) {
        naturalAccountSelected = this.naturalAccountNumberList.find((naturalAccountNumber) => {
          return (toNumber(splitDetail.id) === toNumber(naturalAccountNumber.id))
        })
      }

      if (naturalAccountSelected) {
        if (parameter === 'number') {
          this.naturalAccountNumber = splitDetail?.[parameter];
          this.cdr.detectChanges();
        }
        return splitDetail?.[parameter];
      }

      return this.setSingleNaturalAccount(parameter);

    } else {
      return this.setSingleNaturalAccount(parameter);
    }
  }

  getAnalysisCodeDefaultValue(parameter: any): any {
    if (this.defaultValues.hasOwnProperty('analysisCodeSplit')) {

      const splitDetail = this.defaultValues.analysisCodeSplit.find((analysisCodeSplit: IAnalysisCode) => {
        return analysisCodeSplit.code != null
      });

      let analysisCodeSelected = null;

      if (splitDetail) {
        analysisCodeSelected = this.analysisCodeList.find((analysisCode) => {
          return (toNumber(splitDetail.id) === toNumber(analysisCode.id))
        })
      }

      if (analysisCodeSelected) {
        if (parameter === 'code') {
          this.analysisCode = splitDetail?.[parameter];
          this.cdr.detectChanges();
        }
        return splitDetail?.[parameter];
      }

      return this.setSingleAnalysisCode(parameter);
    } else {
      return this.setSingleAnalysisCode(parameter);
    }
  }

  setSingleAnalysisCode(parameter: any) {
    const list: any = this.analysisCodeList;
    this.analysisCode = (list.length == 1) ? list[0].code : '';
    this.cdr.detectChanges();
    return (list.length == 1) ? list[0][parameter] : ''
  }

  setSingleNaturalAccount(parameter: any) {
    const list: any = this.naturalAccountNumberList;
    this.naturalAccountNumber = (list.length == 1) ? list[0].number : '';
    this.cdr.detectChanges();
    return (list.length == 1) ? list[0][parameter] : '';
  }

  getNaturalAccountNumberById(naturalAccountId: any, parameter: NaturalObjectEnum): any {
    const accountData = this.naturalAccountNumberList.find((accountDetail: any) => accountDetail.id == naturalAccountId)
    return accountData ? accountData[parameter] : '';
  }

  getNaturalAccountDetaulById(naturalAccountId: any): any {
    const accountData = this.naturalAccountNumberList.find((accountDetail: any) => accountDetail.id == naturalAccountId)
    return accountData ? {
      title: accountData?.title,
      id: accountData?.id,
      number: accountData?.number
    } : null;
  }

  getNaturalAccountTitleNumberByNumber(naturalAccountNumber: any): any {
    if (!naturalAccountNumber) {
      // If no number provided, use the first account if there's only one
      if (this.naturalAccountNumberList.length === 1) {
        const accountData = this.naturalAccountNumberList[0];
        return (accountData?.number + ' - (' + accountData?.title + ')');
      }
      return '';
    }

    const accountData = this.naturalAccountNumberList.find((accountDetail: any) => accountDetail.number == naturalAccountNumber)

    return accountData ? (accountData.number + ' - (' + accountData.title + ')') : '';
  }

  /**
   * Get display value for single natural account
   */
  getSingleNaturalAccountDisplay(formControl: any): string {
    // First try to get from form control values
    const accountId = formControl.get('id')?.value;
    const accountNumber = formControl.get('number')?.value;
    const accountTitle = formControl.get('title')?.value;

    // If we have number and title from form control, use them
    if (accountNumber && accountTitle) {
      return `${accountNumber} - (${accountTitle})`;
    }

    // If we have ID, find the account details
    if (accountId) {
      const accountData = this.naturalAccountNumberList.find(account =>
        toNumber(account.id) === toNumber(accountId)
      );
      if (accountData) {
        return `${accountData.number} - (${accountData.title})`;
      }
    }

    // If only one account available, use it
    if (this.naturalAccountNumberList.length === 1) {
      const accountData = this.naturalAccountNumberList[0];
      return `${accountData.number} - (${accountData.title})`;
    }

    return '';
  }

  getNaturalAccountTitleNumberById(id: any): any {
    const accountData = this.naturalAccountNumberList.find((accountDetail: any) => accountDetail.id == id)

    return (accountData?.number + ' - (' + accountData?.title + ')');
  }

  isNaturalAccountExistById(id: any): any {
    const accountData = this.naturalAccountNumberList.find((accountDetail: any) => accountDetail.id == id)
    return !!accountData;
  }


  isAnalysisExistById(id: any): any {
    const accountData = this.analysisCodeList.find((accountDetail: any) => accountDetail.id == id)
    return !!accountData;
  }

  getAnalysisTitleCodeByCode(analysisCode: any): any {
    const accountData = this.analysisCodeList.find((accountDetail: any) => accountDetail.code == analysisCode)
    return (accountData?.code + ' - (' + accountData?.title + ')');
  }

  getAnalysisTitleCodeById(id: any): any {
    const accountData = this.analysisCodeList.find((accountDetail: any) => accountDetail.id == id)
    return (accountData?.code + ' - (' + accountData?.title + ')');
  }

  getAnalysisCodeById(analysysCodeId: any, parameter: AnalysisObjectEnum): any {
    const codeDetail = this.analysisCodeList.find((codeDetail: IAnalysisCode) => codeDetail.id == analysysCodeId)
    return codeDetail ? codeDetail[parameter] : '';
  }

  getAnalysisDetailById(analysysCodeId: any): any {
    const codeDetail = this.analysisCodeList.find((codeDetail: IAnalysisCode) => codeDetail.id == analysysCodeId)
    return codeDetail ? {
      id: codeDetail.id,
      title: codeDetail.title,
      code: codeDetail.code
    } : null;
  }

  setNaturalAccountNumber(index: any) {

    const naturalAccountNumber = this.getNaturalAccountNumberById(this.naturalAccountNumberSplit.at(index)?.value.id, NaturalObjectEnum.NUMBER);

    this.naturalAccountNumberSplit.at(index).patchValue({
      id: toNumber(this.naturalAccountNumberSplit.at(index)?.value.id) ? toNumber(this.naturalAccountNumberSplit.at(index)?.value.id) : '',
      title: this.getNaturalAccountNumberById(this.naturalAccountNumberSplit.at(index)?.value.id, NaturalObjectEnum.TITLE),
      number: naturalAccountNumber ? naturalAccountNumber : ''
    })

    this.costCenterSplit.controls.forEach((costCenterSplit: any) => {
      costCenterSplit.controls.chartOfAccountSplits.patchValue({
        segment3: naturalAccountNumber ? naturalAccountNumber : '********'
      })
    })
  }

  /**
   * Add a new natural account split control to the form array
   */
  addNaturalAccountSplit(): void {
    if (this.naturalAccountNumberList.length > 1) {
      const newNaturalAccountControl = this.getEmptyNaturalAccountSplitControl();
      this.naturalAccountNumberSplit.push(newNaturalAccountControl);
    }
  }

  /**
   * Remove a natural account split control from the form array
   * @param index - The index of the control to remove
   */
  removeNaturalAccountSplit(index: number): void {
    if (this.naturalAccountNumberSplit.length > 1 && index >= 0) {
      this.naturalAccountNumberSplit.removeAt(index);
    }
  }

  /**
   * Get available natural accounts excluding already selected ones
   */
  getAvailableNaturalAccounts(): INaturalAccountNumber[] {
    const selectedAccountIds = this.naturalAccountNumberSplit.controls
      .map(control => control.get('id')?.value)
      .filter(id => id && id !== '');

    return this.naturalAccountNumberList.filter(account =>
      !selectedAccountIds.includes(account.id)
    );
  }

  /**
   * Get available natural accounts for a specific index, excluding already selected ones except current
   */
  getAvailableNaturalAccountsForIndex(currentIndex: number): INaturalAccountNumber[] {
    const selectedAccountIds = this.naturalAccountNumberSplit.controls
      .map((control, index) => {
        // Don't exclude the current index's selection
        if (index === currentIndex) return null;
        return control.get('id')?.value;
      })
      .filter(id => id && id !== '');

    return this.naturalAccountNumberList.filter(account =>
      !selectedAccountIds.includes(account.id)
    );
  }

  /**
   * Create an empty natural account split control for new entries
   */
  getEmptyNaturalAccountSplitControl(): FormGroup {
    return new FormGroup({
      id: new FormControl('', [Validators.required]),
      title: new FormControl(''),
      number: new FormControl('', [Validators.required]),
      amount: new FormControl('', [Validators.required, Validators.min(1), Validators.max(***************)]),
      currency: new FormControl(this.defaultValues.currencyDetail.currency, [Validators.required]),
    });
  }

  /**
   * Check if there's at least one natural account with both account and amount filled
   */
  hasValidNaturalAccountEntry(): boolean {
    if (!this.naturalAccountNumberSplit?.controls?.length) {
      return false;
    }

    return this.naturalAccountNumberSplit.controls.some((control: any) => {
      const id = control.get('id')?.value;
      const amount = control.get('amount')?.value;
      return id && amount && toNumber(amount) > 0;
    });
  }

  setSelectedAnalysisCode() {

  }

  setAnalysisCode(index: any) {
    const analysisCode = this.getAnalysisCodeById(this.analysisCodeSplit.at(index)?.value.id, AnalysisObjectEnum.CODE);
    this.analysisCodeSplit.at(index).patchValue({
      id: toNumber(this.analysisCodeSplit.at(index)?.value.id) ? toNumber(this.analysisCodeSplit.at(index)?.value.id) : '',
      code: analysisCode ? analysisCode : '',
      title: this.getAnalysisCodeById(this.analysisCodeSplit.at(index)?.value.id, AnalysisObjectEnum.TITLE)
    });

    if (this.defaultValues.requestTypeId === 2) {
      this.costCenterSplit.controls.forEach((costCenterSplit: any) => {
        costCenterSplit.controls.chartOfAccountSplits.patchValue({
          segment6: analysisCode ? analysisCode : '000000'
        })
      })
    }
  }

  get budgetTypeSplit(): FormArray {
    return this.form.controls["budgetTypeSplit"] as FormArray;
  }

  get naturalAccountNumberSplit(): FormArray {
    return this.form.controls["naturalAccountNumberSplit"] as FormArray;
  }

  get analysisCodeSplit(): FormArray {
    return this.form.controls["analysisCodeSplit"] as FormArray;
  }

  get costCenterSplit(): FormArray {
    // this.getbudgetReferenceSplit(0);
    return this.form?.controls["costCenterSplit"] as FormArray;
  }

  get budgetBasedProjectSplit(): FormArray {
    // this.getbudgetReferenceSplit(0);
    return this.form?.controls["budgetBasedProjectSplit"] as FormArray;
  }

  getbudgetReferenceSplit(costCenterSplit: any): FormArray {
    return costCenterSplit.get('budgetReferenceNumberSplit') as FormArray;
  }

  getchartOfAccountSplits(costCenterSplit: any): FormArray {
    return costCenterSplit.get('chartOfAccountSplits') as FormArray;
  }

  onRemoveBudgetReferenceNumber(costCenterSplit: any, budgetRefNoIndex: number) {
    costCenterSplit.removeAt(budgetRefNoIndex);
  }

  getchartOfAccountSplitsdata(form: any, controlname: any) {
    return form.controls.chartOfAccountSplits.controls[controlname].value
  }

  toFixed(num: any, fixed: any) {
    if (num) {
      var re = new RegExp('^-?\\d+(?:\.\\d{0,' + (fixed || -1) + '})?');
      return toNumber(num.toString().match(re)[0]);
    }
    return '';
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}