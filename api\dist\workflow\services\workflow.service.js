"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const repositories_1 = require("../../afe-proposal/repositories");
const repositories_2 = require("../../finance/repositories");
const services_1 = require("../../finance/services");
const repositories_3 = require("../../project-component/repositories");
const services_2 = require("../../settings/services");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const validators_1 = require("../../shared/validators");
const dtos_1 = require("../dtos");
const models_1 = require("../models");
const repositories_4 = require("../repositories");
const services_3 = require("../../core/services");
let WorkflowService = class WorkflowService {
    constructor(adminApiClient, workflowMasterSettingRepository, workflowMasterStepRepository, workflowSharedChildLimitRepository, afeProposalLimitDeductionRepository, workflowSharedBucketLimitRepository, projectComponentRepository, costCenterRepository, financeService, settingsService, mSGraphApiClient, afeProposalValidator, currencyTypeRepository, afeProposalRepository, afeProposalAmountSplitRepository, afeProposalApproverRepository, logger) {
        this.adminApiClient = adminApiClient;
        this.workflowMasterSettingRepository = workflowMasterSettingRepository;
        this.workflowMasterStepRepository = workflowMasterStepRepository;
        this.workflowSharedChildLimitRepository = workflowSharedChildLimitRepository;
        this.afeProposalLimitDeductionRepository = afeProposalLimitDeductionRepository;
        this.workflowSharedBucketLimitRepository = workflowSharedBucketLimitRepository;
        this.projectComponentRepository = projectComponentRepository;
        this.costCenterRepository = costCenterRepository;
        this.financeService = financeService;
        this.settingsService = settingsService;
        this.mSGraphApiClient = mSGraphApiClient;
        this.afeProposalValidator = afeProposalValidator;
        this.currencyTypeRepository = currencyTypeRepository;
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalAmountSplitRepository = afeProposalAmountSplitRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.logger = logger;
    }
    getAfeApproversList(computeAfeApproversListDto, submitterId) {
        return __awaiter(this, void 0, void 0, function* () {
            const workflowSettings = yield this.settingsService.getWorkflowSettings();
            if (workflowSettings.workflowYear === 'true' && !computeAfeApproversListDto.year) {
                throw new exceptions_1.HttpException(`Year can't be empty.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const currentYear = new Date().getFullYear();
            if ((workflowSettings === null || workflowSettings === void 0 ? void 0 : workflowSettings.workflowYear) === 'true' &&
                (workflowSettings === null || workflowSettings === void 0 ? void 0 : workflowSettings.enablePreviousWorkflowYear) !== 'true' &&
                computeAfeApproversListDto.year < currentYear) {
                throw new exceptions_1.HttpException(`Past year is not allowed`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if ((workflowSettings === null || workflowSettings === void 0 ? void 0 : workflowSettings.workflowYear) !== 'true') {
                computeAfeApproversListDto.year = currentYear;
            }
            const currencyDetail = yield this.financeService.getCurrencyTypeForEntity(computeAfeApproversListDto.entityId);
            const { conversionRate } = currencyDetail;
            const { totalAmount, projectComponentSplits, costCenters, budgetBasedProjectSplit, budgetTypeSplits, } = computeAfeApproversListDto;
            computeAfeApproversListDto.totalAmount = totalAmount / conversionRate;
            if (projectComponentSplits === null || projectComponentSplits === void 0 ? void 0 : projectComponentSplits.length) {
                computeAfeApproversListDto.projectComponentSplits =
                    yield this.convertProjectSplitAmountToPrimaryCurrency(projectComponentSplits, conversionRate);
            }
            if (costCenters === null || costCenters === void 0 ? void 0 : costCenters.length) {
                computeAfeApproversListDto.costCenters = yield this.convertCostCenterAmountToPrimaryCurrency(costCenters, conversionRate);
            }
            if (budgetBasedProjectSplit === null || budgetBasedProjectSplit === void 0 ? void 0 : budgetBasedProjectSplit.length) {
                computeAfeApproversListDto.budgetBasedProjectSplit =
                    yield this.convertProjectSplitByBudgetTypeAmountToPrimaryCurrency(budgetBasedProjectSplit, conversionRate);
            }
            if (budgetTypeSplits === null || budgetTypeSplits === void 0 ? void 0 : budgetTypeSplits.length) {
                computeAfeApproversListDto.budgetTypeSplits =
                    this.convertBudgetTypeSplitAmountToPrimaryCurrency(budgetTypeSplits, conversionRate);
            }
            return this.computeAfeApproversList(computeAfeApproversListDto, submitterId);
        });
    }
    computeAfeApproversList(computeAfeApproversListDto, submitterId) {
        return __awaiter(this, void 0, void 0, function* () {
            let { requestTypeId, entityId, budgetType, projectComponentSplits, isApprovedByBoard, totalAmount, costCenters, lengthOfCommitment, year, afeType, afeSubType, parentAfeId, budgetBasedProjectSplit, isSupplemental, forUnpublished, budgetTypeSplits, projectLeaderId, } = computeAfeApproversListDto;
            let costCentersAmounts = [];
            let costCentersIds = [];
            if ((costCenters === null || costCenters === void 0 ? void 0 : costCenters.length) && costCenters[costCenters.length - 1].id) {
                const allCostCenterIds = costCenters.map(costCenter => costCenter.id);
                costCentersIds = [...new Set(allCostCenterIds)];
                const doCostCentersExist = yield this.afeProposalValidator.checkAllCostCentersBelongsToBusinessEntity(entityId, costCentersIds);
                if (!doCostCentersExist) {
                    throw new exceptions_1.HttpException(`Invalid cost centers.`, enums_1.HttpStatus.BAD_REQUEST);
                }
                const uniqueCostCenters = new Map();
                for (const costCenter of costCenters) {
                    const { id, code, amount, section } = costCenter;
                    if (uniqueCostCenters.has(id)) {
                        const existingCC = uniqueCostCenters.get(id);
                        uniqueCostCenters.set(id, {
                            id,
                            code,
                            amount: amount + existingCC.amount,
                            sections: [...new Set([...existingCC.sections, section])],
                            aggregateAmount: amount + existingCC.amount,
                        });
                    }
                    else {
                        uniqueCostCenters.set(id, {
                            id,
                            code,
                            amount,
                            sections: [section],
                            aggregateAmount: amount,
                        });
                    }
                }
                costCentersAmounts = Array.from(uniqueCostCenters.values());
            }
            let costCentersWithSection = (costCentersAmounts === null || costCentersAmounts === void 0 ? void 0 : costCentersAmounts.map(costCenter => ({
                id: costCenter.id,
                sectionName: costCenter.sections,
            }))) || [];
            const { currency: primaryCurrency } = yield this.currencyTypeRepository.getPrimaryCurrency();
            const masterSettingWorkflowsDeductions = [];
            let projectComponentSplitsWithChild = [];
            let projectComponentsDetails;
            if (projectComponentSplits === null || projectComponentSplits === void 0 ? void 0 : projectComponentSplits.length) {
                const ids = projectComponentSplits.map(p => p.id);
                projectComponentsDetails = yield this.projectComponentRepository.getProjectComponentsByIds(ids);
                projectComponentSplitsWithChild = yield this.combineProjectComponents(projectComponentSplits, projectComponentsDetails);
            }
            let projectBudgetTypeSplit = [];
            let findMasterWorkflowParams = [];
            if (budgetType === enums_1.BUDGET_TYPE.MIXED) {
                projectBudgetTypeSplit = yield this.splitProjectComponentAmountsInBudgetTypes(budgetBasedProjectSplit, projectComponentsDetails, lengthOfCommitment);
                projectBudgetTypeSplit.forEach(split => {
                    findMasterWorkflowParams.push({
                        requestTypeId,
                        projectComponentId: split.projectComponentId,
                        budgetType: split.budgetType,
                        year: year,
                        amount: split.amount,
                        aggregateAmount: split.amount,
                        lengthOfCommitment: lengthOfCommitment,
                        projectComponentChild: split.projectComponentChild,
                    });
                });
            }
            else if (projectComponentSplitsWithChild === null || projectComponentSplitsWithChild === void 0 ? void 0 : projectComponentSplitsWithChild.length) {
                projectComponentSplitsWithChild.forEach(split => {
                    findMasterWorkflowParams.push({
                        requestTypeId,
                        projectComponentId: split.id,
                        budgetType,
                        year,
                        amount: split.amount,
                        aggregateAmount: split.amount,
                        lengthOfCommitment: lengthOfCommitment,
                        projectComponentChild: split.child,
                    });
                });
            }
            else {
                findMasterWorkflowParams.push({
                    requestTypeId,
                    year: year,
                    amount: totalAmount,
                    aggregateAmount: totalAmount,
                });
            }
            let filteredWorkflowsSteps = [];
            const businessEntities = yield this.adminApiClient.getAllBusinessHierarchy();
            const parentEntities = this.adminApiClient.getPath([businessEntities], `${entityId}`) || [];
            const workflowsMap = new Map();
            for (let i = 0; i < findMasterWorkflowParams.length; i++) {
                const { steps, workflowSetting } = yield this.findWorkflowSteps(findMasterWorkflowParams[i], businessEntities, parentEntities, !!forUnpublished);
                if (steps === null || steps === void 0 ? void 0 : steps.length) {
                    const { workflowMasterSettingId } = steps[steps.length - 1];
                    if (workflowsMap.has(workflowMasterSettingId)) {
                        const existingWorkflow = workflowsMap.get(workflowMasterSettingId);
                        const { amount, lengthOfCommitment, projectComponentChild, aggregateAmount } = existingWorkflow;
                        workflowsMap.set(workflowMasterSettingId, {
                            steps: steps,
                            lengthOfCommitment: Math.max(findMasterWorkflowParams[i].lengthOfCommitment, lengthOfCommitment),
                            amount: findMasterWorkflowParams[i].amount + amount,
                            aggregateAmount: findMasterWorkflowParams[i].aggregateAmount + aggregateAmount,
                            projectComponentChild: [
                                ...new Set([
                                    ...projectComponentChild,
                                    ...findMasterWorkflowParams[i].projectComponentChild,
                                ]),
                            ],
                            workflowSetting,
                            costCenters: costCentersAmounts,
                        });
                    }
                    else {
                        workflowsMap.set(workflowMasterSettingId, {
                            steps: steps,
                            lengthOfCommitment: findMasterWorkflowParams[i].lengthOfCommitment,
                            amount: findMasterWorkflowParams[i].amount,
                            aggregateAmount: findMasterWorkflowParams[i].aggregateAmount,
                            projectComponentChild: findMasterWorkflowParams[i].projectComponentChild,
                            workflowSetting,
                            costCenters: costCentersAmounts,
                        });
                    }
                }
            }
            let costCenterBudgetTypeSplits = null;
            if (workflowsMap.size > 1 &&
                budgetType === enums_1.BUDGET_TYPE.MIXED &&
                (costCentersAmounts === null || costCentersAmounts === void 0 ? void 0 : costCentersAmounts.length) > 0 &&
                Array.from(workflowsMap.values()).every(w => w.workflowSetting.isAggregateLimitApplicable)) {
                costCenterBudgetTypeSplits = this.splitBudgetTypeSplitsIntoCostCenters(budgetTypeSplits, costCentersAmounts);
                this.updateWorkflowCostCenters(workflowsMap, costCentersAmounts, costCenterBudgetTypeSplits);
            }
            let lastWorkflowSteps = [];
            if (isSupplemental && parentAfeId) {
                const latestApprovedVersion = yield this.afeProposalRepository.getLatestVersionInfoOfAfe(parentAfeId);
                const { id: latestApprovedVersionAfeId } = latestApprovedVersion;
                const parerntAfeId = latestApprovedVersion.parentAfeId || latestApprovedVersionAfeId;
                const lastWorkflowApproverSteps = yield this.afeProposalApproverRepository.getApproversByProposalId(latestApprovedVersionAfeId);
                const lastAfeDetail = yield this.afeProposalRepository.getAfeProposalById(latestApprovedVersionAfeId);
                if (year < lastAfeDetail.workflowYear) {
                    throw new exceptions_1.HttpException("Supplemental year can't be less than its parent", enums_1.HttpStatus.BAD_REQUEST);
                }
                lastWorkflowSteps = lastWorkflowApproverSteps
                    .filter(step => !step.originalApproverId && step.workflowMasterStepsId)
                    .map(approver => {
                    const { assignedTo, parallelIdentifier, assignedEntityId, associatedColumn, title, assginedType, assignedLevel, associatedCostCenterId, workflowMasterStepsId } = approver;
                    const step = (0, helpers_1.singleObjectToInstance)(models_1.WorkflowMasterStep, {});
                    step.isIncluded = true;
                    step.associatedLevelEntityId = assignedEntityId;
                    step.parallelIdentifier = parallelIdentifier;
                    step.associateRole = assginedType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE ? assignedTo : null;
                    step.title = title;
                    step.associatedColumn = associatedColumn;
                    step.associateType = assginedType;
                    step.associateLevel = assignedLevel;
                    step.associatedUser = assginedType === associated_type_enum_1.ASSOCIATED_TYPE.USER ? assignedTo : null;
                    step.associatedCostCenterId = associatedCostCenterId || null;
                    step.workflowMasterStepId = workflowMasterStepsId || null;
                    return step;
                });
                const projectAndBudgetTypeSplits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(latestApprovedVersionAfeId, enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT);
                const costCenterSplits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(latestApprovedVersionAfeId, enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT);
                const budgetTypeSplits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndType(latestApprovedVersionAfeId, enums_1.AMOUNT_SPLIT.BUDGET_TYPE_SPLIT);
                if (costCenterSplits === null || costCenterSplits === void 0 ? void 0 : costCenterSplits.length) {
                    const costCenterApprovers = lastWorkflowApproverSteps
                        .filter(approver => {
                        if (!approver.associatedCostCenterId) {
                            return false;
                        }
                        const existingCostCenter = costCentersAmounts.find(c => c.id === approver.associatedCostCenterId);
                        return !existingCostCenter;
                    })
                        .map(approver => {
                        var _a;
                        return ({
                            id: approver.associatedCostCenterId,
                            sectionName: (_a = approver === null || approver === void 0 ? void 0 : approver.otherInfo) === null || _a === void 0 ? void 0 : _a.section,
                        });
                    });
                    const filteredCostCenterApprovers = [];
                    for (const costCenter of costCenterApprovers) {
                        const isCostCenterExist = filteredCostCenterApprovers.find(c => c.id === costCenter.id);
                        if (isCostCenterExist && isCostCenterExist.sectionName && !costCenter.sectionName) {
                            continue;
                        }
                        else if (isCostCenterExist &&
                            !isCostCenterExist.sectionName &&
                            costCenter.sectionName) {
                            isCostCenterExist.sectionName = costCenter.sectionName;
                        }
                        else if (!isCostCenterExist) {
                            filteredCostCenterApprovers.push(costCenter);
                        }
                    }
                    costCentersWithSection = [...costCentersWithSection, ...filteredCostCenterApprovers];
                }
                const uniqueCostCentersSplits = new Map();
                for (const costCenterSplit of costCenterSplits) {
                    const { objectId, amount, objectTitle, currency } = costCenterSplit;
                    if (objectId && uniqueCostCentersSplits.has(objectId)) {
                        const existingCC = uniqueCostCentersSplits.get(objectId);
                        uniqueCostCentersSplits.set(objectId, {
                            id: objectId,
                            title: objectTitle,
                            amount: amount + existingCC.amount,
                            currency,
                        });
                    }
                    else {
                        uniqueCostCentersSplits.set(objectId, {
                            id: objectId,
                            title: objectTitle,
                            amount: amount,
                            currency,
                        });
                    }
                }
                yield this.updateAggregateLimitForSupplemental(projectAndBudgetTypeSplits, Array.from(uniqueCostCentersSplits.values()), budgetTypeSplits, workflowsMap, latestApprovedVersion.totalAmount, costCenterBudgetTypeSplits, costCentersAmounts);
                const recoverDeductions = yield this.returnDeductionEntriesForSupplementalRecovery(parerntAfeId, year, workflowsMap, requestTypeId, costCentersAmounts);
                masterSettingWorkflowsDeductions.push(...recoverDeductions);
            }
            let isAnyCostCentersOperating = false;
            if (costCentersAmounts === null || costCentersAmounts === void 0 ? void 0 : costCentersAmounts.length) {
                isAnyCostCentersOperating = yield this.isAnyCostCenterOperating(costCentersAmounts);
            }
            const projectComponents = projectComponentSplitsWithChild.map(p => ({
                id: p.id,
                amount: p.amount,
            }));
            for (const workflow of workflowsMap.values()) {
                const { steps, amount, lengthOfCommitment, projectComponentChild, aggregateAmount, costCenters, } = workflow;
                const { workflowMasterSetting } = steps[steps.length - 1];
                const filteredSteps = yield this.filterMasterWorkflowSteps(workflowMasterSetting, steps, parentEntities, {
                    amount,
                    aggregateAmount,
                    isApprovedByBoard,
                    entityId,
                    lengthOfCommitment,
                    isAnyCostCentersOperating,
                    isCostCentersPresent: !!(costCenters === null || costCenters === void 0 ? void 0 : costCenters.length),
                    projectComponentChild,
                    projectComponents,
                    afeType,
                    afeSubType,
                    costCenters: costCenters,
                }, primaryCurrency, totalAmount);
                const deductionSteps = filteredSteps.filter(d => (d === null || d === void 0 ? void 0 : d.isDeductionStep) === true);
                for (const deductionStep of deductionSteps) {
                    if (deductionStep) {
                        const { workflowMasterStepId, associatedLevelEntityId, associatedLevelEntityCode, associatedLevelEntityTitle, associateType, parentDeductionStepId, amount, } = deductionStep;
                        if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER) {
                            for (const costCenter of costCenters) {
                                const isCostCenterDeductionExist = masterSettingWorkflowsDeductions.find(d => d.costCenterId === costCenter.id && d.deductionStepId === workflowMasterStepId);
                                if (costCenter.aggregateAmount > 0 && !isCostCenterDeductionExist && amount > 0) {
                                    masterSettingWorkflowsDeductions.push({
                                        workflowMasterSettingId: deductionStep.workflowMasterSettingId,
                                        amount: costCenter.aggregateAmount,
                                        deductionStepId: workflowMasterStepId,
                                        costCenterId: costCenter.id,
                                    });
                                }
                            }
                        }
                        else if (aggregateAmount > 0) {
                            masterSettingWorkflowsDeductions.push({
                                workflowMasterSettingId: deductionStep.workflowMasterSettingId,
                                amount: aggregateAmount,
                                deductionStepId: workflowMasterStepId,
                                associatedEntityId: associatedLevelEntityId,
                                associatedEntityCode: associatedLevelEntityCode,
                                associatedEntityTitle: associatedLevelEntityTitle,
                                parentDeductionStepId: parentDeductionStepId,
                            });
                        }
                    }
                }
                filteredWorkflowsSteps.push(filteredSteps);
            }
            const mergedSteps = this.mergeWorkflowMasterSettingsSteps(filteredWorkflowsSteps, lastWorkflowSteps);
            const { workflow: stepsWithApprovers, isProjectLeaderExist } = yield this.generateApproverListOfWorkflowSteps(mergedSteps, costCentersWithSection, projectLeaderId, submitterId);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.WorkflowResponseDto, {
                steps: stepsWithApprovers,
                masterSettingWorkflows: isApprovedByBoard ? [] : masterSettingWorkflowsDeductions,
                isProjectLeaderExist,
            });
        });
    }
    splitBudgetTypeSplitsIntoCostCenters(budgetTypeSplits, costCenters) {
        var _a, _b;
        const splits = { [enums_1.BUDGET_TYPE.BUDGETED]: [], [enums_1.BUDGET_TYPE.UNBUDGETED]: [] };
        let budgetedAmount = ((_a = budgetTypeSplits.find(b => b.title === enums_1.BUDGET_TYPE.BUDGETED)) === null || _a === void 0 ? void 0 : _a.amount) || 0;
        let unbudgetedAmount = ((_b = budgetTypeSplits.find(b => b.title === enums_1.BUDGET_TYPE.UNBUDGETED)) === null || _b === void 0 ? void 0 : _b.amount) || 0;
        for (let costCenter of costCenters) {
            let { amount: costCenterAmount } = costCenter;
            while (costCenterAmount > 0) {
                if (budgetedAmount > 0) {
                    const amount = Math.min(costCenterAmount, budgetedAmount);
                    splits[enums_1.BUDGET_TYPE.BUDGETED].push(Object.assign(Object.assign({}, costCenter), { amount, aggregateAmount: amount, performBudgetTypeSplit: true }));
                    costCenterAmount -= amount;
                    budgetedAmount -= amount;
                }
                else if (unbudgetedAmount > 0) {
                    const amount = Math.min(costCenterAmount, unbudgetedAmount);
                    splits[enums_1.BUDGET_TYPE.UNBUDGETED].push(Object.assign(Object.assign({}, costCenter), { amount, aggregateAmount: amount, performBudgetTypeSplit: true }));
                    costCenterAmount -= amount;
                    unbudgetedAmount -= amount;
                }
                else {
                    break;
                }
            }
        }
        return splits;
    }
    updateAggregateLimitForSupplemental(projectAndBudgetTypeSplits, costCenterSplits, budgetTypeSplits, workflowsMap, totalAmount, costCenterBudgetTypeSplits = null, costCenters) {
        return __awaiter(this, void 0, void 0, function* () {
            if (projectAndBudgetTypeSplits === null || projectAndBudgetTypeSplits === void 0 ? void 0 : projectAndBudgetTypeSplits.length) {
                for (const split of projectAndBudgetTypeSplits) {
                    const { budgetType: lastBudgetType, objectId: lastProjectComponentId } = split;
                    const possibleFindMasterWorkflowParams = [
                        { budgetType: lastBudgetType, projectComponentId: lastProjectComponentId },
                        { budgetType: lastBudgetType, projectComponentId: null },
                        { budgetType: null, projectComponentId: lastProjectComponentId },
                        { budgetType: null, projectComponentId: null },
                    ];
                    let isWorkflowPresent = false;
                    for (const param of possibleFindMasterWorkflowParams) {
                        for (let [_key, value] of workflowsMap) {
                            const { budgetType, projectComponentId } = value.workflowSetting;
                            if (budgetType === param.budgetType &&
                                projectComponentId === param.projectComponentId) {
                                isWorkflowPresent = true;
                                value.aggregateAmount = value.aggregateAmount - split.amount;
                                break;
                            }
                        }
                        if (isWorkflowPresent) {
                            break;
                        }
                    }
                }
            }
            else {
                const firstWorkflowMap = Array.from(workflowsMap)[0];
                firstWorkflowMap[1].aggregateAmount = parseFloat((firstWorkflowMap[1].aggregateAmount - totalAmount).toFixed(10));
            }
            if (costCenterSplits === null || costCenterSplits === void 0 ? void 0 : costCenterSplits.length) {
                if (costCenterBudgetTypeSplits) {
                    const budgetTypeSplitsObj = budgetTypeSplits.map(budgetTypeSplit => ({
                        id: budgetTypeSplit.objectId,
                        title: budgetTypeSplit.objectTitle,
                        amount: budgetTypeSplit.amount,
                        currency: budgetTypeSplit.currency,
                    }));
                    const costCenterSplitsObj = costCenterSplits.map(costCenterSplit => ({
                        id: costCenterSplit.id,
                        title: costCenterSplit.title,
                        amount: costCenterSplit.amount,
                        currency: costCenterSplit.currency,
                    }));
                    const previousAfeCostCenterBudgetTypeSplits = this.splitBudgetTypeSplitsIntoCostCenters(budgetTypeSplitsObj, costCenterSplitsObj);
                    const { BUDGETED, UNBUDGETED } = costCenterBudgetTypeSplits;
                    const { BUDGETED: PREVIOUS_BUDGETED, UNBUDGETED: PREVIOUS_UNBUDGETED } = previousAfeCostCenterBudgetTypeSplits;
                    const updateCostCenterAmount = (budgetType, costCenters, previousCostCenters) => {
                        for (const costCenter of costCenters) {
                            const { id: costCenterId, amount: costCenterAmount } = costCenter;
                            const previousCostCenter = previousCostCenters.find(c => c.id === costCenterId);
                            if (previousCostCenter) {
                                const { amount: previousCostCenterAmount } = previousCostCenter;
                                const difference = parseFloat((costCenterAmount - previousCostCenterAmount).toFixed(10));
                                const index = costCenters.findIndex(c => c.id === costCenterId);
                                costCenters[index].aggregateAmount = difference;
                            }
                        }
                        costCenterBudgetTypeSplits[budgetType] = costCenters;
                    };
                    updateCostCenterAmount(enums_1.BUDGET_TYPE.BUDGETED, BUDGETED, PREVIOUS_BUDGETED);
                    updateCostCenterAmount(enums_1.BUDGET_TYPE.UNBUDGETED, UNBUDGETED, PREVIOUS_UNBUDGETED);
                    this.updateWorkflowCostCenters(workflowsMap, costCenters, costCenterBudgetTypeSplits);
                }
                else {
                    for (const split of costCenterSplits) {
                        const { id: lastCostCenterId } = split;
                        const existingCostCenter = costCenters.find(costCenter => costCenter.id === lastCostCenterId);
                        if (existingCostCenter) {
                            existingCostCenter.aggregateAmount = parseFloat((existingCostCenter.aggregateAmount - split.amount).toFixed(10));
                        }
                    }
                }
            }
        });
    }
    updateWorkflowCostCenters(workflowsMap, costCenters, costCenterBudgetTypeSplits) {
        let [budgetedWorkflowId, unBudgetedWorkflowId, nonBudgetedWorkflowId] = [null, null, null];
        for (const id of workflowsMap.keys()) {
            const workflow = workflowsMap.get(id);
            const { steps } = workflow;
            const { workflowMasterSetting } = steps[steps.length - 1];
            const { budgetType } = workflowMasterSetting;
            if (budgetType === enums_1.BUDGET_TYPE.BUDGETED) {
                budgetedWorkflowId = id;
            }
            else if (budgetType === enums_1.BUDGET_TYPE.UNBUDGETED) {
                unBudgetedWorkflowId = id;
            }
            else {
                unBudgetedWorkflowId = id;
            }
        }
        if (budgetedWorkflowId || (nonBudgetedWorkflowId && !unBudgetedWorkflowId)) {
            const workflow = workflowsMap.get(budgetedWorkflowId);
            workflow.costCenters = costCenterBudgetTypeSplits[enums_1.BUDGET_TYPE.BUDGETED];
        }
        if (unBudgetedWorkflowId || (nonBudgetedWorkflowId && !budgetedWorkflowId)) {
            const workflow = workflowsMap.get(unBudgetedWorkflowId);
            workflow.costCenters = costCenterBudgetTypeSplits[enums_1.BUDGET_TYPE.UNBUDGETED];
        }
        if (nonBudgetedWorkflowId) {
            const workflow = workflowsMap.get(nonBudgetedWorkflowId);
            workflow.costCenters = costCenters;
        }
    }
    returnDeductionEntriesForSupplementalRecovery(parentAfeId, workflowYear, workflowsMap, requestTypeId, costCenters) {
        return __awaiter(this, void 0, void 0, function* () {
            const masterSettingWorkflowsDeductions = [];
            const allRelatedAfes = yield this.afeProposalRepository.getApprovedAfeAndItsChildernIds(parentAfeId);
            const allRelatedAfeIds = allRelatedAfes.map(afe => afe.id);
            let allDeductions = yield this.afeProposalLimitDeductionRepository.getApprovedLimitDeductionsByProposalIds(allRelatedAfeIds);
            const previousYearDeductions = allDeductions.filter(d => d.workflowMasterSetting.year < workflowYear);
            const previousYearWorkflows = previousYearDeductions.map(d => d.workflowMasterSetting);
            const currentWorkflowMapWithPreviousYearWorkflow = (previousYearWorkflows === null || previousYearWorkflows === void 0 ? void 0 : previousYearWorkflows.length)
                ? yield this.mapCurrentYearWorkflowWithPreviousYearsWorkflow(previousYearWorkflows, requestTypeId, workflowYear)
                : null;
            const costCenterDeductionsStepWiseMap = new Map();
            for (let deduction of allDeductions) {
                if (deduction.costCenterId) {
                    const key = `costCenter-${deduction.costCenterId}-workflowMasterStep-${deduction.workflowMasterStepId}`;
                    if (costCenterDeductionsStepWiseMap.has(key)) {
                        const costCenterDeduction = costCenterDeductionsStepWiseMap.get(key);
                        costCenterDeduction.amount += deduction.amount;
                    }
                    else {
                        const deductionObj = (0, lodash_1.cloneDeep)(deduction);
                        costCenterDeductionsStepWiseMap.set(key, deductionObj);
                    }
                }
            }
            const costCenterDeductionMap = new Map();
            for (let deduction of allDeductions) {
                if (deduction.costCenterId) {
                    const costCenterDeduction = costCenterDeductionMap.get(deduction.costCenterId);
                    if (costCenterDeduction) {
                        deduction.amount += costCenterDeduction.amount;
                    }
                    costCenterDeductionMap.set(deduction.costCenterId, deduction);
                }
            }
            allDeductions = [
                ...allDeductions.filter(d => !d.costCenterId),
                ...Array.from(costCenterDeductionsStepWiseMap.values()),
            ];
            const costCenterTotalDeductionMap = new Map();
            for (const costCenterDeduction of costCenterDeductionMap.values()) {
                const totalCostcenterDeduction = yield this.afeProposalLimitDeductionRepository.totalAmountSpentByCostCenterOnAllRelatedAfes(costCenterDeduction.costCenterId, allRelatedAfeIds);
                costCenterTotalDeductionMap.set(costCenterDeduction.costCenterId, totalCostcenterDeduction);
            }
            const deductionsByWorkflowMasterSettingId = new Map();
            const recoveryDeductions = [];
            for (let deduction of allDeductions) {
                const { workflowMasterSetting, workflowMasterSettingId } = deduction;
                const { year: deductionYear, id: workflowMasterSettingIdInDeduction } = workflowMasterSetting;
                if (deductionYear === workflowYear) {
                    const deductions = deductionsByWorkflowMasterSettingId.get(workflowMasterSettingId);
                    if (deductions) {
                        deductions.push(deduction);
                    }
                    else {
                        deductionsByWorkflowMasterSettingId.set(workflowMasterSettingId, [deduction]);
                    }
                }
                else {
                    const presentCurrentWorkflowIds = Array.from(workflowsMap.values()).map(val => val.workflowSetting.parentId || val.workflowSetting.id);
                    const correspondingCurrentWorkflowId = Array.from(currentWorkflowMapWithPreviousYearWorkflow.entries()).find(value => value[1].id === workflowMasterSettingIdInDeduction &&
                        presentCurrentWorkflowIds.includes(value[0]));
                    if (!correspondingCurrentWorkflowId) {
                        recoveryDeductions.push(deduction);
                    }
                    else {
                        const deductions = deductionsByWorkflowMasterSettingId.get(correspondingCurrentWorkflowId[0]);
                        if (deductions) {
                            deductions.push(deduction);
                        }
                        else {
                            deductionsByWorkflowMasterSettingId.set(correspondingCurrentWorkflowId[0], [deduction]);
                        }
                    }
                }
            }
            for (let [key, deductions] of deductionsByWorkflowMasterSettingId) {
                if (deductions === null || deductions === void 0 ? void 0 : deductions.length) {
                    const totalDeduction = deductions.reduce((acc, obj) => {
                        const { parentId } = obj;
                        if (!parentId) {
                            return acc + obj.amount;
                        }
                        return acc;
                    }, 0);
                    const currentWorkflow = Array.from(workflowsMap.values()).find(value => value.workflowSetting.parentId === +key || value.workflowSetting.id === +key);
                    let recoverAmountRemaining = 0;
                    if (currentWorkflow) {
                        const { amount } = currentWorkflow;
                        const recoverAmount = amount - totalDeduction;
                        if (recoverAmount < 0) {
                            recoverAmountRemaining = Math.abs(recoverAmount);
                        }
                    }
                    else {
                        if (totalDeduction > 0) {
                            recoverAmountRemaining = totalDeduction;
                        }
                    }
                    const deductionsByYear = Object.entries(this.groupedDeductionsByYear(deductions));
                    let start = 0;
                    while (recoverAmountRemaining > 0 && start < deductionsByYear.length) {
                        const yearDeduction = this.totalDeductionByWorkflowStepOrCostCenter(deductionsByYear[start][1][1]);
                        const deductionEntries = this.createRecoverAmountDeductionEntries(recoverAmountRemaining, yearDeduction);
                        masterSettingWorkflowsDeductions.push(...deductionEntries.recoveries);
                        recoverAmountRemaining = deductionEntries.recoverAmountRemaining;
                        start++;
                    }
                }
            }
            if (recoveryDeductions.length) {
                const recoveryDeductionsByYear = Object.entries(this.groupedDeductionsByYear(recoveryDeductions));
                let index = 0;
                while (index < recoveryDeductionsByYear.length) {
                    const yearDeduction = this.totalDeductionByWorkflowStepOrCostCenter(recoveryDeductionsByYear[index][1][1]);
                    const recoveryEntries = yearDeduction.map(deduction => ({
                        amount: deduction.amount * -1,
                        workflowMasterSettingId: deduction.workflowMasterSettingId,
                        associatedEntityId: deduction.entityId,
                        associatedEntityCode: deduction.entityCode,
                        associatedEntityTitle: deduction.entityTitle,
                        deductionStepId: deduction.workflowMasterStepId,
                        costCenterId: deduction.costCenterId,
                        parentDeductionStepId: deduction.parentId,
                    }));
                    masterSettingWorkflowsDeductions.push(...recoveryEntries);
                    index++;
                }
            }
            for (let [key, value] of costCenterDeductionMap) {
                const costCenter = costCenters.find(value => value.id === key);
                if (costCenter) {
                    const totalDeduction = costCenterTotalDeductionMap.get(key);
                    const recoverAmount = costCenter.amount - totalDeduction;
                    const isRestorationExist = masterSettingWorkflowsDeductions.find(d => d.costCenterId === costCenter.id);
                    const workflow = Array.from(workflowsMap.values()).find(val => val.workflowSetting.parentId || val.workflowSetting.id);
                    if (recoverAmount < 0 &&
                        !isRestorationExist &&
                        value.amount >= recoverAmount * -1 &&
                        workflow.aggregateAmount >= 0) {
                        masterSettingWorkflowsDeductions.push({
                            associatedEntityId: null,
                            associatedEntityCode: null,
                            associatedEntityTitle: null,
                            workflowMasterSettingId: value.workflowMasterSettingId,
                            deductionStepId: value.workflowMasterStepId,
                            costCenterId: costCenter.id,
                            amount: recoverAmount,
                            parentDeductionStepId: null,
                        });
                        if (workflow) {
                            workflow.aggregateAmount = workflow.aggregateAmount + Math.abs(recoverAmount);
                        }
                    }
                }
                else {
                    const recoverAmount = value.amount;
                    const isRestorationExist = masterSettingWorkflowsDeductions.find(d => d.costCenterId === key);
                    if (recoverAmount > 0 && !isRestorationExist) {
                        masterSettingWorkflowsDeductions.push({
                            associatedEntityId: null,
                            associatedEntityCode: null,
                            associatedEntityTitle: null,
                            workflowMasterSettingId: value.workflowMasterSettingId,
                            deductionStepId: value.workflowMasterStepId,
                            costCenterId: key,
                            amount: recoverAmount * -1,
                            parentDeductionStepId: null,
                        });
                        const workflow = Array.from(workflowsMap.values()).find(val => val.workflowSetting.parentId || val.workflowSetting.id);
                        if (workflow) {
                            workflow.aggregateAmount = workflow.aggregateAmount + recoverAmount;
                        }
                    }
                }
            }
            return masterSettingWorkflowsDeductions;
        });
    }
    groupedDeductionsByYear(deductions) {
        const groupedByYear = deductions.reduce((result, obj) => {
            var _a;
            const year = (_a = obj.workflowMasterSetting) === null || _a === void 0 ? void 0 : _a.year;
            if (year) {
                if (!result[year]) {
                    result[year] = [];
                }
                result[year].push(obj);
            }
            return result;
        }, {});
        const entries = Object.entries(groupedByYear);
        return entries.sort(([yearA], [yearB]) => Number(yearB) - Number(yearA));
    }
    totalDeductionByWorkflowStepOrCostCenter(deductions) {
        const map = new Map();
        for (const deduction of deductions) {
            const parentDeduction = deductions.find(d => d.id === deduction.parentId && d.afeProposalId === deduction.afeProposalId);
            const childDeduction = deductions.find(d => d.parentId === deduction.id && d.afeProposalId === deduction.afeProposalId);
            let key;
            if (parentDeduction || childDeduction) {
                key = `workflowMasterStep-childSharing-${deduction.workflowMasterStepId}`;
            }
            else {
                key = deduction.costCenterId
                    ? `costCenter-${deduction.costCenterId}`
                    : `workflowMasterStep-${deduction.workflowMasterStepId}`;
            }
            const existingRecord = map.get(key);
            if (existingRecord) {
                existingRecord.amount += deduction.amount;
            }
            else {
                map.set(key, Object.assign({}, deduction));
            }
        }
        return Array.from(map.values());
    }
    mapCurrentYearWorkflowWithPreviousYearsWorkflow(previousYearWorkflows, requestTypeId, year) {
        return __awaiter(this, void 0, void 0, function* () {
            const workflowsMap = new Map();
            const currentYearWorkflows = yield this.workflowMasterSettingRepository.getAllMasterFlowSettings({ requestTypeId, year });
            for (const currentYearWorkflow of currentYearWorkflows) {
                const { budgetType, projectComponentId } = currentYearWorkflow;
                const possibleFindMasterWorkflowParams = [
                    { budgetType: budgetType, projectComponentId: projectComponentId },
                    { budgetType: budgetType, projectComponentId: null },
                    { budgetType: null, projectComponentId: projectComponentId },
                    { budgetType: null, projectComponentId: null },
                ];
                const previousYearWorkflow = previousYearWorkflows.find(workflow => {
                    const { budgetType, projectComponentId } = workflow;
                    return possibleFindMasterWorkflowParams.some(params => params.budgetType == budgetType && params.projectComponentId == projectComponentId);
                });
                if (previousYearWorkflow) {
                    workflowsMap.set(currentYearWorkflow.id, previousYearWorkflow);
                }
            }
            return workflowsMap;
        });
    }
    createRecoverAmountDeductionEntries(recoverAmount, previousDeductions) {
        const masterSettingWorkflowsDeductions = previousDeductions.reduce((acc, previousDeduction) => {
            if (previousDeduction.amount > 0 && !(previousDeduction === null || previousDeduction === void 0 ? void 0 : previousDeduction.evaluated) && recoverAmount > 0) {
                let deductionAmount = 0;
                if (previousDeduction.costCenterId) {
                    deductionAmount = Math.min(previousDeduction.amount, recoverAmount);
                    if (deductionAmount > 0) {
                        acc.push({
                            workflowMasterSettingId: previousDeduction.workflowMasterSettingId,
                            associatedEntityId: previousDeduction.entityId,
                            associatedEntityCode: previousDeduction.entityCode,
                            associatedEntityTitle: previousDeduction.entityTitle,
                            deductionStepId: previousDeduction.workflowMasterStepId,
                            costCenterId: previousDeduction.costCenterId,
                            amount: deductionAmount * -1,
                        });
                    }
                }
                else {
                    const parentDeduction = previousDeductions.find(d => d.id === previousDeduction.parentId &&
                        d.afeProposalId === previousDeduction.afeProposalId);
                    const childDeduction = previousDeductions.find(d => d.parentId === previousDeduction.id &&
                        d.afeProposalId === previousDeduction.afeProposalId);
                    deductionAmount = Math.min(previousDeduction.amount, recoverAmount);
                    if (deductionAmount > 0) {
                        acc.push({
                            workflowMasterSettingId: previousDeduction.workflowMasterSettingId,
                            associatedEntityId: previousDeduction.entityId,
                            associatedEntityCode: previousDeduction.entityCode,
                            associatedEntityTitle: previousDeduction.entityTitle,
                            deductionStepId: previousDeduction.workflowMasterStepId,
                            costCenterId: previousDeduction.costCenterId || null,
                            amount: deductionAmount * -1,
                            parentDeductionStepId: parentDeduction ? parentDeduction.workflowMasterStepId : null,
                        });
                        if (parentDeduction) {
                            parentDeduction.evaluated = true;
                            acc.push({
                                workflowMasterSettingId: parentDeduction.workflowMasterSettingId,
                                associatedEntityId: parentDeduction.entityId,
                                associatedEntityCode: parentDeduction.entityCode,
                                associatedEntityTitle: parentDeduction.entityTitle,
                                deductionStepId: parentDeduction.workflowMasterStepId,
                                costCenterId: previousDeduction.costCenterId || null,
                                amount: deductionAmount * -1,
                            });
                        }
                        else if (childDeduction) {
                            childDeduction.evaluated = true;
                            acc.push({
                                workflowMasterSettingId: childDeduction.workflowMasterSettingId,
                                associatedEntityId: childDeduction.entityId,
                                associatedEntityCode: childDeduction.entityCode,
                                associatedEntityTitle: childDeduction.entityTitle,
                                deductionStepId: childDeduction.workflowMasterStepId,
                                costCenterId: previousDeduction.costCenterId || null,
                                amount: recoverAmount > childDeduction.amount
                                    ? childDeduction.amount * -1
                                    : recoverAmount * -1,
                                parentDeductionStepId: previousDeduction.workflowMasterStepId,
                            });
                        }
                    }
                }
                recoverAmount -= Math.abs(deductionAmount);
                if (recoverAmount === 0) {
                    return acc;
                }
            }
            return acc;
        }, []);
        return { recoveries: masterSettingWorkflowsDeductions, recoverAmountRemaining: recoverAmount };
    }
    splitProjectComponentAmountsInBudgetTypes(budgetBasedProjectSplitDto, projects, lengthOfCommitment) {
        return __awaiter(this, void 0, void 0, function* () {
            let budgetedProjectComponentSplit = [];
            let unbudgetedProjectComponentSplit = [];
            for (let split of budgetBasedProjectSplitDto) {
                budgetedProjectComponentSplit.push({
                    id: split.id,
                    title: split.title,
                    amount: split.budgetedAmount,
                    currency: split.currency,
                });
                unbudgetedProjectComponentSplit.push({
                    id: split.id,
                    title: split.title,
                    amount: split.unbudgetedAmount,
                    currency: split.currency,
                });
            }
            budgetedProjectComponentSplit = yield this.combineProjectComponents(budgetedProjectComponentSplit, projects);
            unbudgetedProjectComponentSplit = yield this.combineProjectComponents(unbudgetedProjectComponentSplit, projects);
            return [
                ...budgetedProjectComponentSplit.map(split => ({
                    projectComponentId: split.id,
                    amount: split.amount,
                    budgetType: enums_1.BUDGET_TYPE.BUDGETED,
                    lengthOfCommitment,
                    projectComponentChild: split.child,
                })),
                ...unbudgetedProjectComponentSplit.map(split => ({
                    projectComponentId: split.id,
                    amount: split.amount,
                    budgetType: enums_1.BUDGET_TYPE.UNBUDGETED,
                    lengthOfCommitment,
                    projectComponentChild: split.child,
                })),
            ];
        });
    }
    combineProjectComponents(projectComponentSplits, projects) {
        return __awaiter(this, void 0, void 0, function* () {
            const map = new Map();
            for (let projectSplit of projectComponentSplits) {
                let { currency, title, amount, id } = projectSplit;
                const project = projects.find(p => p.id === projectSplit.id);
                if (project === null || project === void 0 ? void 0 : project.parentId) {
                    id = project.parentId;
                    title = project.parentProjectComponent.title;
                }
                let child = [];
                if (map.has(id)) {
                    const projectInfo = map.get(id);
                    amount = amount + projectInfo.amount;
                    child = projectInfo.child;
                    if (!child.includes(projectSplit.id)) {
                        child.push(projectSplit.id);
                    }
                }
                else {
                    child = [projectSplit.id];
                }
                map.set(id, { id, title, amount, currency, child });
            }
            return Array.from(map.values());
        });
    }
    findWorkflowSteps(findMasterWorkflowParam, businessEntities, entityParents, forUnpublished = false) {
        return __awaiter(this, void 0, void 0, function* () {
            const year = findMasterWorkflowParam.year;
            if (!(entityParents === null || entityParents === void 0 ? void 0 : entityParents.length)) {
                throw new exceptions_1.HttpException(`Invalid entity id.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const groupEntityId = Number(entityParents[0].id);
            const { requestTypeId, projectComponentId, budgetType } = findMasterWorkflowParam;
            const possibleFindMasterWorkflowParams = [];
            if (budgetType && projectComponentId) {
                possibleFindMasterWorkflowParams.push({ requestTypeId, budgetType, projectComponentId, year }, { requestTypeId, budgetType, projectComponentId: null, year }, { requestTypeId, budgetType: null, projectComponentId, year }, { requestTypeId, budgetType: null, projectComponentId: null, year });
            }
            else if (budgetType) {
                possibleFindMasterWorkflowParams.push({ requestTypeId, budgetType, projectComponentId: null, year }, { requestTypeId, budgetType: null, projectComponentId: null, year });
            }
            else if (projectComponentId) {
                possibleFindMasterWorkflowParams.push({ requestTypeId, budgetType: null, projectComponentId, year }, { requestTypeId, budgetType: null, projectComponentId: null, year });
            }
            else {
                possibleFindMasterWorkflowParams.push({
                    requestTypeId,
                    budgetType: null,
                    projectComponentId: null,
                    year,
                });
            }
            let groupMasterWorkflowSetting = null;
            for (let i = 0; i < possibleFindMasterWorkflowParams.length; i++) {
                groupMasterWorkflowSetting = yield this.workflowMasterSettingRepository.getMasterFlowSettings(Object.assign(Object.assign({}, possibleFindMasterWorkflowParams[i]), { entityId: groupEntityId }), forUnpublished);
                if (groupMasterWorkflowSetting) {
                    break;
                }
            }
            if (!groupMasterWorkflowSetting) {
                return { workflowSetting: null, steps: [] };
            }
            let entityMasterWorkflowSetting;
            const { id: groupMasterWorkflowSettingId } = groupMasterWorkflowSetting;
            for (let i = entityParents.length - 1; i >= 1; --i) {
                entityMasterWorkflowSetting =
                    yield this.workflowMasterSettingRepository.getMasterFlowSettingsByParentIdAndEntityId(groupMasterWorkflowSettingId, Number(entityParents[i].id), forUnpublished);
                if (entityMasterWorkflowSetting) {
                    break;
                }
            }
            const masterWorkflowSetting = entityMasterWorkflowSetting || groupMasterWorkflowSetting;
            const { unpublishedVersion } = masterWorkflowSetting;
            const steps = yield this.workflowMasterStepRepository.getWorkflowStepsByMasterSettingId(masterWorkflowSetting.id, unpublishedVersion, forUnpublished);
            const excludedLevel = ['Group'];
            if (masterWorkflowSetting.isAggregateLimitApplicable) {
                for (const step of steps) {
                    const { associateType, associateLevel, workflowMasterStepId } = step;
                    if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.ROLE &&
                        associateLevel &&
                        !excludedLevel.includes(associateLevel)) {
                        const associatedLevelEntity = entityParents.find(e => e.entity_type === associateLevel);
                        if (associatedLevelEntity) {
                            const sharedBucketEntityId = yield this.workflowSharedBucketLimitRepository.getSharedBucketEntityIdFromMasterStepIdAndEntityId(associatedLevelEntity.id, workflowMasterStepId);
                            if (sharedBucketEntityId) {
                                const parentEntities = this.adminApiClient.getPath([businessEntities], `${sharedBucketEntityId}`) || [];
                                for (let i = parentEntities.length - 1; i >= 1; --i) {
                                    const workflow = yield this.workflowMasterSettingRepository.getMasterFlowSettingsByParentIdAndEntityId(masterWorkflowSetting.parentId || masterWorkflowSetting.id, Number(parentEntities[i].id), forUnpublished);
                                    if (workflow === null || workflow === void 0 ? void 0 : workflow.id) {
                                        const overriddenStep = yield this.workflowMasterStepRepository.getWorkflowStepsByMasterSettingIdAndStepId(workflow === null || workflow === void 0 ? void 0 : workflow.id, workflowMasterStepId);
                                        if (overriddenStep) {
                                            step.aggregateLimit = overriddenStep.aggregateLimit;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return {
                workflowSetting: masterWorkflowSetting,
                steps: steps.sort((a, b) => b.approvalSequence - a.approvalSequence),
            };
        });
    }
    filterMasterWorkflowSteps(workflowMasterSetting, workflowMasterSteps, entityParents, workflowStepsFilterParam, currency, totalAmount) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { isAggregateLimitApplicable, isCommitmentLengthApplicable, id: workflowSettingId, requestTypeId, budgetType, projectComponentId, parentId: workflowSettingParentId, } = workflowMasterSetting;
            const workflowMasterSettingId = workflowSettingParentId || workflowSettingId;
            const { amount, aggregateAmount, lengthOfCommitment, isApprovedByBoard, isCostCentersPresent, isAnyCostCentersOperating, projectComponentChild, afeType, afeSubType, costCenters, projectComponents, } = workflowStepsFilterParam;
            const alreadyAddedSteps = new Map();
            let isDeductionStepAdded = false;
            let foundLastStep = false;
            for (let i = 0; i < workflowMasterSteps.length; i++) {
                workflowMasterSteps[i].messages = [];
                workflowMasterSteps[i].workflowMasterSettingId = workflowMasterSettingId;
                let { singleLimit, aggregateLimit, rule, workflowMasterStepId, lengthOfCommitment: configuredLengthOfCommitment, associateLevel, associateType, skipLimitRule, isMandatory, includeBelowSteps } = workflowMasterSteps[i];
                const key = this.getWorkflowStepKeyUniqueKey(workflowMasterSteps[i]);
                if (alreadyAddedSteps.has(key)) {
                    const existingStep = alreadyAddedSteps.get(key);
                    if (existingStep === null || existingStep === void 0 ? void 0 : existingStep.isIncluded) {
                        continue;
                    }
                }
                else {
                    alreadyAddedSteps.set(key, workflowMasterSteps[i]);
                }
                const associatedLevelEntity = entityParents.find(e => e.entity_type === associateLevel);
                const associatedLevelEntityId = associatedLevelEntity
                    ? Number(associatedLevelEntity.id)
                    : Number(entityParents[0].id);
                workflowMasterSteps[i].associatedLevelEntityId = associatedLevelEntityId;
                workflowMasterSteps[i].associatedLevelEntityCode = associatedLevelEntity
                    ? associatedLevelEntity.code
                    : entityParents[0].code;
                workflowMasterSteps[i].associatedLevelEntityTitle = associatedLevelEntity
                    ? associatedLevelEntity.short_name
                    : entityParents[0].short_name;
                if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER && !isCostCentersPresent) {
                    workflowMasterSteps[i].isIncluded = false;
                    continue;
                }
                const data = {
                    isApprovedByBoard: isApprovedByBoard,
                    amount: amount,
                    totalAmount: totalAmount,
                    requestTypeId: requestTypeId,
                    lengthOfCommitment: lengthOfCommitment,
                    budgetType: budgetType,
                    projectComponentId: projectComponentId,
                    isAnyCostCentersOperating: isAnyCostCentersOperating,
                    afeType: afeType === null || afeType === void 0 ? void 0 : afeType.toLowerCase(),
                    afeSubType: afeSubType === null || afeSubType === void 0 ? void 0 : afeSubType.toLowerCase(),
                    costCenterId: null,
                };
                if (rule) {
                    const isRuleConditionsSatisfied = yield this.evalRuleCondition(rule, data, projectComponentId, projectComponents, projectComponentChild, costCenters.map(costCenter => (0, lodash_1.toNumber)(costCenter.id)));
                    if (!isRuleConditionsSatisfied) {
                        continue;
                    }
                    workflowMasterSteps[i].isRuleConditionsSatisfied = isRuleConditionsSatisfied;
                }
                if (isMandatory && includeBelowSteps) {
                    for (let j = 0; j < i; j++) {
                        if (!workflowMasterSteps[j].isIncluded && ((workflowMasterSteps[j].rule && workflowMasterSteps[j].isRuleConditionsSatisfied) || !workflowMasterSteps[j].rule)) {
                            workflowMasterSteps[j].isIncluded = true;
                            workflowMasterSteps[j].messages.push(`The ${workflowMasterSteps[j].title} is required to approve.`);
                        }
                    }
                }
                if (isMandatory) {
                    workflowMasterSteps[i].isIncluded = true;
                    workflowMasterSteps[i].messages.push(`The ${workflowMasterSteps[i].title} is required to approve.`);
                    continue;
                }
                if (foundLastStep) {
                    continue;
                }
                workflowMasterSteps[i].balanceValues = [];
                let balanceValues = {
                    costCenterId: null,
                    projectComponentId: projectComponentId,
                    budgetType: budgetType,
                    totalAggregateLimit: null,
                    totalAmountInPipeline: 0,
                    totalAmountSpent: 0,
                    totalSingleLimit: singleLimit,
                };
                if (isAggregateLimitApplicable) {
                    if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER) {
                        for (const costCenter of costCenters) {
                            const totalAmountSpent = aggregateLimit > 0
                                ? yield this.afeProposalLimitDeductionRepository.totalAmountSpentByCostCenterOnAfes(costCenter.id, workflowMasterStepId)
                                : 0;
                            const totalAmountInPipeline = aggregateLimit > 0
                                ? yield this.afeProposalLimitDeductionRepository.calculteTotalAmountInPipelineForCostCenter(costCenter.id, workflowMasterStepId)
                                : 0;
                            workflowMasterSteps[i].balanceValues.push(Object.assign(Object.assign({}, balanceValues), { totalAggregateLimit: aggregateLimit, costCenterId: costCenter.id, totalAmountInPipeline: totalAmountInPipeline || 0, totalAmountSpent: totalAmountSpent || 0 }));
                        }
                    }
                    else {
                        const allAssociatedEntities = yield this.getAllAssociatedEntityIds(associatedLevelEntityId, workflowMasterSettingId, workflowMasterStepId);
                        const totalAmountSpent = aggregateLimit > 0
                            ? yield this.afeProposalLimitDeductionRepository.totalAmountSpentByEntitiesOnAfes(allAssociatedEntities, workflowMasterStepId)
                            : 0;
                        const totalAmountInPipeline = aggregateLimit > 0
                            ? yield this.afeProposalLimitDeductionRepository.calculteTotalAmountInPipelineForEntities(allAssociatedEntities, workflowMasterStepId)
                            : 0;
                        workflowMasterSteps[i].balanceValues.push(Object.assign(Object.assign({}, balanceValues), { totalAggregateLimit: aggregateLimit, totalAmountInPipeline: totalAmountInPipeline || 0, totalAmountSpent: totalAmountSpent || 0 }));
                    }
                }
                else {
                    workflowMasterSteps[i].balanceValues = [balanceValues];
                }
                if (skipLimitRule) {
                    const isSkipRuleConditionsSatisfied = yield this.evalRuleCondition(skipLimitRule, data, projectComponentId, projectComponents, projectComponentChild, costCenters.map(costCenter => (0, lodash_1.toNumber)(costCenter.id)));
                    if (isSkipRuleConditionsSatisfied) {
                        workflowMasterSteps[i].isIncluded = true;
                        workflowMasterSteps[i].messages.push(`As a result of matching ${skipLimitRule.title} rule conditions, ${workflowMasterSteps[i].title} approval is required.`);
                        if (isAggregateLimitApplicable && aggregateLimit === -1) {
                            workflowMasterSteps[i].messages.push(`There will be a deduction of ${currency} ${amount} from the ${workflowMasterSteps[i].title}.`);
                            workflowMasterSteps[i].isDeductionStep = true;
                            isDeductionStepAdded = true;
                        }
                        continue;
                    }
                }
                const sharedBucketStep = (0, lodash_1.find)(workflowMasterSteps, {
                    sharedLimitMasterStepChildId: workflowMasterStepId,
                });
                if (sharedBucketStep === null || sharedBucketStep === void 0 ? void 0 : sharedBucketStep.id) {
                    const childSharedLimits = yield this.workflowSharedChildLimitRepository.findChildSharedLimitByEntityAndWorkflowStep(sharedBucketStep.workflowMasterStepId, workflowMasterStepId, associatedLevelEntityId);
                    if (childSharedLimits) {
                        const { singleLimit: childSingleLimit, aggregateLimit: childAggregateLimit } = childSharedLimits;
                        [singleLimit, aggregateLimit] = [childSingleLimit, childAggregateLimit];
                    }
                    if (isAggregateLimitApplicable) {
                        const allAssociatedEntities = yield this.getAllAssociatedEntityIds(associatedLevelEntityId, workflowMasterSettingId, workflowMasterStepId);
                        const totalAmountSpent = aggregateLimit > 0
                            ? yield this.afeProposalLimitDeductionRepository.totalAmountSpentByEntitiesOnAfes(allAssociatedEntities, workflowMasterStepId)
                            : 0;
                        const totalAmountInPipeline = aggregateLimit > 0
                            ? yield this.afeProposalLimitDeductionRepository.calculteTotalAmountInPipelineForEntities(allAssociatedEntities, workflowMasterStepId)
                            : 0;
                        workflowMasterSteps[i].balanceValues[workflowMasterSteps[i].balanceValues.length - 1] = Object.assign(Object.assign({}, workflowMasterSteps[i].balanceValues[workflowMasterSteps[i].balanceValues.length - 1]), { totalAggregateLimit: aggregateLimit, totalSingleLimit: singleLimit, totalAmountInPipeline: totalAmountInPipeline || 0, totalAmountSpent: totalAmountSpent || 0 });
                        const sharedStepAssociatedLevelEntity = entityParents.find(e => e.entity_type === sharedBucketStep.associateLevel);
                        const isParentBucketLimitLeft = yield this.checkParentAggregateLimitLeft(sharedStepAssociatedLevelEntity.id, sharedBucketStep, aggregateAmount);
                        if (!isParentBucketLimitLeft) {
                            workflowMasterSteps[i].messages.push(`There is no aggregate limit balance left for the ${sharedBucketStep.title} to deduct ${currency} ${aggregateAmount} from the ${workflowMasterSteps[i].title} shared aggregate limit.`);
                            workflowMasterSteps[i].isIncluded = true;
                            continue;
                        }
                    }
                }
                if (singleLimit === 0 ||
                    (isCommitmentLengthApplicable && configuredLengthOfCommitment === 0)) {
                    workflowMasterSteps[i].messages.push(`${workflowMasterSteps[i].title} approval is required.`);
                    workflowMasterSteps[i].isIncluded = true;
                    continue;
                }
                if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER && isCostCentersPresent) {
                    const areCostCentersAmountsSatisfyingTheSingleLimit = costCenters === null || costCenters === void 0 ? void 0 : costCenters.every(costCenter => this.isAmountSatisfyingTheSingleLimit(singleLimit, costCenter.amount));
                    if (!areCostCentersAmountsSatisfyingTheSingleLimit) {
                        workflowMasterSteps[i].messages.push(`Amount ${currency} ${amount} is more than the single limit ${currency} ${singleLimit} of ${workflowMasterSteps[i].title}.`);
                        workflowMasterSteps[i].isIncluded = true;
                        continue;
                    }
                }
                else if (!this.isAmountSatisfyingTheSingleLimit(singleLimit, amount)) {
                    workflowMasterSteps[i].messages.push(`Amount ${currency} ${amount} is more than the single limit ${currency} ${singleLimit} of ${workflowMasterSteps[i].title}.`);
                    workflowMasterSteps[i].isIncluded = true;
                    continue;
                }
                if (isCommitmentLengthApplicable &&
                    configuredLengthOfCommitment &&
                    configuredLengthOfCommitment < lengthOfCommitment) {
                    workflowMasterSteps[i].messages.push(`Length of commitment ${lengthOfCommitment} years is less than required length of commitment ${configuredLengthOfCommitment} years.`);
                    workflowMasterSteps[i].isIncluded = true;
                    continue;
                }
                if (isAggregateLimitApplicable && aggregateLimit !== -1) {
                    let isAggregateLimitSatisfying = true;
                    if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER) {
                        for (const costCenter of costCenters) {
                            const totalAmountSpent = (_a = workflowMasterSteps[i].balanceValues.find(b => b.costCenterId === costCenter.id)) === null || _a === void 0 ? void 0 : _a.totalAmountSpent;
                            const aggregateLimitLeft = aggregateLimit - (totalAmountSpent || 0);
                            if (aggregateLimitLeft < costCenter.aggregateAmount) {
                                isAggregateLimitSatisfying = false;
                            }
                        }
                    }
                    else {
                        const totalAmountSpent = ((_b = workflowMasterSteps[i].balanceValues[0]) === null || _b === void 0 ? void 0 : _b.totalAmountSpent) || 0;
                        const aggregateLimitLeft = aggregateLimit - (totalAmountSpent || 0);
                        isAggregateLimitSatisfying = aggregateLimitLeft >= aggregateAmount;
                    }
                    if (!isAggregateLimitSatisfying) {
                        workflowMasterSteps[i].messages.push(`There is no aggregate limit balance left for the ${workflowMasterSteps[i].title} to deduct ${currency} ${aggregateAmount}.`);
                        workflowMasterSteps[i].isIncluded = true;
                        continue;
                    }
                }
                workflowMasterSteps[i].isIncluded = true;
                if (isAggregateLimitApplicable && !isDeductionStepAdded) {
                    if (sharedBucketStep) {
                        sharedBucketStep.isDeductionStep = true;
                        workflowMasterSteps[i].messages.push(`As ${sharedBucketStep.title} shares the aggregate limit with ${workflowMasterSteps[i].title}, ${currency} ${aggregateAmount} will be deducted from the aggregate limit of ${sharedBucketStep.title} as well.`);
                    }
                    workflowMasterSteps[i].isDeductionStep = true;
                    workflowMasterSteps[i].parentDeductionStepId =
                        (sharedBucketStep === null || sharedBucketStep === void 0 ? void 0 : sharedBucketStep.workflowMasterStepId) || null;
                    isDeductionStepAdded = true;
                    workflowMasterSteps[i].messages.push(`There will be a deduction of ${currency} ${aggregateAmount} from the ${workflowMasterSteps[i].title} aggregate limit.`);
                }
                workflowMasterSteps[i].amount = aggregateAmount;
                foundLastStep = true;
            }
            return workflowMasterSteps;
        });
    }
    evalRuleCondition(rule, data, projectComponentId, projectComponents, projectComponentChild, costCenterIds) {
        return __awaiter(this, void 0, void 0, function* () {
            const childProjectRuleEvaluationResults = [];
            if (projectComponentChild === null || projectComponentChild === void 0 ? void 0 : projectComponentChild.length) {
                for (const projectChildId of projectComponentChild) {
                    const ruleData = Object.assign(Object.assign({}, data), { projectComponentId: projectChildId });
                    const isRuleConditionsSatisfied = yield this.isRuleConditionsValid(rule, ruleData);
                    childProjectRuleEvaluationResults.push(isRuleConditionsSatisfied);
                }
            }
            const projectRuleEvaluationResults = [];
            if (!projectComponentId && (projectComponents === null || projectComponents === void 0 ? void 0 : projectComponents.length)) {
                for (const project of projectComponents) {
                    const ruleData = Object.assign(Object.assign({}, data), { projectComponentId: project.id, amount: project.amount });
                    const isRuleConditionsSatisfied = yield this.isRuleConditionsValid(rule, ruleData);
                    projectRuleEvaluationResults.push(isRuleConditionsSatisfied);
                }
            }
            const costCenterRuleEvaluationResults = [];
            if (costCenterIds === null || costCenterIds === void 0 ? void 0 : costCenterIds.length) {
                for (const costCenterId of costCenterIds) {
                    const ruleData = Object.assign(Object.assign({}, data), { costCenterId });
                    const isRuleConditionsSatisfied = yield this.isRuleConditionsValid(rule, ruleData);
                    costCenterRuleEvaluationResults.push(isRuleConditionsSatisfied);
                }
            }
            const isRuleConditionsSatisfied = (projectComponents === null || projectComponents === void 0 ? void 0 : projectComponents.length) && !projectComponentId
                ? projectRuleEvaluationResults.some(p => p)
                : yield this.isRuleConditionsValid(rule, data);
            if (!isRuleConditionsSatisfied &&
                !(childProjectRuleEvaluationResults.length && childProjectRuleEvaluationResults.some(p => p)) &&
                !costCenterRuleEvaluationResults.some(c => c)) {
                return false;
            }
            return true;
        });
    }
    isAmountSatisfyingTheSingleLimit(singleLimit, amount) {
        if (singleLimit !== null &&
            singleLimit !== undefined &&
            singleLimit < amount &&
            singleLimit !== -1) {
            return false;
        }
        return true;
    }
    isAnyCostCenterOperating(costCenters) {
        return __awaiter(this, void 0, void 0, function* () {
            const costCenterIds = costCenters.map(costCenter => costCenter.id);
            const costCentersDetails = yield this.costCenterRepository.getCostCenterByIds(costCenterIds);
            return costCentersDetails.some(costCenter => costCenter.operating);
        });
    }
    isRuleConditionsValid(rule, data) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rule: ruleExpression } = rule;
            const isRuleConditionsSatisfied = yield (0, helpers_1.ruleValidator)(ruleExpression, data);
            return isRuleConditionsSatisfied;
        });
    }
    checkParentAggregateLimitLeft(associatedLevelEntityId, sharedBucketStep, amount) {
        return __awaiter(this, void 0, void 0, function* () {
            const { aggregateLimit, workflowMasterSettingId, workflowMasterStepId, workflowMasterSetting } = sharedBucketStep;
            const entities = yield this.getAllAssociatedEntityIds(associatedLevelEntityId, (workflowMasterSetting === null || workflowMasterSetting === void 0 ? void 0 : workflowMasterSetting.parentId) || workflowMasterSettingId, workflowMasterStepId);
            const aggregateLimitLeft = yield this.calculteAggregateLimitLeftForAnEntity(aggregateLimit, entities, workflowMasterStepId);
            return aggregateLimitLeft >= amount;
        });
    }
    getAllAssociatedEntityIds(entityId, workflowMasterSettingId, workflowMasterStepId) {
        return __awaiter(this, void 0, void 0, function* () {
            const bucketEntityId = yield this.workflowSharedBucketLimitRepository.getEntityIdWithBucketOrSharedEntity(entityId, workflowMasterSettingId, workflowMasterStepId);
            let entities = [entityId];
            if (bucketEntityId) {
                const sharedBucketEntitiesIds = yield this.workflowSharedBucketLimitRepository.getAllBucketSharedEntityId(bucketEntityId, workflowMasterStepId);
                entities = [...sharedBucketEntitiesIds, bucketEntityId, entityId];
            }
            return entities;
        });
    }
    calculteAggregateLimitLeftForAnEntity(allocatedLimit, entities, workflowMasterStepId) {
        return __awaiter(this, void 0, void 0, function* () {
            const totalSpentAmount = yield this.afeProposalLimitDeductionRepository.totalAmountSpentByEntitiesOnAfes(entities, workflowMasterStepId);
            return allocatedLimit - (totalSpentAmount || 0);
        });
    }
    mergeWorkflowMasterSettingsSteps(filteredWorkflowsSteps, lastWorkflowSteps) {
        let mergedSteps = filteredWorkflowsSteps[0];
        for (let i = 1; i < filteredWorkflowsSteps.length; i++) {
            mergedSteps = this.mergeTwoWorkflows(mergedSteps, filteredWorkflowsSteps[i]);
        }
        if ((lastWorkflowSteps === null || lastWorkflowSteps === void 0 ? void 0 : lastWorkflowSteps.length) && (mergedSteps === null || mergedSteps === void 0 ? void 0 : mergedSteps.length)) {
            mergedSteps = this.mergeTwoWorkflows(mergedSteps, lastWorkflowSteps);
        }
        return mergedSteps === null || mergedSteps === void 0 ? void 0 : mergedSteps.filter(step => step.isIncluded === true);
    }
    getWorkflowStepKeyUniqueKey(step) {
        if (step.associateType === associated_type_enum_1.ASSOCIATED_TYPE.USER) {
            return `associatedLevel:${step.associateLevel || ''}#associateRole:${step.associateRole || ''}#associateType:${step.associateType || ''}#associatedColumn:${step.associatedColumn || ''}#parallelIdentifier:${step.parallelIdentifier || ''}#associatedUser:${step.associatedUser || ''}`;
        }
        return `associatedLevel:${step.associateLevel || ''}#associateRole:${step.associateRole || ''}#associateType:${step.associateType || ''}#associatedColumn:${step.associatedColumn || ''}#parallelIdentifier:${step.parallelIdentifier || ''}`;
    }
    mergeTwoWorkflows(workflowSteps1, workflowSteps2) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        const mergedSteps = [];
        const workflowSteps1Map = new Map();
        const workflowSteps2Map = new Map();
        const setWorkflowStepsMap = (steps, map) => {
            steps.forEach((step, index) => {
                const key = this.getWorkflowStepKeyUniqueKey(step);
                map.set(key, index);
            });
        };
        setWorkflowStepsMap(workflowSteps1, workflowSteps1Map);
        setWorkflowStepsMap(workflowSteps2, workflowSteps2Map);
        let workflowSteps2AddedUpto = -1;
        for (let i = 0; i < workflowSteps1.length; i++) {
            const { parallelIdentifier, associateRole, isIncluded } = workflowSteps1[i];
            let workflowSteps2ParallelIdentifierIndex = -1;
            if (associateRole && parallelIdentifier) {
                workflowSteps2ParallelIdentifierIndex = workflowSteps2.findIndex(s => parallelIdentifier === s.parallelIdentifier);
            }
            const key = this.getWorkflowStepKeyUniqueKey(workflowSteps1[i]);
            const keyIndexInWorkflowSteps2 = workflowSteps2Map.get(key);
            let isCommonStepIncluded;
            if ((keyIndexInWorkflowSteps2 !== undefined || keyIndexInWorkflowSteps2 !== null) &&
                workflowSteps2AddedUpto < keyIndexInWorkflowSteps2) {
                for (let j = workflowSteps2AddedUpto + 1; j < keyIndexInWorkflowSteps2; j++) {
                    mergedSteps.push(workflowSteps2[j]);
                }
                workflowSteps2AddedUpto = keyIndexInWorkflowSteps2;
                isCommonStepIncluded = isIncluded || workflowSteps2[keyIndexInWorkflowSteps2].isIncluded;
                if (isCommonStepIncluded) {
                    workflowSteps1[i].isIncluded = isCommonStepIncluded;
                    if (!((_b = (_a = workflowSteps1[i]) === null || _a === void 0 ? void 0 : _a.balanceValues) === null || _b === void 0 ? void 0 : _b.length) &&
                        ((_d = (_c = workflowSteps2[keyIndexInWorkflowSteps2]) === null || _c === void 0 ? void 0 : _c.balanceValues) === null || _d === void 0 ? void 0 : _d.length)) {
                        workflowSteps1[i].balanceValues = [
                            ...(_e = workflowSteps2[keyIndexInWorkflowSteps2]) === null || _e === void 0 ? void 0 : _e.balanceValues,
                        ];
                    }
                    else if (((_g = (_f = workflowSteps1[i]) === null || _f === void 0 ? void 0 : _f.balanceValues) === null || _g === void 0 ? void 0 : _g.length) &&
                        ((_j = (_h = workflowSteps2[keyIndexInWorkflowSteps2]) === null || _h === void 0 ? void 0 : _h.balanceValues) === null || _j === void 0 ? void 0 : _j.length)) {
                        workflowSteps1[i].balanceValues = [
                            ...workflowSteps1[i].balanceValues,
                            ...(_k = workflowSteps2[keyIndexInWorkflowSteps2]) === null || _k === void 0 ? void 0 : _k.balanceValues,
                        ];
                    }
                }
            }
            else if (workflowSteps2ParallelIdentifierIndex !== -1) {
                for (let j = workflowSteps2AddedUpto + 1; j <= workflowSteps2ParallelIdentifierIndex; j++) {
                    mergedSteps.push(workflowSteps2[j]);
                }
                workflowSteps2AddedUpto = workflowSteps2ParallelIdentifierIndex;
            }
            mergedSteps.push(workflowSteps1[i]);
        }
        if (workflowSteps2AddedUpto < workflowSteps2.length - 1) {
            for (let j = workflowSteps2AddedUpto + 1; j < workflowSteps2.length; j++) {
                mergedSteps.push(workflowSteps2[j]);
            }
        }
        return mergedSteps;
    }
    generateApproverListOfWorkflowSteps(steps, costCentersWithSection, projectLeaderId, submitterId) {
        var _a, _b, _c, _d, _e;
        return __awaiter(this, void 0, void 0, function* () {
            const approvers = [];
            let sequenceNumber = 1;
            const uniqueWorkflowSteps = new Map();
            let isProjectLeaderExist = false;
            if (projectLeaderId && submitterId.toLowerCase() !== (projectLeaderId === null || projectLeaderId === void 0 ? void 0 : projectLeaderId.toLowerCase())) {
                const projectLeaderAdDetails = yield this.mSGraphApiClient.getUserDetails(projectLeaderId);
                if (projectLeaderAdDetails) {
                    isProjectLeaderExist = true;
                    const projectLeader = {
                        firstName: projectLeaderAdDetails.givenName,
                        lastName: projectLeaderAdDetails.surname,
                        email: (_a = projectLeaderAdDetails.mail) === null || _a === void 0 ? void 0 : _a.toLowerCase(),
                        title: projectLeaderAdDetails.jobTitle,
                        loginId: projectLeaderAdDetails.userType === enums_1.AD_USER_TYPE.GUEST
                            ? projectLeaderAdDetails === null || projectLeaderAdDetails === void 0 ? void 0 : projectLeaderAdDetails.mail
                            : projectLeaderAdDetails.userPrincipalName,
                    };
                    approvers.push((0, helpers_1.singleObjectToInstance)(dtos_1.AfeApproversStepsResponseDto, {
                        stepId: null,
                        associateRole: null,
                        associatedLevelEntityId: null,
                        canWorkflowStart: false,
                        parallelIdentifier: null,
                        title: 'Project Leader',
                        associatedColumn: null,
                        associateType: associated_type_enum_1.ASSOCIATED_TYPE.USER,
                        associateLevel: null,
                        workflowMasterSettingId: null,
                        isDeductionStep: false,
                        messages: [],
                        approvers: [projectLeader],
                        approvalSequenceType: enums_1.APPROVAL_SEQUENCE_TYPE.ANYONE,
                        sequenceNumber,
                    }));
                    sequenceNumber += 1;
                }
            }
            for (let i = 0; i < (steps === null || steps === void 0 ? void 0 : steps.length); i++) {
                const key = this.getWorkflowStepKeyUniqueKey(steps[i]);
                if (uniqueWorkflowSteps.has(key)) {
                    continue;
                }
                else {
                    uniqueWorkflowSteps.set(key, steps[i]);
                }
                const { workflowMasterStepId, associateRole, associatedLevelEntityId, canWorkflowStart, parallelIdentifier, title, associatedColumn, associateType, associateLevel, associatedUser, workflowMasterSettingId, isDeductionStep, messages, balanceValues, } = steps[i];
                const commonProps = {
                    stepId: workflowMasterStepId,
                    associateRole,
                    associatedLevelEntityId,
                    canWorkflowStart,
                    parallelIdentifier,
                    title,
                    associatedColumn,
                    associateType,
                    associateLevel,
                    workflowMasterSettingId,
                    isDeductionStep,
                    messages,
                    balanceValues,
                };
                let approversDetail;
                if (associateRole && associatedLevelEntityId) {
                    const users = yield this.adminApiClient.getUsersByRoleOfAnEntity(associateRole, associatedLevelEntityId);
                    const approverIds = users === null || users === void 0 ? void 0 : users.map(user => user.user_name.toLowerCase());
                    const approversAdDetails = approverIds.length
                        ? yield this.mSGraphApiClient.getUsersDetails(approverIds)
                        : [];
                    approversDetail = approverIds.length
                        ? approversAdDetails.map(user => {
                            var _a;
                            return ({
                                firstName: user.givenName,
                                lastName: user.surname,
                                email: ((_a = user === null || user === void 0 ? void 0 : user.mail) === null || _a === void 0 ? void 0 : _a.toLowerCase()) || null,
                                title: user.jobTitle || null,
                                loginId: user.userType === enums_1.AD_USER_TYPE.GUEST
                                    ? user.mail.toLowerCase()
                                    : user.userPrincipalName.toLowerCase(),
                            });
                        })
                        : [];
                    const approvalSequenceType = parallelIdentifier
                        ? enums_1.APPROVAL_SEQUENCE_TYPE.PARALLEL
                        : enums_1.APPROVAL_SEQUENCE_TYPE.ANYONE;
                    approvers.push((0, helpers_1.singleObjectToInstance)(dtos_1.AfeApproversStepsResponseDto, Object.assign(Object.assign({}, commonProps), { approvers: approversDetail, approvalSequenceType,
                        sequenceNumber })));
                    sequenceNumber += 1;
                }
                else if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER &&
                    (costCentersWithSection === null || costCentersWithSection === void 0 ? void 0 : costCentersWithSection.length) > 0) {
                    const presentBalanceValues = (_b = steps.find(step => {
                        var _a;
                        return step.associatedColumn === associatedColumn &&
                            associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER &&
                            ((_a = step.balanceValues) === null || _a === void 0 ? void 0 : _a.length);
                    })) === null || _b === void 0 ? void 0 : _b.balanceValues;
                    const heads = yield this.getCostCentersHead(costCentersWithSection, associatedColumn);
                    const costCenterApprovers = heads.map(user => {
                        var _a, _b;
                        const approver = Object.assign(Object.assign(Object.assign(Object.assign({}, commonProps), { balanceValues: presentBalanceValues, associatedCostCenterId: +user.costCenterId, approvers: [
                                {
                                    firstName: user.givenName,
                                    lastName: user.surname,
                                    email: (_a = user.mail) === null || _a === void 0 ? void 0 : _a.toLowerCase(),
                                    title: user.jobTitle,
                                    loginId: user.userType === enums_1.AD_USER_TYPE.GUEST
                                        ? (_b = user.mail) === null || _b === void 0 ? void 0 : _b.toLowerCase()
                                        : user.userPrincipalName.toLowerCase(),
                                },
                            ], approvalSequenceType: enums_1.APPROVAL_SEQUENCE_TYPE.PARALLEL }), (user.section && { section: user.section })), { sequenceNumber });
                        sequenceNumber += 1;
                        return (0, helpers_1.singleObjectToInstance)(dtos_1.AfeApproversStepsResponseDto, approver);
                    });
                    approvers.push(...costCenterApprovers);
                }
                else if (associateType === associated_type_enum_1.ASSOCIATED_TYPE.USER && associatedUser) {
                    const approverAdDetails = yield this.mSGraphApiClient.getUserDetails(associatedUser);
                    if (approverAdDetails) {
                        const approver = Object.assign(Object.assign({}, commonProps), { approvers: [
                                {
                                    firstName: approverAdDetails.givenName,
                                    lastName: approverAdDetails.surname,
                                    email: (_c = approverAdDetails.mail) === null || _c === void 0 ? void 0 : _c.toLowerCase(),
                                    title: approverAdDetails.jobTitle,
                                    loginId: approverAdDetails.userType === enums_1.AD_USER_TYPE.GUEST
                                        ? (_d = approverAdDetails === null || approverAdDetails === void 0 ? void 0 : approverAdDetails.mail) === null || _d === void 0 ? void 0 : _d.toLowerCase()
                                        : (_e = approverAdDetails.userPrincipalName) === null || _e === void 0 ? void 0 : _e.toLowerCase(),
                                },
                            ], approvalSequenceType: enums_1.APPROVAL_SEQUENCE_TYPE.ANYONE, sequenceNumber });
                        approvers.push((0, helpers_1.singleObjectToInstance)(dtos_1.AfeApproversStepsResponseDto, approver));
                    }
                    sequenceNumber += 1;
                }
            }
            return { workflow: approvers, isProjectLeaderExist };
        });
    }
    getCostCentersHead(costCentersWithSection, associateColumn) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            let heads = [];
            for (const cc of costCentersWithSection) {
                const costCenter = yield this.costCenterRepository.getCostCenterById(cc.id);
                if (costCenter) {
                    if (associateColumn === enums_1.ASSOCIATED_COLUMN.SECTION_HEAD &&
                        ((_a = costCenter.sectionHead) === null || _a === void 0 ? void 0 : _a.length) &&
                        ((_b = cc === null || cc === void 0 ? void 0 : cc.sectionName) === null || _b === void 0 ? void 0 : _b.length)) {
                        const uniqueSectionHeads = new Set();
                        for (const section of cc.sectionName) {
                            const sectionHead = costCenter.sectionHead.find(s => s.title.toLowerCase() === (section === null || section === void 0 ? void 0 : section.toLowerCase()));
                            if ((sectionHead === null || sectionHead === void 0 ? void 0 : sectionHead.user) && !uniqueSectionHeads.has(sectionHead.user.id)) {
                                heads.push(Object.assign(Object.assign({}, sectionHead.user), { costCenterId: costCenter.id, section: section }));
                                uniqueSectionHeads.add(sectionHead.user.id);
                            }
                        }
                    }
                    else if (associateColumn === enums_1.ASSOCIATED_COLUMN.DEPARTMENT_HEAD) {
                        heads.push(Object.assign(Object.assign({}, costCenter['departmentHead']), { costCenterId: costCenter.id }));
                    }
                }
            }
            return heads;
        });
    }
    convertProjectSplitAmountToPrimaryCurrency(projectComponentSplits, conversionRate) {
        return (projectComponentSplits === null || projectComponentSplits === void 0 ? void 0 : projectComponentSplits.map(p => (Object.assign(Object.assign({}, p), { amount: p.amount / conversionRate })))) || null;
    }
    convertProjectSplitByBudgetTypeAmountToPrimaryCurrency(budgetBasedProjectSplits, conversionRate) {
        return ((budgetBasedProjectSplits === null || budgetBasedProjectSplits === void 0 ? void 0 : budgetBasedProjectSplits.map(p => (Object.assign(Object.assign({}, p), { totalAmount: p.totalAmount / conversionRate, budgetedAmount: p.budgetedAmount / conversionRate, unbudgetedAmount: p.unbudgetedAmount / conversionRate })))) || null);
    }
    convertCostCenterAmountToPrimaryCurrency(costCenters, conversionRate) {
        return (costCenters === null || costCenters === void 0 ? void 0 : costCenters.map(c => (Object.assign(Object.assign({}, c), { amount: c.amount / conversionRate })))) || null;
    }
    convertBudgetTypeSplitAmountToPrimaryCurrency(budgetTypeSplits, conversionRate) {
        return (budgetTypeSplits === null || budgetTypeSplits === void 0 ? void 0 : budgetTypeSplits.map(b => (Object.assign(Object.assign({}, b), { amount: b.amount / conversionRate })))) || null;
    }
};
WorkflowService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        repositories_4.WorkflowMasterSettingRepository,
        repositories_4.WorkflowMasterStepRepository,
        repositories_4.WorkflowSharedChildLimitRepository,
        repositories_1.AfeProposalLimitDeductionRepository,
        repositories_4.WorkflowSharedBucketLimitRepository,
        repositories_3.ProjectComponentRepository,
        repositories_2.CostCenterRepository,
        services_1.FinanceService,
        services_2.SettingsService,
        clients_1.MSGraphApiClient,
        validators_1.AfeProposalValidator,
        repositories_2.CurrencyTypeRepository,
        repositories_1.AfeProposalRepository,
        repositories_1.AfeProposalAmountSplitRepository,
        repositories_1.AfeProposalApproverRepository,
        services_3.LoggerService])
], WorkflowService);
exports.WorkflowService = WorkflowService;
//# sourceMappingURL=workflow.service.js.map