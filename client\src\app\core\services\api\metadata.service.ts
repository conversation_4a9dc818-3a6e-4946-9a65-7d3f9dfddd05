import { Injectable } from '@angular/core';
import { NetworkService } from '../network/network.service';
import { GlobalUrls } from '../../constants/urls.constants';
import { ReplaceUrlVariable } from 'src/app/core/constants/urls.constants';
import { AfeRequestType } from '../../models';
import { QuestionTypeEnum } from '@core/enums';

@Injectable({
  providedIn: 'root'
})
export class MetadataService {

  constructor(
    private networkService: NetworkService,
  ) { }

  getRequestTypes<AfeRequestType>() {
    return this.networkService.get<AfeRequestType[]>(GlobalUrls.AFE_API.REQUEST_TYPE);
  }

  getNatureTypes(requestTypeId: any, locationId: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.NATURE_TYPE, { requestTypeId, locationId })
    );
  }

  getParallelIdentifier() {
    return this.networkService.get(
      GlobalUrls.AFE_API.PARALLEL_IDENTIFIER
    );
  }

  getAfeRequestTypes(requestTypeId: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.AFE_REQUEST_TYPE, { requestTypeId })
    );
  }

  getBudgetTypes(requestTypeId: number, typeId: any = null) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.BUDGET_TYPE, { requestTypeId, typeId })
    );
  }

  getSubTypes(entityId: number, typeId: any = null) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.SUB_TYPE_ID, { typeId, entityId })
    );
  }

  getGlobalProcurementQuestions(requestTypeId: any, locationId: any, questionType: string) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.GLOBAL_PROCUREMENT_QUESTION, { requestTypeId, locationId, questionType })
    );
  }

  getAdditionalLocations(requestTypeId: any, entityId: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.GET_ADDITIONAL_LOCATIONS, { requestTypeId, entityId })
    );
  }

  getAdditionalLocationsOnEntities(entityIds: number[]) {
    return this.networkService.post(
      GlobalUrls.AFE_API.GET_ADDITIONAL_LOCATIONS_BY_ENTITY_IDS,
      { entityIds }
    );
  }

  getProjectComponents<ProjectComponent>(requestTypeId: number = 0, locationId: number = 0, childProject: boolean = true, budgetTypeId: number | null = null) {
    return this.networkService.get<ProjectComponent[]>(
      ReplaceUrlVariable(GlobalUrls.AFE_API.PROJECT_COMPONENT, { requestTypeId, locationId, childProject, budgetTypeId })
    );
  }

  getLengthOfCommitments() {
    return this.networkService.get(GlobalUrls.AFE_API.LENGTH_OF_COMMITMENT);
  }

  getNaturalAccountNumber(requestTypeId: any, locationId: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.NATURAL_ACCOUNT_NUMBER, { requestTypeId, locationId })
    );
  }

  getIntercompanyList(excludedCompanyCode: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.INTERCOMPANY_LIST, { excludedCompanyCode })
    );
  }

  getAnalysisCode(requestTypeId: any, locationId: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.ANALYSIS_CODE, { requestTypeId, locationId })
    );
  }

  checkCompanyCodeExists(entityId: number) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.COMPANY_CODE_EXISTS, { entityId })
    );
  }


  getCompanyDetailByEntity<CompanyCodeResponse>(entityId: number) {
    return this.networkService.get<CompanyCodeResponse>(
      ReplaceUrlVariable(GlobalUrls.AFE_API.COMPANY_DETAIL_BY_ENTITY, { entityId })
    );
  }

  getCostCenters(locationId: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.COST_CENTER, { locationId })
    );
  }

  getConversionTypeRate(currencyType: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.CONVERSION_RATE, { currencyType })
    );
  }

  getEntityBasedConversionRate(locationId: any) {
    return this.networkService.get(
      ReplaceUrlVariable(GlobalUrls.AFE_API.ENTITY_CONVERSION_RATE, { locationId })
    );
  }

  getAfeTypes<AfeType>() {
    return this.networkService.get<AfeType[]>(GlobalUrls.AFE_API.AFE_TYPE);
  }

  getAfeSubTypes<AfeType>() {
    return this.networkService.get<AfeType[]>(GlobalUrls.AFE_API.AFE_SUB_TYPE);
  }

  getAllCostCenters<AllCostCenters>() {
    return this.networkService.get<AllCostCenters[]>(GlobalUrls.AFE_API.GET_ALL_COST_CENTERS)
  }
}
