import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { SequlizeOperator } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { GetMasterStepsResponseDTO } from '../dtos';
import { NewSharedChildLimitRequestDTO } from '../dtos/request/new-shared-child-limit-request.dto';
import { UpdateSharedChildLimitRequestDTO } from '../dtos/request/update-shared-child-limit-request.dto';
import { GetChildSharedLimitResponseDTO } from '../dtos/response/get-child-shared-limit-response.dto';
import { WorkflowMasterStep } from '../models';
import { WorkflowMasterSettingRepository, WorkflowMasterStepRepository, WorkflowSharedBucketLimitRepository, WorkflowSharedChildLimitRepository } from '../repositories';
export declare class WorkflowSharedChildLimitService {
    private readonly workflowMasterSettingRepository;
    private readonly workflowMasterStepRepository;
    private readonly adminApiClient;
    private readonly workflowSharedChildLimitRepository;
    private readonly workflowSharedBucketLimitRepository;
    private readonly sequlizeOperator;
    private readonly historyApiClient;
    constructor(workflowMasterSettingRepository: WorkflowMasterSettingRepository, workflowMasterStepRepository: WorkflowMasterStepRepository, adminApiClient: AdminApiClient, workflowSharedChildLimitRepository: WorkflowSharedChildLimitRepository, workflowSharedBucketLimitRepository: WorkflowSharedBucketLimitRepository, sequlizeOperator: SequlizeOperator, historyApiClient: HistoryApiClient);
    addNewSharedLimit(newMasterStepRequestDto: NewSharedChildLimitRequestDTO, currentContext: CurrentContext): Promise<GetChildSharedLimitResponseDTO>;
    getChildSharedLimitList(parentStepId: number, entityId: number): Promise<GetChildSharedLimitResponseDTO[]>;
    deleteChildSharedLimit(id: number, entityId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateChildSharedLimit(id: number, updateSharedChildLimitRequestDTO: UpdateSharedChildLimitRequestDTO, currentContext: CurrentContext): Promise<MessageResponseDto>;
    private getLowestLevelStepDetailForEntityId;
    private getAllChildOfParents;
    getActualStepDetail(stepDetail: WorkflowMasterStep, newMasterStepRequestDto: {
        entityId: number;
        aggregateLimit?: number;
    }, addPublishCondition?: boolean): Promise<{
        finalStepDetail: GetMasterStepsResponseDTO;
        entitySharingSameBucket: number[];
    }>;
    doBasicValidationCheck(stepDetail: WorkflowMasterStep, newMasterStepRequestDto: {
        singleLimit?: number;
        aggregateLimit?: number;
    }, isBalanceCheck?: boolean): Promise<void>;
    private totalLimitSharedWithChild;
}
