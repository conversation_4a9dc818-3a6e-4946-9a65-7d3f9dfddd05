import { GetMasterStepsResponseDTO } from './get-master-steps-response.dto';
export declare class GetBucketSharedLimitResponseDTO {
    id: number;
    entityId: number;
    entityCode: string;
    bucketEntityId: number;
    bucketEntityCode: string;
    workflowMasterSettingId: number;
    workflowMasterStepId: number;
    createdOn: Date;
    updatedOn: Date;
    createdBy: string;
    updatedBy: string;
    constructor(partial?: Partial<GetBucketSharedLimitResponseDTO>);
}
export declare class GetBucketSharedLimitWithStepResponseDTO {
    stepDetail: GetMasterStepsResponseDTO;
    bucketList: GetBucketSharedLimitResponseDTO[];
}
