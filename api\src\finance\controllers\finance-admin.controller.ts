import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response } from 'express';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PermissionsGuard } from 'src/core/guards';
import { PERMISSIONS } from 'src/shared/enums';
import { RequestContext } from 'src/shared/types';
import {
	AddCostCenterRequestDto,
	AnalysisCodeResponseDto,
	CompanyCodeResponseDto,
	CostCenterResponseDto,
	CreateAnalysisCodeRequestDto,
	CreateCompanyCodeRequestDto,
	CreateNaturalAccountNumberRequestDto,
	ImportDataRequestDto,
	NaturalAccountResponseDto,
	PaginatedAnalysisCodeResponseDto,
	PaginatedCompanyCodesResponseDto,
	PaginatedCostCentersResponseDto,
	PaginatedNaturalAccountNumberResponseDto,
	ToggleActiveStateCompanyCodeRequestDto,
	UpdateAnalysisCodeRequestDto,
	UpdateCompanyCodeRequestDto,
	UpdateCostCenterRequestDto,
	UpdateFusionIntegrationRequestDto,
	UpdateMultiNaturalAccountConfigRequestDto,
	UpdateNaturalAccountNumberRequestDto,
} from '../dtos';
import { Permissions } from 'src/core/decorators';
import { MessageResponseDto } from 'src/shared/dtos';
import { FinanceAdminService } from '../services/finance-admin.service';
import { Pagination } from 'src/core/pagination';
import { ReportService } from 'src/report/services';
import { GetHistoryResponseDTO } from '../dtos/response/get-history-response.dto';
import { UploadEvidenceRequestDto } from 'src/afe-proposal/dtos';

@ApiTags('Finance Admin APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
@Controller('finance-admin')

export class FinanceAdminController {
	constructor(
		private readonly financeAdminService: FinanceAdminService,
		private readonly reportService: ReportService
	) { }

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Create new company code for the business entity',
		type: CompanyCodeResponseDto,
	})
	@Post('company-code')
	public async createCompanyCode(
		@Req() request: RequestContext,
		@Body() createCompanyCodeRequestDto: CreateCompanyCodeRequestDto,
	): Promise<CompanyCodeResponseDto> {
		return this.financeAdminService.createCompanyCode(createCompanyCodeRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Update company code detail',
		type: MessageResponseDto,
	})
	@Put('company-code')
	public updateCompanyCodeDetail(
		@Body() updateCompanyCodeRequestDto: UpdateCompanyCodeRequestDto,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.updateCompanyCodeDetail(updateCompanyCodeRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Delete company code by its id.',
		type: MessageResponseDto,
	})
	@Delete('company-code/:id')
	public deleteCompanyCodeById(
		@Param('id') id: number,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.deleteCompanyCodeById(id, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Return the company codes list for an business entity.',
		type: [PaginatedCompanyCodesResponseDto],
	})
	@Get('entities/:entityId/company-codes')
	public getCompanyCodesListByEntityId(
		@Param('entityId') entityId: number,
		@Query('limit') limit: number = 10,
		@Query('page') page: number = 1,
	): Promise<Pagination<CompanyCodeResponseDto>> {
		return this.financeAdminService.getCompanyCodesListByEntityId(entityId, limit, page);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Return the active company code details for an business entity.',
		type: CompanyCodeResponseDto,
	})
	@Get('entities/:entityId/company-code')
	public getEntityActiveCompanyCodeDetails(
		@Param('entityId') entityId: number
	): Promise<CompanyCodeResponseDto> {
		return this.financeAdminService.getEntityActiveCompanyCodeDetails(entityId);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Add new cost center for a company code.',
		type: CostCenterResponseDto,
	})
	@Post('cost-center')
	public async addCostCenter(
		@Req() request: RequestContext,
		@Body() addCostCenterRequestDto: AddCostCenterRequestDto,
	): Promise<CostCenterResponseDto> {
		return this.financeAdminService.addCostCenter(addCostCenterRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Return the cost centers list for a company.',
		type: [PaginatedCostCentersResponseDto],
	})
	@Get('companies/:companyId/cost-centers')
	public getCostCentersByCompanyCodeId(
		@Param('companyId') companyId: number,
		@Query('limit') limit: number = 10,
		@Query('page') page: number = 1,
	): Promise<Pagination<CostCenterResponseDto>> {
		return this.financeAdminService.getCostCentersByCompanyCodeId(companyId, limit, page);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Create new analysis code for a company.',
		type: AnalysisCodeResponseDto,
	})
	@Post('analysis-code')
	public async createAnalysisCode(
		@Req() request: RequestContext,
		@Body() createCompanyCodeRequestDto: CreateAnalysisCodeRequestDto,
	): Promise<AnalysisCodeResponseDto> {
		return this.financeAdminService.createAnalysisCode(createCompanyCodeRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Return the analysis codes list for a company.',
		type: [PaginatedAnalysisCodeResponseDto],
	})
	@Get('companies/:companyId/analysis-codes')
	public getAnalsysisCodesByCompanyCodeId(
		@Param('companyId') companyId: number,
		@Query('limit') limit: number = 10,
		@Query('page') page: number = 1,
	): Promise<Pagination<AnalysisCodeResponseDto>> {
		return this.financeAdminService.getAnalsysisCodesByCompanyCodeId(companyId, limit, page);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Create new natural account number for a company',
		type: NaturalAccountResponseDto,
	})
	@Post('natural-account-number')
	public async createNatualAccountNumber(
		@Req() request: RequestContext,
		@Body() createNaturalAccountNumberRequestDto: CreateNaturalAccountNumberRequestDto,
	): Promise<NaturalAccountResponseDto> {
		return this.financeAdminService.createNatualAccountNumber(createNaturalAccountNumberRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Return the natural account numbers list for a company.',
		type: [PaginatedNaturalAccountNumberResponseDto],
	})
	@Get('companies/:companyId/natural-account-numbers')
	public getNaturalAccountNumbersByCompanyCodeId(
		@Param('companyId') companyId: number,
		@Query('limit') limit: number = 10,
		@Query('page') page: number = 1,
	): Promise<Pagination<NaturalAccountResponseDto>> {
		return this.financeAdminService.getNaturalAccountNumbersByCompanyCodeId(companyId, limit, page);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Update cost center detail',
		type: MessageResponseDto,
	})
	@Put('cost-center')
	public updateCostCenterDetail(
		@Body() updateCostCenterRequestDto: UpdateCostCenterRequestDto,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.updateCostCenterDetail(updateCostCenterRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Update Fusion Integration',
		type: MessageResponseDto,
	})
	@Put('fusion-integration')
	public updateFusionIntegration(
		@Body() updateFusionIntegrationRequestDto: UpdateFusionIntegrationRequestDto,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.updateFusionIntegration(updateFusionIntegrationRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Update analysis code detail',
		type: MessageResponseDto,
	})
	@Put('analysis-code')
	public updateAnalysisCodeDetail(
		@Body() updateAnalysisCodeRequestDto: UpdateAnalysisCodeRequestDto,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.updateAnalysisCodeDetail(updateAnalysisCodeRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Update natural account number detail',
		type: MessageResponseDto,
	})
	@Put('natural-account-number')
	public updateNaturalAccountNumber(
		@Body() updateNaturalAccountNumberRequestDto: UpdateNaturalAccountNumberRequestDto,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.updateNaturalAccountNumber(updateNaturalAccountNumberRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Delete cost center by its id.',
		type: MessageResponseDto,
	})
	@Delete('cost-center/:id')
	public deleteCostCenterById(
		@Param('id') id: number,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.deleteCostCenterById(id, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Delete analysis code by its id.',
		type: MessageResponseDto,
	})
	@Delete('analysis-code/:id')
	public deleteAnalysisCodeById(
		@Param('id') id: number,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.deleteAnalysisCodeById(id, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Delete natural account number by its id.',
		type: MessageResponseDto,
	})
	@Delete('natural-account-number/:id')
	public deleteNaturalAccountNumberById(
		@Param('id') id: number,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.deleteNaturalAccountNumberById(id, request.currentContext);
	}


	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Toggle the state of the company code of an entity.',
		type: MessageResponseDto,
	})
	@Post('toggle-company-state')
	public async toggleActiveStateOfCompany(
		@Req() request: RequestContext,
		@Body() toggleActiveStateCompanyCodeRequestDto: ToggleActiveStateCompanyCodeRequestDto,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.toggleActiveStateOfCompany(toggleActiveStateCompanyCodeRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Deactivate the company code.',
		type: MessageResponseDto,
	})
	@Post('/companies/:companyId/deactivate')
	public async deactivateCompanyCode(
		@Param('companyId') companyId: number,
		@Req() request: RequestContext,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.deactivateCompanyCode(companyId, request.currentContext);
	}


	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({ status: 200, description: 'Download analysis code list.' })
	@Get('analysis-codes/:companyId/download')
	public async downloadAnalysisCode(
		@Res() res: Response,
		@Param('companyId') companyId: number,
	) {
		const { report, filename } = await this.reportService.downloadAnalysisCode(companyId);
		res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
		res.header('Access-Control-Expose-Headers', 'Content-Disposition');
		res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		return res.send(report);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({ status: 200, description: 'Download cost center list.' })
	@Get('cost-centers/:companyId/download')
	public async downloadCostCenter(
		@Res() res: Response,
		@Param('companyId') companyId: number,
	) {
		const { report, filename } = await this.reportService.downloadCostCenters(companyId);
		res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
		res.header('Access-Control-Expose-Headers', 'Content-Disposition');
		res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		return res.send(report);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({ status: 200, description: 'Download natural account list.' })
	@Get('natural-accounts/:companyId/download')
	public async downloadNaturalAccount(
		@Res() res: Response,
		@Param('companyId') companyId: number,
	) {
		const { report, filename } = await this.reportService.downloadNaturalAccounts(companyId);
		res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
		res.header('Access-Control-Expose-Headers', 'Content-Disposition');
		res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
		return res.send(report);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Import analysis code for a company using xlsx file.',
		type: MessageResponseDto,
	})
	@Post('import-analysis-code')
	public async importAnalysisCode(
		@Req() request: RequestContext,
		@Body() importDataRequestDto: ImportDataRequestDto,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.importAnalysisCode(importDataRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Import natural account number for a company using xlsx file.',
		type: MessageResponseDto,
	})
	@Post('import-natural-account-number')
	public async importNaturalAccount(
		@Req() request: RequestContext,
		@Body() importDataRequestDto: ImportDataRequestDto,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.importNaturalAccount(importDataRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Import cost centers for a company using xlsx file.',
		type: MessageResponseDto,
	})
	@Post('import-cost-center')
	public async importCostCenter(
		@Req() request: RequestContext,
		@Body() importDataRequestDto: ImportDataRequestDto,
	): Promise<MessageResponseDto> {
		return this.financeAdminService.importCostCenter(importDataRequestDto, request.currentContext);
	}

	//Get company history by entity id.
	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Get company history by entity id.',
		type: [GetHistoryResponseDTO],
	})
	@Get('/companies/:entityId/history')
	public async getCompanyHistory(
		@Param('entityId') entityId: number,
	) {
		return this.financeAdminService.getCompanyHistory(
			entityId
		);
	}

	//Get Cost Center history by cost center id.
	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Get Cost Center history by cost center id.',
		type: [GetHistoryResponseDTO],
	})
	@Get('/cost-center/:costCenterId/history')
	public async getCostCenterHistory(
		@Param('costCenterId') costCenterId: number,
	) {
		return this.financeAdminService.getCostCenterHistory(
			costCenterId
		);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Upload evidence for company.',
		type: MessageResponseDto
	})
	@Post('/companies/:companyId/upload-evidence')
	public async uploadEvidence(
		@Param('companyId') companyId: number,
		@Req() request: RequestContext,
		@Body() evidences: UploadEvidenceRequestDto
	) {
		return this.financeAdminService.uploadEvidence(companyId, evidences, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 201,
		description: 'Update multi natural account configuration.',
		type: MessageResponseDto
	})
	@Post('/companies/update-multi-natural-account')
	public async updateMultiNaturalAccount(
		@Req() request: RequestContext,
		@Body() updateMultiNaturalAccountRequestDto: UpdateMultiNaturalAccountConfigRequestDto,
	) {
		return this.financeAdminService.updateMultiNaturalAccount(updateMultiNaturalAccountRequestDto, request.currentContext);
	}

	@Permissions(PERMISSIONS.AFE_ADMINISTRATION)
	@ApiResponse({
		status: 200,
		description: 'Get Evidence by compnay id.',
		type: [GetHistoryResponseDTO],
	})
	@Get('/companies/:companyId/evidences')
	public async getEvidences(
		@Param('companyId') companyId: number,
	) {
		return this.financeAdminService.getEvidences(
			companyId
		);
	}
}
