"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskModule = void 0;
const common_1 = require("@nestjs/common");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const repositories_1 = require("../afe-draft/repositories");
const repositories_2 = require("../afe-proposal/repositories");
const repositories_3 = require("../workflow/repositories");
const repositories_4 = require("../finance/repositories");
const repositories_5 = require("../afe-config/repositories");
const clients_1 = require("../shared/clients");
const services_2 = require("../shared/services");
const services_3 = require("../workflow/services");
const repositories_6 = require("../project-component/repositories");
const helpers_1 = require("../shared/helpers");
const services_4 = require("../finance/services");
const repositories_7 = require("../business-entity/repositories");
const repositories_8 = require("../settings/repositories");
const repositories_9 = require("../notification/repositories");
const services_5 = require("../settings/services");
const validators_1 = require("../shared/validators");
const repositories_10 = require("../queue/repositories");
const parallel_identifier_repository_1 = require("../afe-config/repositories/parallel-identifier.repository");
const repositories = [
    repositories_1.DraftAfeRepository,
    repositories_2.AfeProposalRepository,
    repositories_3.WorkflowMasterSettingRepository,
    repositories_3.WorkflowMasterStepRepository,
    repositories_3.WorkflowSharedChildLimitRepository,
    repositories_2.AfeProposalLimitDeductionRepository,
    repositories_3.WorkflowSharedBucketLimitRepository,
    repositories_4.CostCenterRepository,
    repositories_2.AfeProposalAmountSplitRepository,
    repositories_2.AfeProposalApproverRepository,
    repositories_4.CurrencyTypeRepository,
    repositories_4.AnalysisCodeRepository,
    repositories_4.NaturalAccountNumberRepository,
    repositories_5.AfeBudgetTypeRepository,
    repositories_6.ProjectComponentRepository,
    repositories_7.EntitySetupRepository,
    repositories_4.CompanyCodeRepository,
    repositories_8.SettingsRepository,
    repositories_9.NotificationRepository,
    repositories_10.QueueLogRepository,
    parallel_identifier_repository_1.ParallelIdentifierRepository,
    repositories_2.UserCostCenterMappingRepository,
    repositories_2.UserProjectComponentMappingRepository
];
let TaskModule = class TaskModule {
};
TaskModule = __decorate([
    (0, common_1.Module)({
        providers: [
            services_1.TaskService,
            ...repositories,
            clients_1.TaskApiClient,
            services_2.ConditionCreatorService,
            services_3.WorkflowService,
            clients_1.AdminApiClient,
            clients_1.NotificationApiClient,
            helpers_1.DatabaseHelper,
            services_2.SharedAttachmentService,
            clients_1.AttachmentApiClient,
            helpers_1.SequlizeOperator,
            clients_1.HistoryApiClient,
            services_4.FinanceService,
            clients_1.MSGraphApiClient,
            services_5.SettingsService,
            services_2.ExcelSheetService,
            services_4.FinanceAdminService,
            validators_1.AfeProposalValidator,
            services_2.SharedPermissionService,
            services_2.SharedNotificationService
        ],
        controllers: [controllers_1.TaskController, controllers_1.TaskPrivateController],
    })
], TaskModule);
exports.TaskModule = TaskModule;
//# sourceMappingURL=task.module.js.map