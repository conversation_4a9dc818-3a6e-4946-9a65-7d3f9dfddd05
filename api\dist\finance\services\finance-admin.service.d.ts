import { Pagination } from 'src/core/pagination';
import { AdminApiClient, AttachmentApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { ExcelSheetService, SharedAttachmentService } from 'src/shared/services';
import { CurrentContext } from 'src/shared/types';
import { AddCostCenterRequestDto, AnalysisCodeResponseDto, CompanyCodeResponseDto, CostCenterResponseDto, CreateAnalysisCodeRequestDto, CreateCompanyCodeRequestDto, CreateNaturalAccountNumberRequestDto, ImportDataRequestDto, NaturalAccountResponseDto, SectionsRequestDto, ToggleActiveStateCompanyCodeRequestDto, UpdateAnalysisCodeRequestDto, UpdateCompanyCodeRequestDto, UpdateCostCenterRequestDto, UpdateFusionIntegrationRequestDto, UpdateMultiNaturalAccountConfigRequestDto, UpdateNaturalAccountNumberRequestDto } from '../dtos';
import { NaturalAccountNumberRepository, CostCenterRepository, CompanyCodeRepository, AnalysisCodeRepository } from '../repositories';
import { UploadEvidenceRequestDto } from 'src/afe-proposal/dtos';
import { AttachmentContentResponseDto } from 'src/attachment/dtos';
export declare class FinanceAdminService {
    private readonly naturalAccountNumberRepository;
    private readonly costCenterRepository;
    private readonly companyCodeRepository;
    private readonly analysisCodeRepository;
    private readonly adminApiClient;
    private readonly databaseHelper;
    private readonly excelSheetService;
    private readonly mSGraphApiClient;
    private readonly historyApiClient;
    private readonly sharedAttachmentService;
    private readonly attachmentApiClient;
    constructor(naturalAccountNumberRepository: NaturalAccountNumberRepository, costCenterRepository: CostCenterRepository, companyCodeRepository: CompanyCodeRepository, analysisCodeRepository: AnalysisCodeRepository, adminApiClient: AdminApiClient, databaseHelper: DatabaseHelper, excelSheetService: ExcelSheetService, mSGraphApiClient: MSGraphApiClient, historyApiClient: HistoryApiClient, sharedAttachmentService: SharedAttachmentService, attachmentApiClient: AttachmentApiClient);
    createCompanyCode(createCompanyCodeRequestDto: CreateCompanyCodeRequestDto, currentContext: CurrentContext): Promise<CompanyCodeResponseDto>;
    addCostCenter(addCostCenterRequestDto: AddCostCenterRequestDto, currentContext: CurrentContext): Promise<CostCenterResponseDto>;
    isDuplicateSectionExist(sectionHeads: SectionsRequestDto[]): boolean;
    createAnalysisCode(createAnalysisCodeRequestDto: CreateAnalysisCodeRequestDto, currentContext: CurrentContext): Promise<AnalysisCodeResponseDto>;
    createNatualAccountNumber(createNaturalAccountNumberRequestDto: CreateNaturalAccountNumberRequestDto, currentContext: CurrentContext): Promise<NaturalAccountResponseDto>;
    updateCompanyCodeDetail(updateCompanyCodeRequestDto: UpdateCompanyCodeRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateCostCenterDetail(updateCostCenterRequestDto: UpdateCostCenterRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateFusionIntegration(UpdateFusionIntegrationRequestDto: UpdateFusionIntegrationRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateAnalysisCodeDetail(updateAnalysisCodeRequestDto: UpdateAnalysisCodeRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateNaturalAccountNumber(updateNaturalAccountNumberRequestDto: UpdateNaturalAccountNumberRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteCompanyCodeById(companyCodeId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteCostCenterById(costCenterId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteAnalysisCodeById(analysisCodeId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteNaturalAccountNumberById(naturalAccountNoId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getCompanyCodesListByEntityId(entityId: number, limit?: number, page?: number): Promise<Pagination<CompanyCodeResponseDto>>;
    getCostCentersByCompanyCodeId(companyCodeId: number, limit?: number, page?: number): Promise<Pagination<CostCenterResponseDto>>;
    getAnalsysisCodesByCompanyCodeId(companyCodeId: number, limit?: number, page?: number): Promise<Pagination<AnalysisCodeResponseDto>>;
    getNaturalAccountNumbersByCompanyCodeId(companyCodeId: number, limit?: number, page?: number): Promise<Pagination<NaturalAccountResponseDto>>;
    getCapexNaturalAccountNumbersByCompanyCodeId(companyCodeId: number): Promise<import("../models").NaturalAccountNumber[]>;
    toggleActiveStateOfCompany(toggleActiveStateCompanyCodeRequestDto: ToggleActiveStateCompanyCodeRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getEntityActiveCompanyCodeDetails(entityId: number, throwError?: boolean): Promise<CompanyCodeResponseDto>;
    deactivateCompanyCode(companyCodeId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    importAnalysisCode(importDataRequestDto: ImportDataRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    importNaturalAccount(importDataRequestDto: ImportDataRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    importCostCenter(importDataRequestDto: ImportDataRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getCompanyHistory(entityId: number): Promise<Record<string, any>>;
    getCostCenterHistory(costCenterId: number): Promise<Record<string, any>>;
    uploadEvidence(companyCodeId: number, evidencesRequestDto: UploadEvidenceRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    getEvidences(companyCodeId: number): Promise<AttachmentContentResponseDto[]>;
    updateMultiNaturalAccount(updateMultiNaturalAccountRequestDto: UpdateMultiNaturalAccountConfigRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
}
