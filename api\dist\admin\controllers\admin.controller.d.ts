import { RequestContext } from 'src/shared/types';
import { ReplaceUserRequestDto } from '../dtos';
import { AdminService } from '../services/admin.service';
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    replaceUserEmail(request: RequestContext, replaceUserRequestDto: ReplaceUserRequestDto): Promise<{
        message: string;
    }>;
}
