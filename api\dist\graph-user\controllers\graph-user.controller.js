"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphUserController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
let GraphUserController = class GraphUserController {
    constructor(graphUserService) {
        this.graphUserService = graphUserService;
    }
    searchUsers(searchText, orderBy, count) {
        return this.graphUserService.searchUsers(searchText, orderBy, count);
    }
    getUserDetailsInMsResponse(userId) {
        return this.graphUserService.getUserDetailsInMsResponse(userId);
    }
    getUsersDetailsFromAdInMsResponse(userIds) {
        return this.graphUserService.getUsersDetailsFromAdInMsResponse(userIds);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search user using graph API.',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchText',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'orderBy',
        type: String,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'count',
        type: Boolean,
    }),
    (0, common_1.Get)('search-users'),
    __param(0, (0, common_1.Query)('searchText')),
    __param(1, (0, common_1.Query)('orderBy')),
    __param(2, (0, common_1.Query)('count')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Boolean]),
    __metadata("design:returntype", void 0)
], GraphUserController.prototype, "searchUsers", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search user detail by id',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userId',
        type: String,
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Query)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], GraphUserController.prototype, "getUserDetailsInMsResponse", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search users detail by ids',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userIds',
        type: String,
    }),
    (0, common_1.Get)('by-userIds'),
    __param(0, (0, common_1.Query)('userIds')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], GraphUserController.prototype, "getUsersDetailsFromAdInMsResponse", null);
GraphUserController = __decorate([
    (0, swagger_1.ApiTags)('MS Graph User APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('graph-users'),
    __metadata("design:paramtypes", [services_1.GraphUserService])
], GraphUserController);
exports.GraphUserController = GraphUserController;
//# sourceMappingURL=graph-user.controller.js.map