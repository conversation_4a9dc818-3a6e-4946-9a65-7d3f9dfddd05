"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepresentativeService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const dtos_1 = require("../../vacation/dtos");
const dtos_2 = require("../dtos");
let RepresentativeService = class RepresentativeService {
    constructor(requestApiClient, adminApiClient, sharedDelegationService) {
        this.requestApiClient = requestApiClient;
        this.adminApiClient = adminApiClient;
        this.sharedDelegationService = sharedDelegationService;
    }
    addRepresentative(addRepresentativeRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { representativeFor, representative } = addRepresentativeRequestDto;
            const { username } = currentContext.user;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            if (isAdministrator && (representativeFor === representative)) {
                throw new exceptions_1.HttpException(`Representative for & representative to can't be the same.`, common_1.HttpStatus.BAD_REQUEST);
            }
            if (!(isAdministrator && representativeFor !== username) && username === representative) {
                throw new exceptions_1.HttpException(`You can't assign representative to yourself.`, common_1.HttpStatus.BAD_REQUEST);
            }
            const originalUserId = (isAdministrator && representativeFor) ? representativeFor.toLowerCase() : username.toLowerCase();
            const isDelegationExistsOnGivenDates = yield this.sharedDelegationService.isDelegationExistsOnGivenDates(addRepresentativeRequestDto.fromDate, addRepresentativeRequestDto.toDate, originalUserId, 'representative');
            const isAvailable = yield this.ifDelegationAlreadyAdded((isAdministrator && addRepresentativeRequestDto.representativeFor) ? addRepresentativeRequestDto.representativeFor.toLowerCase() : username.toLowerCase(), addRepresentativeRequestDto.fromDate, addRepresentativeRequestDto.toDate);
            if (isAvailable) {
                throw new exceptions_1.HttpException('Representative already exist for the selected date..', common_1.HttpStatus.BAD_REQUEST);
            }
            yield this.requestApiClient.addNewDelegation({
                delegate_for_username: originalUserId,
                delegate_to_username: representative.toLowerCase(),
                delegate_from_date: addRepresentativeRequestDto.fromDate,
                delegate_to_date: addRepresentativeRequestDto.toDate,
                created_by: username.toLowerCase(),
                type: 'representative'
            });
            return { message: 'Representative has been added successfully' };
        });
    }
    updateUpcomingRepresentative(representativePayload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            const representativeDetail = yield this.requestApiClient.getUpcomingDelegationById(representativePayload.id);
            if (!representativeDetail || (representativeDetail.hasOwnProperty('id') && !(0, lodash_1.toNumber)(representativeDetail.id))) {
                throw new exceptions_1.HttpException(`Invalid or already deleted representative.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!(isAdministrator &&
                (representativeDetail.delegate_for_username.toLowerCase() !== user.username.toLowerCase())) &&
                (user.username.toLowerCase() === representativePayload.representative.toLowerCase())) {
                throw new exceptions_1.HttpException(`You can't representative to yourself.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!isAdministrator && (representativeDetail.delegate_for_username.toLowerCase() !== user.username.toLowerCase())) {
                throw new exceptions_1.HttpException(`You are not authorized to update this delegation.`, common_1.HttpStatus.UNAUTHORIZED);
            }
            if (isAdministrator && (representativeDetail.delegate_for_username.toLowerCase() === representativePayload.representative.toLowerCase())) {
                throw new exceptions_1.HttpException(`Representative for & representative to can't be same.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            const isAvailable = yield this.ifDelegationAlreadyAdded(representativeDetail.delegate_for_username.toLowerCase(), representativePayload.fromDate, representativePayload.toDate, representativePayload.id);
            if (isAvailable) {
                throw new exceptions_1.HttpException(`Representative already exist for the selected date.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            let updatePayload = null;
            if (representativePayload === null || representativePayload === void 0 ? void 0 : representativePayload.representative) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { delegate_to_username: representativePayload.representative.toLowerCase() });
            }
            if (representativePayload === null || representativePayload === void 0 ? void 0 : representativePayload.fromDate) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { delegate_from_date: representativePayload.fromDate });
            }
            if (representativePayload === null || representativePayload === void 0 ? void 0 : representativePayload.toDate) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { delegate_to_date: representativePayload.toDate });
            }
            if (!updatePayload) {
                throw new exceptions_1.HttpException(`No data to update.`, common_1.HttpStatus.BAD_REQUEST);
            }
            if (representativePayload === null || representativePayload === void 0 ? void 0 : representativePayload.id) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { id: representativePayload.id });
            }
            updatePayload = Object.assign(Object.assign({}, updatePayload), { created_by: user.username.toLowerCase() });
            const updatedDelegation = yield this.requestApiClient.updateDelegation(updatePayload);
            return updatedDelegation;
        });
    }
    getRepresentativesOfUser(currentContext, filterQuery = null) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = currentContext.user;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            const originalUserId = (isAdministrator && (filterQuery === null || filterQuery === void 0 ? void 0 : filterQuery.representativeFor)) ? filterQuery.representativeFor : user.username;
            const storedtRepresentatives = yield this.requestApiClient.getUpcomingDelegations({ user_name: originalUserId, type: 'representative' });
            return storedtRepresentatives.map(representative => (0, helpers_1.instanceToPlain)(new dtos_2.GetRepresentativeResponseDto(representative)));
        });
    }
    deleteRepresentativeById(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const user = currentContext.user;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            const representative = yield this.requestApiClient.getUpcomingDelegationById(id);
            if (!representative) {
                throw new exceptions_1.HttpException(`Invalid or already deleted representative.`, common_1.HttpStatus.BAD_REQUEST);
            }
            if (!isAdministrator && representative.delegate_for_username.toLowerCase() !== user.username) {
                throw new exceptions_1.HttpException(`You are not authorized to delete this representative.`, common_1.HttpStatus.UNAUTHORIZED);
            }
            yield this.requestApiClient.deleteDelegation({ id, created_by: user.username });
            return { message: 'Representative deleted successfully' };
        });
    }
    ifDelegationAlreadyAdded(username, fromDate, toDate, editDelegationId = null) {
        return __awaiter(this, void 0, void 0, function* () {
            const upcomingDelegationResponse = yield this.requestApiClient.getUpcomingDelegations({
                user_name: username, type: 'representative'
            });
            const upcomingDelegations = upcomingDelegationResponse.map(d => (0, helpers_1.instanceToPlain)(new dtos_1.GetVacationDelegationResponseDto(d)));
            let responseReturn = false;
            upcomingDelegations.forEach(upcomingDelegation => {
                if (!(editDelegationId && ((0, lodash_1.toNumber)(upcomingDelegation.id) === (0, lodash_1.toNumber)(editDelegationId)))) {
                    let delegateFromDate = upcomingDelegation.delegateFromDate.split('T');
                    let delegateToDate = upcomingDelegation.delegateToDate.split('T');
                    const existingFromDate = new Date(delegateFromDate[0] + ' 00:00:00').getTime();
                    const existingToDate = new Date(delegateToDate[0] + ' 23:59:00').getTime();
                    const inputFromDate = new Date(fromDate).getTime();
                    const inputToDate = new Date(toDate).getTime();
                    if ((inputFromDate >= existingFromDate) && (inputFromDate <= existingToDate)) {
                        responseReturn = true;
                        return;
                    }
                    if ((inputFromDate < existingFromDate) && (inputToDate > existingFromDate)) {
                        responseReturn = true;
                        return;
                    }
                }
            });
            return responseReturn;
        });
    }
};
RepresentativeService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.RequestApiClient,
        clients_1.AdminApiClient,
        services_1.SharedDelegationService])
], RepresentativeService);
exports.RepresentativeService = RepresentativeService;
//# sourceMappingURL=representative.service.js.map