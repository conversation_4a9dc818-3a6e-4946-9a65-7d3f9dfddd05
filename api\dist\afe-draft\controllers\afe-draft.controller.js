"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeDraftController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../core/decorators");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const enums_1 = require("../../shared/enums");
const dtos_2 = require("../dtos");
const services_1 = require("../services");
let AfeDraftController = class AfeDraftController {
    constructor(afeDraftService) {
        this.afeDraftService = afeDraftService;
    }
    getAllDraftsByActiveUser(request, limit = 10, page = 1) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeDraftService.getAllDraftsByActiveUser(request.currentContext, limit, page);
        });
    }
    getDraftsByIdAndActiveUser(id, request) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeDraftService.getDraftsByIdAndActiveUser(id, request.currentContext);
        });
    }
    saveDraft(request, draftAfeDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeDraftService.saveDraft(draftAfeDto, request.currentContext);
        });
    }
    updateOwnDraft(updateAfeProposalDto, request) {
        return this.afeDraftService.updateDraftByIdAndActiveUser(updateAfeProposalDto, request.currentContext);
    }
    deleteDraftById(id, request) {
        return this.afeDraftService.deleteDraftById(id, request.currentContext);
    }
};
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        type: Number,
        description: 'Number of records in response.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        type: Number,
        description: 'Page number of the paginated record.',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all owned drafted AFE.',
        type: dtos_2.PaginatedGetAfeDraftResponseDto,
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], AfeDraftController.prototype, "getAllDraftsByActiveUser", null);
__decorate([
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get drafted AFE by id.',
        type: dtos_2.GetAfeDraftResponseDto,
    }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AfeDraftController.prototype, "getDraftsByIdAndActiveUser", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_SUBMIT, { checkEntity: true }),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Save New Afe Draft.',
        type: dtos_2.AfeDraftResponseDto,
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.AfeDraftRequestDto]),
    __metadata("design:returntype", Promise)
], AfeDraftController.prototype, "saveDraft", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_SUBMIT, { checkEntity: true }),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update owned saved draft by id.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_2.UpdateDraftRequestDto, Object]),
    __metadata("design:returntype", Promise)
], AfeDraftController.prototype, "updateOwnDraft", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_SUBMIT),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete owned draft by id.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AfeDraftController.prototype, "deleteDraftById", null);
AfeDraftController = __decorate([
    (0, swagger_1.ApiTags)('Draft AFE APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('afe-draft'),
    __metadata("design:paramtypes", [services_1.AfeDraftService])
], AfeDraftController);
exports.AfeDraftController = AfeDraftController;
//# sourceMappingURL=afe-draft.controller.js.map