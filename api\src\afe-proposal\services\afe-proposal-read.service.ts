import { Injectable } from '@nestjs/common';
import _, { toNumber } from 'lodash';
import { AfeBudgetType } from 'src/afe-config/models';
import { AfeBudgetTypeRepository } from 'src/afe-config/repositories';
import { LengthOfCommitmentRepository } from 'src/afe-config/repositories/length-of-commitment.repository';
import { Pagination } from 'src/core/pagination';
import { LoggerService } from 'src/core/services';
import {
	AnalysisCodeRepository,
	CostCenterRepository,
	CurrencyTypeRepository,
	NaturalAccountNumberRepository,
} from 'src/finance/repositories';
import { PdfGeneratorService } from 'src/pdf-generator/pdf-generator.service';
import {
	AdminApiClient,
	AttachmentApiClient,
	HistoryApiClient,
	TaskApiClient,
} from 'src/shared/clients';
import {
	AFE_CATEGORY,
	AFE_PROPOSAL_STATUS,
	AMOUNT_SPLIT,
	APPROVAL_SEQUENCE_TYPE,
	APPROVER_STATUS,
	APPROVER_STATUS_TITLE_FOR_PDF,
	ATTACHMENT_ENTITY_TYPE,
	BUDGET_TYPE,
	BUDGET_TYPE_TITLE,
	HISTORY_ENTITY_TYPE,
	HttpStatus,
	PERMISSIONS,
	TASK_ENTITY_TYPE,
} from 'src/shared/enums';
import { HttpException } from 'src/shared/exceptions';
import {
	formatDateString,
	instanceToPlain,
	multiObjectToInstance,
	serializeBudgetBasedProjectAmountSplits,
	singleObjectToInstance,
} from 'src/shared/helpers';
import {
	AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING,
	BUDGET_TYPE_MAPPING_WITH_ID,
	HistoryActionNameMapping,
} from 'src/shared/mappings';
import { CurrentContext } from 'src/shared/types';
import { AfeProposalValidator } from 'src/shared/validators';
import { TaskService } from 'src/task/services';
import {
	AfeProposalActionHistoryResponseDto,
	AfeProposalAmountSplitResponseDto,
	AfeProposalListingResponseDto,
	AfeProposalResposeDto,
	AmountSplitResponseDto,
	BudgetBasedProjectSplitResponseDto,
	CheckAfeOrItsSupplementalInProgress,
	LatestAfeVersionInfoResponseDto,
	RelatedAfesInfoResponse,
	SearchAfeByProjectRefernceNoResponse,
} from '../dtos';
import { AfeListingFilterRequestDto } from '../dtos/request/afe-listing-filter-request.dto';
import { AfeProposalApproverResponseDto } from '../dtos/response/afe-proposal-approver-response';
import { AfeProposal } from '../models';
import {
	AfeProposalAmountSplitRepository,
	AfeProposalApproverRepository,
	AfeProposalRepository,
	UserCostCenterMappingRepository,
	UserProjectComponentMappingRepository,
} from '../repositories';
import { BusinessEntityService } from 'src/business-entity/services';
import { SharedPermissionService } from 'src/shared/services';
import { ConfigService } from 'src/config/config.service';
import { BASE64_DP_WORLD_LOGO } from 'src/shared/constants';
import { title } from 'process';

@Injectable()
export class AfeProposalReadService {
	constructor(
		private readonly afeProposalRepository: AfeProposalRepository,
		private readonly afeProposalApproverRepository: AfeProposalApproverRepository,
		private readonly afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository,
		private readonly historyApiClient: HistoryApiClient,
		private readonly analysisCodeRepository: AnalysisCodeRepository,
		private readonly naturalAccountNumberRepository: NaturalAccountNumberRepository,
		private readonly costCenterRepository: CostCenterRepository,
		private readonly afeBudgetTypeRepository: AfeBudgetTypeRepository,
		private readonly currencyTypeRepository: CurrencyTypeRepository,
		private readonly taskService: TaskService,
		private readonly taskApiClient: TaskApiClient,
		private readonly afeProposalValidator: AfeProposalValidator,
		private readonly loggerService: LoggerService,
		private readonly pdfGeneratorService: PdfGeneratorService,
		private readonly attachmentApiClient: AttachmentApiClient,
		private readonly lengthOfCommitmentRepository: LengthOfCommitmentRepository,
		private readonly businessEntityService: BusinessEntityService,
		private readonly permissionService: SharedPermissionService,
		private readonly configService: ConfigService
	) {}

	/**
	 * Get paginated afe listing with filtering.
	 * @param id
	 * @returns
	 */
	public async getAfeListing(
		currentContext: CurrentContext,
		limit: number = 10,
		page: number = 1,
		forApprovalHistory: boolean = false,
		filters: AfeListingFilterRequestDto,
	): Promise<Pagination<AfeProposalListingResponseDto>> {
		const { user } = currentContext;
		const { businessEntities } = filters;

		const locations = await this.permissionService.getAllLocationIdForGivenPermission(
			user.username,
			PERMISSIONS.AFE_VIEW,
		);

		if (businessEntities?.length) {
			filters.businessEntities = await this.businessEntityService.getBusinessEntitiesChildIds(
				businessEntities,
			);
		}

		let extraPermissions = null;
		if (!forApprovalHistory) {
			extraPermissions = await this.permissionService.getExtraPermissionObject(user.username);
		}

		const result = forApprovalHistory
			? await this.afeProposalRepository.getAfeListsThatApprovedByUser(
					currentContext,
					limit,
					page,
					filters,
			  )
			: await this.afeProposalRepository.getPaginatedAfeProposals(
					currentContext,
					locations,
					extraPermissions,
					filters,
					limit,
					page,
			  );

		const afeList = await Promise.all(
			result.rows.map(async afe => {
				const approversList = await this.afeProposalApproverRepository.getApproversByProposalId(
					afe.id,
				);
				let approvalPendingWith: string[] = [];

				if (
					afe.internalStatus !== AFE_PROPOSAL_STATUS.APPROVED &&
					afe.internalStatus !== AFE_PROPOSAL_STATUS.REJECTED &&
					afe.internalStatus !== AFE_PROPOSAL_STATUS.CANCELLED
				) {
				}
				{
					for (const approver of approversList) {
						if (approver.actionStatus === APPROVER_STATUS.IN_PROGRESS) {
							const users = approver.otherInfo.usersDetail
								.map(user => `${user.firstName || ''} ${user.lastName || ''}`)
								.filter(user => !!user.trim());
							approvalPendingWith.push(...users);
						}
					}
					approvalPendingWith = [...new Set(approvalPendingWith)];
				}

				const currentUserSteps = await this.taskService.getCurrentTaskSteps(
					currentContext.user.username,
					approversList,
				);
				
				afe.totalAmount = afe?.supplementalDeltaAmounts?.totalAmount
					? afe.supplementalDeltaAmounts.totalAmount
					: afe.totalAmount;

				if (currentUserSteps.length) {
					try {
						const [task] = await this.taskApiClient.getAllTasks(
							currentUserSteps[currentUserSteps.length - 1].id,
							TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
						);
						return {
							...afe,
							task: {
								approvalType: currentUserSteps[currentUserSteps.length - 1].otherInfo.approvalType,
								taskId: task.id,
								relUrl: task.task_rel_url,
							},
							approvalPendingWith,
						};
					} catch (error) {
						this.loggerService.error(error, error.stack, AfeProposalReadService.name);
						return { ...afe, approvalPendingWith };
					}
				}
				return { ...afe, approvalPendingWith };
			}),
		);
		const records = multiObjectToInstance(AfeProposalListingResponseDto, afeList);
		return new Pagination({ records, total: result.count });
	}

	/**
	 * Get AFE details by afe proposal id
	 * @param afeProposalId
	 * @returns
	 */
	public async getAfeProposalById(
		afeProposalId: number,
		currentContext: CurrentContext,
		taskId?: number,
	): Promise<AfeProposalResposeDto> {
		const result = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
		const { user } = currentContext;

		if (!result) {
			throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				result,
				currentContext,
				taskId,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to view this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}

		const {
			id,
			afeRequestTypeId,
			entityId,
			entityCode,
			entityTitle,
			category,
			isApprovedByBoard,
			createdOn,
			readers,
			globalProcurementQuesAns,
			userStatus,
			internalStatus,
			data,
			parentAfeId,
			budgetType,
			projectReferenceNumber,
			submitterId,
			supplementalData,
			yearOfCommitment,
			additionalCurrencyAmount,
			marketValueAdditionalCurrencyAmount,
			workflowYear,
			subscribers,
			version,
			isNewFfoSetting,
			supplementalDeltaAmounts,
			location
		} = result;

		let budgetTypeDetail: AfeBudgetType = null;
		if (budgetType) {
			budgetTypeDetail = await this.afeBudgetTypeRepository.getBudgetTypeById(
				BUDGET_TYPE_MAPPING_WITH_ID[budgetType],
			);
		}
		const currencyDetail = await this.currencyTypeRepository.getCurrencyConversionRateToPrimary(
			additionalCurrencyAmount.currency,
		);

		let prevAfeLatestDetail = {};

		if (
			category === AFE_CATEGORY.SUPPLEMENTAL 
			// && internalStatus === AFE_PROPOSAL_STATUS.IN_PROGRESS
		) {
			const prevAfeDetail = await this.afeProposalRepository.getPreviousAfeDetailByIdAndVersion(
				parentAfeId,
				version,
			);

			if (prevAfeDetail) {
				let budgetTypeDetail: AfeBudgetType = null;
				if (prevAfeDetail?.budgetType) {
					budgetTypeDetail = await this.afeBudgetTypeRepository.getBudgetTypeById(
						BUDGET_TYPE_MAPPING_WITH_ID[prevAfeDetail.budgetType],
					);
				}
				let projectSplits = [];
				let budgetBasedProjectSplits = [];

				const amountSplits =
					await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndTypes(
						toNumber(prevAfeDetail.id),
						[
							AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT,
							AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT,
						],
						['id', 'objectId', 'objectTitle', 'additionalCurrencyAmount', 'type', 'budgetType'],
					);

				amountSplits.forEach(amountSplit => {
					if (amountSplit.type === AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT) {
						projectSplits.push({
							amountSplitId: amountSplit.id,
							id: amountSplit.objectId,
							title: amountSplit.objectTitle,
							amount: amountSplit.additionalCurrencyAmount.amount,
							currency: amountSplit.additionalCurrencyAmount.currency,
						});
					}
					if (amountSplit.type === AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT) {
						budgetBasedProjectSplits.push({
							amountSplitId: amountSplit.id,
							objectId: amountSplit.objectId,
							objectTitle: amountSplit.objectTitle,
							amount: amountSplit.amount,
							additionalCurrencyAmount: amountSplit.additionalCurrencyAmount,
							currency: amountSplit.additionalCurrencyAmount.currency,
							budgetType: amountSplit.budgetType,
						});
					}
				});

				prevAfeLatestDetail = {
					...prevAfeLatestDetail,
					parentAfeLatestBudgetType: budgetTypeDetail,
					parentAfeTotalAmount: prevAfeDetail.additionalCurrencyAmount.amount,
					parentAfeLatestProjectComponentInfo: projectSplits,
					parentAfeLatestBudgetBasedProjectSplit: budgetBasedProjectSplits?.length
						? serializeBudgetBasedProjectAmountSplits(budgetBasedProjectSplits)
						: [],
				};
			}
		}

		const afeDetail = {
			id,
			requestTypeId: afeRequestTypeId,
			entityId,
			isSupplemental: category === AFE_CATEGORY.SUPPLEMENTAL,
			isApprovedByBoard,
			totalAmount: additionalCurrencyAmount.amount,
			totalMarketValue: marketValueAdditionalCurrencyAmount?.amount,
			businessEntity: { id: entityId, code: entityCode, name: entityTitle },
			budgetType: budgetType ? { id: budgetTypeDetail.id, title: budgetType } : null,
			currencyDetail,
			projectReferenceNumber,
			data,
			internalStatus,
			userStatus,
			parentAfeId,
			readers,
			createdOn,
			isNewFfoSetting,
			submitterId,
			questionAnswers: globalProcurementQuesAns,
			supplementalData,
			year: workflowYear,
			lengthOfCommitment: yearOfCommitment,
			isSubscriptionOn: subscribers?.includes(user.username) || false,
			supplementalDeltaAmounts,
			location,
			...prevAfeLatestDetail,
		};
		return singleObjectToInstance(AfeProposalResposeDto, afeDetail);
	}

	/**
	 * Get the list of approvers
	 * @param afeProposalId
	 */
	public async getApproversListOfAfePoposal(
		afeProposalId: number,
		currentContext: CurrentContext,
		taskId?: number,
	): Promise<AfeProposalApproverResponseDto> {
		const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
		if (!proposal) {
			throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		}
		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				proposal,
				currentContext,
				taskId,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to view this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}

		const result = await this.afeProposalApproverRepository.getApproversByProposalId(afeProposalId);
		const approvers = result.map(approver => ({
			id: approver.id,
			title: approver.title,
			approvers: approver.otherInfo?.usersDetail,
			stepId: approver.workflowMasterStepsId,
			isCustomUser: !approver.workflowMasterStepsId,
			associateRole: approver.assignedTo,
			associateType: approver.assginedType,
			associateLevel: approver.assignedLevel,
			sequenceNumber: approver.approvalSequence,
			associatedColumn: approver.associatedColumn,
			parallelIdentifier: approver.parallelIdentifier,
			approvalSequenceType: approver.parallelIdentifier
				? APPROVAL_SEQUENCE_TYPE.PARALLEL
				: APPROVAL_SEQUENCE_TYPE.ANYONE,
			actionStatus: approver.actionStatus,
			actionBy: approver.actionBy,
			comment: approver.comment,
			actionDate: approver.actionDate,
			originalApprover: approver.userDetail ? approver.userDetail.originalApprover : null,
		}));
		return singleObjectToInstance(AfeProposalApproverResponseDto, approvers);
	}

	/**
	 * Get all the cost splits and amount for an AFE.
	 * @param afeProposalId
	 */
	public async getAmountSplitsOfAfeProposal(
		afeProposalId: number,
		currentContext: CurrentContext,
		taskId?: number,
	): Promise<AfeProposalAmountSplitResponseDto> {
		const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
		if (!proposal) {
			throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		}
		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				proposal,
				currentContext,
				taskId,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to view this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}
		return this.getAfeProposalAmountSplitData(proposal);
	}

	/**
	 * Get AFE Proposal Amount Split Data.
	 * @param afeProposalId
	 * @returns
	 */
	private async getAfeProposalAmountSplitData(
		afePropsal: AfeProposal,
	): Promise<AfeProposalAmountSplitResponseDto> {
		const amountSplits = await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalId(
			afePropsal.id,
		);
		let budgetTypeSplit = [];
		let costCenterSplit = [];
		let projectComponentSplit = [];
		let analysisCodeSplit = [];
		let naturalAccountNumberSplit = [];
		let chartOfAccounts = [];
		let budgetReferenceNumberSplit = [];
		let budgetBasedProjectSplit = [];

		for (let split of amountSplits) {
			const { objectId, objectTitle, additionalCurrencyAmount, id } = split;
			const splitData = {
				amountSplitId: +id,
				id: objectId,
				title: objectTitle,
				amount: additionalCurrencyAmount.amount,
				currency: additionalCurrencyAmount.currency,
			};

			switch (split.type) {
				case AMOUNT_SPLIT.BUDGET_TYPE_SPLIT:
					budgetTypeSplit.push({ ...splitData });
					break;
				case AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT:
					projectComponentSplit.push({ ...splitData });
					break;
				case AMOUNT_SPLIT.ANALYSIS_CODE_SPLIT:
					const { code: analysisCode } =
						await this.analysisCodeRepository.getAnalysisCodeByIdIncludingInactiveAndDeleted(
							split.objectId,
						);
					analysisCodeSplit.push({ ...splitData, code: analysisCode });
					break;
				case AMOUNT_SPLIT.NATURAL_ACCOUNT_SPLIT:
					const { number: naturalAccountNumber } =
						await this.naturalAccountNumberRepository?.getNaturalAccountNumberByIdIncludingInactiveAndDeleted(
							split.objectId,
						);
					naturalAccountNumberSplit.push({ ...splitData, number: naturalAccountNumber });
					break;
				case AMOUNT_SPLIT.COST_CENTER_SPLIT:
					// if (split.objectId) {
					const costCenterDetail =
						await this.costCenterRepository.getCostCenterByIdIncludingInactiveAndDeleted(
							split.objectId,
						);

					let moreAccountDetail = {};

					if (split?.additionalInfo?.analysisCode) {
						moreAccountDetail = {
							...moreAccountDetail,
							analysisCode: split?.additionalInfo?.analysisCode || null,
						};
					}

					if (split?.additionalInfo?.naturalAccount) {
						moreAccountDetail = {
							...moreAccountDetail,
							naturalAccount: split?.additionalInfo?.naturalAccount || null,
						};
					}

					if (split?.additionalInfo?.budgetReferenceNumberSplit) {
						const budgetRefNumberList = split?.additionalInfo?.budgetReferenceNumberSplit.map(
							(budgetReferenceNumber: any) => {
								return {
									amount: budgetReferenceNumber?.additionalCurrencyAmount?.amount || 0,
									currency: budgetReferenceNumber?.additionalCurrencyAmount?.currency || 'USD',
									number: budgetReferenceNumber?.number || '',
								};
							},
						);

						moreAccountDetail = {
							...moreAccountDetail,
							budgetReferenceNumberSplit: budgetRefNumberList,
						};
					}

					costCenterSplit.push({
						...splitData,
						code: costCenterDetail?.code || 0,
						section: split?.additionalInfo?.section || null,
						...moreAccountDetail,
					});
					// }
					break;
				case AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT:
					const { title, ...updatedSplit } = splitData;
					budgetReferenceNumberSplit.push({ ...updatedSplit, number: split.objectTitle });
					break;
				case AMOUNT_SPLIT.GL_CODE_SPLIT:
					const segments = split.objectTitle.split('-');
					chartOfAccounts.push({
						amountSplitId: +split.id,
						amount: additionalCurrencyAmount.amount,
						currency: additionalCurrencyAmount.currency,
						segments: {
							segment1: segments[0],
							segment2: segments[1],
							segment3: segments[2],
							segment4: segments[3],
							segment5: segments[4],
							segment6: segments[5],
							segment7: segments[6],
							segment8: segments[7],
						},
					});
					break;
			}
		}

		// if (costCenterSplit.length) {
		// 	for (let costCenter of costCenterSplit) {
		// 		const budgetReferenceNumberSplitInfo = budgetReferenceNumberSplit?.filter(a => a.id === costCenter.id);
		// 		if (budgetReferenceNumberSplitInfo?.length > 0) {
		// 			costCenter.budgetReferenceNumberSplit = budgetReferenceNumberSplitInfo;
		// 		} else {
		// 			costCenter.budgetReferenceNumberSplit = [{ amount: costCenter.amount, currency: costCenter.currency, number: '' }];
		// 		}
		// 	}
		// } else if (budgetReferenceNumberSplit.length) {
		// 	costCenterSplit = [
		// 		{
		// 			id: 0,
		// 			title: null,
		// 			amount: null,
		// 			currency: null,
		// 			budgetReferenceNumberSplit,
		// 		},
		// 	];
		// }

		const projectComponentSplitByBudgetType = amountSplits.filter(
			split => split.type === AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT,
		);
		if (projectComponentSplitByBudgetType.length && afePropsal.budgetType === BUDGET_TYPE.MIXED) {
			budgetBasedProjectSplit = serializeBudgetBasedProjectAmountSplits(
				projectComponentSplitByBudgetType,
			);
		}

		const splits = {
			budgetTypeSplit,
			costCenterSplit,
			projectComponentSplit,
			analysisCodeSplit,
			naturalAccountNumberSplit,
			chartOfAccounts,
			budgetBasedProjectSplit,
		};
		return singleObjectToInstance(AfeProposalAmountSplitResponseDto, splits);
	}

	/**
	 * Return the AFE proposal action history.
	 * @param afeProposalId
	 * @returns
	 */
	public async getAfeProposalActionHistory(
		afeProposalId: number,
		currentContext: CurrentContext,
		taskId?: number,
	): Promise<Record<string, any>> {
		const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
		if (!proposal) {
			throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		}
		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				proposal,
				currentContext,
				taskId,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to view this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}
		const histories = await this.historyApiClient.getRequestHistory(
			afeProposalId,
			HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
		);
		const nonHiddenHistories = histories.filter(history => !history.additional_info?.hidden);
		return nonHiddenHistories.map(history =>
			instanceToPlain(new AfeProposalActionHistoryResponseDto(history)),
		);
	}

	/**
	 * Search all the AFE with project reference number.
	 * @param projectReferenceNumber
	 * @param currentContext
	 * @returns
	 */
	public async searchAfeListByProjectReferenceNumber(
		projectReferenceNumber: string,
		afeRequestTypeIds: number[],
		currentContext: CurrentContext,
		entityId: number,
	): Promise<SearchAfeByProjectRefernceNoResponse[]> {
		const { user } = currentContext;

		const viewPermissionLocations = await this.permissionService.getAllLocationIdForGivenPermission(
			user.unique_name,
			PERMISSIONS.AFE_VIEW,
		);

		const submitPermissionLocations =
			await this.permissionService.getAllLocationIdForGivenPermission(
				user.unique_name,
				PERMISSIONS.AFE_SUBMIT,
			);

		const locations = [...new Set([...viewPermissionLocations, ...submitPermissionLocations])];

		const entityIds = entityId ? [entityId] : locations;
		const afeList = await this.afeProposalRepository.searchAfeListByProjectRefernceNumber(
			projectReferenceNumber,
			afeRequestTypeIds,
			entityIds,
		);
		return multiObjectToInstance(SearchAfeByProjectRefernceNoResponse, afeList);
	}

	/**
	 * Get all the related AFEs info (Parent afe and its supplementals).
	 * @param afeProposalId
	 */
	public async getAllRelatedSupplementalAfes(
		afeProposalId: number,
		currentContext: CurrentContext,
		taskId?: number,
	): Promise<RelatedAfesInfoResponse[]> {
		const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
		if (!proposal) {
			throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		}
		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				proposal,
				currentContext,
				taskId,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to view this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}
		const afeParent = await this.afeProposalRepository.getParentIdOfAfe(afeProposalId);
		let parentAfeId = afeParent ? afeParent.parentAfeId : afeProposalId;

		const afes = await this.afeProposalRepository.getAfeAndItsChildern(parentAfeId);
		return multiObjectToInstance(RelatedAfesInfoResponse, afes);
	}

	/**
	 * Check if afe or its any supplemental in progress.
	 * @param afeProposalId
	 * @returns
	 */
	public async isAfeOrItsSupplementalInProgress(
		afeProposalId: number,
		currentContext: CurrentContext,
	): Promise<CheckAfeOrItsSupplementalInProgress> {
		const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
		if (!proposal) {
			throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		}
		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				proposal,
				currentContext,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to view this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}
		const afes = await this.afeProposalRepository.getAfeAndItsChildern(afeProposalId);
		for (const afe of afes) {
			if (afe.isNewVersionInProgress) {
				return singleObjectToInstance(CheckAfeOrItsSupplementalInProgress, {
					inprogressAfeReferenceNumber: afe.projectReferenceNumber,
				});
			}
		}
		return singleObjectToInstance(CheckAfeOrItsSupplementalInProgress, {
			inprogressAfeReferenceNumber: null,
		});
	}

	/**
	 * Get latest version of AFE info by parent afe reference number.
	 * @param afeProposalRefNumber
	 * @returns
	 */
	public async getLatestVersionAfeInfoByParentAfeRefNumber(
		afeProposalRefNumber: string,
		currentContext: CurrentContext,
	): Promise<LatestAfeVersionInfoResponseDto> {
		const afe = await this.afeProposalRepository.getAfeProposalIdWithSplitByRefNumber(
			afeProposalRefNumber,
		);
		if (!afe?.id) {
			throw new HttpException(`Afe doesn't exist`, HttpStatus.NOT_FOUND);
		}
		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(afe, currentContext);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to view this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}
		const latestApprovedVersionAfe = await this.afeProposalRepository.getLatestVersionInfoOfAfe(
			afe.id,
		);
		latestApprovedVersionAfe.parentAfeId = Number(afe.id);
		latestApprovedVersionAfe.totalAmount = latestApprovedVersionAfe.additionalCurrencyAmount.amount;
		latestApprovedVersionAfe.marketValue =
			latestApprovedVersionAfe?.marketValueAdditionalCurrencyAmount?.amount;
		return singleObjectToInstance(LatestAfeVersionInfoResponseDto, latestApprovedVersionAfe);
	}

	/**
	 * Generate AFE detail pdf.
	 * @param afeProposalId
	 * @param currentContext
	 * @param taskId
	 * @returns
	 */
	public async generateAfePdf(
		afeProposalId: number,
		currentContext: CurrentContext,
		taskId?: number,
	): Promise<Buffer> {
		const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
		if (!proposal) {
			throw new HttpException(`Afe doesn't exist.`, HttpStatus.NOT_FOUND);
		}
		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				proposal,
				currentContext,
				taskId,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to download this AFE information.`,
				HttpStatus.FORBIDDEN,
			);
		}

		/**
		 * AFE attachments.
		 */
		const attachments = await this.attachmentApiClient.getAllAttachments(
			afeProposalId,
			ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT,
		);
		for (const attachment of attachments) {
			const file = await this.attachmentApiClient.getContentByFileId(attachment.file_id);
			if (file.attachment_content_type.includes('image')) {
				attachment.base64 = `data:${file.attachment_content_type};base64,${Buffer.from(
					file.contents.data,
				).toString('base64')}`;
			}
		}

		/**
		 * Get AFE proposal approvers.
		 */
		const approvers = await this.afeProposalApproverRepository.getApproversByProposalId(
			afeProposalId,
		);
		const approversObj = approvers.map(approver => ({
			title: approver.title,
			actionStatus: APPROVER_STATUS_TITLE_FOR_PDF[approver.actionStatus],
			actionBy: approver.actionBy || '-',
			comment: approver.comment || '-',
			actionDate: approver.actionDate || '-',
			approvers:
				approver?.otherInfo?.usersDetail
					.map(user => `${user.firstName} ${user.lastName}`)
					.join(', ') || '-',
			userDetail: approver.userDetail
				? `${approver.userDetail?.originalApprover?.firstName || ''} ${
						approver.userDetail?.originalApprover?.lastName || ''
				  }`
				: null,
		}));

		let allApproversAttachments = [];

		if (approvers?.length) {
			const anyActionPerformed = approvers.find(approver => approver.actionBy);

			if (anyActionPerformed) {
				allApproversAttachments = await this.attachmentApiClient.getAttachmentsByMetadata({
					entity_type: ATTACHMENT_ENTITY_TYPE.AFE_ACTION,
					meta_data_1: afeProposalId.toString(),
				});
			}
		}

		for (const approverAttachment of allApproversAttachments) {
			const file = await this.attachmentApiClient.getContentByFileId(approverAttachment.file_id);
			if (file.attachment_content_type.includes('image')) {
				approverAttachment.base64 = `data:${file.attachment_content_type};base64,${Buffer.from(
					file.contents.data,
				).toString('base64')}`;
			}
		}

		/**
		 * Get AFE proposal amount split data.
		 */
		let splits = await this.getAfeProposalAmountSplitData(proposal);

		if (proposal.supplementalDeltaAmounts) {
			const {
				afeProposalAmountSplits,
				totalAmount,
				additionalCurrencyAmount,
				marketValue,
				marketValueAdditionalCurrencyAmount,
			} = proposal.supplementalDeltaAmounts;
			splits = {
				...splits,
				budgetTypeSplit: afeProposalAmountSplits.budgetTypeSplit.map(s => ({
					id: s.id,
					title: s.title,
					...s.additionalCurrencyAmount,
				})) as unknown as AmountSplitResponseDto[],
				projectComponentSplit: afeProposalAmountSplits.projectComponentSplit.map(s => ({
					id: s.id,
					title: s.title,
					...s.additionalCurrencyAmount,
				})) as unknown as AmountSplitResponseDto[],
				budgetBasedProjectSplit: afeProposalAmountSplits.budgetBasedProjectSplit.map(s => ({
					id: s.id,
					title: s.title,
					...s.additionalCurrencyAmountForBudgetBasedProjectSplit,
					currency: additionalCurrencyAmount.currency,
				})) as unknown as BudgetBasedProjectSplitResponseDto[],
			};
			proposal.totalAmount = totalAmount;
			proposal.additionalCurrencyAmount = additionalCurrencyAmount;
			proposal.marketValue = marketValue;
			proposal.marketValueAdditionalCurrencyAmount = marketValueAdditionalCurrencyAmount;
		}

		splits.budgetTypeSplit.forEach(split => {
			split.title = BUDGET_TYPE_TITLE[split.title];
		});

		/**
		 * AFE proposal history data.
		 */
		const histories = await this.historyApiClient.getRequestHistory(
			afeProposalId,
			HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
		);
		const nonHiddenHistories = histories.filter(history => !history.additional_info?.hidden);
		const afeHistory = nonHiddenHistories.map(history => ({
			actionPerformed: HistoryActionNameMapping[history.action_performed],
			actionComments: history.action_comments,
			actionDate: formatDateString(history.action_date),
			createdBy: history.created_by,
		}));

		const config = this.configService.getAppConfig();

		const data = {
			afeRequestTypeId: proposal.afeRequestTypeId,
			afeRequestType: AFE_REQUEST_TYPE_ID_WITH_DISPLAY_NAME_MAPPING[proposal.afeRequestTypeId],
			projectRefNum: proposal.projectReferenceNumber,
			createAt: proposal.createdOn,
			currentStatus: proposal.userStatus,
			projectName: proposal.name,
			businessEntity: proposal.entityTitle,
			projectJustification: proposal.data?.projectDetails?.projectJustification,
			projectLeaderName: proposal.data?.projectDetails?.projectLeader.displayName,
			projectLeaderTitle: proposal.data?.projectDetails?.projectLeader?.jobTitle,
			projectLeaderContactNum: proposal.data?.projectDetails?.projectLeaderNumber,
			keyContacts: proposal.data?.projectDetails?.keyContacts || '-',
			supplementalData: proposal.supplementalData || null,
			nature: proposal.data?.natureType?.title || null,
			type: proposal.data?.type || null,
			afeCategory: proposal.category === AFE_CATEGORY.SUPPLEMENTAL ? 'Supplemental' : '',
			afeSubCategory: proposal.data?.subType || null,
			budgetType: proposal.budgetType ? BUDGET_TYPE_TITLE[proposal.budgetType] : null,
			budgetTypeJustification: proposal?.data?.budgetTypeJustification || null,
			totalAmount: proposal.totalAmount,
			currencyType: proposal.currencyType,
			marketValue: proposal?.marketValue ? proposal.marketValue : null,
			marketValueCurrency: proposal?.marketValueCurrency ? proposal.marketValueCurrency : null,
			additionalCurrencyAmount: proposal.additionalCurrencyAmount,
			marketValueAdditionalCurrencyAmount: proposal?.marketValueAdditionalCurrencyAmount,
			isApprovedByBoard: proposal?.isApprovedByBoard ? 'Yes' : 'No',
			yearOfCommitment: proposal.yearOfCommitment
				? (
						await this.lengthOfCommitmentRepository.getLengthOfCommitmentByNumberOfYears(
							proposal.yearOfCommitment,
						)
				  )?.title
				: null,
			submitYear: proposal.workflowYear,
			readers:
				proposal.readers?.map(reader => `${reader.displayName}`).join(', ') || 'No Reader Added',
			approversList: approversObj,
			attachments: attachments.length ? attachments : [],
			globalProcurementQuesAns: proposal?.globalProcurementQuesAns,
			splits: splits,
			history: afeHistory,
			baseUrl: config.uiClient.baseUrl,
			allApproversAttachments: allApproversAttachments?.length ? allApproversAttachments : [],
			base64DpWorldLogo: BASE64_DP_WORLD_LOGO,
			location: proposal?.location?.title || null
		};

		const pdf = await this.pdfGeneratorService.generatePdf(data, 'afeDetail');
		return pdf;
	}
}
