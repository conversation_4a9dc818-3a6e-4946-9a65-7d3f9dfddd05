"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowModule = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../shared/clients");
const repositories_1 = require("./repositories");
const workflow_master_setting_controller_1 = require("./controllers/workflow-master-setting.controller");
const workflow_controller_1 = require("./controllers/workflow.controller");
const repositories_2 = require("../afe-proposal/repositories");
const repositories_3 = require("../finance/repositories");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const repositories_4 = require("../project-component/repositories");
const condition_creator_service_1 = require("../shared/services/condition-creator.service");
const services_2 = require("../shared/services");
const helpers_1 = require("../shared/helpers");
const workflow_shared_bucket_controller_1 = require("./controllers/workflow-shared-bucket.controller");
const workflow_shared_bucket_service_1 = require("./services/workflow-shared-bucket.service");
const repositories_5 = require("../finance/repositories");
const repositories_6 = require("../business-entity/repositories");
const services_3 = require("../finance/services");
const repositories_7 = require("../settings/repositories");
const services_4 = require("../settings/services");
const validators_1 = require("../shared/validators");
const services_5 = require("../core/services");
const repositories = [
    repositories_1.WorkflowMasterSettingRepository,
    repositories_1.WorkflowMasterStepRepository,
    repositories_1.WorkflowRuleRepository,
    repositories_1.WorkflowSharedBucketLimitRepository,
    repositories_1.WorkflowSharedChildLimitRepository,
    repositories_2.AfeProposalLimitDeductionRepository,
    repositories_3.CostCenterRepository,
    repositories_4.ProjectComponentRepository,
    repositories_5.CurrencyTypeRepository,
    repositories_5.NaturalAccountNumberRepository,
    repositories_6.EntitySetupRepository,
    repositories_5.CompanyCodeRepository,
    repositories_5.AnalysisCodeRepository,
    repositories_7.SettingsRepository,
    repositories_2.AfeProposalApproverRepository,
    repositories_2.AfeProposalRepository,
    repositories_2.AfeProposalAmountSplitRepository,
    repositories_2.UserCostCenterMappingRepository,
    repositories_2.UserProjectComponentMappingRepository
];
let WorkflowModule = class WorkflowModule {
};
WorkflowModule = __decorate([
    (0, common_1.Module)({
        providers: [
            services_1.WorkflowService,
            services_1.WorkflowRuleService,
            services_1.WorkflowMasterSettingService,
            services_2.SharedPermissionService,
            condition_creator_service_1.ConditionCreatorService,
            services_1.WorkflowMasterStepService,
            condition_creator_service_1.ConditionCreatorService,
            services_1.WorkflowSharedChildLimitService,
            services_1.WorkflowMasterStepService,
            condition_creator_service_1.ConditionCreatorService,
            workflow_shared_bucket_service_1.WorkflowSharedBucketService,
            clients_1.AdminApiClient,
            helpers_1.DatabaseHelper,
            helpers_1.SequlizeOperator,
            services_3.FinanceService,
            clients_1.MSGraphApiClient,
            services_4.SettingsService,
            validators_1.AfeProposalValidator,
            clients_1.HistoryApiClient,
            services_2.ExcelSheetService,
            clients_1.TaskApiClient,
            services_5.LoggerService,
            common_1.ConsoleLogger,
            ...repositories,
        ],
        controllers: [
            workflow_controller_1.WorkflowController,
            controllers_1.WorkflowMasterStepController,
            workflow_master_setting_controller_1.WorkflowMasterSetingController,
            controllers_1.WorkflowRuleController,
            controllers_1.WorkflowSharedChildLimitController,
            workflow_shared_bucket_controller_1.WorkflowSharedBucketController
        ]
    })
], WorkflowModule);
exports.WorkflowModule = WorkflowModule;
//# sourceMappingURL=workflow.module.js.map