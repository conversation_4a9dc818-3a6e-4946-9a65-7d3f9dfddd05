<div *ngIf="formReady; else loadingPage" [formGroup]="form">
  <!-- Total Expenditure START -->
  <div class="w-100 p-5 bg-body rounded">
    <div class="col-12 text-end">
      <button
        *ngIf="defaultValues?.parentAfeLatestBudgetBasedProjectSplit?.length"
        (click)="openAmountBreakdownModal()"
        type="button"
        class="btn btn-sm mb-3 editBtn btnRounded fw-bold ps-4 pe-4 py-1 mx-2"
        translate="FORM.BUTTON.AMOUNT_DIFFERENCE_BUTTON"
      ></button>
    </div>

    <div class="col-12 rounded border p-5">
      <app-total-expenditure
        [currencyDetail]="defaultValues.currencyDetail"
        [totalAmount]="defaultValues.totalAmount"
        [showHeaderTitle]="true"
        [requestTypeId]="defaultValues.requestTypeId"
        [supplementalAmount]="supplementalAmount"
      >
      </app-total-expenditure>
    </div>
  </div>
  <!-- Total Expenditure END -->

  <!-- Budget Type Split START -->
  <ng-container *ngIf="defaultValues.budgetType.id === 3">
    <div class="w-100 p-5 bg-body rounded mt-5">
      <div class="col-12 rounded border p-5">
        <div class="pb-1 border-bottom mb-5">
          <h3 class="fw-bolder d-flex align-items-center">
            {{ "FORM.LABEL.BUDGET" | translate }}
            {{ "FORM.LABEL.TYPE" | translate }}
            {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
          </h3>
        </div>

        <div class="row" formArrayName="budgetTypeSplit">
          <div
            class="col-6"
            *ngFor="
              let budgetTypeSplit of budgetTypeSplit.controls;
              let i = index
            "
            [formGroupName]="i"
          >
            <div class="fv-row mb-0">
              <label class="form-label fw-bold pb-1 required">
                {{ budgetTypeSplit.value.title | titlecase }} (
                {{ "COMMON.AMOUNT" | translate }}
                {{ "COMMON.IN" | translate }}
                {{ defaultValues.currencyDetail.currency }})
              </label>
              <app-input-amount
                (change)="
                  onAddBudgetBasedProjectSplit(true, budgetTypeSplit.value.id)
                "
                type="currency"
                formControlName="amount"
                placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                  'COMMON.AMOUNT' | translate
                }} {{ 'COMMON.IN' | translate }} {{
                  defaultValues.currencyDetail.currency
                }}"
              ></app-input-amount>

              <ng-container
                *ngIf="
                  budgetTypeSplit.get('amount')?.hasError('min') &&
                  (isSubmitted || budgetTypeSplit.get('amount')?.touched)
                "
              >
                <app-input-error-message
                  errorMessage="{{ 'FORM.VALIDATION.MIN_AMOUNT' | translate }}"
                >
                </app-input-error-message>
              </ng-container>

              <ng-container
                *ngIf="
                  budgetTypeSplit.get('amount')?.hasError('max') &&
                  (isSubmitted || budgetTypeSplit.get('amount')?.touched)
                "
              >
                <app-input-error-message
                  errorMessage="{{ 'FORM.VALIDATION.MAX_AMOUNT' | translate }}"
                >
                </app-input-error-message>
              </ng-container>

              <ng-container
                *ngIf="
                  budgetTypeSplit.get('amount')?.hasError('required') &&
                  (isSubmitted || budgetTypeSplit.get('amount')?.touched)
                "
              >
                <app-input-error-message
                  errorMessage="{{
                    'FORM.VALIDATION.REQUIRED_FIELD' | translate
                  }}"
                >
                </app-input-error-message>
              </ng-container>
            </div>
          </div>

          <ng-container
            *ngIf="
              (budgetTypeSplit.hasError('totalAmountError') ||
                totalBudgetError) &&
              (isSubmitted || budgetTypeSplit.get('amount')?.touched)
            "
          >
            <app-input-error-message
              errorMessage="{{
                'FORM.VALIDATION.TOTAL_SUM_AMOUNT'
                  | translate
                    : {
                        amount:
                          defaultValues.totalAmount
                          | currency
                            : defaultValues.currencyDetail.currency
                            : 'symbol'
                            : '1.2-2'
                      }
              }}"
            >
            </app-input-error-message>
          </ng-container>
        </div>
      </div>
    </div>
  </ng-container>
  <!-- Budget Type Split END -->

  <!-- Budget Based Project Component Split START -->
  <ng-container
    *ngIf="
      defaultValues.budgetType.id === 3 &&
      defaultValues.projectComponentSplit.length > 1 &&
      budgetBasedProjectSplit.controls &&
      budgetedAmount &&
      unbudgetedAmount
    "
  >
    <div class="w-100 p-5 bg-body rounded mt-5">
      <div class="col-12 rounded border p-5">
        <div class="pb-1 border-bottom mb-5">
          <h3 class="fw-bolder d-flex align-items-center">
            {{ "COMMON.BUDGET_BASED_PROJECT_SPLIT" | translate }}
            {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
          </h3>
        </div>

        <!-- Budget based Project amount split START -->
        <div class="row" formArrayName="budgetBasedProjectSplit">
          <div
            class="col-12"
            *ngFor="
              let budgetBasedProjectSplit of budgetBasedProjectSplit.controls;
              let i = index
            "
            [formGroupName]="i"
          >
            <div class="fv-row mb-0">
              <label class="form-label fw-bolder pb-1">
                {{ i + 1 }}.
                {{ budgetBasedProjectSplit.value.title | titlecase }}
              </label>
            </div>
            <div class="row">
              <div class="col-6">
                <label class="form-label fw-bold pb-1 required">
                  {{ "COMMON.BUDGETED" | translate }} (
                  {{ "COMMON.AMOUNT" | translate }}
                  {{ "COMMON.IN" | translate }}
                  {{ defaultValues.currencyDetail.currency }} )
                </label>
                <app-input-amount
                  type="currency"
                  formControlName="budgetedAmount"
                  placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                    'COMMON.AMOUNT' | translate
                  }} {{ 'COMMON.IN' | translate }} {{
                    defaultValues.currencyDetail.currency
                  }}"
                ></app-input-amount>

                <ng-container
                  *ngIf="
                    budgetBasedProjectSplit
                      .get('budgetedAmount')
                      ?.hasError('min') &&
                    (isSubmitted ||
                      budgetBasedProjectSplit.get('budgetedAmount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.MIN_AMOUNT' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>

                <ng-container
                  *ngIf="
                    budgetBasedProjectSplit
                      .get('budgetedAmount')
                      ?.hasError('max') &&
                    (isSubmitted ||
                      budgetBasedProjectSplit.get('budgetedAmount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.MAX_AMOUNT' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>

                <ng-container
                  *ngIf="
                    budgetBasedProjectSplit
                      .get('budgetedAmount')
                      ?.hasError('required') &&
                    (isSubmitted ||
                      budgetBasedProjectSplit.get('budgetedAmount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>
              <div class="col-6">
                <label class="form-label fw-bold pb-1 required">
                  {{ "COMMON.UNBUDGETED" | translate }} (
                  {{ "COMMON.AMOUNT" | translate }}
                  {{ "COMMON.IN" | translate }}
                  {{ defaultValues.currencyDetail.currency }} )
                </label>
                <app-input-amount
                  type="currency"
                  formControlName="unbudgetedAmount"
                  placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                    'COMMON.AMOUNT' | translate
                  }} {{ 'COMMON.IN' | translate }} {{
                    defaultValues.currencyDetail.currency
                  }}"
                ></app-input-amount>

                <!-- Amount difference Errors START -->
                <ng-container
                  *ngIf="
                    budgetBasedProjectSplit
                      .get('unbudgetedAmount')
                      ?.hasError('min') &&
                    (isSubmitted ||
                      budgetBasedProjectSplit.get('unbudgetedAmount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.MIN_AMOUNT' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>

                <ng-container
                  *ngIf="
                    budgetBasedProjectSplit
                      .get('unbudgetedAmount')
                      ?.hasError('max') &&
                    (isSubmitted ||
                      budgetBasedProjectSplit.get('unbudgetedAmount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.MAX_AMOUNT' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>

                <ng-container
                  *ngIf="
                    budgetBasedProjectSplit
                      .get('unbudgetedAmount')
                      ?.hasError('required') &&
                    (isSubmitted ||
                      budgetBasedProjectSplit.get('unbudgetedAmount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
                <!-- Amount difference Errors END -->
              </div>

              <ng-container
                *ngIf="
                  budgetBasedProjectSplit.value.totalAmount !==
                    toFixed(
                      budgetBasedProjectSplit.value.budgetedAmount +
                        budgetBasedProjectSplit.value.unbudgetedAmount,
                      2
                    ) &&
                  (isSubmitted ||
                    budgetBasedProjectSplit.get('budgetedAmount')?.touched ||
                    budgetBasedProjectSplit.get('unbudgetedAmount')?.touched)
                "
              >
                <app-input-error-message
                  errorMessage="{{
                    'FORM.VALIDATION.TOTAL_SUM_AMOUNT'
                      | translate
                        : {
                            amount:
                              budgetBasedProjectSplit.value.totalAmount
                              | currency
                                : defaultValues.currencyDetail.currency
                                : 'symbol'
                                : '1.2-2'
                          }
                  }}"
                >
                </app-input-error-message>
              </ng-container>
            </div>
            <div class="separator mt-5 mb-3 opacity-75"></div>
          </div>

          <!-- Error Message for total amount Start -->
          <ng-container
            *ngIf="
              (budgetBasedProjectSplit.hasError('totalAmountError') ||
                totalBudgetProjectError) &&
              (isSubmitted ||
                budgetBasedProjectSplit.get('budgetedAmount')?.touched ||
                budgetBasedProjectSplit.get('unbudgetedAmount')?.touched)
            "
          >
            <app-input-error-message
              errorMessage="{{
                'FORM.VALIDATION.TOTAL_SUM_AMOUNT'
                  | translate
                    : {
                        amount:
                          defaultValues.totalAmount
                          | currency
                            : defaultValues.currencyDetail.currency
                            : 'symbol'
                            : '1.2-2'
                      }
              }}"
            >
            </app-input-error-message>
          </ng-container>
          <!-- Error Message for total amount End  -->

          <!-- Error Message for Budgeted and Unbudgeted amount split Start -->
          <ng-container
            *ngIf="
              (budgetBasedProjectSplit.hasError('totalBudgetedProjectError') ||
                totalBudgetedProjectError) &&
              (isSubmitted ||
                budgetBasedProjectSplit.get('budgetedAmount')?.touched ||
                budgetBasedProjectSplit.get('unbudgetedAmount')?.touched)
            "
          >
            <app-input-error-message
              errorMessage="{{
                'FORM.VALIDATION.TOTAL_BUDGETED_SUM_AMOUNT'
                  | translate
                    : {
                        amount:
                          budgetedAmount
                          | currency
                            : defaultValues.currencyDetail.currency
                            : 'symbol'
                            : '1.2-2'
                      }
              }}"
            >
            </app-input-error-message>
          </ng-container>

          <ng-container
            *ngIf="
              (budgetBasedProjectSplit.hasError(
                'totalUnbudgetedProjectError'
              ) ||
                totalUnbudgetedProjectError) &&
              (isSubmitted ||
                budgetBasedProjectSplit.get('budgetedAmount')?.touched ||
                budgetBasedProjectSplit.get('unbudgetedAmount')?.touched)
            "
          >
            <app-input-error-message
              errorMessage="{{
                'FORM.VALIDATION.TOTAL_UNBUDGETED_SUM_AMOUNT'
                  | translate
                    : {
                        amount:
                          unbudgetedAmount
                          | currency
                            : defaultValues.currencyDetail.currency
                            : 'symbol'
                            : '1.2-2'
                      }
              }}"
            >
            </app-input-error-message>
          </ng-container>
          <!-- Error Message for Budgeted and Unbudgeted amount split END -->
        </div>
        <!-- Budget based Project amount split END -->
      </div>
    </div>
  </ng-container>
  <!-- Budget Based Project Component Split END -->

  <!-- Cost Center Amount Split START -->
  <div class="p-5 w-100 bg-body rounded mt-5">

    <!-- Cost center combination selection -->
    <div class="col-12 rounded border p-5 pb-0">
      <div class="pb-1 mb-5 border-bottom">
        <h3 class="fw-bolder d-flex align-items-center">
          <ng-container *ngIf="this.costCenterList.length">
            {{ "FORM.LABEL.ACCOUNT_DETAILS" | translate }}
            {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
          </ng-container>
          <ng-container *ngIf="!this.costCenterList.length">
            {{ "FORM.LABEL.BUDGET_REFERENCE_NUMBER" | translate }}
            {{ "FORM.LABEL.AMOUNT_SPLIT" | translate }}
          </ng-container>
        </h3>
      </div>

      <div
        class="fv-row mb-7"
        *ngIf="totalCostCenterLeft() && costCenterSplit.controls.length === 0"
      >
        <label
          class="form-label fw-bold pb-1 required"
          translate="FORM.LABEL.COST_CENTER"
        ></label>

        <!-- <select
          name="number"
          class="form-select form-select-lg form-select-solid"
          *ngIf="costCenterList.length >= 1"
          formControlName="selectedCostCenter"
          (change)="onCostCenterAdd()"
        >
          <option value="">
            {{ "FORM.PLACEHOLDER.SELECT" | translate }}
            {{ "FORM.LABEL.COST_CENTER" | translate }}
          </option>
          <ng-container *ngFor="let costCenter of costCenterList">
            <option *ngIf="costCenter.active" [value]="costCenter.id">
              {{ costCenter.code }} - ({{ costCenter.name }})
            </option>
          </ng-container>
        </select> -->

        <ng-container *ngIf="costCenterList.length >= 1">
          <div class="row">
            <ng-select
              class="ng-select-custom"
              [notFoundText]="'FORM.VALIDATION.NO_COST_CENTER' | translate"
              formControlName="selectedCostCenter"
            >
              <ng-option value="">
                {{ "FORM.PLACEHOLDER.SELECT" | translate }}
                {{ "FORM.LABEL.COST_CENTER" | translate }}
              </ng-option>
              <ng-container *ngFor="let costCenter of costCenterList">
                <ng-option *ngIf="costCenter.active" [value]="costCenter.id">
                  {{ costCenter.code }} - ({{ costCenter.name }})
                </ng-option>
              </ng-container>
            </ng-select>

            <ng-container
              *ngIf="isCostCenterError && !form?.value?.selectedCostCenter"
            >
              <app-input-error-message
                errorMessage="{{
                  'FORM.VALIDATION.REQUIRED_FIELD' | translate
                }}"
              >
              </app-input-error-message>
            </ng-container>
          </div>

          <div class="row">
            <!-- <div class="col-6 mt-3">
              <div class="fv-row mb-0">
                <label
                  class="form-label fw-bold pb-1 required"
                  translate="FORM.LABEL.NATURAL_ACCOUNT"
                ></label>

                <ng-select
                  class="ng-select-custom"
                  [notFoundText]="
                    'FORM.VALIDATION.NO_NATURAL_ACCOUNT' | translate
                  "
                  formControlName="selectedNaturalAccount"
                >
                  <ng-option value="">
                    {{ "FORM.PLACEHOLDER.SELECT" | translate }}
                    {{ "FORM.LABEL.NATURAL_ACCOUNT" | translate }}
                  </ng-option>
                  <ng-option
                    *ngFor="
                      let naturalAccountNumber of naturalAccountNumberList
                    "
                    [value]="naturalAccountNumber.id"
                  >
                    {{ naturalAccountNumber.number }} - ({{
                      naturalAccountNumber.title
                    }})
                  </ng-option>
                </ng-select>

                <ng-container
                  *ngIf="
                    isCostCenterError && !form?.value?.selectedNaturalAccount
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>
            </div> -->

            <div class="col-6 mt-3">
              <div>
                <div class="fv-row mb-0">
                  <label
                    class="form-label fw-bold pb-1 required"
                    translate="FORM.LABEL.ANALYSIS_CODE"
                  ></label>

                  <!-- <ng-container *ngIf="analysisCodeList.length > 1"> -->
                  <ng-select
                    class="ng-select-custom"
                    [notFoundText]="
                      'FORM.VALIDATION.NO_ANALYSIS_CODE' | translate
                    "
                    formControlName="selectedAnalysisCode"
                  >
                    <ng-option value="">
                      {{ "FORM.PLACEHOLDER.SELECT" | translate }}
                      {{ "FORM.LABEL.ANALYSIS_CODE" | translate }}
                    </ng-option>
                    <ng-option
                      *ngFor="let analysisCode of analysisCodeList"
                      [value]="analysisCode.id"
                    >
                      {{ analysisCode.code }} - ({{ analysisCode.title }})
                    </ng-option>
                  </ng-select>
                  <!-- </ng-container> -->

                  <!-- <div *ngIf="analysisCodeList.length <= 1" class="fv-row mb-0">
                    <input
                      name="code"
                      [disabled]="true"
                      class="form-control form-control-lg form-control-solid"
                      [value]="getAnalysisTitleCodeByCode(analysisCode)"
                    />
                  </div> -->

                  <ng-container
                    *ngIf="
                      isCostCenterError && !form?.value?.selectedAnalysisCode
                    "
                  >
                    <app-input-error-message
                      errorMessage="{{
                        'FORM.VALIDATION.REQUIRED_FIELD' | translate
                      }}"
                    >
                    </app-input-error-message>
                  </ng-container>
                </div>
              </div>
            </div>
          </div>

          <div class="text-right">
            <button
              type="button"
              class="btn btn-sm btn-primary ps-4 pe-4 py-1 mt-3"
              (click)="onCostCenterAdd()"
            >
              <img
                width="12px"
                src="./assets/media/svg/icons/dpw-icons/plus.png"
                alt="Next"
              />
              <span
                class="px-1 indicator-label"
                translate="FORM.BUTTON.ADD_COMBINATION_BUTTON"
              ></span>
            </button>
          </div>

          <ng-container *ngIf="isCombinationAvailable">
            <app-input-error-message
              errorMessage="{{
                'FORM.VALIDATION.COMBINATION_AVAILABLE'
                  | translate
                  | sentenceCase
              }}"
            >
            </app-input-error-message>
          </ng-container>
        </ng-container>

        <ng-container
          *ngIf="
            form.get('costCenterSplit')?.hasError('required') &&
            (isSubmitted || form.get('costCenterSplit')?.touched)
          "
        >
          <app-input-error-message
            errorMessage="{{
              'FORM.VALIDATION.ADD_COMBINATION_ERROR'
                | translate : { name: 'FORM.LABEL.COST_CENTER' | translate }
                | sentenceCase
            }}"
          >
          </app-input-error-message>
        </ng-container>
      </div>

      <div formArrayName="costCenterSplit">
        <div
          class="border border-gray-300 border-dashed rounded py-3 px-4 mb-4"
          *ngFor="
            let costCenterSplit of costCenterSplit.controls;
            let i = index
          "
          [formGroupName]="i"
        >
          <div *ngIf="costCenterList.length" class="fv-row px-2 mb-5">
            <div class="d-flex justify-content-between">
              <!-- Uncomment this when we can add multiple cost centers -->
              <!-- <label class="form-label fw-bold pb-1 required">
                {{ i + 1 }}. {{ "FORM.LABEL.COST_CENTER" | translate }}
              </label> -->

              <!-- Remove this when we can add multiple cost centers -->
              <label class="form-label fw-bold pb-1 required">
                {{ "FORM.LABEL.COST_CENTER" | translate }}
              </label>

              <!--
                " -->
              <a
                class="cursor-pointer"
                (click)="
                  changeCostCenterStatus(
                    { id: costCenterSplit.get('id')?.value },
                    i
                  )
                "
                style="position: relative; bottom: 5px"
              >
                <img
                  width="32px"
                  src="./assets/media/svg/icons/dpw-icons/cross2.png"
                  alt="Next"
                />
              </a>
            </div>

            <div class="row">
              <div
                [class]="
                  getSection(costCenterSplit.get('id')?.value)?.length
                    ? 'col-lg-8'
                    : 'col-lg-12'
                "
              >
                <input
                  name="code"
                  [disabled]="true"
                  class="form-control form-control-lg form-control-solid"
                  [value]="
                    getCostCenterSelectedValue(costCenterSplit.get('id')?.value)
                  "
                />

                <ng-container
                  *ngIf="
                    costCenterSplit.get('code')?.hasError('required') &&
                    (isSubmitted || costCenterSplit.get('code')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>
              <div
                class="col-lg-4"
                *ngIf="getSection(costCenterSplit.get('id')?.value)?.length"
              >
                <ng-select
                  class="ng-select-custom"
                  [notFoundText]="'FORM.VALIDATION.NO_SECTION' | translate"
                  formControlName="section"
                  (change)="sectionChange(costCenterSplit.get('section'))"
                >
                  <ng-option value="">
                    {{ "FORM.PLACEHOLDER.SELECT" | translate }}
                    {{ "FORM.LABEL.SECTION" | translate }}
                  </ng-option>
                  <ng-container
                    *ngFor="
                      let sectionDetail of getSection(
                        costCenterSplit.get('id')?.value
                      )
                    "
                  >
                    <ng-option [value]="sectionDetail.title">
                      {{ sectionDetail.title }}
                    </ng-option>
                  </ng-container>
                </ng-select>

                <ng-container
                  *ngIf="
                    costCenterSplit.get('section')?.hasError('required') &&
                    (isSubmitted || costCenterSplit.get('section')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>
            </div>

            <div class="row">
              <!-- <div class="col-lg-6 mt-3">
                <label class="form-label fw-bold pb-1 required">
                  {{ "FORM.LABEL.NATURAL_ACCOUNT" | translate }}
                </label>
                <input
                  name="naturalAccount"
                  [disabled]="true"
                  class="form-control form-control-lg form-control-solid"
                  [value]="
                    getNaturalAccountTitleNumberById(
                      costCenterSplit.get('naturalAccountId')?.value
                    )
                  "
                />
              </div> -->

              <div class="col-lg-6 mt-3">
                <label class="form-label fw-bold pb-1 required">
                  {{ "FORM.LABEL.ANALYSIS_CODE" | translate }}
                </label>
                <input
                  name="analysisCode"
                  [disabled]="true"
                  class="form-control form-control-lg form-control-solid"
                  [value]="
                    getAnalysisTitleCodeById(
                      costCenterSplit.get('analysisCodeId')?.value
                    )
                  "
                />
              </div>
            </div>
          </div>

          <!-- Budget Ref Number Split -->
          <div formArrayName="budgetReferenceNumberSplit">
            <div
              class="col-md-12 py-3 my-1"
              [ngClass]="{
                'border-bottom': this.defaultValues.budgetType.id !== 2
              }"
              *ngFor="
                let budgetReferenceNumber of getbudgetReferenceSplit(
                  costCenterSplit
                ).controls;
                let j = index
              "
              [formGroupName]="j"
            >
              <div
                class="d-flex flex-lg-row flex-sm-column flex-wrap justify-lg-content-between"
              >
                <div
                  class="col-lg-6 px-2 col-12 d-sm-flex flex-sm-row justify-sm-content-between"
                >
                  <div class="mr-5 w-100">
                    <div class="d-flex flex-row justify-content-between pb-0">
                      <div>
                        <label
                          class="form-label fw-bold pb-1"
                          [ngClass]="{
                            required: this.defaultValues.budgetType.id !== 2
                          }"
                        >
                          {{ this.costCenterList.length ? i + 1 + "." : ""
                          }}{{ j + 1 }}.
                          {{ "FORM.LABEL.BUDGET_REFERENCE_NUMBER" | translate }}
                        </label>
                      </div>
                      <div
                        class="d-block cursor-pointer d-lg-none d-md-none d-sm-none"
                        *ngIf="j"
                        (click)="
                          onRemoveBudgetReferenceNumber(
                            getbudgetReferenceSplit(costCenterSplit),
                            j
                          )
                        "
                      >
                        <img
                          width="32px"
                          src="./assets/media/svg/icons/dpw-icons/cross2.png"
                          alt="Next"
                        />
                      </div>
                    </div>

                    <input
                      name="number"
                      placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                        'FORM.LABEL.BUDGET_REFERENCE_NUMBER' | translate
                      }}"
                      class="form-control form-control-lg form-control-solid"
                      formControlName="number"
                      maxlength="150"
                    />

                    <ng-container
                      *ngIf="
                        budgetReferenceNumber
                          .get('number')
                          ?.hasError('required') &&
                        (isSubmitted ||
                          budgetReferenceNumber.get('number')?.touched)
                      "
                    >
                      <app-input-error-message
                        errorMessage="{{
                          'FORM.VALIDATION.REQUIRED_FIELD' | translate
                        }}"
                      >
                      </app-input-error-message>
                    </ng-container>
                  </div>
                </div>

                <div
                  class="col-lg-6 px-2 col-12 d-flex flex-row justify-content-between"
                >
                  <div class="w-100">
                    <div class="px-0 py-5 py-lg-0 py-md-0">
                      <label
                        class="form-label required fw-bold pb-1"
                        translate="COMMON.AMOUNT"
                      ></label>
                      <app-input-amount
                        class="mr-5"
                        type="currency"
                        formControlName="amount"
                        placeholder="{{
                          'FORM.PLACEHOLDER.ENTER' | translate
                        }} {{ 'COMMON.AMOUNT' | translate }} {{
                          'COMMON.IN' | translate
                        }} {{ defaultValues.currencyDetail.currency }}"
                      ></app-input-amount>
                      <ng-container
                        *ngIf="
                          budgetReferenceNumber
                            .get('amount')
                            ?.hasError('min') &&
                          (isSubmitted ||
                            budgetReferenceNumber.get('amount')?.touched)
                        "
                      >
                        <app-input-error-message
                          errorMessage="{{
                            'FORM.VALIDATION.MIN_AMOUNT' | translate
                          }}"
                        >
                        </app-input-error-message>
                      </ng-container>

                      <ng-container
                        *ngIf="
                          budgetReferenceNumber
                            .get('amount')
                            ?.hasError('max') &&
                          (isSubmitted ||
                            budgetReferenceNumber.get('amount')?.touched)
                        "
                      >
                        <app-input-error-message
                          errorMessage="{{
                            'FORM.VALIDATION.MAX_AMOUNT' | translate
                          }}"
                        >
                        </app-input-error-message>
                      </ng-container>

                      <ng-container
                        *ngIf="
                          budgetReferenceNumber
                            .get('amount')
                            ?.hasError('required') &&
                          (isSubmitted ||
                            budgetReferenceNumber.get('amount')?.touched)
                        "
                      >
                        <app-input-error-message
                          errorMessage="{{
                            'FORM.VALIDATION.REQUIRED_FIELD' | translate
                          }}"
                        >
                        </app-input-error-message>
                      </ng-container>
                    </div>
                  </div>

                  <div
                    class="align-self-center cursor-pointer d-none d-lg-block d-md-block d-sm-block px-2"
                    *ngIf="j"
                    (click)="
                      onRemoveBudgetReferenceNumber(
                        getbudgetReferenceSplit(costCenterSplit),
                        j
                      )
                    "
                  >
                    <img
                      width="32px"
                      class="pt-6"
                      src="./assets/media/svg/icons/dpw-icons/cross2.png"
                      alt="Next"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div
              class="d-flex justify-content-end pt-4"
              *ngIf="defaultValues.budgetType.id !== 2"
            >
              <button
                type="button"
                class="btn btn-sm btn-primary ps-4 pe-4 py-1"
                (click)="onBudgetReferenceNumberAdd(costCenterSplit)"
              >
                <img
                  width="12px"
                  src="./assets/media/svg/icons/dpw-icons/plus.png"
                  alt="Next"
                />
                <span
                  class="px-1 indicator-label"
                  translate="FORM.BUTTON.ADD_MORE_BUDGET_REFBUTTON"
                ></span>
              </button>
            </div>
          </div>
        </div>

        <ng-container
          *ngIf="
            (costCenterSplit.hasError('totalBudgetReferenceAmountError') ||
              totalBudgetRefError) &&
            isSubmitted
          "
        >
          <app-input-error-message
            errorMessage="{{
              'FORM.VALIDATION.TOTAL_BUDGET_REF_AMOUNT'
                | translate
                  : {
                      amount:
                        defaultValues.totalAmount
                        | currency
                          : defaultValues.currencyDetail.currency
                          : 'symbol'
                          : '1.2-2'
                    }
            }}"
          >
          </app-input-error-message>
        </ng-container>
      </div>
    </div>

    <!-- New Natural Account Split -->
    <div
      *ngIf="costCenterList.length && costCenterSplit.controls.length && naturalAccountNumberSplit?.controls?.length"
      class="p-0 w-100 bg-body rounded mt-5"
    >
      <div class="col-12 rounded border p-5">
        <div class="pb-1 border-bottom mb-5">
          <h3
            class="fw-bolder d-flex align-items-center"
            translate="FORM.LABEL.NATURAL_ACCOUNTS"
          ></h3>
        </div>

        <ng-container *ngIf="naturalAccountNumberSplit?.controls?.length">
          <div class="row" formArrayName="naturalAccountNumberSplit">
            <div
              class="row mb-3"
              *ngFor="
                let naturalAccountNumberSplitDetail of naturalAccountNumberSplit.controls;
                let i = index
              "
              [formGroupName]="i"
            >
              <!-- Account Selection -->
              <div class="col-sm-12 col-md-6 col-lg-6 mb-0">
                <label
                  class="form-label fw-bold pb-1 required"
                  translate="FORM.LABEL.ACCOUNT_NUMBER"
                ></label>

                <!-- Multiple Account -->
                <ng-container *ngIf="naturalAccountNumberList.length > 1">
                  <ng-select
                    class="ng-select-custom"
                    [notFoundText]="
                      'FORM.VALIDATION.NO_NATURAL_ACCOUNT' | translate
                    "
                    (change)="setNaturalAccountNumber(i)"
                    formControlName="id"
                  >
                    <ng-option value="">
                      {{ "FORM.PLACEHOLDER.SELECT" | translate }}
                      {{ "FORM.LABEL.NATURAL_ACCOUNT" | translate }}
                    </ng-option>
                    <ng-option
                      *ngFor="
                        let naturalAccountNumber of getAvailableNaturalAccountsForIndex(
                          i
                        )
                      "
                      [value]="naturalAccountNumber.id"
                    >
                      {{ naturalAccountNumber.number }} - ({{
                        naturalAccountNumber.title
                      }})
                    </ng-option>
                  </ng-select>
                </ng-container>

                <!-- Single Account -->
                <div
                  *ngIf="naturalAccountNumberList.length <= 1"
                  class="fv-row mb-0"
                >
                  <input
                    name="number"
                    [disabled]="true"
                    class="form-control form-control-lg form-control-solid"
                    [value]="
                      getSingleNaturalAccountDisplay(
                        naturalAccountNumberSplitDetail
                      )
                    "
                  />
                </div>

                <ng-container
                  *ngIf="
                    naturalAccountNumberSplitDetail
                      .get('number')
                      ?.hasError('required') &&
                    (isSubmitted ||
                      naturalAccountNumberSplitDetail.get('number')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>

              <!-- Amount Entry -->
              <div class="col-sm-12 col-md-5 col-lg-5 mb-0">
                <label class="form-label fw-bold pb-1 required">
                  {{ "COMMON.AMOUNT" | translate }}
                  {{ "COMMON.IN" | translate }}
                  {{ defaultValues.currencyDetail.currency }}
                </label>

                <div class="fv-row mb-0">
                  <!-- (change)="
                  onAddBudgetBasedProjectSplit(true, budgetTypeSplit.value.id)
                " -->
                  <app-input-amount
                    [isDisabled]="naturalAccountNumberList.length <= 1"
                    type="currency"
                    formControlName="amount"
                    (change)="onNaturalAccountAmountChange()"
                    placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
                      'COMMON.AMOUNT' | translate
                    }} {{ 'COMMON.IN' | translate }} {{
                      defaultValues.currencyDetail.currency
                    }}"
                  ></app-input-amount>
                </div>

                <ng-container
                  *ngIf="
                    naturalAccountNumberSplitDetail
                      .get('amount')
                      ?.hasError('required') &&
                    (isSubmitted ||
                      naturalAccountNumberSplitDetail.get('amount')?.touched)
                  "
                >
                  <app-input-error-message
                    errorMessage="{{
                      'FORM.VALIDATION.REQUIRED_FIELD' | translate
                    }}"
                  >
                  </app-input-error-message>
                </ng-container>
              </div>

              <!-- Remove Button -->
              <div
                class="col-sm-12 col-md-1 col-lg-1 mb-0 d-flex align-items-end"
                *ngIf="
                  naturalAccountNumberList.length > 1 &&
                  naturalAccountNumberSplit.controls.length > 1
                "
              >
                <div
                  title="{{ 'COMMON.REMOVE' | translate }}"
                  (click)="removeNaturalAccountSplit(i)"
                  class="cursor-pointer mt-5 mb-5 d-flex justify-content-center align-items-center"
                >
                  <img
                    width="32px"
                    src="./assets/media/svg/icons/dpw-icons/cross2.png"
                    alt="Remove"
                  />
                </div>
              </div>
            </div>

            <ng-container
              *ngIf="
                naturalAccountNumberSplit.hasError('totalAmountError') &&
                (isSubmitted ||
                  naturalAccountNumberSplit.get('amount')?.touched)
              "
            >
              <app-input-error-message
                errorMessage="{{
                  'FORM.VALIDATION.TOTAL_SUM_AMOUNT'
                    | translate
                      : {
                          amount:
                            defaultValues.totalAmount
                            | currency
                              : defaultValues.currencyDetail.currency
                              : 'symbol'
                              : '1.2-2'
                        }
                }}"
              >
              </app-input-error-message>
            </ng-container>
          </div>

          <!-- Add Natural Account Button -->
          <div
            class="d-flex justify-content-end pt-4"
            *ngIf="
              companyDetail?.enableMultiNaturalAccount &&
              naturalAccountNumberList.length > 1 &&
              naturalAccountNumberSplit.controls.length <
                naturalAccountNumberList.length
            "
          >
            <button
              (click)="addNaturalAccountSplit()"
              type="button"
              class="btn btn-sm btn-primary ps-4 pe-4 py-1"
            >
              <img
                width="12px"
                src="./assets/media/svg/icons/dpw-icons/plus.png"
                alt="Add"
              />
              <span class="px-1 indicator-label">
                {{ "COMMON.ADD" | translate }}
                {{ "FORM.LABEL.NATURAL_ACCOUNT" | translate }}
              </span>
            </button>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Chart of Account Amount Split -->
    <div
      *ngIf="costCenterList.length && costCenterSplit.controls.length && chartOfAccountSplits.controls.length && hasValidNaturalAccountEntry()"
      class="p-0 w-100 bg-body rounded mt-5"
    >
      <div class="col-12 rounded border p-lg-5 p-md-5 p-sm-5 p-2 pb-0">
        <div formArrayName="costCenterSplit">
          <div class="row pb-1 border-bottom mb-5">
            <h3
              class="fw-bolder d-flex align-items-center"
              translate="FORM.LABEL.CHART_OF_ACCOUNT"
            ></h3>
          </div>

          <div
            *ngFor="
              let costCenterSplit of getUniqueChartOfAccountView();
              let k = index
            "
            class="mt-5"
          >
            <div
              *ngIf="isMultiChartOfAccount || (!isMultiChartOfAccount && !k)"
              class="border border-gray-300 border-dashed rounded py-3 px-4 mb-4"
            >
              <div>
                <div class="row pb-3 mb-3 border-bottom">
                  <h6 class="card-title align-items-start flex-column">
                    <span
                      translate="FORM.LABEL.CHARGE_AMMOUNT"
                      class="text-gray-600 mt-1 fs-6 d-block"
                    ></span
                    ><span class="card-label fw-bold text-dark">{{
                      costCenterSplit.amount
                        | currency
                          : defaultValues.currencyDetail.currency
                          : "symbol"
                          : "1.2-2"
                    }}</span>
                  </h6>
                </div>
                <div class="row">
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.COMPANY"
                        class="text-gray-600 fw-bold mt-1 fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment1
                      }}</span>
                    </div>
                  </div>
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.COST_CENTER"
                        class="text-gray-600 fw-bold mt-1 fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment2
                      }}</span>
                    </div>
                  </div>
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.ACCOUNT"
                        class="text-gray-600 fw-bold mt-1 fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment3
                      }}</span>
                    </div>
                  </div>
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.INTERCOMPANY"
                        class="text-gray-600 fw-bold mt-1 fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment4
                      }}</span>
                    </div>
                  </div>
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.SERVICE"
                        class="text-gray-600 fw-bold mt-1 fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment5
                      }}</span>
                    </div>
                  </div>
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.ANALYSIS"
                        class="text-gray-600 fw-bold mt-1 fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment6
                      }}</span>
                    </div>
                  </div>
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.FUTURE_USE_1"
                        class="text-gray-600 mt-1 fw-bold fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment7
                      }}</span>
                    </div>
                  </div>
                  <div class="col-md-3 col-6 my-2">
                    <div class="card-title align-items-start flex-column">
                      <span
                        translate="FORM.LABEL.FUTURE_USE_2"
                        class="text-gray-600 mt-1 fw-bold fs-7 d-block"
                      ></span
                      ><span class="card-label fw-bold text-dark">{{
                        costCenterSplit.segment8
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Cost Center Amount Split END -->
</div>

<ng-template #loadingPage>
  <app-form-skeleton-loader></app-form-skeleton-loader>
</ng-template>

<ng-container
  *ngIf="
    supplimentalModalReady &&
    defaultValues?.parentAfeLatestBudgetBasedProjectSplit?.length &&
    newBudgetBasedProjectSplit?.length
  "
>
  <app-modal
    #supplimentalAmountBreakup
    [modalConfig]="supplimentalAmountBreakupConfig"
  >
    <app-supplimental-amount-breakup
      [prevBudgetBasedProjectSplit]="
        defaultValues.parentAfeLatestBudgetBasedProjectSplit
      "
      [newBudgetBasedProjectSplit]="newBudgetBasedProjectSplit"
    ></app-supplimental-amount-breakup>
  </app-modal>
</ng-container>
