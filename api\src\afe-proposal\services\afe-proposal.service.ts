import { Injectable } from '@nestjs/common';
import { DraftAfeRepository } from 'src/afe-draft/repositories/draft-afe-repository';
import { ConfigService } from 'src/config/config.service';
import { CurrencyConversionResponseDto } from 'src/finance/dtos';
import { FinanceService } from 'src/finance/services';
import { NotificationRepository } from 'src/notification/repositories';
import { QueueLogRepository } from 'src/queue/repositories';
import {
	AdminApiClient,
	AttachmentApiClient,
	HistoryApiClient,
	MSGraphApiClient,
	RequestApiClient,
	TaskApiClient,
} from 'src/shared/clients';
import {
	AFE_USER_STATUS,
	ATTACHMENT_REL_PATH,
	NOTIFICATION_TITLES,
	NOTIFICATION_URLS,
	SYSTEM_USER,
} from 'src/shared/constants';
import {
    AD_USER_TYPE,
    AFE_CATEGORY,
    AFE_LIMIT_DEDUCATION_STATUS,
    AFE_PROPOSAL_STATUS,
    AFE_PROPOSAL_STATUS_DISPLAY,
    AMOUNT_SPLIT,
    APPROVAL_TYPE,
    APPROVER_STATUS,
    ASSIGNED_TYPE,
    ASSOCIATED_COLUMN,
    ATTACHMENT_ENTITY_TYPE,
    BUDGET_TYPE,
    CURRENCY_TYPE,
    HISTORY_ACTION_TYPE,
    HISTORY_ENTITY_TYPE,
    HttpStatus,
    NOTIFICATION_ENTITY_TYPE,
    NOTIFICATION_TYPE,
    PERMISSIONS,
    QUEUE_LOG_ACTION,
    TASK_ACTION,
    TASK_ENTITY_TYPE,
    TOGGLE_ON_OFF,
} from 'src/shared/enums';
import { ASSOCIATED_TYPE } from 'src/shared/enums/associated-type.enum';
import { HttpException } from 'src/shared/exceptions';
import {
	DatabaseHelper,
	getNotificationExpiryDate,
	isSupplementalAfeByCategory,
	replaceUrlVariable,
	serializeBudgetBasedProjectAmountSplits,
	stringPlaceholderReplacer,
} from 'src/shared/helpers';
import {
	AFE_REQUEST_TYPE_ID_MAPPING,
	BUDGET_TYPE_ID_MAPPING,
	BUDGET_TYPE_MAPPING_WITH_ID,
	TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING,
} from 'src/shared/mappings';
import { SharedAttachmentService, SharedNotificationService } from 'src/shared/services';
import { AddHistoryRequest, ADUserDetails, CurrentContext, MoveAttachmentsRequest } from 'src/shared/types';
import { AfeProposalValidator } from 'src/shared/validators';
import { TaskService } from 'src/task/services';
import {
	WorkflowResponseDto,
	ComputeAfeApproversListDto,
	AfeApproversStepsResponseDto,
	MasterSettingWorkflowsResponseDto,
	ProjectComponentSplitDto,
} from 'src/workflow/dtos';
import { WorkflowService } from 'src/workflow/services';
import {
	AfeProposalApproverRequestDto,
	SubmitAfeProposalRequestDto,
	CreateAfeProposalResposeDto,
	ResubmitAfeProposalRequestDto,
	CreateAfeProposalDataDto,
	AnalysisCodeAmountSplitRequestDto,
	CostCenterAmountSplitRequestDto,
	BudgetTypeSplitRequestDto,
	NaturalAccountNumberSplitRequestDto,
	BudgetReferenceNumberSplitRequestDto,
	ChartOfAccountsRequestDto,
	SupplementalDataRequestDto,
	BudgetBasedProjectSplitDto,
	UpdateAfeDetailRequestDTO,
	WithdrawAfeProposalRequestDto,
	AddNewReadersRequestDto,
	SendBackAfeProposalRequestDto,
	ReopenAfeProposalRequestDto,
	UploadEvidenceRequestDto,
	UpdateApproverUserRequestDTO,
} from '../dtos';
import { AfeProposal, AfeProposalApprover } from '../models';
import {
	AfeProposalAmountSplitRepository,
	AfeProposalApproverRepository,
	AfeProposalLimitDeductionRepository,
	AfeProposalRepository,
} from '../repositories';
import { AdditionalCurrencyAmount, LimitDeductionAfeData, UserDetail } from '../types';
import { toNumber } from 'lodash';
import { TASK_ACTION_WITH_EMAIL_TEMPLATE } from 'src/task/mappings/task-action-with-email-template.mapping';

@Injectable()
export class AfeProposalService {
	constructor(
		private readonly afeProposalRepository: AfeProposalRepository,
		private readonly databaseHelper: DatabaseHelper,
		private readonly draftAfeRepository: DraftAfeRepository,
		private readonly workflowService: WorkflowService,
		private readonly afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository,
		private readonly afeProposalApproverRepository: AfeProposalApproverRepository,
		private readonly attachmentApiClient: AttachmentApiClient,
		private readonly adminApiClient: AdminApiClient,
		private readonly requestApiClient: RequestApiClient,
		private readonly configService: ConfigService,
		private readonly historyApiClient: HistoryApiClient,
		private readonly afeProposalLimitDeductionRepository: AfeProposalLimitDeductionRepository,
		private readonly taskService: TaskService,
		private readonly sharedAttachmentService: SharedAttachmentService,
		private readonly taskApiClient: TaskApiClient,
		private readonly financeService: FinanceService,
		private readonly notificationRepository: NotificationRepository,
		private readonly mSGraphApiClient: MSGraphApiClient,
		private readonly afeProposalValidator: AfeProposalValidator,
		private readonly queueLogRepository: QueueLogRepository,
		private readonly sharedNotificationService: SharedNotificationService,
	) { }

	/**
	 * Creating new AFE proposal
	 * @param createAfeProposalDto
	 * @returns
	 */
	public async submitAfeProposal(
		submitAfeProposalRequestDto: SubmitAfeProposalRequestDto,
		currentContext: CurrentContext,
	): Promise<CreateAfeProposalResposeDto> {
		const { draftId } = submitAfeProposalRequestDto;
		const {
			username: submitterId,
			given_name: firstName,
			family_name: lastName,
		} = currentContext.user;

		const afeDraft = await this.draftAfeRepository.getDraftById(draftId);
		if (!afeDraft) {
			throw new HttpException(`Afe draft doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (afeDraft.createdBy !== submitterId.toLowerCase()) {
			throw new HttpException(
				`You are not authorized to create AFE with this draft id.`,
				HttpStatus.FORBIDDEN,
			);
		}

		const { data: afeDraftData } = afeDraft;
		const {
			requestTypeId,
			businessEntity,
			readers,
			data,
			costCenterSplit,
			budgetType,
			totalAmount,
			projectComponentSplit,
			isSupplemental,
			supplementalData,
		} = afeDraftData;
		const { projectDetails, subType, type } = data;
		const year = afeDraftData.year || new Date().getFullYear();

		/**
		 * Validating supplemental data and finding parent afe id with max version.
		 */
		let parentAfeId: number = null;
		let version: number = 1;
		let lastSubmittedVersion: number = null;
		if (isSupplemental) {
			const { parentAfeProposalId } = await this.supplementalAfeValidation(
				supplementalData,
				businessEntity.id,
				afeDraftData,
			);

			parentAfeId = +parentAfeProposalId;
			lastSubmittedVersion = await this.afeProposalRepository.maxVersionOfSupplementalAfe(
				parentAfeProposalId,
			);
			lastSubmittedVersion = lastSubmittedVersion || 1;
			version = lastSubmittedVersion + 1;
		}

		/**
		 * Get currency detail for an entity.
		 */
		const currencyDetail = await this.financeService.getCurrencyTypeForEntity(businessEntity.id);
		await this.areCostCentersValid(businessEntity.id, costCenterSplit);

		//Step 2: Generate project reference number (RegionCode-BUCode-AFEType-SequenceNumber) for new AFE and for supplemental - (RegionCode-BUCode-AFEType-SequenceNumber/version)
		const projectReferenceNumber = isSupplemental
			? `${supplementalData.parentAfeNo.split('/')[0]}/${version}`
			: await this.createProjectReferenceNumber(requestTypeId, businessEntity.id, year);

		let subscribers: string[] = [];
		let afeProposal: AfeProposal;
		const submitterDetails = await this.mSGraphApiClient.getUserDetails(submitterId);
		await this.databaseHelper.startTransaction(async () => {
			subscribers = [
				...new Set([
					...(readers?.length ? readers.map(r => r.loginId.toLowerCase()) : []),
					projectDetails.projectLeader.loginId.toLocaleLowerCase(),
				]),
			];


			const response = await this.createAfeProposal(
				afeDraftData,
				projectReferenceNumber,
				currentContext,
				subscribers,
				submitterDetails,
				currencyDetail,
				parentAfeId,
				version,
			);
			afeProposal = response.afeProposal;

			/**
			 * Creating queue log entry.
			 */


			const entityParents = await this.adminApiClient.getParentsOfEntity(businessEntity.id);
			const logPayload = {
				entityId: businessEntity.id,
				action: QUEUE_LOG_ACTION.SUBMITTED,
				data: {
					proposalId: afeProposal.id,
					budgetType: budgetType ? BUDGET_TYPE_MAPPING_WITH_ID[budgetType.id] : null,
					totalAmount: totalAmount / currencyDetail.conversionRate,
					requestTypeId: requestTypeId,
					afeType: type,
					afeSubType: subType,
					projectComponentId: projectComponentSplit?.map(p => p.id),
					costCenterId: costCenterSplit?.map(c => c.id),
					approversLevel: [...new Set(response.steps.map(step => step.associateLevel))].filter(
						a => !!a,
					),
					businessUnitHierarchy: entityParents.map(entity => ({
						id: entity.id,
						code: entity.code,
						level: entity.entity_type,
					})),
					status: QUEUE_LOG_ACTION.SUBMITTED,
				},
			};


			await this.queueLogRepository.createQueueLogEntry(logPayload, currentContext);

			/**
			 * Step 3: Move the draft attachments to proposal attachments.
			 */
			//TODO: Will go to Queue implementation and excute async.


			await this.moveAttachmentsFromSourceToDestination(
				ATTACHMENT_ENTITY_TYPE.AFE_DRAFT,
				draftId,
				ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT,
				afeProposal.id,
			);

			/**
			 * Step 4: Generation of tasks and send notification of tasks. (use lazy approval)
			 */
			//TODO: Will go to Queue implementation and excute async.


			await this.taskService.createNextTasks(afeProposal.id, TASK_ACTION.APPROVE, currentContext);

			//TODO: Will go to Queue implementation and excute async.
			/**
			 * Step 5: Notification of AFE submission (send this notification to readers, project leader, and submitter).
			 */

			await this.sendAfeCreationNotificationToSubmitter(
				afeProposal,
				submitterDetails.mail,
				isSupplemental ? 'AFE.SUPPLEMENTAL.SUBMISSION.SUBMITTER' : 'AFE.SUBMISSION.SUBMITTER',
			);



			await this.sendAfeCreationNotificationToOthers(
				afeProposal,
				isSupplemental ? 'AFE.SUPPLEMENTAL.SUBMISSION.OTHER' : 'AFE.SUBMISSION.OTHER',
			);

			/**
			 * Step 6: Delete the draft record.
			 */
			await this.draftAfeRepository.deleteDraftById(draftId, currentContext);

			//TODO: Will go to Queue implementation and excute async.
			/**
			 * Step 7: History API
			 */
			const addHistoryPayload: AddHistoryRequest = {
				created_by: submitterId,
				entity_id: afeProposal.id,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.SUBMITTED,
			};
			await this.historyApiClient.addRequestHistory(addHistoryPayload);


			//Step 8: Create notifications for subscribers and submitter.
			await this.createNotificationOnAfeSubmission(
				{ id: afeProposal.id, afeReferenceNumber: projectReferenceNumber },
				{ id: submitterId, name: `${firstName} ${lastName}` },
				subscribers,
				currentContext,
			);

		});

		return { projectReferenceNumber };
	}

	/**
	 * Resubmit the afe proposal and create the new version of the original AFE.
	 * @param resubmitAfeProposalRequestDto
	 * @param currentContext
	 * @returns
	 */
	public async resubmitAfeProposal(
		resubmitAfeProposalRequestDto: ResubmitAfeProposalRequestDto,
		currentContext: CurrentContext,
	): Promise<CreateAfeProposalResposeDto> {
		const {
			id: originalAfeProposalId,
			supportingDocuments,
			businessEntity,
			taskId,
			isSupplemental,
			readers,
			data,
			costCenterSplit,
			budgetType,
			totalAmount,
			requestTypeId,
			projectComponentSplit,
			deletedSupportingDocuments,
		} = resubmitAfeProposalRequestDto;
		const { projectDetails, type, subType } = data;
		const {
			username: submitterId,
			given_name: firstName,
			family_name: lastName,
		} = currentContext.user;

		//Step 1: Validating if original afe exists in the system or not.
		const originalAfeProposal = await this.afeProposalRepository.getAfeProposalById(
			originalAfeProposalId,
		);
		if (!originalAfeProposal) {
			throw new HttpException(`Afe original afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		await this.areCostCentersValid(businessEntity.id, costCenterSplit);

		//Step 2: Validating if user has rights or can resubmit the AFE proposal.
		const {
			submitterId: originalAfeCreatorId,
			projectReferenceNumber,
			version: originalAfeVersion,
			internalStatus,
			entityId,
			category,
			subscribers: originalAfeSubscribers,
			parentAfeId,
			createdOn,
		} = originalAfeProposal;

		if (businessEntity.id !== entityId) {
			throw new HttpException(
				`Business entity can't be different from original AFE.`,
				HttpStatus.FORBIDDEN,
			);
		}

		if (originalAfeCreatorId.toLowerCase() !== submitterId.toLowerCase() && !taskId) {
			throw new HttpException(
				`You are not authorized to resubmit this AFE proposal.`,
				HttpStatus.FORBIDDEN,
			);
		}

		if (isSupplementalAfeByCategory(category) !== isSupplemental) {
			throw new HttpException(
				`You can't resubmit supplemental afe as new afe and vice-versa.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		if (internalStatus !== AFE_PROPOSAL_STATUS.SENT_BACK) {
			throw new HttpException(
				`Can't resubmit the AFE proposal which is not in sent back status.`,
				HttpStatus.FORBIDDEN,
			);
		}

		let afeProposal: AfeProposal;
		let originalApproverDetail: UserDetail;
		const approverIdOfSendBackInProgressStep =
			await this.afeProposalApproverRepository.getInprogressApproverByProposalId(
				originalAfeProposalId,
			);
		if (taskId && taskId !== approverIdOfSendBackInProgressStep?.id) {
			const task = await this.taskApiClient.getTaskById(taskId);

			if (
				task.entity_id !== approverIdOfSendBackInProgressStep?.id ||
				task?.task_status !== 'Not Started'
			) {
				throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
			}

			if (task.original_owner && task.delegated_task_type === 'representative') {
				const {
					givenName: firstName,
					surname: lastName,
					userPrincipalName: upn,
					userType,
					mail: email,
					jobTitle: title,
				} = await this.mSGraphApiClient.getUserDetails(task.original_owner);
				const loginId = userType == AD_USER_TYPE.GUEST ? email.toLowerCase() : upn.toLowerCase();
				originalApproverDetail = { firstName, lastName, loginId, email, title };
			}
		}

		const submitterDetails = await this.mSGraphApiClient.getUserDetails(submitterId);
		const currencyDetail = await this.financeService.getCurrencyTypeForEntity(businessEntity.id);
		let subscribers: string[] = [];

		await this.databaseHelper.startTransaction(async () => {
			//Step 4: Mark the current task approver status as resubmitted.
			await this.afeProposalApproverRepository.updateApproverStatusOnAction(
				approverIdOfSendBackInProgressStep.id,
				APPROVER_STATUS.RESUBMITTED,
				currentContext,
				originalApproverDetail,
			);

			//Step 5: Create the afe proposal.
			subscribers = [
				...new Set([
					...(readers?.length ? readers.map(r => r.loginId.toLowerCase()) : []),
					projectDetails.projectLeader.loginId.toLocaleLowerCase(),
					...(originalAfeSubscribers || []),
				]),
			];
			const response = await this.createAfeProposal(
				resubmitAfeProposalRequestDto,
				projectReferenceNumber,
				currentContext,
				subscribers,
				submitterDetails,
				currencyDetail,
				parentAfeId,
				originalAfeVersion,
				createdOn,
			);

			afeProposal = response.afeProposal;

			//Step 6: delete the original afe proposal and cancelled the limit deduction entries.
			await this.afeProposalRepository.deleteAfeProposalById(originalAfeProposalId, currentContext);
			await this.afeProposalLimitDeductionRepository.cancelLimitDeductionsByAfeId(
				originalAfeProposalId,
				currentContext,
			);

			/**
			 * Creating queue log entry.
			 */
			const entityParents = await this.adminApiClient.getParentsOfEntity(entityId);
			const logPayload = {
				entityId: businessEntity.id,
				action: QUEUE_LOG_ACTION.SUBMITTED,
				data: {
					proposalId: afeProposal.id,
					budgetType: budgetType ? BUDGET_TYPE_MAPPING_WITH_ID[budgetType.id] : null,
					totalAmount: totalAmount / currencyDetail.conversionRate,
					requestTypeId: requestTypeId,
					afeType: type,
					afeSubType: subType,
					projectComponentId: projectComponentSplit?.map(p => p.id),
					costCenterId: costCenterSplit?.map(c => c.id),
					approversLevel: [...new Set(response.steps.map(step => step?.associateLevel))].filter(
						a => !!a,
					),
					businessUnitHierarchy: entityParents.map(entity => ({
						id: entity.id,
						code: entity.code,
						level: entity.entity_type,
					})),
					status: QUEUE_LOG_ACTION.SUBMITTED,
				},
			};
			await this.queueLogRepository.createQueueLogEntry(logPayload, currentContext);

			/**
			 * Step 7: Upload the new afe attachments.
			 */
			//TODO: Will go to Queue implementation and excute async.
			if (deletedSupportingDocuments?.length) {
				await this.sharedAttachmentService.deleteBulkAttachment(deletedSupportingDocuments);
			}
			if (supportingDocuments?.length) {
				await this.sharedAttachmentService.supportingDocumentsActivity(
					supportingDocuments,
					+afeProposal.id,
					ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT,
					ATTACHMENT_REL_PATH.AFE_DRAFT,
					currentContext.user.username,
					afeProposal.id,
				);
				await this.moveAttachmentsFromSourceToDestination(
					ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT,
					originalAfeProposal.id,
					ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT,
					afeProposal.id,
				);
			}

			/**
			 * Step 8: Copy history data of original afe to new resubmitted AFE.
			 */
			await this.copyHistoryFromOriginalAfeToResubmitAfe(originalAfeProposalId, afeProposal.id);

			//TODO: Will go to Queue implementation and excute async.
			/**
			 * Step 9: Add history for resubmit
			 */
			const addHistoryPayload: AddHistoryRequest = {
				created_by: submitterId,
				entity_id: afeProposal.id,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.RESUBMITTED,
			};
			if (originalApproverDetail) {
				await Promise.all([
					this.historyApiClient.addRequestHistory({
						...addHistoryPayload,
						created_by: originalApproverDetail.loginId,
					}),
					this.historyApiClient.addRequestHistory({
						...addHistoryPayload,
						created_by: submitterId,
						additional_info: { hidden: true },
					}),
				]);
			} else {
				await this.historyApiClient.addRequestHistory(addHistoryPayload);
			}

			/**
			 * Step 10: Mark the current resubmit task to completed.
			 */
			await this.taskApiClient.completeAllTasks({
				entity_id: approverIdOfSendBackInProgressStep.id,
				entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
				outcome: TASK_ACTION.RESUBMIT,
			});

			/**
			 * Step 11: Generation of tasks and send notification of tasks. (use lazy approval)
			 */
			//TODO: Will go to Queue implementation and excute async.
			await this.taskService.createNextTasks(afeProposal.id, TASK_ACTION.APPROVE, currentContext);

			// //TODO: Will go to Queue implementation and excute async.
			/**
			 * Step 12: Notification of AFE submission (send this notification to readers, project leader, and submitter).
			 */
			await this.sendAfeCreationNotificationToSubmitter(
				afeProposal,
				submitterDetails.mail,
				'AFE.TASK.APPROVAL.RESUBMITTED',
			);
			await this.sendAfeCreationNotificationToOthers(afeProposal, 'AFE.TASK.APPROVAL.RESUBMITTED');

			//Step 13: Create notifications for subscribers and submitter.
			await this.createNotificationOnAfeResubmission(
				{ id: afeProposal.id, afeReferenceNumber: projectReferenceNumber },
				{ id: submitterId, name: `${firstName} ${lastName}` },
				subscribers,
				currentContext,
			);
		});

		return { projectReferenceNumber };
	}

	/**
	 * Check if all the cost centers in the splits are valid.
	 * @param businessEntityId
	 * @param costCenterSplit
	 */
	private async areCostCentersValid(
		businessEntityId: number,
		costCenterSplit: CostCenterAmountSplitRequestDto[],
	) {
		if (costCenterSplit?.length && costCenterSplit[costCenterSplit.length - 1].id) {
			const allCostCenterIds = costCenterSplit.map(costCenter => costCenter.id);
			const costCenterIds = [...new Set(allCostCenterIds)];

			const doAllCostCenterExist =
				await this.afeProposalValidator.checkAllCostCentersBelongsToBusinessEntity(
					businessEntityId,
					costCenterIds,
				);
			if (!doAllCostCenterExist) {
				throw new HttpException(`Invalid cost centers.`, HttpStatus.BAD_REQUEST);
			}
		}
	}

	/**
	 * Check if we are allowed to create the supplemental AFE for the parent AFE.
	 * @param supplementalData
	 * @returns
	 */
	private async supplementalAfeValidation(
		supplementalData: SupplementalDataRequestDto,
		supplementalEntityId: number,
		afeDraftData,
	): Promise<{ parentAfeProposalId: number }> {
		if (!supplementalData) {
			throw new HttpException('Supplemental data is missing.', HttpStatus.BAD_REQUEST);
		}

		const { parentAfeNo: parentAfeProjectReferenceNumber } = supplementalData;

		if (!parentAfeProjectReferenceNumber) {
			throw new HttpException(
				'Parent afe proposal reference number is missing',
				HttpStatus.BAD_REQUEST,
			);
		}

		const parentAfeProposal = await this.afeProposalRepository.getAfeProposalByReferenceNumber(
			parentAfeProjectReferenceNumber,
		);
		if (!parentAfeProposal) {
			throw new HttpException(`Parent Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (parentAfeProposal.entityId !== supplementalEntityId) {
			throw new HttpException(
				`Supplemental business entity should be same with parent business entity. `,
				HttpStatus.BAD_REQUEST,
			);
		}

		if (parentAfeProposal.category === AFE_CATEGORY.SUPPLEMENTAL) {
			throw new HttpException(
				`Supplemental AFE can't be parent of another supplemental.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		if (parentAfeProposal.internalStatus !== AFE_PROPOSAL_STATUS.APPROVED) {
			throw new HttpException(`Can't raise supplemental of unapproved AFE`, HttpStatus.BAD_REQUEST);
		}

		const inProgressSupplementalAfe = await this.afeProposalRepository.inProgressAfeByParentAfeId(
			parentAfeProposal.id,
		);
		if (
			inProgressSupplementalAfe &&
			inProgressSupplementalAfe.internalStatus !== AFE_PROPOSAL_STATUS.SENT_BACK
		) {
			throw new HttpException(
				`Other supplemental is in progress for the AFE.`,
				HttpStatus.CONFLICT,
			);
		}

		const { requestTypeId } = afeDraftData;

		if ([6, 7].includes(requestTypeId)) {
			const { totalAmount: currentTotalAmount, totalMarketValue: currentTotalMarketValue } =
				afeDraftData;
			const { totalAmount: parentTotalAmount, marketValue: parentMarketValue } = parentAfeProposal;

			const parentDifference =
				toNumber(parentMarketValue) - toNumber(parentTotalAmount) <= 0
					? 'LESS_THAN_EQUAL_ZERO'
					: 'GREATER_THAN_ZERO';

			const supplementalDifference =
				toNumber(currentTotalMarketValue) - toNumber(currentTotalAmount) <= 0
					? 'LESS_THAN_EQUAL_ZERO'
					: 'GREATER_THAN_ZERO';

			if (parentDifference !== supplementalDifference) {
				const message = `Total Market Value must be ${
					parentDifference === 'GREATER_THAN_ZERO' ? 'greater than' : 'less than or equal to'
					} Net Book Value.`;

				throw new HttpException(message, HttpStatus.BAD_REQUEST);
			}
		}

		return { parentAfeProposalId: parentAfeProposal.id };
	}

	/**
	 * Creating new AFE proposal
	 * @param createAfeProposalDto
	 * @returns
	 */
	public async createAfeProposal(
		createAfeProposalDto: CreateAfeProposalDataDto,
		projectReferenceNumber: string,
		currentContext: CurrentContext,
		subscribers: string[],
		submitterInfo: ADUserDetails,
		currencyDetail: CurrencyConversionResponseDto,
		parentAfeId: number = null,
		version: number = 1,
		createdOn: Date = null,
	): Promise<{ afeProposal: AfeProposal; steps: AfeApproversStepsResponseDto[] }> {
		const { username: submitterId } = currentContext.user;
		let {
			projectComponentSplit,
			costCenterSplit,
			analysisCodeSplit,
			naturalAccountNumberSplit,
			budgetBasedProjectSplit,
			chartOfAccounts,
			totalAmount,
			requestTypeId,
			budgetTypeSplit,
			budgetType,
			isApprovedByBoard,
			isNewFfoSetting,
			data,
			isSupplemental,
			businessEntity,
			questionAnswers,
			readers,
			approverList,
			entityId,
			supplementalData,
			lengthOfCommitment,
			totalMarketValue: marketValue,
			year: workflowYear,
			location,
		} = createAfeProposalDto;
		const { currency, primaryCurrency, conversionRate } = currencyDetail;

		/**
		 * Convert market value to primary currency if exists and do validation.
		 */
		let marketValueAdditionalCurrencyAmount: AdditionalCurrencyAmount = null;

		if (marketValue !== null && totalAmount !== undefined) {
			// if (
			// 	(marketValue > totalAmount &&
			// 		AFE_REQUEST_TYPE_ID_WITH_NAME_MAPPING[requestTypeId] !==
			// 		AFE_REQUEST_TYPE.SALE_WRITE_OFF_MV_MORE_NBV) ||
			// 	(marketValue <= totalAmount &&
			// 		AFE_REQUEST_TYPE_ID_WITH_NAME_MAPPING[requestTypeId] !==
			// 		AFE_REQUEST_TYPE.SALE_WRITE_OFF_MV_LESS_EQUAL_NBV)
			// ) {
			// 	throw new HttpException(`Invalid afe request type id.`, HttpStatus.BAD_REQUEST);
			// }

			/**
			 * In addition currency we store the user input amount value in whichever currency type
			 */
			marketValueAdditionalCurrencyAmount = {
				amount: marketValue,
				currency: currency,
				exchangeRateToPrimary: conversionRate,
			};
		}

		/**
		 * In addition currency we store the user input amount value in whichever currency type
		 */
		const additionalCurrencyAmount: AdditionalCurrencyAmount = {
			amount: totalAmount,
			currency: currencyDetail.currency,
			exchangeRateToPrimary: conversionRate,
		};
		const { projectDetails } = data;
		const { projectLeader } = projectDetails;
		projectLeader.loginId = projectLeader.loginId.toLowerCase();

		const {
			givenName: firstName,
			surname: lastName,
			mail: email,
			userPrincipalName,
			userType,
			jobTitle: title,
		} = submitterInfo;
		const submitterDetails: UserDetail = {
			firstName,
			lastName,
			email,
			loginId:
				userType === AD_USER_TYPE.GUEST ? email.toLowerCase() : userPrincipalName.toLowerCase(),
			title,
		};

		let afeProposalDetails: any = {
			name: data.projectDetails.projectName,
			category: isSupplemental ? AFE_CATEGORY.SUPPLEMENTAL : AFE_CATEGORY.NEW,
			afeRequestTypeId: requestTypeId,
			submitterId: submitterId,
			isNewVersionInProgress: true,
			isApprovedByBoard: isApprovedByBoard,
			budgetType: budgetType?.id ? BUDGET_TYPE_ID_MAPPING[budgetType.id] : null,
			currencyType: primaryCurrency,
			totalAmount: totalAmount / conversionRate,
			additionalCurrencyAmount: additionalCurrencyAmount,
			marketValue:
				marketValue !== null && marketValue !== undefined ? marketValue / conversionRate : null,
			marketValueCurrency:
				marketValue !== null && marketValue !== undefined ? primaryCurrency : null,
			marketValueAdditionalCurrencyAmount,
			entityId: entityId,
			entityCode: businessEntity.code,
			entityTitle: businessEntity.name,
			projectReferenceNumber: projectReferenceNumber,
			data: { ...data, submitterDetails },
			globalProcurementQuesAns: questionAnswers,
			readers: readers?.map(reader => ({ ...reader, loginId: reader.loginId.toLowerCase() })),
			yearOfCommitment: lengthOfCommitment,
			version: version,
			workflowYear: workflowYear || new Date().getFullYear(),
			...(isSupplemental && {
				supplementalData: supplementalData,
			}),
			subscribers,
			parentAfeId,
			isNewFfoSetting,
			locationId: location?.id || null,
		};

		if (createdOn) {
			afeProposalDetails = {
				...afeProposalDetails,
				createdOn,
			};
		}

		/**
		 * Step 1: Compute the latest and updated and latest list of approvers and then
		 * check if there is any conflicts with the saved darft AFE approvers list.
		 */
		const { steps, masterSettingWorkflows, isProjectLeaderExist } = await this.computeLastestApproversList(
			{ ...createAfeProposalDto, parentAfeId: afeProposalDetails.parentAfeId },
			submitterDetails.loginId,
		);

		if(isProjectLeaderExist && steps.length === 1) {
			throw new HttpException(`Approval workflow does't exist.`, HttpStatus.BAD_REQUEST);
		}

		if (this.isConflictExistInWorkflowSteps(steps, approverList)) {
			throw new HttpException(`Conflict in the workflow steps.`, HttpStatus.CONFLICT);
		}

		/**
		 * Step 2: Store afe proposal data in afe proposal table.
		 */
		const afeProposal = await this.afeProposalRepository.createAfeProposal(
			afeProposalDetails,
			currentContext,
		);

		const { id: afeProposalId } = afeProposal;

		/**
		 * Step 3: Create cost split payloads and store in cost split table.
		 */
		const afeAmountSplits = [];
		if (projectComponentSplit?.length) {
			const projectComponentSplits = this.createCostSplitPayload(
				AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT,
				projectComponentSplit,
				afeProposalId,
				primaryCurrency,
				conversionRate,
			);
			afeAmountSplits.push(...projectComponentSplits);

			if (!budgetBasedProjectSplit?.length && afeProposalDetails.budgetType !== BUDGET_TYPE.MIXED) {
				const projectAndBudgetTypeSplits = this.createCostSplitPayload(
					AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT,
					projectComponentSplit.map(p => ({
						id: p.id,
						title: p.title,
						totalAmount: p.amount,
						currency: p.currency,
						budgetedAmount: afeProposalDetails.budgetType === BUDGET_TYPE.BUDGETED ? p.amount : 0,
						unbudgetedAmount:
							afeProposalDetails.budgetType === BUDGET_TYPE.UNBUDGETED ? p.amount : 0,
					})),
					afeProposalId,
					primaryCurrency,
					conversionRate,
				);
				afeAmountSplits.push(...projectAndBudgetTypeSplits);
			}
		}

		if (budgetBasedProjectSplit?.length) {
			const splits = this.createCostSplitPayload(
				AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT,
				budgetBasedProjectSplit,
				afeProposalId,
				primaryCurrency,
				conversionRate,
			);
			afeAmountSplits.push(...splits);
		}

		if (budgetTypeSplit?.length) {
			const splits = this.createCostSplitPayload(
				AMOUNT_SPLIT.BUDGET_TYPE_SPLIT,
				budgetTypeSplit,
				afeProposalId,
				primaryCurrency,
				conversionRate,
			);
			afeAmountSplits.push(...splits);
		}

		if (naturalAccountNumberSplit?.length) {
			const splits = this.createCostSplitPayload(
				AMOUNT_SPLIT.NATURAL_ACCOUNT_SPLIT,
				naturalAccountNumberSplit,
				afeProposalId,
				primaryCurrency,
				conversionRate,
			);
			afeAmountSplits.push(...splits);
		}

		if (analysisCodeSplit?.length) {
			const splits = this.createCostSplitPayload(
				AMOUNT_SPLIT.ANALYSIS_CODE_SPLIT,
				analysisCodeSplit,
				afeProposalId,
				primaryCurrency,
				conversionRate,
			);
			afeAmountSplits.push(...splits);
		}

		if (costCenterSplit?.length) {
			const splits = this.createCostSplitPayload(
				AMOUNT_SPLIT.COST_CENTER_SPLIT,
				costCenterSplit,
				afeProposalId,
				primaryCurrency,
				conversionRate,
			);
			afeAmountSplits.push(...splits);
		}

		if (chartOfAccounts?.length) {
			const splits = this.createCostSplitPayload(
				AMOUNT_SPLIT.GL_CODE_SPLIT,
				chartOfAccounts,
				afeProposalId,
				primaryCurrency,
				conversionRate,
			);
			afeAmountSplits.push(...splits);
		}

		console.dir(afeAmountSplits, { depth: 10 });

		//Storing all the amount splits in the database table
		await this.afeProposalAmountSplitRepository.bulkAmountSplitsInsert(
			afeAmountSplits,
			currentContext,
		);


		/**
		 * Step 4: Store approver lists in the database.
		 */

		await this.saveAfeApproversList(approverList, afeProposalId, currentContext);

		/**
		 * Step 5: Create limit deducation entries.
		 */

		if (masterSettingWorkflows?.length) {
			await this.createLimitDeductionEntries(afeProposal, masterSettingWorkflows, currentContext);
		}

		/**
		 * Store Delta amounts
		 */


		if (isSupplemental) {
			const lastApprovedSupplementalAfe =
				await this.afeProposalRepository.getLastApprovedAfeByParentId(parentAfeId);
			const supplementalDeltaAmounts = await this.calculateDeltaAmounts(
				lastApprovedSupplementalAfe,
				afeProposal,
			);
			this.afeProposalRepository.updateAfeProposalById(afeProposal.id, currentContext, {
				supplementalDeltaAmounts,
			});

			const isPreviousSupplementalOrParentAfeApprovedByBoard =
				await this.afeProposalRepository.isAnyPreviousSupplementalOrParentAfeApprovedByBoard(
					parentAfeId,
				);

			if (
				masterSettingWorkflows.some(entry => entry.amount < 0) &&
				!isApprovedByBoard &&
				isPreviousSupplementalOrParentAfeApprovedByBoard &&
				isSupplemental
			) {
				const businessHierarchy = await this.adminApiClient.getAllBusinessHierarchy();
				const itSupportEmails = await this.getUsersEmailByRoleAndEntityId(
					'ITAdmin',
					businessHierarchy.id,
				);
				await this.sharedNotificationService.sendNotificationForAfeProposal(
					afeProposalId,
					afeProposalId,
					NOTIFICATION_ENTITY_TYPE.FUSION_NOTIFICATION,
					{ to: [...itSupportEmails] },
					'AFE.EMAIL.BOARDSECRETARY.DEDUCTION.ISSUE',
					false,
				);
			}
		}

		return { afeProposal, steps };
	}

	private async getUsersEmailByRoleAndEntityId(role: string, entityId: number): Promise<string[]> {
		const userEmails: string[] = [];
		const users = await this.adminApiClient.getUsersByRoleOfAnEntity(role, entityId);
		const userIds = users?.map(user => user.user_name.toLowerCase());
		const userAdDetails = userIds.length
			? await this.mSGraphApiClient.getUsersDetails(userIds)
			: [];
		userEmails.push(...userAdDetails.map(user => user.mail));
		return userEmails;
	}

	/**
	 * Create notifications on new AFE Submission.
	 * @param afeDetails
	 * @param submitterDetails
	 * @param subscribers
	 * @param currentContext
	 */
	private async createNotificationOnAfeSubmission(
		afeDetails: { id: number; afeReferenceNumber: string },
		submitterDetails: { id: string; name: string },
		subscribers: string[],
		currentContext: CurrentContext,
	) {
		const url = `${stringPlaceholderReplacer(NOTIFICATION_URLS.AFE_DETAILS_URL, {
			afeId: `${afeDetails.id}`,
		})}`;
		const expireAt = getNotificationExpiryDate();
		const type = NOTIFICATION_TYPE.INFO;
		const submitterNotification = {
			title: stringPlaceholderReplacer(NOTIFICATION_TITLES.AFE_SUBMIT.SUBMITTER, {
				afeReferenceNumber: afeDetails.afeReferenceNumber,
			}),
			url: url,
			subscribers: [submitterDetails.id],
			expireAt,
			type,
		};

		const subscribersNotification = {
			title: stringPlaceholderReplacer(NOTIFICATION_TITLES.AFE_SUBMIT.SUBSCRIBER, {
				afeReferenceNumber: afeDetails.afeReferenceNumber,
				submitterName: submitterDetails.name,
			}),
			url: url,
			subscribers,
			expireAt,
			type,
		};
		await this.notificationRepository.bulkNotificationsInsert(
			[submitterNotification, subscribersNotification],
			currentContext,
		);
	}

	/**
	 * Create notifications on AFE resubmission.
	 * @param afeDetails
	 * @param submitterDetails
	 * @param subscribers
	 * @param currentContext
	 */
	private async createNotificationOnAfeResubmission(
		newAfeDetails: { id: number; afeReferenceNumber: string },
		submitterDetails: { id: string; name: string },
		subscribers: string[],
		currentContext: CurrentContext,
	) {
		const { afeReferenceNumber, id } = newAfeDetails;
		const url = `${stringPlaceholderReplacer(NOTIFICATION_URLS.AFE_DETAILS_URL, {
			afeId: `${id}`,
		})}`;
		const expireAt = getNotificationExpiryDate();
		const type = NOTIFICATION_TYPE.INFO;

		const submitterNotification = {
			title: stringPlaceholderReplacer(NOTIFICATION_TITLES.AFE_RE_SUBMIT.SUBMITTER, {
				afeReferenceNumber,
			}),
			url: url,
			subscribers: [submitterDetails.id],
			expireAt,
			type,
		};

		const subscribersNotification = {
			title: stringPlaceholderReplacer(NOTIFICATION_TITLES.AFE_RE_SUBMIT.SUBSCRIBER, {
				afeReferenceNumber,
				submitterName: submitterDetails.name,
			}),
			url: url,
			subscribers,
			expireAt,
			type,
		};
		await this.notificationRepository.bulkNotificationsInsert(
			[submitterNotification, subscribersNotification],
			currentContext,
		);
	}

	/**
	 * Create limit deduction entries for the afe.
	 * @param afeProposalId
	 * @param workflows
	 * @param currentContext
	 */
	private async createLimitDeductionEntries(
		afeProposal: AfeProposal,
		workflows: MasterSettingWorkflowsResponseDto[],
		currentContext: CurrentContext,
	): Promise<void> {
		const limitDeductionAfeData: LimitDeductionAfeData = {
			afeReferenceNumber: afeProposal.projectReferenceNumber,
			projectName: afeProposal.name,
			requestTypeId: afeProposal.afeRequestTypeId,
			isSupplemental: afeProposal.category === AFE_CATEGORY.SUPPLEMENTAL,
			submitterId: afeProposal.submitterId,
			budgetType: afeProposal.budgetType || null,
			year: afeProposal.workflowYear,
			isApprovedByBoard: afeProposal.isApprovedByBoard,
			parentAfeId: afeProposal.parentAfeId || null,
		};
		const entries = workflows.map(workflow => ({
			afeProposalId: afeProposal.id,
			entityId: workflow?.associatedEntityId || null,
			entityTitle: workflow?.associatedEntityTitle || null,
			entityCode: workflow?.associatedEntityCode || null,
			costCenterId: workflow?.costCenterId || null,
			workflowMasterSettingId: workflow.workflowMasterSettingId,
			workflowMasterStepId: workflow.deductionStepId,
			amount: workflow.amount,
			status: AFE_LIMIT_DEDUCATION_STATUS.IN_PROGRESS,
			data: limitDeductionAfeData,
		}));

		const deductions =
			await this.afeProposalLimitDeductionRepository.bulkInsertAfeProposalLimitDeductions(
				entries,
				currentContext,
			);

		for (const deduction of deductions) {
			const workflowDeduction = workflows.find(
				workflow => workflow.deductionStepId === deduction.workflowMasterStepId,
			);
			if (workflowDeduction?.parentDeductionStepId) {
				const parentDeduction = deductions.find(
					d =>
						d.workflowMasterStepId === workflowDeduction.parentDeductionStepId &&
						d.amount === workflowDeduction.amount,
				);
				if (parentDeduction) {
					await this.afeProposalLimitDeductionRepository.updateParentDeductionId(
						deduction.id,
						parentDeduction.id,
						currentContext,
					);
				}
			}
		}
	}

	/**
	 * Compute the latest or updated list of approvers for the AFE.
	 * @param afeDraftData
	 * @returns
	 */
	private async computeLastestApproversList(
		afeData: CreateAfeProposalDataDto,
		submitterId: string,
	): Promise<WorkflowResponseDto> {
		const {
			projectComponentSplit,
			costCenterSplit,
			budgetBasedProjectSplit,
			totalAmount,
			requestTypeId,
			budgetTypeSplit,
			budgetType,
			isApprovedByBoard,
			businessEntity,
			lengthOfCommitment,
			currencyDetail,
			year: workflowYear,
			data,
			parentAfeId,
			isSupplemental,
		} = afeData;

		const computeApproversListPayload: ComputeAfeApproversListDto = {
			requestTypeId: requestTypeId,
			budgetType: budgetType?.title || null,
			entityId: businessEntity.id,
			totalAmount: totalAmount,
			isApprovedByBoard: isApprovedByBoard,
			currency: currencyDetail.currency,
			year: workflowYear,
			lengthOfCommitment: lengthOfCommitment,
			afeSubType: data?.subType || null,
			afeType: data?.type || null,
			parentAfeId: parentAfeId || null,
			isSupplemental: isSupplemental || false,
			projectLeaderId: data?.projectDetails?.projectLeader?.loginId || null,
		};

		if (projectComponentSplit?.length) {
			computeApproversListPayload.projectComponentSplits = projectComponentSplit.map(split => ({
				id: split.id,
				title: split.title,
				amount: split.amount,
				currency: split.currency,
			}));
		}

		if (
			budgetBasedProjectSplit?.length &&
			budgetType.id === BUDGET_TYPE_MAPPING_WITH_ID[BUDGET_TYPE.MIXED]
		) {
			computeApproversListPayload.budgetBasedProjectSplit = budgetBasedProjectSplit.map(split => ({
				id: split.id,
				title: split.title,
				totalAmount: split.totalAmount,
				currency: split.currency,
				unbudgetedAmount: split.unbudgetedAmount,
				budgetedAmount: split.budgetedAmount,
			}));
		}

		if (budgetTypeSplit?.length) {
			computeApproversListPayload.budgetTypeSplits = budgetTypeSplit.map(split => ({
				id: split.id,
				title: split.title,
				amount: split.amount,
				currency: split.currency,
			}));
		}

		if (costCenterSplit?.length && costCenterSplit[costCenterSplit.length - 1].id) {
			computeApproversListPayload.costCenters = costCenterSplit
				.map(split => ({
					id: split.id,
					code: split.code,
					amount: split.budgetReferenceNumberSplit.reduce(
						(accumulator, currentValue) => accumulator + currentValue.amount,
						0,
					),
					section: split.section,
				}))
				.filter(split => split.id !== 0);
		}

		//Get the lastest or updated list of approvers
		return this.workflowService.getAfeApproversList(computeApproversListPayload, submitterId);
	}

	/**
	 * Check if there is any conflict in
	 * @param computedApprovers
	 * @param savedApprovers
	 * @returns
	 */
	private isConflictExistInWorkflowSteps(
		computedApprovers: AfeApproversStepsResponseDto[],
		savedApprovers: AfeProposalApproverRequestDto[],
	): boolean {
		const computedStepsInSavedListCount = savedApprovers.reduce(
			(count, appover) => (count = appover.isCustomUser ? count : count + 1),
			0,
		);

		if (computedStepsInSavedListCount !== computedApprovers.length) {
			return true;
		} else {
			let i = 0,
				j = 0;
			while (i < savedApprovers.length) {
				if (!savedApprovers[i].isCustomUser) {
					const draftWorkflowStepKey = this.createStepUniqueKey(
						savedApprovers[i].associateLevel,
						savedApprovers[i].associateRole,
						savedApprovers[i].associateType,
						savedApprovers[i].associatedColumn,
					);

					const computedWorkflowStepKey = this.createStepUniqueKey(
						computedApprovers[j].associateLevel,
						computedApprovers[j].associateRole,
						computedApprovers[j].associateType,
						computedApprovers[j].associatedColumn,
					);

					if (draftWorkflowStepKey !== computedWorkflowStepKey) {
						return true;
					}
					j += 1;
				}
				i += 1;
			}
		}
		return false;
	}

	/**
	 * Create project reference number for AFE proposal
	 * @param requestTypeId
	 * @param entityId
	 * @param entityCode
	 * @returns
	 */
	private async createProjectReferenceNumber(
		requestTypeId: number,
		entityId: number,
		year: number,
	): Promise<string> {
		const { businessEntityLevelForProjectReferenceNumber } = this.configService.getAppConfig();

		const { other_info: regionOtherInfo } =
			await this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(
				entityId,
				businessEntityLevelForProjectReferenceNumber,
			);

		const { other_info: businessUnitOtherInfo } =
			await this.adminApiClient.getBusinessEntityDetailsById(entityId);

		const afeRequestTypeCode = AFE_REQUEST_TYPE_ID_MAPPING[requestTypeId];
		const sequenceNumber = await this.requestApiClient.generateNextSequenceNumber({
			prefix: afeRequestTypeCode,
			meta_data_1: `${year}`,
		});

		const getLastTwoDigitsOfYear = (year: number) => {
			let lastTwoDigits = year % 100;
			if (year < 0) {
				lastTwoDigits = -lastTwoDigits;
			}
			return lastTwoDigits;
		};

		const padWithZeros = (num: number, desiredLength: number) => {
			const numString = num.toString();

			if (numString.length < desiredLength) {
				const zerosToAdd = desiredLength - numString.length;
				return '0'.repeat(zerosToAdd) + numString;
			} else {
				return numString;
			}
		};

		let referenceNumber = `${getLastTwoDigitsOfYear(year)}${afeRequestTypeCode}${padWithZeros(
			sequenceNumber,
			4,
		)}`;

		if (regionOtherInfo?.display_code && businessUnitOtherInfo?.display_code) {
			referenceNumber = `${regionOtherInfo.display_code}-${businessUnitOtherInfo.display_code}-${referenceNumber}`;
		} else if (regionOtherInfo?.display_code) {
			referenceNumber = `${regionOtherInfo.display_code}-${referenceNumber}`;
		} else if (businessUnitOtherInfo?.display_code) {
			referenceNumber = `${businessUnitOtherInfo.display_code}-${referenceNumber}`;
		}

		return referenceNumber;
	}

	/**
	 * Create cost split payload that we store in cost split table.
	 * @param splitType
	 * @param amountSplitData
	 * @param afeProposalId
	 * @returns
	 */
	private createCostSplitPayload(
		splitType: AMOUNT_SPLIT,
		amountSplitData:
			| ProjectComponentSplitDto[]
			| BudgetTypeSplitRequestDto[]
			| CostCenterAmountSplitRequestDto[]
			| AnalysisCodeAmountSplitRequestDto[]
			| NaturalAccountNumberSplitRequestDto[]
			| BudgetReferenceNumberSplitRequestDto[]
			| ChartOfAccountsRequestDto[]
			| BudgetBasedProjectSplitDto[],
		afeProposalId: number,
		primaryCurrencyType: CURRENCY_TYPE,
		conversionRate: number,
	) {
		if (splitType === AMOUNT_SPLIT.COST_CENTER_SPLIT) {
			const costCenter = amountSplitData[amountSplitData.length - 1];
			const currency = (costCenter as CostCenterAmountSplitRequestDto)
				.budgetReferenceNumberSplit[0];
			const splits = [];
			// for (const costCenter of amountSplitData) {
			// 	const costCenterBudgetReferenceSplit = (costCenter as CostCenterAmountSplitRequestDto).budgetReferenceNumberSplit
			// 		.filter(split => !!split.number)
			// 		.map(
			// 			split => ({
			// 				objectId: (costCenter as CostCenterAmountSplitRequestDto).id,
			// 				objectTitle: split.number,
			// 				afeProposalId: afeProposalId,
			// 				type: AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT,
			// 				currency: primaryCurrencyType,
			// 				amount: split.amount / conversionRate,
			// 				additionalCurrencyAmount: {
			// 					amount: split.amount,
			// 					currency: currency.currency,
			// 					exchangeRateToPrimary: conversionRate,
			// 				}
			// 			}),
			// 		);
			// 	splits.push(...costCenterBudgetReferenceSplit);
			// }
			const costCenterSplits = amountSplitData.map(split => {
				const amount = split.budgetReferenceNumberSplit.reduce(
					(total, referenceNumber) => total + referenceNumber.amount,
					0,
				);
				const costCenterBudgetReferenceSplit = (
					split as CostCenterAmountSplitRequestDto
				).budgetReferenceNumberSplit
					// .filter(split => !!split.number)
					.map(split => ({
						number: split?.number || '',
						currency: primaryCurrencyType,
						amount: split.amount / conversionRate,
						additionalCurrencyAmount: {
							amount: split.amount,
							currency: currency.currency,
							exchangeRateToPrimary: conversionRate,
						},
					}));
				return {
					objectId: split.id,
					objectTitle: split.title,
					afeProposalId: afeProposalId,
					type: splitType,
					currency: primaryCurrencyType,
					amount: amount / conversionRate,
					additionalCurrencyAmount: {
						amount: amount,
						currency: currency.currency,
						exchangeRateToPrimary: conversionRate,
					},
					additionalInfo: {
						section: split.section,
						// naturalAccount: split?.naturalAccount || null,
						analysisCode: split?.analysisCode || null,
						budgetReferenceNumberSplit: costCenterBudgetReferenceSplit,
					},
				};
			});
			splits.push(...costCenterSplits);
			return splits;
		} else if (splitType === AMOUNT_SPLIT.GL_CODE_SPLIT) {
			return amountSplitData.map(split => ({
				objectId: null,
				objectTitle: `${split.segments.segment1}-${split.segments.segment2}-${split.segments.segment3}-${split.segments.segment4}-${split.segments.segment5}-${split.segments.segment6}-${split.segments.segment7}-${split.segments.segment8}`,
				afeProposalId: afeProposalId,
				type: splitType,
				currency: primaryCurrencyType,
				amount: split.amount / conversionRate,
				additionalCurrencyAmount: {
					amount: split.amount,
					currency: split.currency,
					exchangeRateToPrimary: conversionRate,
				},
			}));
		} else if (splitType === AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT) {
			const amountSplitObjects = [];
			for (const split of amountSplitData) {
				const { id, title, budgetedAmount, unbudgetedAmount, currency } =
					split as BudgetBasedProjectSplitDto;
				const commonProps = {
					objectId: id,
					objectTitle: title,
					afeProposalId: afeProposalId,
					type: splitType,
					currency: primaryCurrencyType,
				};
				if (budgetedAmount) {
					amountSplitObjects.push({
						...commonProps,
						budgetType: BUDGET_TYPE.BUDGETED,
						amount: budgetedAmount / conversionRate,
						additionalCurrencyAmount: {
							amount: budgetedAmount,
							currency: currency,
							exchangeRateToPrimary: conversionRate,
						},
					});
				}
				if (unbudgetedAmount) {
					amountSplitObjects.push({
						...commonProps,
						budgetType: BUDGET_TYPE.UNBUDGETED,
						amount: unbudgetedAmount / conversionRate,
						additionalCurrencyAmount: {
							amount: unbudgetedAmount,
							currency: currency,
							exchangeRateToPrimary: conversionRate,
						},
					});
				}
			}
			return amountSplitObjects;
		} else {
			return amountSplitData.map(split => ({
				objectId: split.id,
				objectTitle: split.title || split.number,
				afeProposalId: afeProposalId,
				type: splitType,
				currency: primaryCurrencyType,
				amount: split.amount / conversionRate,
				additionalCurrencyAmount: {
					amount: split.amount,
					currency: split.currency,
					exchangeRateToPrimary: conversionRate,
				},
			}));
		}
	}

	/**
	 * Creating unique key for the workflow step.
	 * @param associatedLevel
	 * @param associateRole
	 * @param associateType
	 * @param associatedColumn
	 * @returns
	 */
	private createStepUniqueKey(
		associatedLevel: string,
		associateRole: string,
		associateType: ASSOCIATED_TYPE,
		associatedColumn: ASSOCIATED_COLUMN,
	): string {
		return `associatedLevel:${associatedLevel || ''}#associateRole:${
			associateRole || ''
			}#associateType:${associateType || ''}#associatedColumn:${associatedColumn || ''}`;
	}

	/**
	 * Return number of approver that we need to skip
	 * @param approvers
	 * @returns
	 */
	private getCountOfApproverStepSkip(approvers: AfeProposalApproverRequestDto[]) {
		let count: number = 0;
		for (const approver of approvers) {
			if (approver.isStartingPoint) {
				return count;
			}
			count += 1;
		}
		return 0;
	}

	/**
	 * Save all the approvers of an AFE.
	 * @param approvers
	 * @param proposalId
	 * @param currentContext
	 */
	private async saveAfeApproversList(
		approvers: AfeProposalApproverRequestDto[],
		proposalId: number,
		currentContext: CurrentContext,
	): Promise<AfeProposalApprover[]> {
		approvers.sort((a, b) => a.sequenceNumber - b.sequenceNumber);
		const skipCount = this.getCountOfApproverStepSkip(approvers);
		let sequenceNumber = 1;
		//Storing all the approvers
		const approversList = approvers.slice(skipCount).map(approver => ({
			assignedTo:
				approver.associateRole ||
				approver.approvers[approver.approvers.length - 1].loginId.toLowerCase(),
			title: approver.title,
			afeProposalId: proposalId,
			userDetail: null,
			otherInfo: {
				usersDetail: approver.approvers,
				approvalType: APPROVAL_TYPE.APPROVAL,
				...(approver.associateType === ASSOCIATED_TYPE.COST_CENTER &&
					approver.section && { section: approver.section }),
			},
			assignedLevel: approver.associateLevel,
			assginedType: approver.associateType || ASSIGNED_TYPE.USER,
			assignedEntityId: approver.associatedLevelEntityId || null,
			associatedColumn: approver.associatedColumn,
			parallelIdentifier: approver.parallelIdentifier,
			approvalSequence: sequenceNumber++,
			workflowMasterStepsId: approver.stepId,
			associatedCostCenterId: approver?.associatedCostCenterId || null,
		}));
		return this.afeProposalApproverRepository.bulkApproversInsert(approversList, currentContext);
	}

	/**
	 * Return afe detail UI url.
	 * @param proposalId
	 * @returns
	 */
	private getAfeDetailUrl(proposalId: number) {
		const config = this.configService.getAppConfig();
		return `${config.uiClient.baseUrl}/afe/afe-detail/${proposalId}`;
	}

	/**
	 * Send afe creation notification to submitter.
	 * @param afeProposal
	 * @param submitterEmail
	 * @param templateName
	 */
	private async sendAfeCreationNotificationToSubmitter(
		afeProposal: AfeProposal,
		submitterEmail: string,
		templateName: string,
	): Promise<void> {
		const placeholdersValues = {
			afeDetailLink: this.getAfeDetailUrl(afeProposal.id),
		};

		await this.sharedNotificationService.sendNotificationForAfeProposal(
			afeProposal.id,
			afeProposal.id,
			NOTIFICATION_ENTITY_TYPE.AFE_PROPOSAL_CREATION_NOTIFICATION,
			{ to: [submitterEmail] },
			templateName,
			false,
			placeholdersValues,
		);
	}

	/**
	 * Send afe creation notification to others like readers, project readers.
	 * @param afeProposal
	 * @param templateName
	 */
	private async sendAfeCreationNotificationToOthers(
		afeProposal: AfeProposal,
		templateName: string,
	): Promise<void> {
		const placeholdersValues = {
			afeDetailLink: this.getAfeDetailUrl(afeProposal.id),
		};

		const { readers, data } = afeProposal;
		let readersEmail = [];
		if (readers?.length) {
			readersEmail = readers.filter(r => !!r.mail).map(r => r.mail);
		}
		const receiverEmails = [...new Set([data.projectDetails.projectLeader.mail, ...readersEmail])];

		await this.sharedNotificationService.sendNotificationForAfeProposal(
			afeProposal.id,
			afeProposal.id,
			NOTIFICATION_ENTITY_TYPE.AFE_PROPOSAL_CREATION_NOTIFICATION,
			{ to: receiverEmails },
			templateName,
			false,
			placeholdersValues,
		);
	}

	/**
	 * Move all the attachments from source AFE to submitted afe proposal.
	 * @param sourceEntityType
	 * @param sourceId
	 * @param destinationEntityType
	 * @param destinationId
	 */
	private async moveAttachmentsFromSourceToDestination(
		sourceEntityType: ATTACHMENT_ENTITY_TYPE,
		sourceId: number,
		destinationEntityType: ATTACHMENT_ENTITY_TYPE,
		destinationId: number,
	): Promise<void> {
		const payload: MoveAttachmentsRequest = {
			source_entity_type: sourceEntityType,
			source_entity_id: sourceId,
			destination_entity_type: destinationEntityType,
			destination_entity_id: +destinationId,
			destination_folder_path: replaceUrlVariable(ATTACHMENT_REL_PATH.AFE_SUBMIT, {
				entity_id: destinationId,
			}),
			meta_data_1: destinationId.toString(),
		};
		await this.attachmentApiClient.moveAttachments(payload);
	}

	/**
	 * Copy history data of original afe to new resubmitted AFE.
	 * @param sourceEntityId
	 * @param destinationEntityId
	 */
	private async copyHistoryFromOriginalAfeToResubmitAfe(
		sourceEntityId: number,
		destinationEntityId: number,
	): Promise<void> {
		const sourceHistories = await this.historyApiClient.getRequestHistory(
			sourceEntityId,
			HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
		);
		const destinationHistoriesPromise = sourceHistories.map(history => {
			return this.historyApiClient.addRequestHistory({
				created_by: history.created_by,
				entity_id: destinationEntityId,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: history.action_performed,
				comments: history.action_comments,
				additional_info: history.additional_info,
				action_date: history.action_date,
			});
		});
		await Promise.all(destinationHistoriesPromise);
	}

	/**
	 * Unsubscribe or subscribe the AFE notifications for an user.
	 * @param afeProposalId
	 * @param toggleValue
	 * @param currentContext
	 */
	public async toggleAfeNotificationSubscription(
		afeProposalId: number,
		toggleValue: TOGGLE_ON_OFF,
		currentContext: CurrentContext,
	): Promise<void> {
		const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);

		if (!proposal) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		const hasUserPermission =
			await this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(
				proposal,
				currentContext,
			);
		if (!hasUserPermission) {
			throw new HttpException(
				`You are not authorized to perform this action.`,
				HttpStatus.FORBIDDEN,
			);
		}

		if (proposal.createdBy === currentContext.user.username) {
			throw new HttpException(`Afe submitter can't perform this action.`, HttpStatus.BAD_REQUEST);
		}

		if (toggleValue === TOGGLE_ON_OFF.ON) {
			await this.afeProposalRepository.addContextUserToSubscriberList(
				afeProposalId,
				currentContext,
			);
			return;
		} else if (toggleValue === TOGGLE_ON_OFF.OFF) {
			await this.afeProposalRepository.removeContextUserFromSubscibersList(
				afeProposalId,
				currentContext,
			);
			return;
		}

		throw new HttpException(`Toggle value should be 'ON' or 'OFF`, HttpStatus.BAD_REQUEST);
	}

	public async updateProposalDetail(
		updateAfeDetailRequestDTO: UpdateAfeDetailRequestDTO,
		currentContext: CurrentContext,
	) {
		const generalUpdatePermission = await this.adminApiClient.hasPermissionToUser(
			currentContext.user.username,
			PERMISSIONS.AFE_GENERAL_UPADTES,
			updateAfeDetailRequestDTO.entityId,
		);

		const financeUpdatePermission = await this.adminApiClient.hasPermissionToUser(
			currentContext.user.username,
			PERMISSIONS.AFE_ALLOW_FINANCE_UPDATES,
			updateAfeDetailRequestDTO.entityId,
		);

		if (
			!financeUpdatePermission &&
			(updateAfeDetailRequestDTO?.glCodes ||
				updateAfeDetailRequestDTO?.naturalAccountNumbers ||
				updateAfeDetailRequestDTO?.analysisCodeSplits)
		) {
			throw new HttpException(
				`You are not authorized to update GL Code, Natural Account & Analysis Code.`,
				HttpStatus.UNAUTHORIZED,
			);
		}

		if (
			!generalUpdatePermission &&
			(updateAfeDetailRequestDTO?.projectName ||
				updateAfeDetailRequestDTO?.projectLeaderNumber ||
				updateAfeDetailRequestDTO?.projectJustification ||
				updateAfeDetailRequestDTO?.budgetTypeJustification ||
				updateAfeDetailRequestDTO?.budgetRefNos?.length ||
				updateAfeDetailRequestDTO?.natureType)
		) {
			throw new HttpException(
				`You are not authorized to update general AFE details.`,
				HttpStatus.UNAUTHORIZED,
			);
		}

		if (generalUpdatePermission || financeUpdatePermission) {
			const proposal = await this.afeProposalRepository.getAfeProposalWithSplitById(
				updateAfeDetailRequestDTO.proposalId,
			);

			if (proposal) {
				return await this.databaseHelper.startTransaction(async () => {
					if (generalUpdatePermission) {
						if (proposal.internalStatus !== AFE_PROPOSAL_STATUS.IN_PROGRESS) {
							throw new HttpException(
								AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus] + ` AFE can't be modified.`,
								HttpStatus.NOT_ACCEPTABLE,
							);
						}

						let proposalData = proposal.data;
						let projectName = proposal.name;
						let splitData = proposal?.afeProposalAmountSplits || [];

						if (updateAfeDetailRequestDTO?.projectName) {
							projectName = updateAfeDetailRequestDTO.projectName;
							proposalData.projectDetails.projectName = updateAfeDetailRequestDTO.projectName;
						}

						if (updateAfeDetailRequestDTO?.projectJustification) {
							proposalData.projectDetails.projectJustification =
								updateAfeDetailRequestDTO.projectJustification;
						}

						if (updateAfeDetailRequestDTO?.projectLeaderNumber) {
							if (!proposalData.projectDetails?.projectLeaderNumber) {
								proposalData.projectDetails = {
									...proposalData.projectDetails,
									projectLeaderNumber: '',
								};
							}
							proposalData.projectDetails.projectLeaderNumber =
								updateAfeDetailRequestDTO.projectLeaderNumber;
						}

						if (updateAfeDetailRequestDTO?.budgetTypeJustification) {
							proposalData.budgetTypeJustification =
								updateAfeDetailRequestDTO.budgetTypeJustification;
						}

						if (updateAfeDetailRequestDTO?.natureType) {
							proposalData.natureType = updateAfeDetailRequestDTO.natureType;
						}

						let updatedData = {
							name: projectName,
							data: proposalData,
						};

						await this.afeProposalRepository.updateAfeProposalById(
							updateAfeDetailRequestDTO.proposalId,
							currentContext,
							updatedData,
						);

						if (updateAfeDetailRequestDTO?.budgetRefNos?.length) {
							const updatedBudgetRefNos = updateAfeDetailRequestDTO?.budgetRefNos || [];

							const prevCostCenterSplits = splitData.filter(splitDetail => {
								return splitDetail.type === AMOUNT_SPLIT.COST_CENTER_SPLIT;
							});

							const costCenterObj = prevCostCenterSplits.map(costCenterDetail => {
								return {
									splitId: costCenterDetail.id,
									budgetReferenceNumberSplit:
										costCenterDetail?.additionalInfo?.budgetReferenceNumberSplit || [],
									additionalInfo: costCenterDetail?.additionalInfo || {},
								};
							});

							const mergedCCData = costCenterObj.map(costCenterDetail => {
								const brnSplit = costCenterDetail.budgetReferenceNumberSplit.map(
									budgetReferenceNumberSplitDetail => {
										const newNumber = updatedBudgetRefNos.find(updatedBudgetRefDetail => {
											if (
												toNumber(updatedBudgetRefDetail.id) ===
												toNumber(costCenterDetail.splitId) &&
												updatedBudgetRefDetail.oldNumber === budgetReferenceNumberSplitDetail.number
											) {
												return updatedBudgetRefDetail;
											}
										});

										return {
											...budgetReferenceNumberSplitDetail,
											number: newNumber?.number || '',
										};
									},
								);

								return {
									...costCenterDetail,
									additionalInfo: {
										...costCenterDetail.additionalInfo,
										budgetReferenceNumberSplit: brnSplit,
									},
								};
							});

							if (mergedCCData?.length) {
								for (let i = 0; i < mergedCCData.length; i++) {
									const additionalInfo = mergedCCData[i].additionalInfo;
									const id = mergedCCData[i].splitId;

									await this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(
										id,
										currentContext,
										{
											additionalInfo,
										},
									);
								}
							}
						}
					}

					if (financeUpdatePermission && updateAfeDetailRequestDTO?.glCodes?.length) {
						for (let i = 0; i < updateAfeDetailRequestDTO.glCodes.length; i++) {
							const glcode = updateAfeDetailRequestDTO.glCodes[i].glcode;
							const id = updateAfeDetailRequestDTO.glCodes[i].id;

							await this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(
								id,
								currentContext,
								{
									objectTitle: glcode,
								},
							);
						}
					}

					if (financeUpdatePermission && updateAfeDetailRequestDTO?.analysisCodeSplits?.length) {
						for (let i = 0; i < updateAfeDetailRequestDTO.analysisCodeSplits.length; i++) {
							const objectId = updateAfeDetailRequestDTO.analysisCodeSplits[i].analysisId;
							const title = updateAfeDetailRequestDTO.analysisCodeSplits[i].title;
							const id = updateAfeDetailRequestDTO.analysisCodeSplits[i].id;

							await this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(
								id,
								currentContext,
								{
									objectTitle: title,
									objectId,
								},
							);
						}
					}

					if (financeUpdatePermission && updateAfeDetailRequestDTO?.naturalAccountNumbers?.length) {
						for (let i = 0; i < updateAfeDetailRequestDTO.naturalAccountNumbers.length; i++) {
							const objectId = updateAfeDetailRequestDTO.naturalAccountNumbers[i].naturalAccountId;
							const title = updateAfeDetailRequestDTO.naturalAccountNumbers[i].title;
							const id = updateAfeDetailRequestDTO.naturalAccountNumbers[i].id;

							await this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(
								id,
								currentContext,
								{
									objectTitle: title,
									objectId,
								},
							);
						}
					}

					const addHistoryPayload: AddHistoryRequest = {
						created_by: currentContext.user.username,
						entity_id: updateAfeDetailRequestDTO.proposalId,
						entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
						action_performed: HISTORY_ACTION_TYPE.UPDATE,
						additional_info: {
							newUpdate: updateAfeDetailRequestDTO,
						},
					};
					await this.historyApiClient.addRequestHistory(addHistoryPayload);

					return { message: 'AFE detail has been updated successfully.' };
				});
			} else {
				throw new HttpException(`AFE detail is not available.`, HttpStatus.NOT_FOUND);
			}
		}

		throw new HttpException(
			`You are not authorized to update AFE detail.`,
			HttpStatus.UNAUTHORIZED,
		);
	}

	public async addNewReader(
		proposalId: number,
		addNewReadersRequestDto: AddNewReadersRequestDto,
		currentContext: CurrentContext,
	) {
		let { readers } = addNewReadersRequestDto;
		const proposal = await this.afeProposalRepository.getAfeProposalById(proposalId);

		if (!proposal) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (proposal?.readers?.length) {
			readers = [...proposal.readers, ...readers];
		}

		await this.afeProposalRepository.updateAfeProposalById(proposalId, currentContext, {
			readers,
		});

		const addHistoryPayload: AddHistoryRequest = {
			created_by: currentContext.user.username,
			entity_id: proposalId,
			entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
			action_performed: HISTORY_ACTION_TYPE.UPDATED,
			comments: 'Reader addedd.',
		};

		await this.historyApiClient.addRequestHistory(addHistoryPayload);

		return { message: 'New Reader has been added successfully!' };
	}

	public async uploadEvidence(
		proposalId: number,
		evidencesRequestDto: UploadEvidenceRequestDto,
		currentContext: CurrentContext,
	) {
		const proposal = await this.afeProposalRepository.getAfeProposalById(proposalId);

		if (!proposal) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		const { evidences } = evidencesRequestDto;

		if (evidences?.length) {
			await this.sharedAttachmentService.supportingDocumentsActivity(
				evidences,
				proposalId,
				ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT,
				ATTACHMENT_REL_PATH.AFE_SUBMIT,
				currentContext.user.username,
				proposalId,
			);

			const addHistoryPayload: AddHistoryRequest = {
				created_by: currentContext.user.username,
				entity_id: proposalId,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.UPDATED,
				comments: 'The evidence/document has been uploaded to the supporting documents section.',
			};

			await this.historyApiClient.addRequestHistory(addHistoryPayload);

			return { message: 'Evidence has been uploaded successfully!' };
		}

		throw new HttpException(`Evidence required.`, HttpStatus.NOT_FOUND);
	}

	public async withdrawAfeProposal(
		withdrawAfeProposalRequestDto: WithdrawAfeProposalRequestDto,
		currentContext: CurrentContext,
	) {
		const { id, comments } = withdrawAfeProposalRequestDto;
		const { user } = currentContext;
		const proposal = await this.afeProposalRepository.getAfeProposalById(id);

		if (!proposal) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (
			proposal.submitterId.toLowerCase() !== user.username.toLowerCase() &&
			!this.adminApiClient.hasPermissionToUser(
				user.username.toLowerCase(),
				PERMISSIONS.AFE_ADMINISTRATION,
				proposal.entityId,
			)
		) {
			throw new HttpException(
				`You are not authorized to withraw this AFE`,
				HttpStatus.UNAUTHORIZED,
			);
		}

		if (
			proposal.internalStatus !== AFE_PROPOSAL_STATUS.IN_PROGRESS &&
			proposal.internalStatus !== AFE_PROPOSAL_STATUS.SENT_BACK
		) {
			throw new HttpException(
				`It is not possible to withdraw the proposal since this AFE has already been ${
					AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]
				}.`,
				HttpStatus.NOT_ACCEPTABLE,
			);
		}

		return await this.databaseHelper.startTransaction(async () => {
			// let internalStatus = (requestType === 'SEND_BACK') ? AFE_PROPOSAL_STATUS.SENT_BACK : AFE_PROPOSAL_STATUS.CANCELLED;

			// let userStatus = (requestType === 'SEND_BACK') ? AFE_USER_STATUS.SENT_BACK : AFE_USER_STATUS.CANCELLED;

			const approvers =
				await this.afeProposalApproverRepository.getInProgressApproversListByProposalId(id);

			let modifiedComment = '';
			if (proposal.submitterId.toLowerCase() === user.username.toLowerCase()) {
				modifiedComment = `Proposal has been withdrawn by the submitter with the reason '${comments}'`;
			} else {
				modifiedComment = `Proposal has been withdrawn by the AFE Support Team on user request with the reason '${comments}'`;
			}

			if (approvers?.length) {
				let approverIds = [];

				for (let i = 0; i < approvers.length; i++) {
					approverIds.push(approvers[i].id);

					await this.taskApiClient.cancelAllTasks({
						entity_id: approvers[i].id,
						entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
						comments: modifiedComment,
						created_by: user.username.toLowerCase(),
					});
				}

				// await this.afeProposalApproverRepository.updateWithdrawStatusOnActionByIds(
				await this.afeProposalApproverRepository.updateStatusOnActionByIds(
					approverIds,
					APPROVER_STATUS.DISCARDED,
					currentContext,
					modifiedComment,
				);
			}

			let internalStatus = AFE_PROPOSAL_STATUS.CANCELLED;
			let userStatus = AFE_USER_STATUS.CANCELLED;

			await this.afeProposalRepository.changeStatus(id, internalStatus, userStatus, currentContext);

			await this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(
				id,
				AFE_LIMIT_DEDUCATION_STATUS.CANCELLED,
				currentContext,
			);

			const addHistoryPayload: AddHistoryRequest = {
				created_by: currentContext.user.username,
				entity_id: id,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.CANCELLED,
				comments: modifiedComment,
			};

			await this.historyApiClient.addRequestHistory(addHistoryPayload);

			return { message: 'Proposal has been successfully withdrawn!' };
		});
	}

	/**
	 * Get AFE Proposal Amount Split Data.
	 * @param afeProposalId
	 * @returns
	 */
	public async getAfeProposalAmountSplitData(afePropsal: AfeProposal): Promise<any> {
		const amountSplits = await this.afeProposalAmountSplitRepository.getAmountSplitsByProposalId(
			afePropsal.id,
		);
		let budgetTypeSplit = [];
		let projectComponentSplit = [];
		let budgetBasedProjectSplit = [];

		for (let split of amountSplits) {
			const { objectId, objectTitle, additionalCurrencyAmount, amount, currency } = split;
			const splitData = {
				id: objectId,
				title: objectTitle,
				amount: amount,
				currency: currency,
				additionalCurrencyAmount,
			};

			switch (split.type) {
				case AMOUNT_SPLIT.BUDGET_TYPE_SPLIT:
					budgetTypeSplit.push({ ...splitData });
					break;
				case AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT:
					projectComponentSplit.push({ ...splitData });
					break;
			}
		}

		const projectComponentSplitByBudgetType = amountSplits.filter(
			split => split.type === AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT,
		);

		if (projectComponentSplitByBudgetType.length && afePropsal.budgetType === BUDGET_TYPE.MIXED) {
			const secondaryBudgetBasedProjectSplit = serializeBudgetBasedProjectAmountSplits(
				projectComponentSplitByBudgetType,
			);
			const inPrimaryCurrencyBudgetBasedProjectSplit = serializeBudgetBasedProjectAmountSplits(
				projectComponentSplitByBudgetType,
				false,
			);

			for (let split of inPrimaryCurrencyBudgetBasedProjectSplit) {
				const { id } = split;
				const additionalCurrencyAmountForBudgetBasedProjectSplit =
					secondaryBudgetBasedProjectSplit.find(s => s.id === id);
				budgetBasedProjectSplit.push({
					...split,
					additionalCurrencyAmountForBudgetBasedProjectSplit,
				});
			}
		}

		const splits = {
			budgetTypeSplit,
			projectComponentSplit,
			budgetBasedProjectSplit,
		};

		return splits;
	}

	/**
	 * Calculate the delta amount of supplemental AFE with its last Approved AFE.
	 * @param currentAfeProposal
	 * @param lastApprovedAfe
	 */
	private async calculateDeltaAmounts(
		lastApprovedAfe: AfeProposal,
		currentAfeProposal: AfeProposal,
	) {
		const currentAfeAmountSplits = await this.getAfeProposalAmountSplitData(currentAfeProposal);
		const lastApprovedAfeAmountSplits = await this.getAfeProposalAmountSplitData(lastApprovedAfe);

		const {
			totalAmount: currentAfeTotalAmount,
			additionalCurrencyAmount: currentAfeAdditionalCurrencyAmount,
			marketValue: currentAfeMarketValue,
			marketValueAdditionalCurrencyAmount: currentAfeMarketValueAdditionalCurrencyAmount,
			currencyType,
		} = currentAfeProposal;

		const {
			totalAmount,
			additionalCurrencyAmount,
			marketValue,
			marketValueAdditionalCurrencyAmount,
		} = lastApprovedAfe;

		const returnDeltaAmountsObject = (lastApprovedSplit, currentSplits) => {
			const deltaAmounts = [];
			if (currentSplits) {
				for (const split of currentSplits) {
					const supplementalSplit = lastApprovedSplit?.find(s => s.id === split.id);
					if (supplementalSplit) {
						deltaAmounts.push({
							...supplementalSplit,
							amount: split.amount - supplementalSplit.amount,
							additionalCurrencyAmount: {
								...split.additionalCurrencyAmount,
								amount:
									split.additionalCurrencyAmount.amount -
									supplementalSplit.additionalCurrencyAmount.amount,
							},
						});
					} else {
						deltaAmounts.push(split);
					}
				}
			}

			// Find objects in  lastSupplemental but not in currentAfeProposal split
			const onlyInLastSupplementalAfe = lastApprovedSplit
				?.filter(obj2 => !currentSplits?.some(obj1 => obj1.id === obj2.id))
				?.map(split => ({
					...split,
					amount: split.amount * -1,
					additionalCurrencyAmount: {
						...split.additionalCurrencyAmount,
						amount: split.additionalCurrencyAmount.amount * -1,
					},
				}));

			if (onlyInLastSupplementalAfe) {
				deltaAmounts.push(...onlyInLastSupplementalAfe);
			}

			return deltaAmounts;
		};

		const calculateDeltaAmountForProjectBudgetBased = () => {
			const deltaEntries = [];
			let primaryLastApprovedAmountSplits = lastApprovedAfeAmountSplits.budgetBasedProjectSplit;
			let primaryCurrentAmountSplits = currentAfeAmountSplits.budgetBasedProjectSplit;

			if (
				currentAfeProposal.budgetType === BUDGET_TYPE.MIXED &&
				!primaryLastApprovedAmountSplits?.length
			) {
				primaryLastApprovedAmountSplits = lastApprovedAfeAmountSplits?.projectComponentSplit?.map(
					split => {
						const { id, title, amount, currency, additionalCurrencyAmount } = split;
						return {
							id,
							title,
							currency,
							totalAmount: amount,
							budgetedAmount: lastApprovedAfe.budgetType === BUDGET_TYPE.BUDGETED ? amount : 0,
							unbudgetedAmount: lastApprovedAfe.budgetType === BUDGET_TYPE.UNBUDGETED ? amount : 0,
							additionalCurrencyAmountForBudgetBasedProjectSplit: {
								id,
								title,
								currency: additionalCurrencyAmount.currency,
								totalAmount: additionalCurrencyAmount.amount,
								budgetedAmount:
									lastApprovedAfe.budgetType === BUDGET_TYPE.BUDGETED
										? additionalCurrencyAmount.amount
										: 0,
								unbudgetedAmount:
									lastApprovedAfe.budgetType === BUDGET_TYPE.UNBUDGETED
										? additionalCurrencyAmount.amount
										: 0,
							},
						};
					},
				);
			}

			if (lastApprovedAfe.budgetType === BUDGET_TYPE.MIXED && !primaryCurrentAmountSplits?.length) {
				primaryCurrentAmountSplits = currentAfeAmountSplits?.projectComponentSplit?.map(split => {
					const { id, title, amount, currency, additionalCurrencyAmount } = split;
					return {
						id,
						title,
						currency,
						totalAmount: amount,
						budgetedAmount: currentAfeProposal.budgetType === BUDGET_TYPE.BUDGETED ? amount : 0,
						unbudgetedAmount: currentAfeProposal.budgetType === BUDGET_TYPE.UNBUDGETED ? amount : 0,
						additionalCurrencyAmountForBudgetBasedProjectSplit: {
							id,
							title,
							currency: additionalCurrencyAmount.currency,
							totalAmount: additionalCurrencyAmount.amount,
							budgetedAmount:
								currentAfeProposal.budgetType === BUDGET_TYPE.BUDGETED
									? additionalCurrencyAmount.amount
									: 0,
							unbudgetedAmount:
								currentAfeProposal.budgetType === BUDGET_TYPE.UNBUDGETED
									? additionalCurrencyAmount.amount
									: 0,
						},
					};
				});
			}

			if (primaryCurrentAmountSplits?.length || primaryLastApprovedAmountSplits?.length) {
				if (primaryCurrentAmountSplits?.length) {
					for (const currentAmountSplit of primaryCurrentAmountSplits) {
						const {
							id,
							budgetedAmount,
							unbudgetedAmount,
							totalAmount,
							currency,
							additionalCurrencyAmountForBudgetBasedProjectSplit,
							title,
						} = currentAmountSplit;
						const lastApprovedEntry = primaryLastApprovedAmountSplits?.find(
							split => split.id === id,
						);

						if (lastApprovedEntry) {
							const {
								budgetedAmount: lastBudgetedAmount,
								unbudgetedAmount: lastUnbudgetedAmount,
								totalAmount: lastTotalAmount,
								additionalCurrencyAmountForBudgetBasedProjectSplit: lastAdditionalCurrencyAmount,
							} = lastApprovedEntry;

							deltaEntries.push({
								id,
								title,
								budgetedAmount: budgetedAmount - lastBudgetedAmount,
								unbudgetedAmount: unbudgetedAmount - lastUnbudgetedAmount,
								totalAmount: totalAmount - lastTotalAmount,
								currency: currency,
								additionalCurrencyAmountForBudgetBasedProjectSplit: {
									budgetedAmount:
										additionalCurrencyAmountForBudgetBasedProjectSplit.budgetedAmount -
										lastAdditionalCurrencyAmount.budgetedAmount,
									unbudgetedAmount:
										additionalCurrencyAmountForBudgetBasedProjectSplit.unbudgetedAmount -
										lastAdditionalCurrencyAmount.unbudgetedAmount,
									totalAmount:
										additionalCurrencyAmountForBudgetBasedProjectSplit.totalAmount -
										lastAdditionalCurrencyAmount.totalAmount,
									currency: currency,
								},
							});
						} else {
							deltaEntries.push(currentAmountSplit);
						}
					}
				}

				const onlyInLastApprovedAfe = primaryLastApprovedAmountSplits
					?.filter(obj2 => !primaryCurrentAmountSplits?.some(obj1 => obj1.id === obj2.id))
					?.map(split => ({
						...split,
						budgetedAmount: split.budgetedAmount * -1,
						unbudgetedAmount: split.unbudgetedAmount * -1,
						totalAmount: split.totalAmount * -1,
						additionalCurrencyAmountForBudgetBasedProjectSplit: {
							...split.additionalCurrencyAmount,
							budgetedAmount:
								split.additionalCurrencyAmountForBudgetBasedProjectSplit.budgetedAmount * -1,
							unbudgetedAmount:
								split.additionalCurrencyAmountForBudgetBasedProjectSplit.unbudgetedAmount * -1,
							totalAmount:
								split.additionalCurrencyAmountForBudgetBasedProjectSplit.totalAmount * -1,
						},
					}));

				if (onlyInLastApprovedAfe) {
					deltaEntries.push(...onlyInLastApprovedAfe);
				}
			}
			return deltaEntries;
		};

		const budgetTypeSplit = returnDeltaAmountsObject(
			lastApprovedAfeAmountSplits.budgetTypeSplit,
			currentAfeAmountSplits.budgetTypeSplit,
		);

		const projectComponentSplit = returnDeltaAmountsObject(
			lastApprovedAfeAmountSplits.projectComponentSplit,
			currentAfeAmountSplits.projectComponentSplit,
		);

		const budgetBasedProjectSplit = calculateDeltaAmountForProjectBudgetBased();

		const deltaAmounts = {
			totalAmount: currentAfeTotalAmount - totalAmount,
			additionalCurrencyAmount: {
				...currentAfeAdditionalCurrencyAmount,
				amount: currentAfeAdditionalCurrencyAmount.amount - additionalCurrencyAmount.amount,
			},
			...(marketValue && { marketValue: currentAfeMarketValue - marketValue }),
			...(marketValueAdditionalCurrencyAmount && {
				marketValueAdditionalCurrencyAmount: {
					...currentAfeMarketValueAdditionalCurrencyAmount,
					amount:
						currentAfeMarketValueAdditionalCurrencyAmount.amount -
						marketValueAdditionalCurrencyAmount.amount,
				},
			}),
			currency: currencyType,
			afeProposalAmountSplits: {
				budgetTypeSplit,
				projectComponentSplit,
				budgetBasedProjectSplit,
			},
		};
		return deltaAmounts;
	}

	/**
	 * Temprory function for the delta amount creation for old entries.
	 * @param parentId
	 * @returns
	 */
	public async updateParentAfeSupplementalsDeltaAmounts(parentId: number) {
		const allAfes = await this.afeProposalRepository.getAllSupplementalIdsWithVersion(parentId);
		if (allAfes.length > 1) {
			await this.databaseHelper.startTransaction(async () => {
				let startIndex = 0;
				let lastApprovedAfeId = allAfes[startIndex].id;
				while (startIndex < allAfes.length - 1) {
					startIndex += 1;
					await this.updateDeltaAmountPayloadForSupplemental(
						lastApprovedAfeId,
						allAfes[startIndex].id,
						SYSTEM_USER,
					);
					if (allAfes[startIndex].internalStatus === AFE_PROPOSAL_STATUS.APPROVED) {
						lastApprovedAfeId = allAfes[startIndex].id;
					}
				}
			});
		}
		return { message: `Update all the supplementals.` };
	}

	public async updateDeltaAmountPayloadForSupplemental(
		previousApprovedAfeId: number,
		currentAfeId: number,
		currentContext: CurrentContext,
	) {
		const previousApprovedAfe = await this.afeProposalRepository.getAfeProposalById(
			previousApprovedAfeId,
		);
		const currentAfe = await this.afeProposalRepository.getAfeProposalById(currentAfeId);

		const supplementalDeltaAmounts = await this.calculateDeltaAmounts(
			previousApprovedAfe,
			currentAfe,
		);
		await this.afeProposalRepository.updateAfeProposalById(currentAfeId, currentContext, {
			supplementalDeltaAmounts,
		});
	}

	/**
	 * Send back the afe proposal to the submitter by rthe admin or submitter.
	 * @param sendBackAfeProposalRequestDto
	 * @param currentContext
	 * @returns
	 */
	public async sendBackAfeProposal(
		sendBackAfeProposalRequestDto: SendBackAfeProposalRequestDto,
		currentContext: CurrentContext,
	) {
		const { id, comments } = sendBackAfeProposalRequestDto;
		const { user } = currentContext;
		const proposal = await this.afeProposalRepository.getAfeProposalById(id);

		if (!proposal) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (
			proposal.submitterId.toLowerCase() !== user.username.toLowerCase() &&
			!this.adminApiClient.hasPermissionToUser(
				user.username.toLowerCase(),
				PERMISSIONS.AFE_ADMINISTRATION,
				proposal.entityId,
			)
		) {
			throw new HttpException(
				`You are not authorized to send back this AFE`,
				HttpStatus.UNAUTHORIZED,
			);
		}

		if (proposal.internalStatus !== AFE_PROPOSAL_STATUS.IN_PROGRESS) {
			throw new HttpException(
				`It is not possible to send back the proposal since this AFE has already been ${
					AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]
				}.`,
				HttpStatus.NOT_ACCEPTABLE,
			);
		}

		return await this.databaseHelper.startTransaction(async () => {
			const approvers =
				await this.afeProposalApproverRepository.getInProgressApproversListByProposalId(id);

			let modifiedComment = '';
			if (proposal.submitterId.toLowerCase() === user.username.toLowerCase()) {
				modifiedComment = `Proposal has been send back by the submitter with the reason '${comments}'`;
			} else {
				modifiedComment = `Proposal has been send back by the AFE Support Team on user request with the reason '${comments}'`;
			}

			if (approvers?.length) {
				let approverIds = [];

				for (let i = 0; i < approvers.length; i++) {
					approverIds.push(approvers[i].id);

					await this.taskApiClient.cancelAllTasks({
						entity_id: approvers[i].id,
						entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
						comments: modifiedComment,
						created_by: user.username.toLowerCase(),
					});
				}

				await this.afeProposalApproverRepository.updateStatusOnActionByIds(
					approverIds,
					APPROVER_STATUS.DISCARDED,
					currentContext,
					modifiedComment,
				);
			}

			await this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(
				id,
				AFE_LIMIT_DEDUCATION_STATUS.CANCELLED,
				currentContext,
			);

			const { submitterId: afeCreatorId } = proposal;
			const currentApprover = approvers[approvers.length - 1];
			const afeCreatorApprover = {
				assignedTo: afeCreatorId.toLowerCase(),
				title: afeCreatorId,
				afeProposalId: id,
				userDetail: null,
				otherInfo: {
					usersDetail: [{ loginId: afeCreatorId }],
					approvalType: APPROVAL_TYPE.RESUBMISSION,
				},
				assignedLevel: null,
				assginedType: ASSIGNED_TYPE.USER,
				associatedColumn: null,
				parallelIdentifier: null,
				approvalSequence: currentApprover.approvalSequence,
				workflowMasterStepsId: null,
				assignedEntityId: null,
			};
			await this.afeProposalApproverRepository.createAfeProposalApprover(
				afeCreatorApprover,
				currentContext,
			);

			await this.taskService.createNextTasks(id, TASK_ACTION.SEND_BACK, currentContext);

			await this.taskService.createQueueLogOnApprovalAction(
				TASK_ACTION.SEND_BACK,
				proposal,
				true,
				approvers,
				currentContext,
			);

			//Send Notification on task action completion.
			const submitterDetails = await this.mSGraphApiClient.getUserDetails(afeCreatorId);
			const config = this.configService.getAppConfig();
			const placeholdersValues = {
				afeDetailLink: `${config.uiClient.baseUrl}/afe/afe-detail/${proposal.id}`,
				approvers: `${currentContext.user.name}`,
				approver: `${currentApprover.title}`,
				comments: comments || '-',
			};

			if (submitterDetails?.mail) {
				await this.sharedNotificationService.sendNotificationForAfeProposal(
					proposal.id,
					currentApprover.id,
					NOTIFICATION_ENTITY_TYPE.AFE_TASK_APPROVAL_NOTIFICATION,
					{ to: [submitterDetails.mail] },
					TASK_ACTION_WITH_EMAIL_TEMPLATE[TASK_ACTION.SEND_BACK],
					false,
					placeholdersValues,
				);
			}

			const addHistoryPayload: AddHistoryRequest = {
				created_by: currentContext.user.username,
				entity_id: id,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.SENT_BACK,
				comments: modifiedComment,
			};

			await this.historyApiClient.addRequestHistory(addHistoryPayload);

			return { message: 'Proposal has been sent back successfully!' };
		});
	}

	public async reopenAfeProposal(
		reopenAfeProposalRequestDto: ReopenAfeProposalRequestDto,
		currentContext: CurrentContext,
	) {
		const { approverId, proposalId, comments } = reopenAfeProposalRequestDto;
		const { user } = currentContext;

		const proposal = await this.afeProposalRepository.getAfeProposalById(proposalId);

		if (!proposal) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (proposal.internalStatus !== AFE_PROPOSAL_STATUS.REJECTED) {
			throw new HttpException(
				`It is not possible to re-open the proposal since this AFE is currently in ${
					AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]
				} status.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		const approverDetail = await this.afeProposalApproverRepository.getApproverById(approverId);

		if (!approverDetail) {
			throw new HttpException(`Invalid approver.`, HttpStatus.NOT_FOUND);
		}

		if (approverDetail.actionStatus !== APPROVER_STATUS.REJECTED) {
			throw new HttpException(
				`The AFE was not rejected by the selected approver.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		return await this.databaseHelper.startTransaction(async () => {
			// 1. update approver status.
			const parallelIdentifier = approverDetail.parallelIdentifier;

			await this.afeProposalApproverRepository.reopenApproverByProposalIdAndStatus(
				proposalId,
				parallelIdentifier,
				APPROVER_STATUS.REJECTED,
				currentContext,
			);

			// 2. update if deduction
			await this.afeProposalLimitDeductionRepository.changeRejectedStatusByProposalId(
				proposalId,
				AFE_LIMIT_DEDUCATION_STATUS.IN_PROGRESS,
				currentContext,
			);

			// 5. Update proposal table internal and user status.
			await this.taskService.changeAfeStatusToInprogress(proposalId, currentContext);

			// 3. create new task for approvers.
			await this.taskService.recreateTask(proposalId, proposal, TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING.APPROVE, true);

			// 4. Add history.
			const modifiedComment = `Proposal has been re-open by AFE Support Team on user request with the reason '${comments}'`;

			const addHistoryPayload: AddHistoryRequest = {
				created_by: currentContext.user.username,
				entity_id: proposalId,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.REOPEN,
				comments: modifiedComment,
			};

			await this.historyApiClient.addRequestHistory(addHistoryPayload);

			return { message: 'Proposal has been re-open successfully!' };
		});
	}

	public async revertSentBackAction(
		requestDto: ReopenAfeProposalRequestDto,
		currentContext: CurrentContext,
	) {
		const { approverId, proposalId, comments } = requestDto;

		const proposal = await this.afeProposalRepository.getAfeProposalById(proposalId);

		if (!proposal) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (proposal.internalStatus !== AFE_PROPOSAL_STATUS.SENT_BACK) {
			throw new HttpException(
				`It is not possible to revert the last action as a Sent Back since this AFE is currently in ${
					AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]
				} status.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		const approverDetail = await this.afeProposalApproverRepository.getApproverById(approverId);

		if (!approverDetail) {
			throw new HttpException(`Invalid approver.`, HttpStatus.NOT_FOUND);
		}

		if (approverDetail.actionStatus !== APPROVER_STATUS.IN_PROGRESS) {
			throw new HttpException(
				`The AFE is not pending with the selected approver.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		const sendBackApproverDetail =
			await this.afeProposalApproverRepository.getSentBackApproverByProposalId(proposalId);

		if (!sendBackApproverDetail) {
			throw new HttpException(`No approver has sent back this AFE.`, HttpStatus.NOT_FOUND);
		}

		return await this.databaseHelper.startTransaction(async () => {
			//Delete all previous task.
			const approvers =
				await this.afeProposalApproverRepository.getInProgressApproversListByProposalId(proposalId);

			const modifiedComment = `Last action has been reverted by AFE Support Team on user request with the reason '${comments}'`;

			for (let i = 0; i < approvers.length; i++) {
				await this.taskApiClient.cancelAllTasks({
					entity_id: approvers[i].id,
					entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
					comments: modifiedComment,
					created_by: currentContext.user.username.toLowerCase(),
				});
			}

			const parallelIdentifier = sendBackApproverDetail.parallelIdentifier;

			// 1. Delete current InProgress approver (Submitter).
			await this.afeProposalApproverRepository.deleteApproversByIds([approverId], currentContext);

			// 2. Reopen the send back approvers.
			await this.afeProposalApproverRepository.reopenApproverByProposalIdAndStatus(
				proposalId,
				parallelIdentifier,
				APPROVER_STATUS.SEND_BACK,
				currentContext,
			);

			// 5. Update proposal table internal and user status.
			await this.taskService.changeAfeStatusToInprogress(proposalId, currentContext);

			// 3. create new task for approvers.
			await this.taskService.recreateTask(proposalId, proposal, TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING.APPROVE, true);

			const addHistoryPayload: AddHistoryRequest = {
				created_by: currentContext.user.username,
				entity_id: proposalId,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.SENT_BACK_REVERTED,
				comments: modifiedComment,
			};

			await this.historyApiClient.addRequestHistory(addHistoryPayload);

			return { message: 'Action has been reverted successfully!' };
		});
	}

	public async updateApproverUser(
		proposalId: number,
		updateApproverUserRequestDTO: UpdateApproverUserRequestDTO,
		currentContext: CurrentContext,
	) {
		const { approverId, userDetail } = updateApproverUserRequestDTO;

		const proposalData = await this.afeProposalRepository.getAfeProposalById(proposalId);

		if (!proposalData) {
			throw new HttpException(`Afe proposal doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		const approverDetail = await this.afeProposalApproverRepository.getApproverById(approverId);

		if (!approverDetail) {
			throw new HttpException(`Approver doesn't exist.`, HttpStatus.NOT_FOUND);
		}

		if (approverDetail.actionStatus !== APPROVER_STATUS.IN_PROGRESS) {
			throw new HttpException(
				`The AFE is not pending with the selected approver.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		if (approverDetail.assginedType !== ASSOCIATED_TYPE.USER) {
			throw new HttpException(
				`The AFE is not pending with the selected approver.`,
				HttpStatus.BAD_REQUEST,
			);
		}

		return this.databaseHelper.startTransaction(async () => {
			//Cancel current task.
			if (approverDetail) {
				await this.taskApiClient.cancelAllTasks({
					entity_id: approverDetail.id,
					entity_type: TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
					comments: `User has been changed`,
					created_by: currentContext.user.username.toLowerCase(),
				});
			}

			//Update Other info, assigned_to, title in approvers table.
			await this.afeProposalApproverRepository.updateCustomUserByApproverId(
				approverId,
				userDetail,
				currentContext,
			);

			// Update userStatus in proposal table.
			await this.afeProposalRepository.updateAfeProposalById(proposalId, currentContext, {
				userStatus: `Pending with ${userDetail?.title ? userDetail.title : userDetail.loginId}`,
			});

			// Generate new task for the new user.
			await this.taskService.recreateTask(
				proposalId,
				proposalData,
				approverDetail.otherInfo.approvalType,
				true
			);

			let prevUserLoginId = '';

			if (
				approverDetail?.otherInfo?.usersDetail.length &&
				approverDetail.otherInfo.usersDetail[0]?.loginId
			) {
				prevUserLoginId = approverDetail.otherInfo.usersDetail[0].loginId;
			}

			// Generate history
			const addHistoryPayload: AddHistoryRequest = {
				created_by: currentContext.user.username,
				entity_id: proposalId,
				entity_type: HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
				action_performed: HISTORY_ACTION_TYPE.APPROVERS_LIST_UPDATED,
				comments:
					'User has been changed ' +
					(prevUserLoginId ? 'from ' + prevUserLoginId : '') +
					' to ' +
					userDetail.loginId,
				additional_info: {
					previousUser: approverDetail.otherInfo.usersDetail,
					newUser: userDetail,
				},
			};

			await this.historyApiClient.addRequestHistory(addHistoryPayload);

			return { message: 'User has been updated successfully!' };
		});
	}
}







