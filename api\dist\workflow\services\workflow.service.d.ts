import { AfeProposalAmountSplitRepository, AfeProposalApproverRepository, AfeProposalLimitDeductionRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { CostCenterRepository, CurrencyTypeRepository } from 'src/finance/repositories';
import { FinanceService } from 'src/finance/services';
import { ProjectComponentRepository } from 'src/project-component/repositories';
import { SettingsService } from 'src/settings/services';
import { AdminApiClient, MSGraphApiClient } from 'src/shared/clients';
import { AfeProposalValidator } from 'src/shared/validators';
import { ComputeAfeApproversListDto, WorkflowResponseDto } from '../dtos';
import { WorkflowMasterSettingRepository, WorkflowMasterStepRepository, WorkflowSharedBucketLimitRepository, WorkflowSharedChildLimitRepository } from '../repositories';
import { LoggerService } from 'src/core/services';
export declare class WorkflowService {
    private readonly adminApiClient;
    private readonly workflowMasterSettingRepository;
    private readonly workflowMasterStepRepository;
    private readonly workflowSharedChildLimitRepository;
    private readonly afeProposalLimitDeductionRepository;
    private readonly workflowSharedBucketLimitRepository;
    private readonly projectComponentRepository;
    private readonly costCenterRepository;
    private readonly financeService;
    private readonly settingsService;
    private readonly mSGraphApiClient;
    private readonly afeProposalValidator;
    private readonly currencyTypeRepository;
    private readonly afeProposalRepository;
    private readonly afeProposalAmountSplitRepository;
    private readonly afeProposalApproverRepository;
    private readonly logger;
    constructor(adminApiClient: AdminApiClient, workflowMasterSettingRepository: WorkflowMasterSettingRepository, workflowMasterStepRepository: WorkflowMasterStepRepository, workflowSharedChildLimitRepository: WorkflowSharedChildLimitRepository, afeProposalLimitDeductionRepository: AfeProposalLimitDeductionRepository, workflowSharedBucketLimitRepository: WorkflowSharedBucketLimitRepository, projectComponentRepository: ProjectComponentRepository, costCenterRepository: CostCenterRepository, financeService: FinanceService, settingsService: SettingsService, mSGraphApiClient: MSGraphApiClient, afeProposalValidator: AfeProposalValidator, currencyTypeRepository: CurrencyTypeRepository, afeProposalRepository: AfeProposalRepository, afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository, afeProposalApproverRepository: AfeProposalApproverRepository, logger: LoggerService);
    getAfeApproversList(computeAfeApproversListDto: ComputeAfeApproversListDto, submitterId: string): Promise<WorkflowResponseDto>;
    computeAfeApproversList(computeAfeApproversListDto: ComputeAfeApproversListDto, submitterId: string): Promise<WorkflowResponseDto>;
    private splitBudgetTypeSplitsIntoCostCenters;
    private updateAggregateLimitForSupplemental;
    private updateWorkflowCostCenters;
    private returnDeductionEntriesForSupplementalRecovery;
    private groupedDeductionsByYear;
    totalDeductionByWorkflowStepOrCostCenter(deductions: any): any[];
    private mapCurrentYearWorkflowWithPreviousYearsWorkflow;
    private createRecoverAmountDeductionEntries;
    private splitProjectComponentAmountsInBudgetTypes;
    private combineProjectComponents;
    private findWorkflowSteps;
    private filterMasterWorkflowSteps;
    private evalRuleCondition;
    private isAmountSatisfyingTheSingleLimit;
    private isAnyCostCenterOperating;
    private isRuleConditionsValid;
    private checkParentAggregateLimitLeft;
    private getAllAssociatedEntityIds;
    private calculteAggregateLimitLeftForAnEntity;
    private mergeWorkflowMasterSettingsSteps;
    private getWorkflowStepKeyUniqueKey;
    private mergeTwoWorkflows;
    private generateApproverListOfWorkflowSteps;
    private getCostCentersHead;
    private convertProjectSplitAmountToPrimaryCurrency;
    private convertProjectSplitByBudgetTypeAmountToPrimaryCurrency;
    private convertCostCenterAmountToPrimaryCurrency;
    private convertBudgetTypeSplitAmountToPrimaryCurrency;
}
