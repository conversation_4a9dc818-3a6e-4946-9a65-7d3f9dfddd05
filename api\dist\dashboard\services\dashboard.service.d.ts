import { DraftAfeRepository } from 'src/afe-draft/repositories';
import { AfeProposalRepository } from 'src/afe-proposal/repositories';
import { AdminApiClient } from 'src/shared/clients';
import { SequlizeOperator } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { RequestTypeWiseAfeResponseDto } from '../dtos/response/request-type-wise-afe-response.dto';
import { StatusWiseAFEResponseDto } from '../dtos/response/status-wise-afe-response.dto';
export declare class DashboardService {
    private readonly afeProposalRepository;
    private readonly adminApiClient;
    private readonly draftAfeRepository;
    private readonly sequlizeOperator;
    constructor(afeProposalRepository: AfeProposalRepository, adminApiClient: AdminApiClient, draftAfeRepository: DraftAfeRepository, sequlizeOperator: SequlizeOperator);
    getStatusWiseCount(currentContext: CurrentContext, year: number): Promise<StatusWiseAFEResponseDto>;
    getRequestWiseMonthlyCount(currentContext: CurrentContext, year: number, entityId: number): Promise<RequestTypeWiseAfeResponseDto[]>;
}
