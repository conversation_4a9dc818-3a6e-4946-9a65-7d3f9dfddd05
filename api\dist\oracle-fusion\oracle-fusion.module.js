"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OracleFusionModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../afe-proposal/repositories");
const helpers_1 = require("../shared/helpers");
const controllers_1 = require("./controllers");
const oracle_fusion_service_1 = require("./services/oracle-fusion.service");
const services_1 = require("../finance/services");
const repositories_2 = require("../finance/repositories");
const clients_1 = require("../shared/clients");
const services_2 = require("../shared/services");
const repositories = [
    repositories_1.AfeProposalRepository,
    repositories_1.AfeProposalAmountSplitRepository,
    repositories_2.NaturalAccountNumberRepository,
    services_1.FinanceAdminService,
    repositories_2.CostCenterRepository,
    repositories_2.CompanyCodeRepository,
    repositories_2.AnalysisCodeRepository,
    repositories_2.CostCenterRepository
];
let OracleFusionModule = class OracleFusionModule {
};
OracleFusionModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.OracleFusionController],
        providers: [
            oracle_fusion_service_1.OracleFusionService,
            helpers_1.SequlizeOperator,
            clients_1.AdminApiClient,
            clients_1.FusionApiClient,
            clients_1.FusionUaeApiClient,
            helpers_1.DatabaseHelper,
            services_1.FinanceAdminService,
            services_2.ExcelSheetService,
            services_2.SharedAttachmentService,
            clients_1.AttachmentApiClient,
            clients_1.MSGraphApiClient,
            clients_1.HistoryApiClient,
            services_2.SharedNotificationService,
            clients_1.NotificationApiClient,
            ...repositories
        ],
    })
], OracleFusionModule);
exports.OracleFusionModule = OracleFusionModule;
//# sourceMappingURL=oracle-fusion.module.js.map