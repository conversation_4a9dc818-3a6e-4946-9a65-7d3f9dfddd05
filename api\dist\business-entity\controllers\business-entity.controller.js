"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessEntityController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
const dtos_1 = require("../dtos");
const business_entity_level_dto_1 = require("../dtos/response/business-entity-level.dto");
const business_entity_role_dto_1 = require("../dtos/response/business-entity-role.dto");
let BusinessEntityController = class BusinessEntityController {
    constructor(businessEntityService) {
        this.businessEntityService = businessEntityService;
    }
    getBusinessEntitiesForGivenPermissionForUser(request, permission, parentId, lastLevel, isSkipLevelRequired = false, isFilterRequest = false) {
        return this.businessEntityService.getBusinessEntitiesForGivenPermissionForUser(request.currentContext, permission, parentId, lastLevel, isSkipLevelRequired, isFilterRequest);
    }
    getAllBusinessEntityLevels() {
        return this.businessEntityService.getAllBusinessEntityLevels();
    }
    getAllBusinessEntityRoles(entityLevel) {
        return this.businessEntityService.getAllBusinessEntityRoles(entityLevel);
    }
    getUsersByRoleOfAnEntity(entityId, roleName) {
        return this.businessEntityService.getUsersByRoleOfAnEntity(entityId, roleName);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all business entities for a given permission for a user',
        type: dtos_1.BusinessEntityResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'permission',
        type: String,
        description: 'User Permission.',
        required: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'parentId',
        type: Number,
        description: 'Business Entity Parent Id (optional).',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'lastLevel',
        type: String,
        description: 'Last Level To Get Till That Level Of Hierarchy (optional).',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'isSkipLevelRequired',
        type: Boolean,
        description: 'Is Skip Level Required is true this will skip the level and all child will merge in parent from the hierarchy based on condition defined by system. (optional).',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'isFilterRequest',
        type: Boolean,
        description: 'If Is Filter Request is true then it will check if user has permission on project component or not based on that hierarchy will be fetched.  (optional).',
        required: false,
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('permission')),
    __param(2, (0, common_1.Query)('parentId')),
    __param(3, (0, common_1.Query)('lastLevel')),
    __param(4, (0, common_1.Query)('isSkipLevelRequired')),
    __param(5, (0, common_1.Query)('isFilterRequest')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Number, String, Boolean, Boolean]),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getBusinessEntitiesForGivenPermissionForUser", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all business entities for a given permission for a user',
        type: [business_entity_level_dto_1.BusinessEntityLevelResponseDto],
    }),
    (0, common_1.Get)('/entity-levels'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getAllBusinessEntityLevels", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all business entities roles for given entity level',
        type: [business_entity_role_dto_1.BusinessEntityRoleResponseDto],
    }),
    (0, common_1.Get)('/entity-roles'),
    __param(0, (0, common_1.Query)('entityLevel')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getAllBusinessEntityRoles", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get user list by role of an entity',
        type: [business_entity_role_dto_1.BusinessEntityRoleResponseDto],
    }),
    (0, common_1.Get)('/user-list'),
    __param(0, (0, common_1.Query)('entityId')),
    __param(1, (0, common_1.Query)('roleName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getUsersByRoleOfAnEntity", null);
BusinessEntityController = __decorate([
    (0, swagger_1.ApiTags)('Business Entity APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('business-entities'),
    __metadata("design:paramtypes", [services_1.BusinessEntityService])
], BusinessEntityController);
exports.BusinessEntityController = BusinessEntityController;
//# sourceMappingURL=business-entity.controller.js.map