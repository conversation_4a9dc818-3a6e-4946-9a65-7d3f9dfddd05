import { APPROVAL_ACTION_ID_TYPE } from 'src/shared/enums';
export declare class DelegateeRequestDto {
    firstName: string;
    lastName: string;
    loginId: string;
    title: string;
}
export declare class PerformActionOnAfeProposalRequestDto {
    id: number;
    taskId?: number;
    idType: APPROVAL_ACTION_ID_TYPE;
    comments?: string;
    assignedToAfeSubmitter?: boolean;
    attachments?: [];
    delegatee?: DelegateeRequestDto;
}
