"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OneAppModule = void 0;
const common_1 = require("@nestjs/common");
const parallel_identifier_repository_1 = require("../afe-config/repositories/parallel-identifier.repository");
const repositories_1 = require("../afe-proposal/repositories");
const repositories_2 = require("../business-entity/repositories");
const config_service_1 = require("../config/config.service");
const repositories_3 = require("../finance/repositories");
const services_1 = require("../finance/services");
const repositories_4 = require("../notification/repositories");
const repositories_5 = require("../project-component/repositories");
const queue_log_repository_1 = require("../queue/repositories/queue-log.repository");
const repositories_6 = require("../settings/repositories");
const services_2 = require("../settings/services");
const clients_1 = require("../shared/clients");
const helpers_1 = require("../shared/helpers");
const services_3 = require("../shared/services");
const validators_1 = require("../shared/validators");
const services_4 = require("../task/services");
const repositories_7 = require("../workflow/repositories");
const services_5 = require("../workflow/services");
const controllers_1 = require("./controllers");
const services_6 = require("./services");
const repositories = [
    repositories_1.AfeProposalRepository,
    repositories_1.AfeProposalAmountSplitRepository,
    repositories_1.AfeProposalApproverRepository,
    repositories_3.CostCenterRepository,
    repositories_1.AfeProposalLimitDeductionRepository,
    repositories_4.NotificationRepository,
    queue_log_repository_1.QueueLogRepository,
    repositories_7.WorkflowMasterSettingRepository,
    repositories_7.WorkflowMasterStepRepository,
    repositories_7.WorkflowSharedChildLimitRepository,
    repositories_7.WorkflowSharedBucketLimitRepository,
    repositories_5.ProjectComponentRepository,
    repositories_3.CurrencyTypeRepository,
    repositories_3.NaturalAccountNumberRepository,
    repositories_3.CompanyCodeRepository,
    repositories_3.AnalysisCodeRepository,
    repositories_2.EntitySetupRepository,
    repositories_6.SettingsRepository,
    parallel_identifier_repository_1.ParallelIdentifierRepository,
    repositories_3.CompanyCodeRepository,
    repositories_1.UserCostCenterMappingRepository,
    repositories_1.UserProjectComponentMappingRepository
];
let OneAppModule = class OneAppModule {
};
OneAppModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.OneAppController],
        providers: [
            services_6.OneAppService,
            clients_1.TaskApiClient,
            clients_1.AttachmentApiClient,
            validators_1.AfeProposalValidator,
            clients_1.AdminApiClient,
            config_service_1.ConfigService,
            clients_1.MSGraphApiClient,
            services_3.SharedPermissionService,
            helpers_1.SequlizeOperator,
            services_4.TaskService,
            services_5.WorkflowService,
            services_3.ExcelSheetService,
            helpers_1.DatabaseHelper,
            services_3.SharedAttachmentService,
            clients_1.HistoryApiClient,
            services_3.SharedNotificationService,
            services_1.FinanceAdminService,
            services_1.FinanceService,
            services_2.SettingsService,
            clients_1.NotificationApiClient,
            services_3.ConditionCreatorService,
            ...repositories
        ],
    })
], OneAppModule);
exports.OneAppModule = OneAppModule;
//# sourceMappingURL=one-app.module.js.map