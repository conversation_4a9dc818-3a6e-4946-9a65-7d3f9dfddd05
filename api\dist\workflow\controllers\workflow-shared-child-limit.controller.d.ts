import { MessageResponseDto } from 'src/shared/dtos';
import { RequestContext } from 'src/shared/types';
import { NewSharedChildLimitRequestDTO } from '../dtos/request/new-shared-child-limit-request.dto';
import { UpdateSharedChildLimitRequestDTO } from '../dtos/request/update-shared-child-limit-request.dto';
import { GetChildSharedLimitResponseDTO } from '../dtos/response/get-child-shared-limit-response.dto';
import { WorkflowSharedChildLimitService } from '../services';
export declare class WorkflowSharedChildLimitController {
    private workflowSharedChildLimitService;
    constructor(workflowSharedChildLimitService: WorkflowSharedChildLimitService);
    addNewSharedLimit(request: RequestContext, newSharedChildLimitRequestDTO: NewSharedChildLimitRequestDTO): Promise<GetChildSharedLimitResponseDTO>;
    getChilSharedLimitList(parentStepId: number, entityId: number): Promise<GetChildSharedLimitResponseDTO[]>;
    deleteChilSharedLimit(id: number, entityId: number, request: RequestContext): Promise<MessageResponseDto>;
    updateChilSharedLimit(id: number, updateSharedChildLimitRequestDTO: UpdateSharedChildLimitRequestDTO, request: RequestContext): Promise<MessageResponseDto>;
}
