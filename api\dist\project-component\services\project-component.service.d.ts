import { LengthOfCommitmentRepository } from 'src/afe-config/repositories/length-of-commitment.repository';
import { EntityService } from 'src/shared/services';
import { ProjectComponentDto } from '../dtos';
import { LengthOfCommitmentResponseDTO } from '../dtos/response/length-of-commitment-response.dto';
import { ProjectComponentRepository } from '../repositories';
export declare class ProjectComponentService {
    private readonly entityService;
    private readonly projectComponentRepository;
    private readonly lengthOfCommitmentRepository;
    constructor(entityService: EntityService, projectComponentRepository: ProjectComponentRepository, lengthOfCommitmentRepository: LengthOfCommitmentRepository);
    getProjectComponents(requestTypeId?: number, locationId?: number, childProject?: boolean, budgetTypeId?: number): Promise<ProjectComponentDto[]>;
    getLengthOfCommitment(): Promise<LengthOfCommitmentResponseDTO[]>;
}
