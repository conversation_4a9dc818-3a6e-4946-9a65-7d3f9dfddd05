"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerModule = void 0;
const common_1 = require("@nestjs/common");
const services_1 = require("./services");
const controllers_1 = require("./controllers");
const repositories_1 = require("../queue/repositories");
const repositories_2 = require("./repositories");
const clients_1 = require("../shared/clients");
const repositories_3 = require("../afe-proposal/repositories");
const helpers_1 = require("../shared/helpers");
const services_2 = require("../shared/services");
const services_3 = require("../oracle-fusion/services");
const services_4 = require("../finance/services");
const repositories_4 = require("../finance/repositories");
const services_5 = require("../core/services");
const services_6 = require("../business-entity/services");
const sftp_service_1 = require("../sftp-service/sftp.service");
const sftp_client_provider_1 = require("../sftp-service/provider/sftp-client.provider");
const repositories_5 = require("../sftp-service/repositories");
const repositories = [
    repositories_2.SchedulerRepository,
    repositories_1.QueueLogRepository,
    repositories_3.AfeProposalRepository,
    repositories_3.AfeProposalApproverRepository,
    repositories_4.NaturalAccountNumberRepository,
    repositories_4.CostCenterRepository,
    repositories_4.CompanyCodeRepository,
    repositories_4.AnalysisCodeRepository,
    repositories_3.AfeProposalAmountSplitRepository,
    repositories_3.UserProjectComponentMappingRepository,
    repositories_5.DataSharingSchedulerRepository
];
let SchedulerModule = class SchedulerModule {
};
SchedulerModule = __decorate([
    (0, common_1.Module)({
        providers: [
            ...repositories,
            services_1.SchedulerService,
            clients_1.NotificationApiClient,
            services_4.FinanceAdminService,
            clients_1.HistoryApiClient,
            services_2.SharedAttachmentService,
            clients_1.AttachmentApiClient,
            clients_1.FusionUaeApiClient,
            clients_1.AdminApiClient,
            helpers_1.DatabaseHelper,
            helpers_1.SequlizeOperator,
            services_2.ExcelSheetService,
            services_2.SharedNotificationService,
            clients_1.MSGraphApiClient,
            services_3.OracleFusionService,
            clients_1.FusionApiClient,
            services_5.LoggerService,
            common_1.ConsoleLogger,
            services_6.BusinessEntityService,
            sftp_service_1.SftpService,
            sftp_client_provider_1.SftpClientProvider,
            clients_1.TaskApiClient
        ],
        controllers: [controllers_1.SchedulerController]
    })
], SchedulerModule);
exports.SchedulerModule = SchedulerModule;
//# sourceMappingURL=scheduler.module.js.map