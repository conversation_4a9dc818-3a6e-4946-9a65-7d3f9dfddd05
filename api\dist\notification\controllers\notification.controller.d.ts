import { Pagination } from 'src/core/pagination';
import { RequestContext } from 'src/shared/types';
import { NotificationResponseDto, ViewNotificationRequestDto } from '../dtos';
import { NotificationService } from '../services';
export declare class NotificationController {
    private readonly notificationService;
    constructor(notificationService: NotificationService);
    notificationsViewedByUser(request: RequestContext, viewNotificationRequestDto: ViewNotificationRequestDto): Promise<void>;
    getNotificationsListForUser(request: RequestContext, limit?: number, page?: number): Promise<Pagination<NotificationResponseDto>>;
}
