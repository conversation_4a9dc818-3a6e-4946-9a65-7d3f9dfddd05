"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetHistoryResponseDTO = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const enums_1 = require("../../../shared/enums");
class GetHistoryResponseDTO {
    constructor(partial = {}) {
        Object.assign(this, partial);
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'actionPerformed' }),
    (0, class_transformer_1.Expose)({ name: 'actionPerformed' }),
    __metadata("design:type", String)
], GetHistoryResponseDTO.prototype, "action_performed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, name: 'actionComments' }),
    (0, class_transformer_1.Expose)({ name: 'actionComments' }),
    __metadata("design:type", String)
], GetHistoryResponseDTO.prototype, "action_comments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date, name: 'actionDate' }),
    (0, class_transformer_1.Expose)({ name: 'actionDate' }),
    __metadata("design:type", Date)
], GetHistoryResponseDTO.prototype, "action_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'additionalInfo' }),
    (0, class_transformer_1.Expose)({ name: 'additionalInfo' }),
    __metadata("design:type", Object)
], GetHistoryResponseDTO.prototype, "additional_info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String, name: 'createdBy' }),
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetHistoryResponseDTO.prototype, "created_by", void 0);
exports.GetHistoryResponseDTO = GetHistoryResponseDTO;
//# sourceMappingURL=get-history-response.dto.js.map