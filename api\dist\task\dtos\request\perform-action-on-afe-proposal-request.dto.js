"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformActionOnAfeProposalRequestDto = exports.DelegateeRequestDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const enums_1 = require("../../../shared/enums");
class DelegateeRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DelegateeRequestDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DelegateeRequestDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DelegateeRequestDto.prototype, "loginId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DelegateeRequestDto.prototype, "title", void 0);
exports.DelegateeRequestDto = DelegateeRequestDto;
class PerformActionOnAfeProposalRequestDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], PerformActionOnAfeProposalRequestDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], PerformActionOnAfeProposalRequestDto.prototype, "taskId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(enums_1.APPROVAL_ACTION_ID_TYPE),
    (0, swagger_1.ApiProperty)({ enum: enums_1.APPROVAL_ACTION_ID_TYPE }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], PerformActionOnAfeProposalRequestDto.prototype, "idType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], PerformActionOnAfeProposalRequestDto.prototype, "comments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], PerformActionOnAfeProposalRequestDto.prototype, "assignedToAfeSubmitter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], PerformActionOnAfeProposalRequestDto.prototype, "attachments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: DelegateeRequestDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => DelegateeRequestDto),
    __metadata("design:type", DelegateeRequestDto)
], PerformActionOnAfeProposalRequestDto.prototype, "delegatee", void 0);
exports.PerformActionOnAfeProposalRequestDto = PerformActionOnAfeProposalRequestDto;
//# sourceMappingURL=perform-action-on-afe-proposal-request.dto.js.map