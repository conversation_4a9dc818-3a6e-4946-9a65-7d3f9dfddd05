import { AfeConfigService } from '../services';
import { AfeBudgetTypeResponseDto, AfeNatureTypeResponseDto, AfeRequestTypeResponseDto, AfeSubTypeResponseDto, AfeTypeResponseDto, GetLocationByEntitiesRequestDto, LocationsResponseDto, LocationsWithEntityIdResponseDto, ParallelIdentifierResponseDto, QuestionResponseDto } from '../dtos';
import { QUESTION_TYPE } from 'src/shared/enums';
export declare class AfeConfigController {
    private readonly afeConfigService;
    constructor(afeConfigService: AfeConfigService);
    getNavBarByPosition(position: string): Promise<Record<string, any>>;
    getAfeRequestTypes(): Promise<AfeRequestTypeResponseDto[]>;
    getParallelIdentifiers(): Promise<ParallelIdentifierResponseDto[]>;
    getAfeNatureTypesByRequestTypeAndLocation(requestTypeId: number, locationId: number): Promise<AfeNatureTypeResponseDto[]>;
    getAfeTypesByRequestType(requestTypeId: number): Promise<AfeTypeResponseDto[]>;
    getAllAfeTypes(): Promise<AfeTypeResponseDto[]>;
    getAllBudgetType(requestTypeId: number, typeId?: number): Promise<AfeBudgetTypeResponseDto[]>;
    getQuestionsByRequestTypeAndLocation(requestTypeId: number, locationId: number, questionType?: QUESTION_TYPE): Promise<QuestionResponseDto[]>;
    getAfeSubTypes(typeId?: number, entityId?: number): Promise<AfeSubTypeResponseDto[]>;
    getLocations(requestTypeId: number, entityId: number): Promise<LocationsResponseDto[]>;
    getLocationsByEntityIds(getLocationByEntitiesRequestDto: GetLocationByEntitiesRequestDto): Promise<LocationsWithEntityIdResponseDto[]>;
}
