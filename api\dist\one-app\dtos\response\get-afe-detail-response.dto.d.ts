export declare class AttachmentDTO {
    AfeID: number;
    AppType: string;
    AttachmentID: number;
    AttachmentPath: string;
    ContentType: string;
    FileName: string;
}
export declare class GetAfeDetailResponseDTO {
    AFEHistory: [];
    AFEInitiator: string;
    AFENature: string;
    AFERegionType: string;
    AFEStatus: string;
    AFEType: string;
    Approved_Date: Date;
    Attachments: AttachmentDTO[];
    BudgetReferenceNumber: string;
    BudgetType: string;
    BusinessDivision: string;
    CostCenter: string;
    DepartmentName: string;
    ExpenditureAmountInUSD: number;
    ExpenseSummary: string;
    ID: number;
    IsSupplementary: boolean;
    ParentAFEId: number;
    ParentProjectReferenceNumber: string;
    ProjectJustification: string;
    ProjectLeader: string;
    ProjectLeaderContactNumber: string;
    ProjectName: string;
    ProjectReferenceNumber: string;
    RegionName: string;
    Submission_Date: Date;
    SupplementaryAFEs: string;
    TerminalName: string;
    TotalExpenditureAmountinUSD: number;
    UnbudgetedReason: string;
    constructor(partial?: Partial<GetAfeDetailResponseDTO>);
}
