import { WorkflowRuleService } from '../services';
import { CreateWorkflowRuleRequestDto, CreateWorkflowRuleResponseDto, GetWorkflowRuleResponseDto, UpdateWorkflowRuleRequestDto } from '../dtos';
import { RequestContext } from 'src/shared/types';
import { MessageResponseDto } from 'src/shared/dtos';
import { Pagination } from 'src/core/pagination';
export declare class WorkflowRuleController {
    private readonly workflowRuleService;
    constructor(workflowRuleService: WorkflowRuleService);
    createWorkflowRule(createWorkflowRuleRequestDto: CreateWorkflowRuleRequestDto, request: RequestContext): Promise<CreateWorkflowRuleResponseDto>;
    getWorkflowRuleById(id: number): Promise<GetWorkflowRuleResponseDto>;
    deleteWorkflowRuleById(id: number, request: RequestContext): Promise<MessageResponseDto>;
    updateWorkflowRule(updateWorkflowRuleRequestDto: UpdateWorkflowRuleRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    getWorkflowRulesList(limit?: number, page?: number, noLimit?: boolean, searchTerm?: string): Promise<Pagination<GetWorkflowRuleResponseDto>>;
}
