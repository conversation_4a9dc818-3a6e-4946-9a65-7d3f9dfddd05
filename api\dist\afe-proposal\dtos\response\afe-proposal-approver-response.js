"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeProposalApproverResponseDto = exports.ApproverUserDetailResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const enums_1 = require("../../../shared/enums");
const associated_type_enum_1 = require("../../../shared/enums/associated-type.enum");
class ApproverUserDetailResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ApproverUserDetailResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ApproverUserDetailResponseDto.prototype, "loginId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ApproverUserDetailResponseDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ApproverUserDetailResponseDto.prototype, "firstName", void 0);
exports.ApproverUserDetailResponseDto = ApproverUserDetailResponseDto;
class AfeProposalApproverResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AfeProposalApproverResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AfeProposalApproverResponseDto.prototype, "stepId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: ApproverUserDetailResponseDto, isArray: true }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], AfeProposalApproverResponseDto.prototype, "approvers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], AfeProposalApproverResponseDto.prototype, "isCustomUser", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "associateRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "associateType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "associateLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AfeProposalApproverResponseDto.prototype, "sequenceNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "associatedColumn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "parallelIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "approvalSequenceType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "actionBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: ApproverUserDetailResponseDto, required: false }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", ApproverUserDetailResponseDto)
], AfeProposalApproverResponseDto.prototype, "originalApprover", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AfeProposalApproverResponseDto.prototype, "actionDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "actionStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AfeProposalApproverResponseDto.prototype, "comment", void 0);
exports.AfeProposalApproverResponseDto = AfeProposalApproverResponseDto;
//# sourceMappingURL=afe-proposal-approver-response.js.map