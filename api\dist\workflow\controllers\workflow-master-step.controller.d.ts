import { Pagination } from 'src/core/pagination';
import { MessageResponseDto } from 'src/shared/dtos';
import { IPagination } from 'src/shared/interfaces/filter.interface';
import { RequestContext } from 'src/shared/types';
import { CopyStepRequestDto, DeleteStepRequestDto, GetMasterStepsResponseDTO, UpdateStepLimitShareRequestDTO } from '../dtos';
import { NewMasterStepRequestDto } from '../dtos/request/new-master-step-request.dto';
import { StepMovementRequestDto, UpdateStepRequestDto } from '../dtos/request/update-master-step-request.dto';
import { GetAggregateLimitbalanceDTO } from '../dtos/response/get-aggregate-limit-balance.dto';
import { GetChildSharedLimitWithParentDetailDTO } from '../dtos/response/get-child-shared-limit-response.dto';
import { GetExceptionStepsResponseDTO } from '../dtos/response/get-exception-steps-response.dto';
import { GetRoleBasedStepsResponse } from '../dtos/response/get-role-based-steps-response.dto';
import { WorkflowMasterStepService } from '../services';
export declare class WorkflowMasterStepController {
    private workflowMasterStepService;
    constructor(workflowMasterStepService: WorkflowMasterStepService);
    addNewWorkflowStep(request: RequestContext, newMasterStepRequestDto: NewMasterStepRequestDto): Promise<GetMasterStepsResponseDTO>;
    copyStepToOverridden(request: RequestContext, copyStepRequestDto: CopyStepRequestDto): Promise<{
        message: string;
    }>;
    deleteStepFromOverridden(request: RequestContext, deleteStepRequestDto: DeleteStepRequestDto): Promise<{
        message: string;
        overiddenWorkflowIds: any[];
    }>;
    updateWorkflowStep(updateWorkflowStepRequestDto: UpdateStepRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    deleteWorkflowStep(stepId: number, request: RequestContext): Promise<MessageResponseDto>;
    deleteUnpublishedVersionWorkflowStep(stepId: number, request: RequestContext): Promise<MessageResponseDto>;
    childLimitShareStep(stepId: number, updateStepLimitShareRequestDTO: UpdateStepLimitShareRequestDTO, request: RequestContext): Promise<MessageResponseDto>;
    changeApprovalSequence(stepId: number, stepMovementRequestDto: StepMovementRequestDto, request: RequestContext): Promise<MessageResponseDto>;
    getRoleBasedSteps(request: RequestContext, query: IPagination): Promise<Pagination<GetRoleBasedStepsResponse>>;
    getStepDetail(stepId: number): Promise<GetChildSharedLimitWithParentDetailDTO>;
    getStepBalance(stepId: number, request: RequestContext, entityId?: string | null): Promise<GetAggregateLimitbalanceDTO[]>;
    getExceptionSteps(stepId: number): Promise<GetExceptionStepsResponseDTO[]>;
    getWorkflowStepHistory(stepId: number): Promise<Record<string, any>>;
}
