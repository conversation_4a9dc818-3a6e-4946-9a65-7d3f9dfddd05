import { Injectable } from '@nestjs/common';
import { instanceToPlain } from 'class-transformer';
import { isArray, isObject } from 'lodash';
import { Pagination } from 'src/core/pagination';
import { AdminApiClient, AttachmentApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { AD_USER_TYPE, AFE_REQUEST_TYPE, ATTACHMENT_ENTITY_TYPE, HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE, HttpStatus } from 'src/shared/enums';
import { HttpException } from 'src/shared/exceptions';
import { DatabaseHelper, multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import { AFE_REQUEST_TYPE_WITH_ID_MAPPING, REQUEST_TYPE } from 'src/shared/mappings';
import { ExcelSheetService, SharedAttachmentService } from 'src/shared/services';
import { AddHistoryRequest, CurrentContext } from 'src/shared/types';
import {
    AddCostCenterRequestDto,
    AnalysisCodeResponseDto,
    CompanyCodeResponseDto,
    CostCenterResponseDto,
    CreateAnalysisCodeRequestDto,
    CreateCompanyCodeRequestDto,
    CreateNaturalAccountNumberRequestDto,
    ImportDataRequestDto,
    NaturalAccountResponseDto,
    SectionsRequestDto,
    ToggleActiveStateCompanyCodeRequestDto,
    UpdateAnalysisCodeRequestDto,
    UpdateCompanyCodeRequestDto,
    UpdateCostCenterRequestDto,
    UpdateFusionIntegrationRequestDto,
    UpdateMultiNaturalAccountConfigRequestDto,
    UpdateNaturalAccountNumberRequestDto,
} from '../dtos';
import { GetHistoryResponseDTO } from '../dtos/response/get-history-response.dto';
import { CompanyCode } from '../models';
import {
    NaturalAccountNumberRepository,
    CostCenterRepository,
    CompanyCodeRepository,
    AnalysisCodeRepository,
} from '../repositories';
import { UploadEvidenceRequestDto } from 'src/afe-proposal/dtos';
import { ATTACHMENT_REL_PATH } from 'src/shared/constants';
import { AttachmentContentResponseDto } from 'src/attachment/dtos';

@Injectable()
export class FinanceAdminService {
    constructor(
        private readonly naturalAccountNumberRepository: NaturalAccountNumberRepository,
        private readonly costCenterRepository: CostCenterRepository,
        private readonly companyCodeRepository: CompanyCodeRepository,
        private readonly analysisCodeRepository: AnalysisCodeRepository,
        private readonly adminApiClient: AdminApiClient,
        private readonly databaseHelper: DatabaseHelper,
        private readonly excelSheetService: ExcelSheetService,
        private readonly mSGraphApiClient: MSGraphApiClient,
        private readonly historyApiClient: HistoryApiClient,
        private readonly sharedAttachmentService: SharedAttachmentService,
        private readonly attachmentApiClient: AttachmentApiClient,

    ) { }

    /**
     * Create new company code for an business entity.
     * @param createCompanyCodeRequestDto 
     * @param currentContext 
     */
    public async createCompanyCode(createCompanyCodeRequestDto: CreateCompanyCodeRequestDto, currentContext: CurrentContext): Promise<CompanyCodeResponseDto> {
        const { code, name, entityId } = createCompanyCodeRequestDto;

        const existingCompanyCode = await this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
        if (existingCompanyCode) {
            throw new HttpException(`Company code is already exist for this business entity.`, HttpStatus.CONFLICT);
        }
        const businessEntity = await this.adminApiClient.getBusinessEntityDetailsById(entityId);
        if (!businessEntity) {
            throw new HttpException('Invalid business entity id.', HttpStatus.BAD_REQUEST);
        }
        if (!businessEntity.is_node) {
            throw new HttpException(`Company code ${code} can only be added at node level.`, HttpStatus.BAD_REQUEST);
        }

        const { code: entityCode, entity_type: entityType } = businessEntity;

        let company: CompanyCode = null;
        let historyRequest = [];

        await this.databaseHelper.startTransaction(async () => {
            company = await this.companyCodeRepository.createCompanyCode({
                code: code,
                name,
                entityId,
                entityCode,
                entityType,
                fusionIntegrationForRequestTypeIds: []
            }, currentContext);

            const defaultValue = {
                title: 'DEFAULT',
                companyCodeId: company.id,
                requestTypeIds: [AFE_REQUEST_TYPE_WITH_ID_MAPPING[AFE_REQUEST_TYPE.CAPEX], AFE_REQUEST_TYPE_WITH_ID_MAPPING[AFE_REQUEST_TYPE.OPEX]]
            };

            await this.naturalAccountNumberRepository.createNaturalAccountNumber({ ...defaultValue, number: '********' }, currentContext);
            await this.analysisCodeRepository.createAnalysisCode({ ...defaultValue, code: '000000' }, currentContext);

            historyRequest.push({
                created_by: currentContext.user.username,
                entity_id: entityId,
                entity_type: HISTORY_ENTITY_TYPE.COMPANY,
                action_performed: HISTORY_ACTION_TYPE.ADD,
                comments: 'A new company ' + createCompanyCodeRequestDto.name + ' (' + createCompanyCodeRequestDto.code + ') ' + ' has been created.',
                additional_info: {
                    companyDetail: createCompanyCodeRequestDto
                }
            });
        });

        if (historyRequest.length) {
            await this.historyApiClient.addBulkRequestHistory(historyRequest);
        }
        return singleObjectToInstance(CompanyCodeResponseDto, company);
    }

    /**
     * Add cost center for the company code.
     * @param addCostCenterRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async addCostCenter(addCostCenterRequestDto: AddCostCenterRequestDto, currentContext: CurrentContext): Promise<CostCenterResponseDto> {
        const { companyCodeId, code } = addCostCenterRequestDto;

        const isCompanyCodeExists = await this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
        if (!isCompanyCodeExists) {
            throw new HttpException('Company does\'t exist or deactivated. ', HttpStatus.BAD_REQUEST);
        }

        const isCodeExistsForCompanyCode = await this.costCenterRepository.isCostCenterExistForCompanyCode(code, companyCodeId);

        if (isCodeExistsForCompanyCode) {
            throw new HttpException(`Cost center code ${code} already exists in company code.`, HttpStatus.CONFLICT);
        }

        this.isDuplicateSectionExist(addCostCenterRequestDto.sectionHead);

        const response = await this.costCenterRepository.createCostCenter(addCostCenterRequestDto, currentContext);

        await this.historyApiClient.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: response.id,
            entity_type: HISTORY_ENTITY_TYPE.COST_CENTER,
            action_performed: HISTORY_ACTION_TYPE.ADD,
            comments: 'A new cost center ' + addCostCenterRequestDto.name + ' (' + addCostCenterRequestDto.code + ') ' + ' has been created.',
            additional_info: {
                costCenterDetail: addCostCenterRequestDto
            }
        });

        return singleObjectToInstance(CostCenterResponseDto, response);
    }

    isDuplicateSectionExist(sectionHeads: SectionsRequestDto[]) {
        const titles = sectionHeads.map(obj => obj.title);
        const uniqueTitles = new Set(titles);

        if (titles.length !== uniqueTitles.size) {
            throw new HttpException(`Duplicate section available.`, HttpStatus.BAD_REQUEST);
        }

        return false;
    }

    /**
     * Create new analysis code for the company.
     * @param createAnalysisCodeRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async createAnalysisCode(createAnalysisCodeRequestDto: CreateAnalysisCodeRequestDto, currentContext: CurrentContext): Promise<AnalysisCodeResponseDto> {
        const { companyCodeId, code } = createAnalysisCodeRequestDto;

        const isCompanyCodeExists = await this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
        if (!isCompanyCodeExists) {
            throw new HttpException('Company does\'t exist or deactivated. ', HttpStatus.BAD_REQUEST);
        }

        const isCodeExist = await this.analysisCodeRepository.isAnalysisCodeExistForCompanyCode(code, companyCodeId);

        if (isCodeExist) {
            throw new HttpException(`Analysis code ${code} already exists for company code.`, HttpStatus.CONFLICT);
        }

        const response = await this.analysisCodeRepository.createAnalysisCode(createAnalysisCodeRequestDto, currentContext);
        return singleObjectToInstance(AnalysisCodeResponseDto, response);
    }

    /**
     * Create new natural account number.
     * @param createNaturalAccountNumberRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async createNatualAccountNumber(createNaturalAccountNumberRequestDto: CreateNaturalAccountNumberRequestDto, currentContext: CurrentContext): Promise<NaturalAccountResponseDto> {
        const { companyCodeId, number } = createNaturalAccountNumberRequestDto;

        const isCompanyCodeExists = await this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
        if (!isCompanyCodeExists) {
            throw new HttpException('Company does\'t exist or deactivated. ', HttpStatus.BAD_REQUEST);
        }

        const isNumberExist = await this.naturalAccountNumberRepository.isNaturalAccountNumberExistForCompanyCode(number, companyCodeId);

        if (isNumberExist) {
            throw new HttpException(`Natural account number ${number} already exists for company code.`, HttpStatus.CONFLICT);
        }

        const response = await this.naturalAccountNumberRepository.createNaturalAccountNumber(createNaturalAccountNumberRequestDto, currentContext);
        return singleObjectToInstance(NaturalAccountResponseDto, response);
    }


    /**
     * Update the details of company code.
     * @param updateCompanyCodeRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async updateCompanyCodeDetail(
        updateCompanyCodeRequestDto: UpdateCompanyCodeRequestDto,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {
        const { id, code, entityId } = updateCompanyCodeRequestDto;

        const businessEntity = await this.adminApiClient.getBusinessEntityDetailsById(entityId);
        if (!businessEntity) {
            throw new HttpException('Invalid business entity id.', HttpStatus.BAD_REQUEST);
        }
        if (!businessEntity.is_node) {
            throw new HttpException(`Company code ${code} can only be added at node level.`, HttpStatus.BAD_REQUEST);
        }

        const { code: entityCode, entity_type: entityType } = businessEntity;
        await this.companyCodeRepository.updateCompanyCodeDetailById(id, { code: code, entityId, entityCode, entityType }, currentContext);

        await this.historyApiClient.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: entityId,
            entity_type: HISTORY_ENTITY_TYPE.COMPANY,
            action_performed: HISTORY_ACTION_TYPE.UPDATE,
            comments: 'Company detail has been updated.',
            additional_info: {
                updatedCompanyDetail: updateCompanyCodeRequestDto
            }
        });

        return { message: 'Company code detail has been updated successfully.' };
    }

    /**
     * Update cost center details.
     * @param updateCostCenterRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async updateCostCenterDetail(
        updateCostCenterRequestDto: UpdateCostCenterRequestDto,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {
        const { id, companyCodeId, code } = updateCostCenterRequestDto;

        const costCenterDetails = await this.costCenterRepository.getCostCenterById(id);

        if (!costCenterDetails) {
            throw new HttpException(`Invalid cost center id.`, HttpStatus.BAD_REQUEST);
        }

        if (costCenterDetails.code !== code) {
            const isCodeExistsForCompanyCode = await this.costCenterRepository.isCostCenterExistForCompanyCode(code, companyCodeId);
            if (isCodeExistsForCompanyCode) {
                throw new HttpException(`Cost center code ${code} already exists in company code.`, HttpStatus.CONFLICT);
            }
        }

        this.isDuplicateSectionExist(updateCostCenterRequestDto.sectionHead);

        await this.costCenterRepository.updateCostCenterDetailById(id, updateCostCenterRequestDto, currentContext);

        await this.historyApiClient.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: id,
            entity_type: HISTORY_ENTITY_TYPE.COST_CENTER,
            action_performed: HISTORY_ACTION_TYPE.UPDATE,
            comments: 'Cost center detail has been updated.',
            additional_info: {
                updatedCostCenterDetail: updateCostCenterRequestDto,
                currentCostCenterDetail: costCenterDetails
            }
        });

        return { message: 'Cost center detail has been updated successfully.' };
    }

    /**
     * Update fusion Integration.
     * @param UpdateFusionIntegrationRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async updateFusionIntegration(
        UpdateFusionIntegrationRequestDto: UpdateFusionIntegrationRequestDto,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {
        const { id, requestTypeIds: fusionIntegrationForRequestTypeIds } = UpdateFusionIntegrationRequestDto;
        const companyCodeDetails = await this.companyCodeRepository.getCompanyCodeById(id);
        if (!companyCodeDetails) {
            throw new HttpException(`Invalid company code id.`, HttpStatus.BAD_REQUEST);
        }
        await this.companyCodeRepository.updateCompanyCodeDetailById(id, { fusionIntegrationForRequestTypeIds }, currentContext);

        await this.historyApiClient.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: companyCodeDetails.entityId,
            entity_type: HISTORY_ENTITY_TYPE.COMPANY,
            action_performed: HISTORY_ACTION_TYPE.UPDATE,
            comments: 'Fusion Integration has been updated',
            additional_info: {
                updatedCompanyDetail: UpdateFusionIntegrationRequestDto
            }
        });
        return { message: 'Fusion Integration Updated Successfully.' };
    }

    /**
     * Update analyis code details.
     * @param updateAnalysisCodeRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async updateAnalysisCodeDetail(
        updateAnalysisCodeRequestDto: UpdateAnalysisCodeRequestDto,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {
        const { id, companyCodeId, code } = updateAnalysisCodeRequestDto;

        const analysisCodeDetails = await this.analysisCodeRepository.getAnalysisCodeById(id);
        if (!analysisCodeDetails) {
            throw new HttpException(`Invalid analysis code id.`, HttpStatus.BAD_REQUEST);
        }
        if (analysisCodeDetails.code !== code) {
            const isCodeExist = await this.analysisCodeRepository.isAnalysisCodeExistForCompanyCode(code, companyCodeId);
            if (isCodeExist) {
                throw new HttpException(`Analysis code ${code} already exists for company code.`, HttpStatus.CONFLICT);
            }
        }

        await this.analysisCodeRepository.updateAnalysisCodeDetailById(id, updateAnalysisCodeRequestDto, currentContext);

        return { message: 'Analysis code detail has been updated successfully.' };
    }

    /**
     * Update natural account number details.
     * @param updateAnalysisCodeRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async updateNaturalAccountNumber(
        updateNaturalAccountNumberRequestDto: UpdateNaturalAccountNumberRequestDto,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {
        const { id, companyCodeId, number } = updateNaturalAccountNumberRequestDto;
        const naturalAccNoDetails = await this.naturalAccountNumberRepository.getNaturalAccountNumberById(id);
        if (!naturalAccNoDetails) {
            throw new HttpException(`Invalid natural account number id.`, HttpStatus.BAD_REQUEST);
        }
        if (naturalAccNoDetails.number !== number) {
            const isNumberExist = await this.naturalAccountNumberRepository.isNaturalAccountNumberExistForCompanyCode(number, companyCodeId);
            if (isNumberExist) {
                throw new HttpException(`Natural account number ${number} already exists for company code.`, HttpStatus.CONFLICT);
            }
        }

        await this.naturalAccountNumberRepository.updateNaturalAccountNumberDetailById(id, updateNaturalAccountNumberRequestDto, currentContext);
        return { message: 'Natural account number detail has been updated successfully.' };
    }

    /**
     * Delete company code by its id.
     * @param companyCodeId 
     * @param currentContext 
     * @returns 
     */
    public async deleteCompanyCodeById(companyCodeId: number, currentContext: CurrentContext): Promise<MessageResponseDto> {

        const companyDetail = await this.companyCodeRepository.getCompanyCodeById(companyCodeId);

        if (companyDetail) {
            const result = await this.companyCodeRepository.deleteCompanyCodeById(companyCodeId, currentContext);
            if (result) {
                await this.historyApiClient.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: companyDetail.entityId,
                    entity_type: HISTORY_ENTITY_TYPE.COMPANY,
                    action_performed: HISTORY_ACTION_TYPE.UPDATE,
                    comments: 'Company has been deleted',
                    additional_info: null
                });
                return { message: 'Company code has been deleted successfully.' };
            }
            throw new HttpException('Unable to delete the company code.', HttpStatus.BAD_REQUEST);
        }

        throw new HttpException('Company not found or already deleted.', HttpStatus.NOT_FOUND);

    }

    /**
     * Delete cost center by its id.
     * @param companyCodeId 
     * @param currentContext 
     * @returns 
     */
    public async deleteCostCenterById(costCenterId: number, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const result = await this.costCenterRepository.deleteCostCenterById(costCenterId, currentContext);
        if (result) {
            await this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: costCenterId,
                entity_type: HISTORY_ENTITY_TYPE.COST_CENTER,
                action_performed: HISTORY_ACTION_TYPE.DELETE,
                comments: 'Cost center detail has been deleted.',
                additional_info: null
            });

            return { message: 'Cost center has been deleted successfully.' };
        }

        throw new HttpException('Unable to delete the cost center.', HttpStatus.BAD_REQUEST);
    }

    /**
     * Delete analysis code by its id.
     * @param analysisCodeId 
     * @param currentContext 
     * @returns 
     */
    public async deleteAnalysisCodeById(analysisCodeId: number, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const analyisCode = await this.analysisCodeRepository.getAnalysisCodeById(analysisCodeId);
        if (!analyisCode) {
            throw new HttpException('Analysis code doesn\'t exist', HttpStatus.BAD_REQUEST);
        }

        const analyisCodesCountForCompany = await this.analysisCodeRepository.countAnalysisCodesForCompany(analyisCode.companyCodeId);
        if (analyisCodesCountForCompany === 1) {
            throw new HttpException('Aleast one analysis code should be associate with the company', HttpStatus.BAD_REQUEST);
        }
        const result = await this.analysisCodeRepository.deleteAnalysisCodeById(analysisCodeId, currentContext);
        if (result) {
            return { message: 'Analysis code has been deleted successfully.' };
        }
        throw new HttpException('Unable to delete the analysis code.', HttpStatus.BAD_REQUEST);
    }

    /**
     * Delete natural account number by its id.
     * @param naturalAccountNoId 
     * @param currentContext 
     * @returns 
     */
    public async deleteNaturalAccountNumberById(naturalAccountNoId: number, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const naturalAccountNo = await this.naturalAccountNumberRepository.getNaturalAccountNumberById(naturalAccountNoId);
        if (!naturalAccountNo) {
            throw new HttpException('Natural account number doesn\'t exist', HttpStatus.BAD_REQUEST);
        }
        const naturalAccountNumbersCountForCompany = await this.naturalAccountNumberRepository.countNaturalAccountNumbersForCompany(naturalAccountNo.companyCodeId);
        if (naturalAccountNumbersCountForCompany === 1) {
            throw new HttpException('Aleast one natural account number should be associate with the company', HttpStatus.BAD_REQUEST);
        }
        const result = await this.naturalAccountNumberRepository.deleteNaturalAccountNumberById(naturalAccountNoId, currentContext);
        if (result) {
            return { message: 'Natural account number has been deleted successfully.' };
        }
        throw new HttpException('Unable to delete the natural account number.', HttpStatus.BAD_REQUEST);
    }

    /**
     * Return the company codes list for an business entity.
     * @param entityId 
     * @param limit 
     * @param page 
     * @returns 
     */
    public async getCompanyCodesListByEntityId(entityId: number, limit?: number, page?: number): Promise<Pagination<CompanyCodeResponseDto>> {
        const { rows, count } = await this.companyCodeRepository.getCompanyCodesListByEntityId(entityId, limit, page);
        const records = multiObjectToInstance(CompanyCodeResponseDto, rows);
        return new Pagination({ records, total: count });
    }

    /**
     * Return paginated cost centers by company code id.
     * @param companyCodeId 
     * @param limit 
     * @param page 
     * @returns 
     */
    public async getCostCentersByCompanyCodeId(companyCodeId: number, limit?: number, page?: number): Promise<Pagination<CostCenterResponseDto>> {
        const { rows, count } = await this.costCenterRepository.getCostCentersListByCompanyCodeId(companyCodeId, limit, page);

        const records = multiObjectToInstance(CostCenterResponseDto, rows);
        return new Pagination({ records, total: count });
    }

    /**
     * Return paginated analysis codes by company code id.
     * @param companyCodeId 
     * @param limit 
     * @param page 
     * @returns 
     */
    public async getAnalsysisCodesByCompanyCodeId(companyCodeId: number, limit?: number, page?: number): Promise<Pagination<AnalysisCodeResponseDto>> {
        const { rows, count } = await this.analysisCodeRepository.getAnalysisCodesListByCompanyCodeId(companyCodeId, limit, page);
        const records = multiObjectToInstance(AnalysisCodeResponseDto, rows);
        return new Pagination({ records, total: count });
    }

    /**
     * Return paginated natural account numbers by company code id.
     * @param companyCodeId 
     * @param limit 
     * @param page 
     * @returns 
     */
    public async getNaturalAccountNumbersByCompanyCodeId(companyCodeId: number, limit?: number, page?: number): Promise<Pagination<NaturalAccountResponseDto>> {
        const { rows, count } = await this.naturalAccountNumberRepository.getNaturalAccountNumbersListByCompanyCodeId(companyCodeId, limit, page);
        const records = multiObjectToInstance(NaturalAccountResponseDto, rows);
        return new Pagination({ records, total: count });
    }

    public async getCapexNaturalAccountNumbersByCompanyCodeId(companyCodeId: number) {
        return await this.naturalAccountNumberRepository.getNaturalAccountNumbersByRequestType(1, companyCodeId);
    }

    /**
     * Toggle the state of the company code of an entity.
     * @param entityId 
     * @param companyCodeId 
     * @param active 
     * @returns 
     */
    public async toggleActiveStateOfCompany(
        toggleActiveStateCompanyCodeRequestDto: ToggleActiveStateCompanyCodeRequestDto,
        currentContext: CurrentContext
    ): Promise<MessageResponseDto> {
        const { companyCodeId, entityId, active } = toggleActiveStateCompanyCodeRequestDto;

        const isCompanyExist = await this.companyCodeRepository.isCompanyCodeExistByIdAndEntityId(companyCodeId, entityId);

        if (isCompanyExist) {
            throw new HttpException('Company code doesn\'t exist.', HttpStatus.NOT_FOUND);
        }

        if (active) {
            await this.databaseHelper.startTransaction(async () => {
                await this.companyCodeRepository.markEntityCompanyCodeInactiveExceptSpecificId(companyCodeId, entityId, currentContext);
                await this.companyCodeRepository.markCompanyCodeActive(companyCodeId, currentContext);
            });
        } else {
            await this.companyCodeRepository.markCompanyCodeInactiveById(companyCodeId, currentContext);
        }

        await this.historyApiClient.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: entityId,
            entity_type: HISTORY_ENTITY_TYPE.COMPANY,
            action_performed: HISTORY_ACTION_TYPE.UPDATE,
            comments: `Company has been mark ${active ? 'active' : 'inactive'}.`,
            additional_info: {
                companyDetail: toggleActiveStateCompanyCodeRequestDto
            }
        });

        return { message: `Company code has been mark ${active ? 'active' : 'inactive'} successfully.` };
    }

    /**
     * Get current active company code of an entity.
     * @param entityId 
     * @returns 
     */
    public async getEntityActiveCompanyCodeDetails(entityId: number, throwError: boolean = true): Promise<CompanyCodeResponseDto> {
        const companyCode = await this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
        if (!companyCode && throwError) {
            throw new HttpException('Company code doesn\'t exist.', HttpStatus.NOT_FOUND);
        }
        return singleObjectToInstance(CompanyCodeResponseDto, companyCode);
    }

    /**
     * Deactivate company code.
     * @param entityId 
     * @returns 
     */
    public async deactivateCompanyCode(companyCodeId: number, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const companyCode = await this.companyCodeRepository.getCompanyCodeById(companyCodeId);
        if (!companyCode) {
            throw new HttpException('Company code doesn\'t exist or deactivated', HttpStatus.NOT_FOUND);
        }
        await this.companyCodeRepository.markCompanyCodeInactiveById(companyCodeId, currentContext);

        //TODO: May be needed in future.
        // await this.databaseHelper.startTransaction(async () => {
        //     await this.companyCodeRepository.markCompanyCodeInactiveById(companyCodeId, currentContext);
        //     await this.costCenterRepository.markCostCentersInactiveByCompanyId(companyCodeId, currentContext);
        //     await this.analysisCodeRepository.markAnalysisCodeInactiveByCompanyId(companyCodeId, currentContext);
        //     await this.naturalAccountNumberRepository.markNaturalAccountNumbersInactiveByCompanyId(companyCodeId, currentContext);
        // });
        await this.historyApiClient.addRequestHistory({
            created_by: currentContext.user.username,
            entity_id: companyCode.entityId,
            entity_type: HISTORY_ENTITY_TYPE.COMPANY,
            action_performed: HISTORY_ACTION_TYPE.DEACTIVATED,
            comments: 'Company ' + companyCode.name + ' (' + companyCode.code + ') has been deactivated.',
            additional_info: {
                companyDetail: companyCode
            }
        });

        return { message: `Company code has been deactivated successfully.` };
    }

    /**
     * Import analysis code for the company.
     * @param importDataRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async importAnalysisCode(importDataRequestDto: ImportDataRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const { companyCodeId, bufferData } = importDataRequestDto;

        const isCompanyCodeExists = await this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);

        if (!isCompanyCodeExists) {
            throw new HttpException('Company does\'t exist or deactivated. ', HttpStatus.BAD_REQUEST);
        }

        const arrayBufferObj = new ArrayBuffer(Object.keys(bufferData).length);

        const arrayBuffer = new Uint8Array(arrayBufferObj);
        for (let i = 0; i < Object.keys(bufferData).length; ++i) {
            arrayBuffer[i] = bufferData[i];
        }

        const analysisCodeList = await this.excelSheetService.readAnalysisCodeSheet(arrayBuffer);

        let deletedList = [];
        let newList = [];
        let addedCodes = [];
        let updateList = [];

        for (let i = 0; i < analysisCodeList.length; i++) {

            const analysisCodeDetail = analysisCodeList[i];

            const prevAnalysisCodeDetail = await this.analysisCodeRepository.getAnalysisCodeForCompanyCode(analysisCodeDetail.code.toString(), companyCodeId);

            if (!prevAnalysisCodeDetail && analysisCodeDetail.isDeleted) {
                throw new HttpException('Analysis code ' + analysisCodeDetail.code + ' is not available to delete. Issue at [B' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
            }

            if (addedCodes.includes(analysisCodeDetail.code)) {
                throw new HttpException('Analysis code ' + analysisCodeDetail.code + ' already added, duplicate code at [B' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
            }

            if (prevAnalysisCodeDetail && analysisCodeDetail.isDeleted) {
                deletedList.push(analysisCodeDetail.code.toString());
            } else {
                const detail = {
                    title: analysisCodeDetail.title,
                    code: analysisCodeDetail.code.toString(),
                    companyCodeId,
                    requestTypeIds: analysisCodeDetail.requestTypes ? REQUEST_TYPE[analysisCodeDetail.requestTypes.toUpperCase()] : []
                };

                if (prevAnalysisCodeDetail) {
                    let updateRequired = true;

                    if ((detail.title === prevAnalysisCodeDetail.title)) {
                        if ((detail.requestTypeIds.length === prevAnalysisCodeDetail.requestTypeIds.length) &&
                            detail.requestTypeIds.every((val, index) => val === prevAnalysisCodeDetail.requestTypeIds[index])
                        ) {
                            updateRequired = false;
                        }
                    }
                    if (updateRequired) {
                        updateList.push(detail);
                    }
                } else {
                    newList.push(detail);
                }
            }

            addedCodes.push(analysisCodeDetail.code)
        }

        return await this.databaseHelper.startTransaction(async () => {

            if (newList.length) {
                await this.analysisCodeRepository.addBulkAnalysisCode(newList, currentContext);
            }

            if (deletedList.length) {
                await this.analysisCodeRepository.bulkDeleteAnalysisCode(deletedList, companyCodeId, currentContext);
            }

            if (updateList.length) {
                for (let i = 0; i < updateList.length; i++) {
                    await this.analysisCodeRepository.updateAnalysisCodeDetailByCode(
                        updateList[i].code,
                        companyCodeId,
                        {
                            title: updateList[i].title,
                            requestTypeIds: updateList[i].requestTypeIds
                        },
                        currentContext);
                }
            }

            return { message: `Data has been imported successfully.` };
        });
    }

    /**
     * Import natural account number for the company.
     * @param importDataRequestDto 
     * @param currentContext 
     * @returns 
     */
    public async importNaturalAccount(importDataRequestDto: ImportDataRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const { companyCodeId, bufferData } = importDataRequestDto;

        const isCompanyCodeExists = await this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);

        if (!isCompanyCodeExists) {
            throw new HttpException('Company does\'t exist or deactivated. ', HttpStatus.BAD_REQUEST);
        }

        const arrayBufferObj = new ArrayBuffer(Object.keys(bufferData).length);

        const arrayBuffer = new Uint8Array(arrayBufferObj);
        for (let i = 0; i < Object.keys(bufferData).length; ++i) {
            arrayBuffer[i] = bufferData[i];
        }

        const naturalAccountList = await this.excelSheetService.readNaturalAccountSheet(arrayBuffer);

        let deletedList = [];
        let newList = [];
        let addedNumbers = [];
        let updateList = [];

        for (let i = 0; i < naturalAccountList.length; i++) {

            const naturalAccountDetail = naturalAccountList[i];

            const prevNaturalAccountDetail = await this.naturalAccountNumberRepository.getNaturalAccountNumberForCompanyCode(naturalAccountDetail.number.toString(), companyCodeId);

            if (!prevNaturalAccountDetail && naturalAccountDetail.isDeleted) {
                throw new HttpException('Natural account number ' + naturalAccountDetail.number + ' is not available to delete. Issue at [B' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
            }

            if (addedNumbers.includes(naturalAccountDetail.number)) {
                throw new HttpException('Natural account number ' + naturalAccountDetail.number + ' already added, duplicate number at [B' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
            }

            if (prevNaturalAccountDetail && naturalAccountDetail.isDeleted) {
                deletedList.push(naturalAccountDetail.number.toString());
            } else {
                const detail = {
                    title: naturalAccountDetail.title,
                    number: naturalAccountDetail.number.toString(),
                    companyCodeId,
                    requestTypeIds: naturalAccountDetail.requestTypes ? REQUEST_TYPE[naturalAccountDetail.requestTypes.toUpperCase()] : []
                };

                if (prevNaturalAccountDetail) {
                    let updateRequired = true;

                    if ((detail.title === prevNaturalAccountDetail.title)) {
                        if ((detail.requestTypeIds.length === prevNaturalAccountDetail.requestTypeIds.length) &&
                            detail.requestTypeIds.every((val, index) => val === prevNaturalAccountDetail.requestTypeIds[index])
                        ) {
                            updateRequired = false;
                        }
                    }
                    if (updateRequired) {
                        updateList.push(detail);
                    }
                } else {
                    newList.push(detail);
                }
            }

            addedNumbers.push(naturalAccountDetail.number)
        }

        return await this.databaseHelper.startTransaction(async () => {
            if (newList.length) {
                await this.naturalAccountNumberRepository.addBulkNaturalAccountNumbers(newList, currentContext);
            }

            if (deletedList.length) {
                await this.naturalAccountNumberRepository.bulkDeleteNaturalAccountNumbers(deletedList, companyCodeId, currentContext);
            }

            if (updateList.length) {
                for (let i = 0; i < updateList.length; i++) {
                    await this.naturalAccountNumberRepository.updateNaturalAccountNumberDetailByCode(
                        updateList[i].number,
                        companyCodeId,
                        {
                            title: updateList[i].title,
                            requestTypeIds: updateList[i].requestTypeIds
                        },
                        currentContext);
                }
            }

            return { message: `Data has been imported successfully.` };
        });

    }

    /**
    * Import cost center for the company.
    * @param importDataRequestDto 
    * @param currentContext 
    * @returns 
    */
    public async importCostCenter(importDataRequestDto: ImportDataRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto> {
        const { companyCodeId, bufferData } = importDataRequestDto;

        const isCompanyCodeExists = await this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);

        if (!isCompanyCodeExists) {
            throw new HttpException('Company does\'t exist or deactivated. ', HttpStatus.BAD_REQUEST);
        }

        const arrayBufferObj = new ArrayBuffer(Object.keys(bufferData).length);

        const arrayBuffer = new Uint8Array(arrayBufferObj);
        for (let i = 0; i < Object.keys(bufferData).length; ++i) {
            arrayBuffer[i] = bufferData[i];
        }

        const costCenterList = await this.excelSheetService.readCostCenterSheet(arrayBuffer);

        let deletedList = [];
        let newList = [];
        let addedCodes = [];
        let updateList = [];
        let historyRequest = [];

        for (let i = 0; i < costCenterList.length; i++) {

            const costCenterDetail = { ...costCenterList[i], sectionHead: costCenterList[i].sections };

            const prevCostCenterDetail = await this.costCenterRepository.gerCostCenterForCompanyCode(costCenterDetail.code.toString(), companyCodeId);

            if (!prevCostCenterDetail && costCenterDetail.isDeleted) {
                throw new HttpException('Cose center code ' + costCenterDetail.code + ' is not available to delete. Issue at [B' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
            }

            if (addedCodes.includes(costCenterDetail.code)) {
                throw new HttpException('Cose center code ' + costCenterDetail.code + ' already added, duplicate code at [B' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
            }

            if (prevCostCenterDetail && costCenterDetail.isDeleted) {
                deletedList.push(costCenterDetail.code.toString());
                historyRequest.push({
                    created_by: currentContext.user.username,
                    entity_id: costCenterDetail.id,
                    entity_type: HISTORY_ENTITY_TYPE.COST_CENTER,
                    action_performed: HISTORY_ACTION_TYPE.DELETE,
                    comments: 'Cost center detail has been deleted.',
                    additional_info: null
                });
            } else {

                const detail = {
                    name: costCenterDetail.name,
                    code: costCenterDetail.code.toString(),
                    operating: costCenterDetail.operating,
                    departmentHead: isObject(costCenterDetail.departmentHead) ? costCenterDetail.departmentHead.text : costCenterDetail.departmentHead,
                    sectionHead: costCenterDetail.sectionHead ? (isObject(costCenterDetail.sectionHead) ? costCenterDetail.sectionHead.text : costCenterDetail.sectionHead) : '',
                    companyCodeId,
                };

                if (prevCostCenterDetail) {
                    //UPDATE LIST
                    let updateDetail = null;

                    if ((detail.name !== prevCostCenterDetail.name)) {
                        updateDetail = {
                            ...updateDetail,
                            name: detail.name
                        }
                    }
                    if ((detail.operating !== prevCostCenterDetail.operating)) {
                        updateDetail = {
                            ...updateDetail,
                            operating: detail.operating
                        }
                    }

                    if ((detail.departmentHead.toLowerCase() !== prevCostCenterDetail.departmentHead.mail.toLowerCase()) && (detail.departmentHead.toLowerCase() !== prevCostCenterDetail.departmentHead.userPrincipalName.toLowerCase())) {
                        const userDetail = await this.mSGraphApiClient.getUserDetails(detail.departmentHead);

                        if (!userDetail) {
                            throw new HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [D' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
                        }

                        updateDetail = {
                            ...updateDetail,
                            departmentHead: {
                                ...userDetail,
                                loginId: userDetail.userType === AD_USER_TYPE.GUEST ? userDetail?.mail : userDetail.userPrincipalName
                            }
                        }
                    }

                    const prevSectionHead = prevCostCenterDetail?.sectionHead?.length ? prevCostCenterDetail?.sectionHead.map(
                        (entry: any) => `${entry.title}#${(entry.user.userType === AD_USER_TYPE.GUEST) ? entry.user.mail.toLowerCase() : entry.user.userPrincipalName.toLowerCase()}`
                    ).join(";") : ''

                    if (detail.sectionHead.toLowerCase() !== prevSectionHead.toLowerCase()) {
                        const colText = detail.sectionHead;
                        const sectionText = detail.sectionHead;

                        const sections = sectionText ? (isObject(sectionText) ? colText.text.toString().split(';') : colText.toString().split(';')) : ''

                        let sectionList = [];

                        if (detail.sectionHead && sections.length) {
                            for (let si = 0; si <= sections.length; si++) {
                                const sectionDetail = sections[si] ? sections[si].split('#') : '';
                                if (sections[si] && isArray(sectionDetail)) {
                                    const userDetail = await this.mSGraphApiClient.getUserDetails(sectionDetail[1]);

                                    if (!userDetail) {
                                        throw new HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [E' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
                                    }
                                    sectionList.push({
                                        title: sectionDetail[0],
                                        user: userDetail
                                    });
                                }
                            }
                        }


                        updateDetail = {
                            ...updateDetail,
                            sectionHead: sectionList
                        }
                    }

                    if (updateDetail) {
                        updateList.push({
                            ...updateDetail,
                            code: detail.code,
                            companyCodeId
                        });

                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: prevCostCenterDetail.id,
                            entity_type: HISTORY_ENTITY_TYPE.COST_CENTER,
                            action_performed: HISTORY_ACTION_TYPE.UPDATE,
                            comments: 'Cost center detail has been updated.',
                            additional_info: {
                                updatedCostCenterDetail: {
                                    ...updateDetail,
                                    code: detail.code,
                                    companyCodeId
                                },
                                currentCostCenterDetail: prevCostCenterDetail
                            }
                        });
                    }
                } else {
                    //NEW LIST
                    // if(detail.departmentHead.toLowerCase() === detail.sectionHead.toLowerCase()) {
                    if (0) {
                        const userDetail = await this.mSGraphApiClient.getUserDetails(detail.departmentHead);

                        if (!userDetail) {
                            throw new HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [D' + (i + 2) + '] & [E' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
                        }

                        detail.departmentHead = {
                            ...userDetail,
                            loginId: userDetail.userType === AD_USER_TYPE.GUEST ? userDetail?.mail : userDetail.userPrincipalName
                        };
                        // detail.sectionHead = {
                        //     ...userDetail,
                        //     loginId: userDetail.userType === AD_USER_TYPE.GUEST ? userDetail?.mail : userDetail.userPrincipalName
                        // };
                    } else {
                        const departmentHeadUser = await this.mSGraphApiClient.getUserDetails(detail.departmentHead);

                        if (!departmentHeadUser) {
                            throw new HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [D' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
                        }

                        // const sectionHeadUser = await this.mSGraphApiClient.getUserDetails(detail.sectionHead);

                        // if(!sectionHeadUser) {
                        //     throw new HttpException('Invalid email id ' + detail.sectionHead + '. Issue at [E'+(i+2) + '].', HttpStatus.BAD_REQUEST);
                        // }

                        const colText = detail.sectionHead;
                        const sectionText = detail.sectionHead;

                        const sections = sectionText ? (isObject(sectionText) ? colText.text.toString().split(';') : colText.toString().split(';')) : ''

                        let sectionList = [];

                        if (detail.sectionHead && sections.length) {
                            for (let si = 0; si <= sections.length; si++) {
                                const sectionDetail = sections[si] ? sections[si].split('#') : '';

                                if (sections[si] && isArray(sectionDetail)) {
                                    const userDetail = await this.mSGraphApiClient.getUserDetails(sectionDetail[1]);

                                    if (!userDetail) {
                                        throw new HttpException('Invalid email id ' + sectionDetail[1] + '. Issue at [E' + (i + 2) + '].', HttpStatus.BAD_REQUEST);
                                    }
                                    sectionList.push({
                                        title: sectionDetail[0],
                                        user: userDetail
                                    });
                                }
                            }
                        }

                        detail.departmentHead = {
                            ...departmentHeadUser,
                            loginId: departmentHeadUser.userType === AD_USER_TYPE.GUEST ? departmentHeadUser?.mail : departmentHeadUser.userPrincipalName
                        };

                        detail.sectionHead = sectionList;

                        // detail.sectionHead = {
                        //     ...sectionHeadUser,
                        //     loginId: sectionHeadUser.userType === AD_USER_TYPE.GUEST ? sectionHeadUser?.mail : sectionHeadUser.userPrincipalName
                        // };
                    }

                    newList.push(detail);

                }
            }

            addedCodes.push(costCenterDetail.code)
        }

        return await this.databaseHelper.startTransaction(async () => {
            if (newList.length) {
                const addedCostCenters = await this.costCenterRepository.addBulkCostCenters(newList, currentContext);

                addedCostCenters.forEach((addedCostCenter) => {
                    historyRequest.push({
                        created_by: currentContext.user.username,
                        entity_id: addedCostCenter.id,
                        entity_type: HISTORY_ENTITY_TYPE.COST_CENTER,
                        action_performed: HISTORY_ACTION_TYPE.ADD,
                        comments: 'A new cost center ' + addedCostCenter.name + ' (' + addedCostCenter.code + ') ' + ' has been created.',
                        additional_info: {
                            costCenterDetail: addedCostCenter
                        }
                    })
                });
            }

            if (deletedList.length) {
                await this.costCenterRepository.bulkDeleteCostCenters(deletedList, companyCodeId, currentContext);
            }

            if (updateList.length) {
                for (let ui = 0; ui < updateList.length; ui++) {
                    const { code, companyCodeId, ...updateDetail } = updateList[ui];
                    await this.costCenterRepository.updateCostCenterDetailByCode(
                        code, companyCodeId,
                        updateDetail,
                        currentContext
                    );
                }
            }

            if (historyRequest.length) {
                await this.historyApiClient.addBulkRequestHistory(historyRequest);
            }

            return { message: `Data has been imported successfully.` };
        });

    }

    /**
     * Get company history by entity id.
     * @returns
     */
    public async getCompanyHistory(
        entityId: number,
    ): Promise<Record<string, any>> {
        const result = await this.historyApiClient.getRequestHistory(
            entityId,
            HISTORY_ENTITY_TYPE.COMPANY
        );
        return result.map(d => instanceToPlain(new GetHistoryResponseDTO(d)));
    }

    /**
     * Get cost center history by entity id.
     * @returns
     */
    public async getCostCenterHistory(
        costCenterId: number,
    ): Promise<Record<string, any>> {
        const result = await this.historyApiClient.getRequestHistory(
            costCenterId,
            HISTORY_ENTITY_TYPE.COST_CENTER
        );
        return result.map(d => instanceToPlain(new GetHistoryResponseDTO(d)));
    }

    public async uploadEvidence(
        companyCodeId: number,
        evidencesRequestDto: UploadEvidenceRequestDto,
        currentContext: CurrentContext,
    ) {
        const isCompanyCodeExists = await this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);

        if (!isCompanyCodeExists) {
            throw new HttpException('Company does\'t exist or deactivated. ', HttpStatus.BAD_REQUEST);
        }

        const { evidences } = evidencesRequestDto;

        if (evidences?.length) {
            await this.sharedAttachmentService.supportingDocumentsActivity(
                evidences,
                companyCodeId,
                ATTACHMENT_ENTITY_TYPE.COMPANY_EVIDENCE,
                ATTACHMENT_REL_PATH.COMPANY_EVIDENCE,
                currentContext.user.username,
                companyCodeId,
            );

            const addHistoryPayload: AddHistoryRequest = {
                created_by: currentContext.user.username,
                entity_id: companyCodeId,
                entity_type: HISTORY_ENTITY_TYPE.COMPANY,
                action_performed: HISTORY_ACTION_TYPE.EVIDENCE_UPLOAD,
                comments: 'New evidence/document has been uploaded.',
            };

            await this.historyApiClient.addRequestHistory(addHistoryPayload);

            return { message: 'Evidence has been uploaded successfully!' };
        }

        throw new HttpException(`Evidence required.`, HttpStatus.NOT_FOUND);
    }

    public async getEvidences(companyCodeId: number) {
        const response = await this.attachmentApiClient.getAllAttachments(
            companyCodeId,
            ATTACHMENT_ENTITY_TYPE.COMPANY_EVIDENCE,
        );
        return multiObjectToInstance(AttachmentContentResponseDto, response);
    }

    public async updateMultiNaturalAccount(
        updateMultiNaturalAccountRequestDto: UpdateMultiNaturalAccountConfigRequestDto,
        currentContext: CurrentContext,
    ) {
        const { companyId, enableMultiNaturalAccount } = updateMultiNaturalAccountRequestDto;

        return this.databaseHelper.startTransaction(async () => {
            await this.companyCodeRepository.updateCompanyCodeDetailById(companyId, { enableMultiNaturalAccount }, currentContext);

            await this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: companyId,
                entity_type: HISTORY_ENTITY_TYPE.COMPANY,
                action_performed: HISTORY_ACTION_TYPE.UPDATE,
                comments: `Natural Account configuration has been switched to ${enableMultiNaturalAccount ? 'multi' : 'single'} natural account.`,
            });

            return { message: 'Natural Account configuration has been updated successfully.' };
        });
    }

}
