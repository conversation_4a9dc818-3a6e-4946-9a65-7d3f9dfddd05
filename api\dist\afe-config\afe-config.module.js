"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeConfigModule = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../shared/clients");
const services_1 = require("../shared/services");
const controllers_1 = require("./controllers");
const services_2 = require("./services");
const repositories_1 = require("./repositories");
const afe_budget_type_mapping_repository_1 = require("./repositories/afe-budget-type-mapping.repository");
const parallel_identifier_repository_1 = require("./repositories/parallel-identifier.repository");
const repositories_2 = require("../afe-proposal/repositories");
const helpers_1 = require("../shared/helpers");
const repositories = [
    repositories_1.AfeRequestTypeRepository,
    repositories_1.AfeNatureTypeMappingRepository,
    repositories_1.AfeNatureTypeRepository,
    repositories_1.AfeTypeRepository,
    repositories_1.AfeBudgetTypeRepository,
    repositories_1.QuestionRepository,
    parallel_identifier_repository_1.ParallelIdentifierRepository,
    afe_budget_type_mapping_repository_1.AfeBudgetTypeMappingRepository,
    repositories_1.AfeSubTypeRepository,
    repositories_2.LocationRepository
];
let AfeConfigModule = class AfeConfigModule {
};
AfeConfigModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.AfeConfigController],
        providers: [
            services_2.AfeConfigService,
            clients_1.AdminApiClient,
            services_1.EntityService,
            helpers_1.SequlizeOperator,
            ...repositories
        ],
    })
], AfeConfigModule);
exports.AfeConfigModule = AfeConfigModule;
//# sourceMappingURL=afe-config.module.js.map