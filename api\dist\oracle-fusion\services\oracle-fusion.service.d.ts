import { AfeProposalAmountSplitRepository, AfeProposalRepository } from 'src/afe-proposal/repositories';
import { SequlizeOperator } from 'src/shared/helpers';
import { FinanceAdminService } from 'src/finance/services';
import { AdminApiClient, FusionApiClient, FusionUaeApiClient, MSGraphApiClient } from 'src/shared/clients';
import { CostCenterRepository } from 'src/finance/repositories';
import { SharedNotificationService } from 'src/shared/services';
export declare class OracleFusionService {
    private readonly afeProposalRepository;
    private readonly afeProposalAmountSplitRepository;
    private readonly fussionApi;
    private readonly fussionUaeApi;
    private readonly sequlizeOperator;
    private readonly financeAdminService;
    private readonly adminApiClient;
    private readonly costCenterRepository;
    private readonly mSGraphApiClient;
    private readonly sharedNotificationService;
    constructor(afeProposalRepository: AfeProposalRepository, afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository, fussionApi: FusionApiClient, fussionUaeApi: FusionUaeApiClient, sequlizeOperator: SequlizeOperator, financeAdminService: FinanceAdminService, adminApiClient: AdminApiClient, costCenterRepository: CostCenterRepository, mSGraphApiClient: MSGraphApiClient, sharedNotificationService: SharedNotificationService);
    sendFusionEnabledApprovedAfe(): Promise<{
        message: string;
        data: any[];
    } | {
        message: string;
        data?: undefined;
    }>;
    private getUsersEmailByRoleAndEntityId;
    private sendEmailToITSupportOnFailure;
}
