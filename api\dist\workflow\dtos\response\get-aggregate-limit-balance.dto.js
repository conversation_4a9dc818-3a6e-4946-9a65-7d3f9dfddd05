"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetAggregateLimitbalanceDTO = exports.DeductionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const enums_1 = require("../../../shared/enums");
const associated_type_enum_1 = require("../../../shared/enums/associated-type.enum");
class DeductionDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], DeductionDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], DeductionDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], DeductionDto.prototype, "updatedOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], DeductionDto.prototype, "afeProposalId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: JSON }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], DeductionDto.prototype, "data", void 0);
exports.DeductionDto = DeductionDto;
class GetAggregateLimitbalanceDTO {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "entityTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "entityCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "entityType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "entityShortName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "associateLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "associateRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "associateType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "associatedColumn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAggregateLimitbalanceDTO.prototype, "associatedUser", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetAggregateLimitbalanceDTO.prototype, "aggregateLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetAggregateLimitbalanceDTO.prototype, "limitDeduction", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Array }),
    (0, class_transformer_1.Expose)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], GetAggregateLimitbalanceDTO.prototype, "deductions", void 0);
exports.GetAggregateLimitbalanceDTO = GetAggregateLimitbalanceDTO;
//# sourceMappingURL=get-aggregate-limit-balance.dto.js.map