import { ASSOCIATED_COLUMN, BUDGET_TYPE } from "src/shared/enums";
import { ASSOCIATED_TYPE } from "src/shared/enums/associated-type.enum";
export declare class ProjectComponentTitle {
    title: string;
}
export declare class GetRoleBasedSettingResponse {
    requestTypeId: number;
    budgetType: BUDGET_TYPE;
    projectComponentId: number;
    entityId: number;
    entityCode: string;
    entityTitle: string;
    entityType: string;
    projectComponent: ProjectComponentTitle;
    year: number;
}
export declare class GetRoleBasedStepsResponse {
    id: number;
    workflowMasterStepId: number;
    title: string;
    workflowMasterSettingId: number;
    associateLevel: string;
    associateRole: string;
    associateType: ASSOCIATED_TYPE;
    associatedColumn: ASSOCIATED_COLUMN;
    associatedUser: string;
    createdOn: Date;
    updatedOn: Date;
    createdBy: string;
    updatedBy: string;
    workflowMasterSetting: GetRoleBasedSettingResponse;
}
export declare class PaginatedGetRoleBasedStepsResponseDTO {
    pageTotal: number;
    records: GetRoleBasedStepsResponse[];
    total: number;
}
