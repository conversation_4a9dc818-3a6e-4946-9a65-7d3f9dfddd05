"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyCode = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const analysis_code_model_1 = require("./analysis-code.model");
const cost_center_model_1 = require("./cost-center.model");
const natural_account_number_model_1 = require("./natural-account-number.model");
let CompanyCode = class CompanyCode extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], CompanyCode.prototype, "code", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], CompanyCode.prototype, "name", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], CompanyCode.prototype, "entityId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_code', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], CompanyCode.prototype, "entityCode", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_type', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], CompanyCode.prototype, "entityType", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'fusion_for_request_type_ids', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], CompanyCode.prototype, "fusionIntegrationForRequestTypeIds", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'allow_multiple_natual_accounts', type: sequelize_typescript_1.DataType.BOOLEAN, allowNull: false, defaultValue: false }),
    __metadata("design:type", Boolean)
], CompanyCode.prototype, "enableMultiNaturalAccount", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => cost_center_model_1.CostCenter),
    __metadata("design:type", Array)
], CompanyCode.prototype, "costCenters", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => natural_account_number_model_1.NaturalAccountNumber),
    __metadata("design:type", Array)
], CompanyCode.prototype, "naturalAccountNumbers", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => analysis_code_model_1.AnalysisCode),
    __metadata("design:type", Array)
], CompanyCode.prototype, "analysisCodes", void 0);
CompanyCode = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'company_codes' })
], CompanyCode);
exports.CompanyCode = CompanyCode;
//# sourceMappingURL=company-code.model.js.map