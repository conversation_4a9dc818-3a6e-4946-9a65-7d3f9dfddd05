import { AfeProposalLimitDeductionRepository } from 'src/afe-proposal/repositories';
import { Pagination } from 'src/core/pagination';
import { AdminApiClient, HistoryApiClient } from 'src/shared/clients';
import { MessageResponseDto } from 'src/shared/dtos';
import { <PERSON><PERSON>elper, SequlizeOperator } from 'src/shared/helpers';
import { IWorkFlowFilter } from 'src/shared/interfaces/filter.interface';
import { ExcelSheetService } from 'src/shared/services';
import { CurrentContext } from 'src/shared/types';
import { NewMasterWorkflowRequestDto, NewMasterWorkflowResponseDto, PublishedOverriddenWorkflowRequest } from '../dtos';
import { CloneWorkflowRequestDto } from '../dtos/request/clone-workflow-request.dto';
import { ListMasterWorkflowRequestDto } from '../dtos/request/list-master-workflow-request.dto';
import { OverrideWorkflowRequestDto } from '../dtos/request/override-workflow-request.dto';
import { GetMasterSettingResponseDTO, WorkflowDetailResponseDTO } from '../dtos/response/get-master-setting-response.dto';
import { GetOverridenSettingListResponseDTO } from '../dtos/response/get-overriden-setting-list-response.dto';
import { WorkflowMasterSettingRepository, WorkflowMasterStepRepository, WorkflowSharedBucketLimitRepository, WorkflowSharedChildLimitRepository } from '../repositories';
import { WorkflowMasterStepService } from './workflow-master-step.service';
export declare class WorkflowMasterSettingService {
    private readonly workflowMasterSettingRepository;
    private readonly workflowMasterStepRepository;
    private readonly workflowSharedChildLimitRepository;
    private readonly workflowSharedBucketLimitRepository;
    private readonly adminApiClient;
    private readonly limitDeductionRepository;
    private readonly databaseHelper;
    private readonly sequlizeOperator;
    private readonly historyApiClient;
    private readonly excelSheetService;
    private readonly workflowMasterStepService;
    constructor(workflowMasterSettingRepository: WorkflowMasterSettingRepository, workflowMasterStepRepository: WorkflowMasterStepRepository, workflowSharedChildLimitRepository: WorkflowSharedChildLimitRepository, workflowSharedBucketLimitRepository: WorkflowSharedBucketLimitRepository, adminApiClient: AdminApiClient, limitDeductionRepository: AfeProposalLimitDeductionRepository, databaseHelper: DatabaseHelper, sequlizeOperator: SequlizeOperator, historyApiClient: HistoryApiClient, excelSheetService: ExcelSheetService, workflowMasterStepService: WorkflowMasterStepService);
    getAllMasterSetting(limit: number, page: number, filter: IWorkFlowFilter): Promise<Pagination<GetMasterSettingResponseDTO>>;
    getOverrideAllSettingsDetail(workflowSettingId: number): Promise<GetOverridenSettingListResponseDTO[]>;
    getAllAssignedUniqueRolesForWorkflowSetting(workflowSettingId: number): Promise<any[]>;
    getAllUnpublishedOverriden(parentId: number): Promise<GetOverridenSettingListResponseDTO[]>;
    getAllMasterSettingList(limit: number, page: number, filter: IWorkFlowFilter, listMasterWorkflowRequestDto: ListMasterWorkflowRequestDto): Promise<Pagination<GetMasterSettingResponseDTO>>;
    addNewMasterWorkflowSetting(newMasterSettingRequestDto: NewMasterWorkflowRequestDto, currentContext: CurrentContext): Promise<NewMasterWorkflowResponseDto>;
    getWorkflowDetailByWorkflowSettingId(id: number): Promise<WorkflowDetailResponseDTO>;
    getPolicyDetailByWorkflowSettingId(id: number): Promise<WorkflowDetailResponseDTO>;
    downloadPolicyDetailByWorkflowSettingId(id: number): Promise<{
        report: import("exceljs").Buffer;
        filename: string;
    }>;
    getWorkflowSettingHistory(id: number): Promise<Record<string, any>>;
    publishWorkflow(workflowSettingId: number, unpublishedVersion: boolean, currentContext: CurrentContext): Promise<MessageResponseDto>;
    publishOverriddenWorkflowSetting(workflowMasterSettingId: number, publishedOverriddenWorkflowRequest: PublishedOverriddenWorkflowRequest, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    unpublishWorkflow(workflowSettingId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    validateOverrideWorkflow(workflowSettingId: number, overrideWorkflowRequestDto: OverrideWorkflowRequestDto): Promise<any[]>;
    overrideWorkflow(workflowSettingId: number, overrideWorkflowRequestDto: OverrideWorkflowRequestDto, currentContext: CurrentContext): Promise<NewMasterWorkflowResponseDto[]>;
    cloneWorkflowStep(workflowSettingId: number, cloneWorkflowRequestDto: CloneWorkflowRequestDto, currentContext: CurrentContext): Promise<WorkflowDetailResponseDTO>;
    private updateInBatches;
    createNewVersionWorkflow(workflowSettingId: number, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    private insertWorkflowStepsInBatches;
    private getSequenceNumberWithStepId;
    private getStepIdWithSequenceNumber;
    deleteWorkflow(workflowSettingId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteMultipleOverridenWorkflowSetting(overridenWorkflowSettingIds: number[], currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteUnpublishedNewVersionWorkflow(workflowSettingId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteOverriddenWorkflow(workflowSettingId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteUnpublishedOverriddenWorkflowSetting(workflowSettingId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getUnpublishedVersionWorkflowDetailBySettingId(id: number): Promise<WorkflowDetailResponseDTO>;
}
