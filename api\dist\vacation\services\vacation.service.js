"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VacationService = void 0;
const common_1 = require("@nestjs/common");
const class_transformer_1 = require("class-transformer");
const lodash_1 = require("lodash");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const dtos_1 = require("../dtos");
let VacationService = class VacationService {
    constructor(requestApiClient, adminApiClient) {
        this.requestApiClient = requestApiClient;
        this.adminApiClient = adminApiClient;
    }
    getAllUpcomingDelegations(currentContext, filterQuery = null) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            let userName = user.username;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            if (isAdministrator && filterQuery && (filterQuery === null || filterQuery === void 0 ? void 0 : filterQuery.delegateFor)) {
                userName = filterQuery.delegateFor;
            }
            const upcomingDelegations = yield this.requestApiClient.getUpcomingDelegations({
                user_name: userName
            });
            return upcomingDelegations.map(d => (0, class_transformer_1.instanceToPlain)(new dtos_1.GetVacationDelegationResponseDto(d)));
        });
    }
    addUpcomingDelegation(delegationPayload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            const isAvailable = yield this.ifDelegationAlreadyAdded((isAdministrator && delegationPayload.delegateFor) ? delegationPayload.delegateFor.toLowerCase() : user.username.toLowerCase(), delegationPayload.fromDate, delegationPayload.toDate, null, delegationPayload.requestTypeId);
            if (isAvailable) {
                throw new common_1.HttpException(`Delegation already exist for the selected date.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (isAdministrator &&
                (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.delegateFor) &&
                (delegationPayload.delegateFor.toLowerCase() === delegationPayload.delegateTo.toLowerCase())) {
                throw new common_1.HttpException(`Delegate for & delegate to can't be same.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!(isAdministrator && (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.delegateFor) &&
                (delegationPayload.delegateFor.toLowerCase() !== user.username.toLowerCase())) &&
                (user.username.toLowerCase() === delegationPayload.delegateTo.toLowerCase())) {
                throw new common_1.HttpException(`You can't delegate to yourself.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            let additional_info = {};
            if (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.requestTypeId) {
                additional_info = {
                    filter_param: {
                        request_type_id: delegationPayload.requestTypeId
                    }
                };
            }
            const newDelegation = yield this.requestApiClient.addNewDelegation({
                delegate_for_username: (isAdministrator && delegationPayload.delegateFor) ? delegationPayload.delegateFor.toLowerCase() : user.username.toLowerCase(),
                delegate_to_username: delegationPayload.delegateTo.toLowerCase(),
                delegate_from_date: delegationPayload.fromDate,
                delegate_to_date: delegationPayload.toDate,
                created_by: user.username.toLowerCase(),
                additional_info
            });
            return newDelegation;
        });
    }
    updateUpcomingDelegation(delegationPayload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            const delegationDetail = yield this.requestApiClient.getUpcomingDelegationById(delegationPayload.id);
            if (!delegationDetail || (delegationDetail.hasOwnProperty('id') && !(0, lodash_1.toNumber)(delegationDetail.id))) {
                throw new common_1.HttpException(`Invalid or already deleted delegation.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            [];
            if (!(isAdministrator &&
                (delegationDetail.delegate_for_username.toLowerCase() !== user.username.toLowerCase())) &&
                (user.username.toLowerCase() === delegationPayload.delegateTo.toLowerCase())) {
                throw new common_1.HttpException(`You can't delegate to yourself.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!isAdministrator && (delegationDetail.delegate_for_username.toLowerCase() !== user.username.toLowerCase())) {
                throw new common_1.HttpException(`You are not authorized to update this delegation.`, common_1.HttpStatus.UNAUTHORIZED);
            }
            if (isAdministrator && (delegationDetail.delegate_for_username.toLowerCase() === delegationPayload.delegateTo.toLowerCase())) {
                throw new common_1.HttpException(`Delegate for & delegate to can't be same.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            const isAvailable = yield this.ifDelegationAlreadyAdded(delegationDetail.delegate_for_username.toLowerCase(), delegationPayload.fromDate, delegationPayload.toDate, delegationPayload.id, delegationPayload.requestTypeId);
            if (isAvailable) {
                throw new common_1.HttpException(`Delegation already exist for the selected date.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            let updatePayload = null;
            if (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.delegateTo) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { delegate_to_username: delegationPayload.delegateTo.toLowerCase() });
            }
            if (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.fromDate) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { delegate_from_date: delegationPayload.fromDate });
            }
            if (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.toDate) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { delegate_to_date: delegationPayload.toDate });
            }
            let additional_info = {};
            if (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.requestTypeId) {
                additional_info = {
                    filter_param: {
                        request_type_id: delegationPayload.requestTypeId
                    }
                };
            }
            updatePayload = Object.assign(Object.assign({}, updatePayload), { additional_info });
            if (!updatePayload) {
                throw new common_1.HttpException(`No data to update.`, common_1.HttpStatus.BAD_REQUEST);
            }
            if (delegationPayload === null || delegationPayload === void 0 ? void 0 : delegationPayload.id) {
                updatePayload = Object.assign(Object.assign({}, updatePayload), { id: delegationPayload.id });
            }
            updatePayload = Object.assign(Object.assign({}, updatePayload), { created_by: user.username.toLowerCase() });
            const updatedDelegation = yield this.requestApiClient.updateDelegation(updatePayload);
            return updatedDelegation;
        });
    }
    deleteUpcomingDelegation(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            const delegationDetail = yield this.requestApiClient.getUpcomingDelegationById(id);
            if (!delegationDetail || (delegationDetail.hasOwnProperty('id') && !(0, lodash_1.toNumber)(delegationDetail.id))) {
                throw new common_1.HttpException(`Invalid or already deleted delegation.`, common_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!isAdministrator && (delegationDetail.delegate_for_username.toLowerCase() !== user.username.toLowerCase())) {
                throw new common_1.HttpException(`You are not authorized to delete this delegation.`, common_1.HttpStatus.UNAUTHORIZED);
            }
            let deletePayload = {
                id,
                created_by: user.username
            };
            const deletedDelegation = yield this.requestApiClient.deleteDelegation(deletePayload);
            return deletedDelegation;
        });
    }
    deleteAllUpcomingDelegation(deleteRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { user } = currentContext;
            const isAdministrator = yield this.adminApiClient.hasPermissionToUser(user.username, enums_1.PERMISSIONS.AFE_ADMINISTRATION);
            let username = user.username;
            if (!isAdministrator && (deleteRequestDto === null || deleteRequestDto === void 0 ? void 0 : deleteRequestDto.username.toLowerCase()) !== username.toLowerCase()) {
                throw new common_1.HttpException(`You are not authorized to delete this user delegation.`, common_1.HttpStatus.UNAUTHORIZED);
            }
            if (isAdministrator && (deleteRequestDto === null || deleteRequestDto === void 0 ? void 0 : deleteRequestDto.username)) {
                username = deleteRequestDto.username;
            }
            let deletePayload = {
                user_name: username.toLowerCase(),
                created_by: user.username.toLowerCase()
            };
            const deletedDelegations = yield this.requestApiClient.removeAllDelegations(deletePayload);
            return deletedDelegations;
        });
    }
    ifDelegationAlreadyAdded(username, fromDate, toDate, editDelegationId = null, requestTypeId = null) {
        return __awaiter(this, void 0, void 0, function* () {
            const upcomingDelegationResponse = yield this.requestApiClient.getUpcomingDelegations({
                user_name: username, type: 'default'
            });
            const upcomingDelegations = upcomingDelegationResponse.map(d => (0, class_transformer_1.instanceToPlain)(new dtos_1.GetVacationDelegationResponseDto(d)));
            let responseReturn = false;
            upcomingDelegations.forEach(upcomingDelegation => {
                var _a, _b;
                if (!(editDelegationId && ((0, lodash_1.toNumber)(upcomingDelegation.id) === (0, lodash_1.toNumber)(editDelegationId)))) {
                    let delegateFromDate = upcomingDelegation.delegateFromDate.split('T');
                    let delegateToDate = upcomingDelegation.delegateToDate.split('T');
                    const existingFromDate = new Date(delegateFromDate[0] + ' 00:00:00').getTime();
                    const existingToDate = new Date(delegateToDate[0] + ' 23:59:00').getTime();
                    const inputFromDate = new Date(fromDate).getTime();
                    const inputToDate = new Date(toDate).getTime();
                    const otherRequestTypeId = ((_b = (_a = upcomingDelegation.additionalInfo) === null || _a === void 0 ? void 0 : _a.filterParam) === null || _b === void 0 ? void 0 : _b.requestTypeId) || null;
                    if ((inputFromDate >= existingFromDate) && (inputFromDate <= existingToDate)) {
                        if ((otherRequestTypeId === requestTypeId) ||
                            (!otherRequestTypeId && requestTypeId) ||
                            (otherRequestTypeId && !requestTypeId)) {
                            responseReturn = true;
                            return;
                        }
                    }
                    if ((inputFromDate < existingFromDate) && (inputToDate > existingFromDate)) {
                        if ((otherRequestTypeId === requestTypeId) ||
                            (!otherRequestTypeId && requestTypeId) ||
                            (otherRequestTypeId && !requestTypeId)) {
                            responseReturn = true;
                            return;
                        }
                    }
                }
            });
            return responseReturn;
        });
    }
};
VacationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.RequestApiClient,
        clients_1.AdminApiClient])
], VacationService);
exports.VacationService = VacationService;
//# sourceMappingURL=vacation.service.js.map