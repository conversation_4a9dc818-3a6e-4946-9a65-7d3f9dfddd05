import { GetAfeAttachmentRequestDto, GetAfeDetailResponseDTO, GetAFEHistoryResponseDTO, GetApproversForMoreDetailRequestDto, GetAttachmentDTO, GetTaskApprovalActionsRequestDto, GetUserActionedAfeProposalRequestDto, GetUserSubmittedAfeProposalRequestDto, MaximoRequestDto, TaskApprovalRequestDto, TaskListResponseDTO } from '../dtos';
import { GetAfeDetailRequestDto } from '../dtos/request/get-afe-detail-request.dto';
import { UserEmailRequestDto } from '../dtos/request/user-email-request.dto';
import { OneAppService } from '../services';
export declare class OneAppController {
    private readonly oneAppService;
    constructor(oneAppService: OneAppService);
    getAfeTasks(userEmailRequestDto: UserEmailRequestDto): Promise<TaskListResponseDTO[]>;
    getAfeDetail(getAfeDetailRequestDto: GetAfeDetailRequestDto): Promise<GetAfeDetailResponseDTO>;
    getAfeAttachment(getAfeAttachmentRequestDto: GetAfeAttachmentRequestDto): Promise<GetAttachmentDTO>;
    getAfeHistory(getAfeHistoryDto: GetAfeDetailRequestDto): Promise<GetAFEHistoryResponseDTO[]>;
    approveTask(taskApprovalRequestDto: TaskApprovalRequestDto): Promise<string>;
    rejectTask(taskApprovalRequestDto: TaskApprovalRequestDto): Promise<string>;
    delegateTask(taskApprovalRequestDto: TaskApprovalRequestDto): Promise<string>;
    askMoreDetailTaskApproval(taskApprovalRequestDto: TaskApprovalRequestDto): Promise<string>;
    reassignTask(taskApprovalRequestDto: TaskApprovalRequestDto): Promise<string>;
    sendBackTaskAction(taskApprovalRequestDto: TaskApprovalRequestDto): Promise<string>;
    getTaskApprovalActions(getTaskApprovalActionsRequestDto: GetTaskApprovalActionsRequestDto): Promise<string[]>;
    getApproversForMoreDetailTaskAction(getApproversForMoreDetailRequestDto: GetApproversForMoreDetailRequestDto): Promise<import("../dtos").GetApproversForMoreDetailsResponseDTO[]>;
    getUserActionedAFEProposals(getUserActionedAfeProposalRequestDto: GetUserActionedAfeProposalRequestDto): Promise<{
        ID: number;
        AFEModule: string;
        AFEType: any;
        BusinessUnit: string;
        BudgetType: import("src/shared/enums").BUDGET_TYPE_TITLE;
        ProjectName: string;
        ProjectReferenceNumber: string;
        TotalAmount: string;
        BudgetReferenceNumber: string;
        SubmissionDate: Date;
        ActionDateTime: any;
        AFEStatus: string;
        ApproverRole: any;
        Action: any;
        Comments: any;
        TotalCount: any;
    }[]>;
    getUserSubmittedAFEProposal(getUserSubmittedAfeProposalRequestDto: GetUserSubmittedAfeProposalRequestDto): Promise<GetAfeDetailResponseDTO[]>;
    getAFECompleteDetail(maximoRequestDto: MaximoRequestDto): Promise<any>;
}
