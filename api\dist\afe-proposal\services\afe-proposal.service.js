"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeProposalService = void 0;
const common_1 = require("@nestjs/common");
const draft_afe_repository_1 = require("../../afe-draft/repositories/draft-afe-repository");
const config_service_1 = require("../../config/config.service");
const services_1 = require("../../finance/services");
const repositories_1 = require("../../notification/repositories");
const repositories_2 = require("../../queue/repositories");
const clients_1 = require("../../shared/clients");
const constants_1 = require("../../shared/constants");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const mappings_1 = require("../../shared/mappings");
const services_2 = require("../../shared/services");
const validators_1 = require("../../shared/validators");
const services_3 = require("../../task/services");
const services_4 = require("../../workflow/services");
const repositories_3 = require("../repositories");
const lodash_1 = require("lodash");
const task_action_with_email_template_mapping_1 = require("../../task/mappings/task-action-with-email-template.mapping");
let AfeProposalService = class AfeProposalService {
    constructor(afeProposalRepository, databaseHelper, draftAfeRepository, workflowService, afeProposalAmountSplitRepository, afeProposalApproverRepository, attachmentApiClient, adminApiClient, requestApiClient, configService, historyApiClient, afeProposalLimitDeductionRepository, taskService, sharedAttachmentService, taskApiClient, financeService, notificationRepository, mSGraphApiClient, afeProposalValidator, queueLogRepository, sharedNotificationService) {
        this.afeProposalRepository = afeProposalRepository;
        this.databaseHelper = databaseHelper;
        this.draftAfeRepository = draftAfeRepository;
        this.workflowService = workflowService;
        this.afeProposalAmountSplitRepository = afeProposalAmountSplitRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.attachmentApiClient = attachmentApiClient;
        this.adminApiClient = adminApiClient;
        this.requestApiClient = requestApiClient;
        this.configService = configService;
        this.historyApiClient = historyApiClient;
        this.afeProposalLimitDeductionRepository = afeProposalLimitDeductionRepository;
        this.taskService = taskService;
        this.sharedAttachmentService = sharedAttachmentService;
        this.taskApiClient = taskApiClient;
        this.financeService = financeService;
        this.notificationRepository = notificationRepository;
        this.mSGraphApiClient = mSGraphApiClient;
        this.afeProposalValidator = afeProposalValidator;
        this.queueLogRepository = queueLogRepository;
        this.sharedNotificationService = sharedNotificationService;
    }
    submitAfeProposal(submitAfeProposalRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { draftId } = submitAfeProposalRequestDto;
            const { username: submitterId, given_name: firstName, family_name: lastName, } = currentContext.user;
            const afeDraft = yield this.draftAfeRepository.getDraftById(draftId);
            if (!afeDraft) {
                throw new exceptions_1.HttpException(`Afe draft doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (afeDraft.createdBy !== submitterId.toLowerCase()) {
                throw new exceptions_1.HttpException(`You are not authorized to create AFE with this draft id.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const { data: afeDraftData } = afeDraft;
            const { requestTypeId, businessEntity, readers, data, costCenterSplit, budgetType, totalAmount, projectComponentSplit, isSupplemental, supplementalData, } = afeDraftData;
            const { projectDetails, subType, type } = data;
            const year = afeDraftData.year || new Date().getFullYear();
            let parentAfeId = null;
            let version = 1;
            let lastSubmittedVersion = null;
            if (isSupplemental) {
                const { parentAfeProposalId } = yield this.supplementalAfeValidation(supplementalData, businessEntity.id, afeDraftData);
                parentAfeId = +parentAfeProposalId;
                lastSubmittedVersion = yield this.afeProposalRepository.maxVersionOfSupplementalAfe(parentAfeProposalId);
                lastSubmittedVersion = lastSubmittedVersion || 1;
                version = lastSubmittedVersion + 1;
            }
            const currencyDetail = yield this.financeService.getCurrencyTypeForEntity(businessEntity.id);
            yield this.areCostCentersValid(businessEntity.id, costCenterSplit);
            const projectReferenceNumber = isSupplemental
                ? `${supplementalData.parentAfeNo.split('/')[0]}/${version}`
                : yield this.createProjectReferenceNumber(requestTypeId, businessEntity.id, year);
            let subscribers = [];
            let afeProposal;
            const submitterDetails = yield this.mSGraphApiClient.getUserDetails(submitterId);
            yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                subscribers = [
                    ...new Set([
                        ...((readers === null || readers === void 0 ? void 0 : readers.length) ? readers.map(r => r.loginId.toLowerCase()) : []),
                        projectDetails.projectLeader.loginId.toLocaleLowerCase(),
                    ]),
                ];
                const response = yield this.createAfeProposal(afeDraftData, projectReferenceNumber, currentContext, subscribers, submitterDetails, currencyDetail, parentAfeId, version);
                afeProposal = response.afeProposal;
                const entityParents = yield this.adminApiClient.getParentsOfEntity(businessEntity.id);
                const logPayload = {
                    entityId: businessEntity.id,
                    action: enums_1.QUEUE_LOG_ACTION.SUBMITTED,
                    data: {
                        proposalId: afeProposal.id,
                        budgetType: budgetType ? mappings_1.BUDGET_TYPE_MAPPING_WITH_ID[budgetType.id] : null,
                        totalAmount: totalAmount / currencyDetail.conversionRate,
                        requestTypeId: requestTypeId,
                        afeType: type,
                        afeSubType: subType,
                        projectComponentId: projectComponentSplit === null || projectComponentSplit === void 0 ? void 0 : projectComponentSplit.map(p => p.id),
                        costCenterId: costCenterSplit === null || costCenterSplit === void 0 ? void 0 : costCenterSplit.map(c => c.id),
                        approversLevel: [...new Set(response.steps.map(step => step.associateLevel))].filter(a => !!a),
                        businessUnitHierarchy: entityParents.map(entity => ({
                            id: entity.id,
                            code: entity.code,
                            level: entity.entity_type,
                        })),
                        status: enums_1.QUEUE_LOG_ACTION.SUBMITTED,
                    },
                };
                yield this.queueLogRepository.createQueueLogEntry(logPayload, currentContext);
                yield this.moveAttachmentsFromSourceToDestination(enums_1.ATTACHMENT_ENTITY_TYPE.AFE_DRAFT, draftId, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT, afeProposal.id);
                yield this.taskService.createNextTasks(afeProposal.id, enums_1.TASK_ACTION.APPROVE, currentContext);
                yield this.sendAfeCreationNotificationToSubmitter(afeProposal, submitterDetails.mail, isSupplemental ? 'AFE.SUPPLEMENTAL.SUBMISSION.SUBMITTER' : 'AFE.SUBMISSION.SUBMITTER');
                yield this.sendAfeCreationNotificationToOthers(afeProposal, isSupplemental ? 'AFE.SUPPLEMENTAL.SUBMISSION.OTHER' : 'AFE.SUBMISSION.OTHER');
                yield this.draftAfeRepository.deleteDraftById(draftId, currentContext);
                const addHistoryPayload = {
                    created_by: submitterId,
                    entity_id: afeProposal.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.SUBMITTED,
                };
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                yield this.createNotificationOnAfeSubmission({ id: afeProposal.id, afeReferenceNumber: projectReferenceNumber }, { id: submitterId, name: `${firstName} ${lastName}` }, subscribers, currentContext);
            }));
            return { projectReferenceNumber };
        });
    }
    resubmitAfeProposal(resubmitAfeProposalRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id: originalAfeProposalId, supportingDocuments, businessEntity, taskId, isSupplemental, readers, data, costCenterSplit, budgetType, totalAmount, requestTypeId, projectComponentSplit, deletedSupportingDocuments, } = resubmitAfeProposalRequestDto;
            const { projectDetails, type, subType } = data;
            const { username: submitterId, given_name: firstName, family_name: lastName, } = currentContext.user;
            const originalAfeProposal = yield this.afeProposalRepository.getAfeProposalById(originalAfeProposalId);
            if (!originalAfeProposal) {
                throw new exceptions_1.HttpException(`Afe original afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            yield this.areCostCentersValid(businessEntity.id, costCenterSplit);
            const { submitterId: originalAfeCreatorId, projectReferenceNumber, version: originalAfeVersion, internalStatus, entityId, category, subscribers: originalAfeSubscribers, parentAfeId, createdOn, } = originalAfeProposal;
            if (businessEntity.id !== entityId) {
                throw new exceptions_1.HttpException(`Business entity can't be different from original AFE.`, enums_1.HttpStatus.FORBIDDEN);
            }
            if (originalAfeCreatorId.toLowerCase() !== submitterId.toLowerCase() && !taskId) {
                throw new exceptions_1.HttpException(`You are not authorized to resubmit this AFE proposal.`, enums_1.HttpStatus.FORBIDDEN);
            }
            if ((0, helpers_1.isSupplementalAfeByCategory)(category) !== isSupplemental) {
                throw new exceptions_1.HttpException(`You can't resubmit supplemental afe as new afe and vice-versa.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (internalStatus !== enums_1.AFE_PROPOSAL_STATUS.SENT_BACK) {
                throw new exceptions_1.HttpException(`Can't resubmit the AFE proposal which is not in sent back status.`, enums_1.HttpStatus.FORBIDDEN);
            }
            let afeProposal;
            let originalApproverDetail;
            const approverIdOfSendBackInProgressStep = yield this.afeProposalApproverRepository.getInprogressApproverByProposalId(originalAfeProposalId);
            if (taskId && taskId !== (approverIdOfSendBackInProgressStep === null || approverIdOfSendBackInProgressStep === void 0 ? void 0 : approverIdOfSendBackInProgressStep.id)) {
                const task = yield this.taskApiClient.getTaskById(taskId);
                if (task.entity_id !== (approverIdOfSendBackInProgressStep === null || approverIdOfSendBackInProgressStep === void 0 ? void 0 : approverIdOfSendBackInProgressStep.id) ||
                    (task === null || task === void 0 ? void 0 : task.task_status) !== 'Not Started') {
                    throw new exceptions_1.HttpException('Forbidden', enums_1.HttpStatus.FORBIDDEN);
                }
                if (task.original_owner && task.delegated_task_type === 'representative') {
                    const { givenName: firstName, surname: lastName, userPrincipalName: upn, userType, mail: email, jobTitle: title, } = yield this.mSGraphApiClient.getUserDetails(task.original_owner);
                    const loginId = userType == enums_1.AD_USER_TYPE.GUEST ? email.toLowerCase() : upn.toLowerCase();
                    originalApproverDetail = { firstName, lastName, loginId, email, title };
                }
            }
            const submitterDetails = yield this.mSGraphApiClient.getUserDetails(submitterId);
            const currencyDetail = yield this.financeService.getCurrencyTypeForEntity(businessEntity.id);
            let subscribers = [];
            yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                yield this.afeProposalApproverRepository.updateApproverStatusOnAction(approverIdOfSendBackInProgressStep.id, enums_1.APPROVER_STATUS.RESUBMITTED, currentContext, originalApproverDetail);
                subscribers = [
                    ...new Set([
                        ...((readers === null || readers === void 0 ? void 0 : readers.length) ? readers.map(r => r.loginId.toLowerCase()) : []),
                        projectDetails.projectLeader.loginId.toLocaleLowerCase(),
                        ...(originalAfeSubscribers || []),
                    ]),
                ];
                const response = yield this.createAfeProposal(resubmitAfeProposalRequestDto, projectReferenceNumber, currentContext, subscribers, submitterDetails, currencyDetail, parentAfeId, originalAfeVersion, createdOn);
                afeProposal = response.afeProposal;
                yield this.afeProposalRepository.deleteAfeProposalById(originalAfeProposalId, currentContext);
                yield this.afeProposalLimitDeductionRepository.cancelLimitDeductionsByAfeId(originalAfeProposalId, currentContext);
                const entityParents = yield this.adminApiClient.getParentsOfEntity(entityId);
                const logPayload = {
                    entityId: businessEntity.id,
                    action: enums_1.QUEUE_LOG_ACTION.SUBMITTED,
                    data: {
                        proposalId: afeProposal.id,
                        budgetType: budgetType ? mappings_1.BUDGET_TYPE_MAPPING_WITH_ID[budgetType.id] : null,
                        totalAmount: totalAmount / currencyDetail.conversionRate,
                        requestTypeId: requestTypeId,
                        afeType: type,
                        afeSubType: subType,
                        projectComponentId: projectComponentSplit === null || projectComponentSplit === void 0 ? void 0 : projectComponentSplit.map(p => p.id),
                        costCenterId: costCenterSplit === null || costCenterSplit === void 0 ? void 0 : costCenterSplit.map(c => c.id),
                        approversLevel: [...new Set(response.steps.map(step => step === null || step === void 0 ? void 0 : step.associateLevel))].filter(a => !!a),
                        businessUnitHierarchy: entityParents.map(entity => ({
                            id: entity.id,
                            code: entity.code,
                            level: entity.entity_type,
                        })),
                        status: enums_1.QUEUE_LOG_ACTION.SUBMITTED,
                    },
                };
                yield this.queueLogRepository.createQueueLogEntry(logPayload, currentContext);
                if (deletedSupportingDocuments === null || deletedSupportingDocuments === void 0 ? void 0 : deletedSupportingDocuments.length) {
                    yield this.sharedAttachmentService.deleteBulkAttachment(deletedSupportingDocuments);
                }
                if (supportingDocuments === null || supportingDocuments === void 0 ? void 0 : supportingDocuments.length) {
                    yield this.sharedAttachmentService.supportingDocumentsActivity(supportingDocuments, +afeProposal.id, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT, constants_1.ATTACHMENT_REL_PATH.AFE_DRAFT, currentContext.user.username, afeProposal.id);
                    yield this.moveAttachmentsFromSourceToDestination(enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT, originalAfeProposal.id, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT, afeProposal.id);
                }
                yield this.copyHistoryFromOriginalAfeToResubmitAfe(originalAfeProposalId, afeProposal.id);
                const addHistoryPayload = {
                    created_by: submitterId,
                    entity_id: afeProposal.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.RESUBMITTED,
                };
                if (originalApproverDetail) {
                    yield Promise.all([
                        this.historyApiClient.addRequestHistory(Object.assign(Object.assign({}, addHistoryPayload), { created_by: originalApproverDetail.loginId })),
                        this.historyApiClient.addRequestHistory(Object.assign(Object.assign({}, addHistoryPayload), { created_by: submitterId, additional_info: { hidden: true } })),
                    ]);
                }
                else {
                    yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                }
                yield this.taskApiClient.completeAllTasks({
                    entity_id: approverIdOfSendBackInProgressStep.id,
                    entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                    outcome: enums_1.TASK_ACTION.RESUBMIT,
                });
                yield this.taskService.createNextTasks(afeProposal.id, enums_1.TASK_ACTION.APPROVE, currentContext);
                console.log('>>>>>>> submitterDetails', submitterDetails);
                yield this.sendAfeCreationNotificationToSubmitter(afeProposal, submitterDetails.mail, 'AFE.TASK.APPROVAL.RESUBMITTED');
                yield this.sendAfeCreationNotificationToOthers(afeProposal, 'AFE.TASK.APPROVAL.RESUBMITTED');
                yield this.createNotificationOnAfeResubmission({ id: afeProposal.id, afeReferenceNumber: projectReferenceNumber }, { id: submitterId, name: `${firstName} ${lastName}` }, subscribers, currentContext);
            }));
            return { projectReferenceNumber };
        });
    }
    areCostCentersValid(businessEntityId, costCenterSplit) {
        return __awaiter(this, void 0, void 0, function* () {
            if ((costCenterSplit === null || costCenterSplit === void 0 ? void 0 : costCenterSplit.length) && costCenterSplit[costCenterSplit.length - 1].id) {
                const allCostCenterIds = costCenterSplit.map(costCenter => costCenter.id);
                const costCenterIds = [...new Set(allCostCenterIds)];
                const doAllCostCenterExist = yield this.afeProposalValidator.checkAllCostCentersBelongsToBusinessEntity(businessEntityId, costCenterIds);
                if (!doAllCostCenterExist) {
                    throw new exceptions_1.HttpException(`Invalid cost centers.`, enums_1.HttpStatus.BAD_REQUEST);
                }
            }
        });
    }
    supplementalAfeValidation(supplementalData, supplementalEntityId, afeDraftData) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!supplementalData) {
                throw new exceptions_1.HttpException('Supplemental data is missing.', enums_1.HttpStatus.BAD_REQUEST);
            }
            const { parentAfeNo: parentAfeProjectReferenceNumber } = supplementalData;
            if (!parentAfeProjectReferenceNumber) {
                throw new exceptions_1.HttpException('Parent afe proposal reference number is missing', enums_1.HttpStatus.BAD_REQUEST);
            }
            const parentAfeProposal = yield this.afeProposalRepository.getAfeProposalByReferenceNumber(parentAfeProjectReferenceNumber);
            if (!parentAfeProposal) {
                throw new exceptions_1.HttpException(`Parent Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (parentAfeProposal.entityId !== supplementalEntityId) {
                throw new exceptions_1.HttpException(`Supplemental business entity should be same with parent business entity. `, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (parentAfeProposal.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL) {
                throw new exceptions_1.HttpException(`Supplemental AFE can't be parent of another supplemental.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (parentAfeProposal.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.APPROVED) {
                throw new exceptions_1.HttpException(`Can't raise supplemental of unapproved AFE`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const inProgressSupplementalAfe = yield this.afeProposalRepository.inProgressAfeByParentAfeId(parentAfeProposal.id);
            if (inProgressSupplementalAfe &&
                inProgressSupplementalAfe.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.SENT_BACK) {
                throw new exceptions_1.HttpException(`Other supplemental is in progress for the AFE.`, enums_1.HttpStatus.CONFLICT);
            }
            const { requestTypeId } = afeDraftData;
            if ([6, 7].includes(requestTypeId)) {
                const { totalAmount: currentTotalAmount, totalMarketValue: currentTotalMarketValue } = afeDraftData;
                const { totalAmount: parentTotalAmount, marketValue: parentMarketValue } = parentAfeProposal;
                const parentDifference = (0, lodash_1.toNumber)(parentMarketValue) - (0, lodash_1.toNumber)(parentTotalAmount) <= 0
                    ? 'LESS_THAN_EQUAL_ZERO'
                    : 'GREATER_THAN_ZERO';
                const supplementalDifference = (0, lodash_1.toNumber)(currentTotalMarketValue) - (0, lodash_1.toNumber)(currentTotalAmount) <= 0
                    ? 'LESS_THAN_EQUAL_ZERO'
                    : 'GREATER_THAN_ZERO';
                if (parentDifference !== supplementalDifference) {
                    const message = `Total Market Value must be ${parentDifference === 'GREATER_THAN_ZERO' ? 'greater than' : 'less than or equal to'} Net Book Value.`;
                    throw new exceptions_1.HttpException(message, enums_1.HttpStatus.BAD_REQUEST);
                }
            }
            return { parentAfeProposalId: parentAfeProposal.id };
        });
    }
    createAfeProposal(createAfeProposalDto, projectReferenceNumber, currentContext, subscribers, submitterInfo, currencyDetail, parentAfeId = null, version = 1, createdOn = null) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username: submitterId } = currentContext.user;
            let { projectComponentSplit, costCenterSplit, analysisCodeSplit, naturalAccountNumberSplit, budgetBasedProjectSplit, chartOfAccounts, totalAmount, requestTypeId, budgetTypeSplit, budgetType, isApprovedByBoard, isNewFfoSetting, data, isSupplemental, businessEntity, questionAnswers, readers, approverList, entityId, supplementalData, lengthOfCommitment, totalMarketValue: marketValue, year: workflowYear, location, } = createAfeProposalDto;
            const { currency, primaryCurrency, conversionRate } = currencyDetail;
            let marketValueAdditionalCurrencyAmount = null;
            if (marketValue !== null && totalAmount !== undefined) {
                marketValueAdditionalCurrencyAmount = {
                    amount: marketValue,
                    currency: currency,
                    exchangeRateToPrimary: conversionRate,
                };
            }
            const additionalCurrencyAmount = {
                amount: totalAmount,
                currency: currencyDetail.currency,
                exchangeRateToPrimary: conversionRate,
            };
            const { projectDetails } = data;
            const { projectLeader } = projectDetails;
            projectLeader.loginId = projectLeader.loginId.toLowerCase();
            const { givenName: firstName, surname: lastName, mail: email, userPrincipalName, userType, jobTitle: title, } = submitterInfo;
            const submitterDetails = {
                firstName,
                lastName,
                email,
                loginId: userType === enums_1.AD_USER_TYPE.GUEST ? email.toLowerCase() : userPrincipalName.toLowerCase(),
                title,
            };
            let afeProposalDetails = Object.assign(Object.assign({ name: data.projectDetails.projectName, category: isSupplemental ? enums_1.AFE_CATEGORY.SUPPLEMENTAL : enums_1.AFE_CATEGORY.NEW, afeRequestTypeId: requestTypeId, submitterId: submitterId, isNewVersionInProgress: true, isApprovedByBoard: isApprovedByBoard, budgetType: (budgetType === null || budgetType === void 0 ? void 0 : budgetType.id) ? mappings_1.BUDGET_TYPE_ID_MAPPING[budgetType.id] : null, currencyType: primaryCurrency, totalAmount: totalAmount / conversionRate, additionalCurrencyAmount: additionalCurrencyAmount, marketValue: marketValue !== null && marketValue !== undefined ? marketValue / conversionRate : null, marketValueCurrency: marketValue !== null && marketValue !== undefined ? primaryCurrency : null, marketValueAdditionalCurrencyAmount, entityId: entityId, entityCode: businessEntity.code, entityTitle: businessEntity.name, projectReferenceNumber: projectReferenceNumber, data: Object.assign(Object.assign({}, data), { submitterDetails }), globalProcurementQuesAns: questionAnswers, readers: readers === null || readers === void 0 ? void 0 : readers.map(reader => (Object.assign(Object.assign({}, reader), { loginId: reader.loginId.toLowerCase() }))), yearOfCommitment: lengthOfCommitment, version: version, workflowYear: workflowYear || new Date().getFullYear() }, (isSupplemental && {
                supplementalData: supplementalData,
            })), { subscribers,
                parentAfeId,
                isNewFfoSetting, locationId: (location === null || location === void 0 ? void 0 : location.id) || null });
            if (createdOn) {
                afeProposalDetails = Object.assign(Object.assign({}, afeProposalDetails), { createdOn });
            }
            const { steps, masterSettingWorkflows, isProjectLeaderExist } = yield this.computeLastestApproversList(Object.assign(Object.assign({}, createAfeProposalDto), { parentAfeId: afeProposalDetails.parentAfeId }), submitterDetails.loginId);
            if (isProjectLeaderExist && steps.length === 1) {
                throw new exceptions_1.HttpException(`Approval workflow does't exist.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (this.isConflictExistInWorkflowSteps(steps, approverList)) {
                throw new exceptions_1.HttpException(`Conflict in the workflow steps.`, enums_1.HttpStatus.CONFLICT);
            }
            const afeProposal = yield this.afeProposalRepository.createAfeProposal(afeProposalDetails, currentContext);
            const { id: afeProposalId } = afeProposal;
            const afeAmountSplits = [];
            if (projectComponentSplit === null || projectComponentSplit === void 0 ? void 0 : projectComponentSplit.length) {
                const projectComponentSplits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT, projectComponentSplit, afeProposalId, primaryCurrency, conversionRate);
                afeAmountSplits.push(...projectComponentSplits);
                if (!(budgetBasedProjectSplit === null || budgetBasedProjectSplit === void 0 ? void 0 : budgetBasedProjectSplit.length) && afeProposalDetails.budgetType !== enums_1.BUDGET_TYPE.MIXED) {
                    const projectAndBudgetTypeSplits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT, projectComponentSplit.map(p => ({
                        id: p.id,
                        title: p.title,
                        totalAmount: p.amount,
                        currency: p.currency,
                        budgetedAmount: afeProposalDetails.budgetType === enums_1.BUDGET_TYPE.BUDGETED ? p.amount : 0,
                        unbudgetedAmount: afeProposalDetails.budgetType === enums_1.BUDGET_TYPE.UNBUDGETED ? p.amount : 0,
                    })), afeProposalId, primaryCurrency, conversionRate);
                    afeAmountSplits.push(...projectAndBudgetTypeSplits);
                }
            }
            if (budgetBasedProjectSplit === null || budgetBasedProjectSplit === void 0 ? void 0 : budgetBasedProjectSplit.length) {
                const splits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT, budgetBasedProjectSplit, afeProposalId, primaryCurrency, conversionRate);
                afeAmountSplits.push(...splits);
            }
            if (budgetTypeSplit === null || budgetTypeSplit === void 0 ? void 0 : budgetTypeSplit.length) {
                const splits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.BUDGET_TYPE_SPLIT, budgetTypeSplit, afeProposalId, primaryCurrency, conversionRate);
                afeAmountSplits.push(...splits);
            }
            if (naturalAccountNumberSplit === null || naturalAccountNumberSplit === void 0 ? void 0 : naturalAccountNumberSplit.length) {
                const splits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.NATURAL_ACCOUNT_SPLIT, naturalAccountNumberSplit, afeProposalId, primaryCurrency, conversionRate);
                afeAmountSplits.push(...splits);
            }
            if (analysisCodeSplit === null || analysisCodeSplit === void 0 ? void 0 : analysisCodeSplit.length) {
                const splits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.ANALYSIS_CODE_SPLIT, analysisCodeSplit, afeProposalId, primaryCurrency, conversionRate);
                afeAmountSplits.push(...splits);
            }
            if (costCenterSplit === null || costCenterSplit === void 0 ? void 0 : costCenterSplit.length) {
                const splits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT, costCenterSplit, afeProposalId, primaryCurrency, conversionRate);
                afeAmountSplits.push(...splits);
            }
            if (chartOfAccounts === null || chartOfAccounts === void 0 ? void 0 : chartOfAccounts.length) {
                const splits = this.createCostSplitPayload(enums_1.AMOUNT_SPLIT.GL_CODE_SPLIT, chartOfAccounts, afeProposalId, primaryCurrency, conversionRate);
                afeAmountSplits.push(...splits);
            }
            console.dir(afeAmountSplits, { depth: 10 });
            yield this.afeProposalAmountSplitRepository.bulkAmountSplitsInsert(afeAmountSplits, currentContext);
            yield this.saveAfeApproversList(approverList, afeProposalId, currentContext);
            if (masterSettingWorkflows === null || masterSettingWorkflows === void 0 ? void 0 : masterSettingWorkflows.length) {
                yield this.createLimitDeductionEntries(afeProposal, masterSettingWorkflows, currentContext);
            }
            if (isSupplemental) {
                const lastApprovedSupplementalAfe = yield this.afeProposalRepository.getLastApprovedAfeByParentId(parentAfeId);
                const supplementalDeltaAmounts = yield this.calculateDeltaAmounts(lastApprovedSupplementalAfe, afeProposal);
                this.afeProposalRepository.updateAfeProposalById(afeProposal.id, currentContext, {
                    supplementalDeltaAmounts,
                });
                const isPreviousSupplementalOrParentAfeApprovedByBoard = yield this.afeProposalRepository.isAnyPreviousSupplementalOrParentAfeApprovedByBoard(parentAfeId);
                if (masterSettingWorkflows.some(entry => entry.amount < 0) &&
                    !isApprovedByBoard &&
                    isPreviousSupplementalOrParentAfeApprovedByBoard &&
                    isSupplemental) {
                    const businessHierarchy = yield this.adminApiClient.getAllBusinessHierarchy();
                    const itSupportEmails = yield this.getUsersEmailByRoleAndEntityId('ITAdmin', businessHierarchy.id);
                    yield this.sharedNotificationService.sendNotificationForAfeProposal(afeProposalId, afeProposalId, enums_1.NOTIFICATION_ENTITY_TYPE.FUSION_NOTIFICATION, { to: [...itSupportEmails] }, 'AFE.EMAIL.BOARDSECRETARY.DEDUCTION.ISSUE', false);
                }
            }
            return { afeProposal, steps };
        });
    }
    getUsersEmailByRoleAndEntityId(role, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const userEmails = [];
            const users = yield this.adminApiClient.getUsersByRoleOfAnEntity(role, entityId);
            const userIds = users === null || users === void 0 ? void 0 : users.map(user => user.user_name.toLowerCase());
            const userAdDetails = userIds.length
                ? yield this.mSGraphApiClient.getUsersDetails(userIds)
                : [];
            userEmails.push(...userAdDetails.map(user => user.mail));
            return userEmails;
        });
    }
    createNotificationOnAfeSubmission(afeDetails, submitterDetails, subscribers, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const url = `${(0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_URLS.AFE_DETAILS_URL, {
                afeId: `${afeDetails.id}`,
            })}`;
            const expireAt = (0, helpers_1.getNotificationExpiryDate)();
            const type = enums_1.NOTIFICATION_TYPE.INFO;
            const submitterNotification = {
                title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_SUBMIT.SUBMITTER, {
                    afeReferenceNumber: afeDetails.afeReferenceNumber,
                }),
                url: url,
                subscribers: [submitterDetails.id],
                expireAt,
                type,
            };
            const subscribersNotification = {
                title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_SUBMIT.SUBSCRIBER, {
                    afeReferenceNumber: afeDetails.afeReferenceNumber,
                    submitterName: submitterDetails.name,
                }),
                url: url,
                subscribers,
                expireAt,
                type,
            };
            yield this.notificationRepository.bulkNotificationsInsert([submitterNotification, subscribersNotification], currentContext);
        });
    }
    createNotificationOnAfeResubmission(newAfeDetails, submitterDetails, subscribers, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { afeReferenceNumber, id } = newAfeDetails;
            const url = `${(0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_URLS.AFE_DETAILS_URL, {
                afeId: `${id}`,
            })}`;
            const expireAt = (0, helpers_1.getNotificationExpiryDate)();
            const type = enums_1.NOTIFICATION_TYPE.INFO;
            const submitterNotification = {
                title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_RE_SUBMIT.SUBMITTER, {
                    afeReferenceNumber,
                }),
                url: url,
                subscribers: [submitterDetails.id],
                expireAt,
                type,
            };
            const subscribersNotification = {
                title: (0, helpers_1.stringPlaceholderReplacer)(constants_1.NOTIFICATION_TITLES.AFE_RE_SUBMIT.SUBSCRIBER, {
                    afeReferenceNumber,
                    submitterName: submitterDetails.name,
                }),
                url: url,
                subscribers,
                expireAt,
                type,
            };
            yield this.notificationRepository.bulkNotificationsInsert([submitterNotification, subscribersNotification], currentContext);
        });
    }
    createLimitDeductionEntries(afeProposal, workflows, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const limitDeductionAfeData = {
                afeReferenceNumber: afeProposal.projectReferenceNumber,
                projectName: afeProposal.name,
                requestTypeId: afeProposal.afeRequestTypeId,
                isSupplemental: afeProposal.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL,
                submitterId: afeProposal.submitterId,
                budgetType: afeProposal.budgetType || null,
                year: afeProposal.workflowYear,
                isApprovedByBoard: afeProposal.isApprovedByBoard,
                parentAfeId: afeProposal.parentAfeId || null,
            };
            const entries = workflows.map(workflow => ({
                afeProposalId: afeProposal.id,
                entityId: (workflow === null || workflow === void 0 ? void 0 : workflow.associatedEntityId) || null,
                entityTitle: (workflow === null || workflow === void 0 ? void 0 : workflow.associatedEntityTitle) || null,
                entityCode: (workflow === null || workflow === void 0 ? void 0 : workflow.associatedEntityCode) || null,
                costCenterId: (workflow === null || workflow === void 0 ? void 0 : workflow.costCenterId) || null,
                workflowMasterSettingId: workflow.workflowMasterSettingId,
                workflowMasterStepId: workflow.deductionStepId,
                amount: workflow.amount,
                status: enums_1.AFE_LIMIT_DEDUCATION_STATUS.IN_PROGRESS,
                data: limitDeductionAfeData,
            }));
            const deductions = yield this.afeProposalLimitDeductionRepository.bulkInsertAfeProposalLimitDeductions(entries, currentContext);
            for (const deduction of deductions) {
                const workflowDeduction = workflows.find(workflow => workflow.deductionStepId === deduction.workflowMasterStepId);
                if (workflowDeduction === null || workflowDeduction === void 0 ? void 0 : workflowDeduction.parentDeductionStepId) {
                    const parentDeduction = deductions.find(d => d.workflowMasterStepId === workflowDeduction.parentDeductionStepId &&
                        d.amount === workflowDeduction.amount);
                    if (parentDeduction) {
                        yield this.afeProposalLimitDeductionRepository.updateParentDeductionId(deduction.id, parentDeduction.id, currentContext);
                    }
                }
            }
        });
    }
    computeLastestApproversList(afeData, submitterId) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            const { projectComponentSplit, costCenterSplit, budgetBasedProjectSplit, totalAmount, requestTypeId, budgetTypeSplit, budgetType, isApprovedByBoard, businessEntity, lengthOfCommitment, currencyDetail, year: workflowYear, data, parentAfeId, isSupplemental, } = afeData;
            const computeApproversListPayload = {
                requestTypeId: requestTypeId,
                budgetType: (budgetType === null || budgetType === void 0 ? void 0 : budgetType.title) || null,
                entityId: businessEntity.id,
                totalAmount: totalAmount,
                isApprovedByBoard: isApprovedByBoard,
                currency: currencyDetail.currency,
                year: workflowYear,
                lengthOfCommitment: lengthOfCommitment,
                afeSubType: (data === null || data === void 0 ? void 0 : data.subType) || null,
                afeType: (data === null || data === void 0 ? void 0 : data.type) || null,
                parentAfeId: parentAfeId || null,
                isSupplemental: isSupplemental || false,
                projectLeaderId: ((_b = (_a = data === null || data === void 0 ? void 0 : data.projectDetails) === null || _a === void 0 ? void 0 : _a.projectLeader) === null || _b === void 0 ? void 0 : _b.loginId) || null,
            };
            if (projectComponentSplit === null || projectComponentSplit === void 0 ? void 0 : projectComponentSplit.length) {
                computeApproversListPayload.projectComponentSplits = projectComponentSplit.map(split => ({
                    id: split.id,
                    title: split.title,
                    amount: split.amount,
                    currency: split.currency,
                }));
            }
            if ((budgetBasedProjectSplit === null || budgetBasedProjectSplit === void 0 ? void 0 : budgetBasedProjectSplit.length) &&
                budgetType.id === mappings_1.BUDGET_TYPE_MAPPING_WITH_ID[enums_1.BUDGET_TYPE.MIXED]) {
                computeApproversListPayload.budgetBasedProjectSplit = budgetBasedProjectSplit.map(split => ({
                    id: split.id,
                    title: split.title,
                    totalAmount: split.totalAmount,
                    currency: split.currency,
                    unbudgetedAmount: split.unbudgetedAmount,
                    budgetedAmount: split.budgetedAmount,
                }));
            }
            if (budgetTypeSplit === null || budgetTypeSplit === void 0 ? void 0 : budgetTypeSplit.length) {
                computeApproversListPayload.budgetTypeSplits = budgetTypeSplit.map(split => ({
                    id: split.id,
                    title: split.title,
                    amount: split.amount,
                    currency: split.currency,
                }));
            }
            if ((costCenterSplit === null || costCenterSplit === void 0 ? void 0 : costCenterSplit.length) && costCenterSplit[costCenterSplit.length - 1].id) {
                computeApproversListPayload.costCenters = costCenterSplit
                    .map(split => ({
                    id: split.id,
                    code: split.code,
                    amount: split.budgetReferenceNumberSplit.reduce((accumulator, currentValue) => accumulator + currentValue.amount, 0),
                    section: split.section,
                }))
                    .filter(split => split.id !== 0);
            }
            return this.workflowService.getAfeApproversList(computeApproversListPayload, submitterId);
        });
    }
    isConflictExistInWorkflowSteps(computedApprovers, savedApprovers) {
        const computedStepsInSavedListCount = savedApprovers.reduce((count, appover) => (count = appover.isCustomUser ? count : count + 1), 0);
        if (computedStepsInSavedListCount !== computedApprovers.length) {
            return true;
        }
        else {
            let i = 0, j = 0;
            while (i < savedApprovers.length) {
                if (!savedApprovers[i].isCustomUser) {
                    const draftWorkflowStepKey = this.createStepUniqueKey(savedApprovers[i].associateLevel, savedApprovers[i].associateRole, savedApprovers[i].associateType, savedApprovers[i].associatedColumn);
                    const computedWorkflowStepKey = this.createStepUniqueKey(computedApprovers[j].associateLevel, computedApprovers[j].associateRole, computedApprovers[j].associateType, computedApprovers[j].associatedColumn);
                    if (draftWorkflowStepKey !== computedWorkflowStepKey) {
                        return true;
                    }
                    j += 1;
                }
                i += 1;
            }
        }
        return false;
    }
    createProjectReferenceNumber(requestTypeId, entityId, year) {
        return __awaiter(this, void 0, void 0, function* () {
            const { businessEntityLevelForProjectReferenceNumber } = this.configService.getAppConfig();
            const { other_info: regionOtherInfo } = yield this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(entityId, businessEntityLevelForProjectReferenceNumber);
            const { other_info: businessUnitOtherInfo } = yield this.adminApiClient.getBusinessEntityDetailsById(entityId);
            const afeRequestTypeCode = mappings_1.AFE_REQUEST_TYPE_ID_MAPPING[requestTypeId];
            const sequenceNumber = yield this.requestApiClient.generateNextSequenceNumber({
                prefix: afeRequestTypeCode,
                meta_data_1: `${year}`,
            });
            const getLastTwoDigitsOfYear = (year) => {
                let lastTwoDigits = year % 100;
                if (year < 0) {
                    lastTwoDigits = -lastTwoDigits;
                }
                return lastTwoDigits;
            };
            const padWithZeros = (num, desiredLength) => {
                const numString = num.toString();
                if (numString.length < desiredLength) {
                    const zerosToAdd = desiredLength - numString.length;
                    return '0'.repeat(zerosToAdd) + numString;
                }
                else {
                    return numString;
                }
            };
            let referenceNumber = `${getLastTwoDigitsOfYear(year)}${afeRequestTypeCode}${padWithZeros(sequenceNumber, 4)}`;
            if ((regionOtherInfo === null || regionOtherInfo === void 0 ? void 0 : regionOtherInfo.display_code) && (businessUnitOtherInfo === null || businessUnitOtherInfo === void 0 ? void 0 : businessUnitOtherInfo.display_code)) {
                referenceNumber = `${regionOtherInfo.display_code}-${businessUnitOtherInfo.display_code}-${referenceNumber}`;
            }
            else if (regionOtherInfo === null || regionOtherInfo === void 0 ? void 0 : regionOtherInfo.display_code) {
                referenceNumber = `${regionOtherInfo.display_code}-${referenceNumber}`;
            }
            else if (businessUnitOtherInfo === null || businessUnitOtherInfo === void 0 ? void 0 : businessUnitOtherInfo.display_code) {
                referenceNumber = `${businessUnitOtherInfo.display_code}-${referenceNumber}`;
            }
            return referenceNumber;
        });
    }
    createCostSplitPayload(splitType, amountSplitData, afeProposalId, primaryCurrencyType, conversionRate) {
        if (splitType === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT) {
            const costCenter = amountSplitData[amountSplitData.length - 1];
            const currency = costCenter
                .budgetReferenceNumberSplit[0];
            const splits = [];
            const costCenterSplits = amountSplitData.map(split => {
                const amount = split.budgetReferenceNumberSplit.reduce((total, referenceNumber) => total + referenceNumber.amount, 0);
                const costCenterBudgetReferenceSplit = split.budgetReferenceNumberSplit
                    .map(split => ({
                    number: (split === null || split === void 0 ? void 0 : split.number) || '',
                    currency: primaryCurrencyType,
                    amount: split.amount / conversionRate,
                    additionalCurrencyAmount: {
                        amount: split.amount,
                        currency: currency.currency,
                        exchangeRateToPrimary: conversionRate,
                    },
                }));
                return {
                    objectId: split.id,
                    objectTitle: split.title,
                    afeProposalId: afeProposalId,
                    type: splitType,
                    currency: primaryCurrencyType,
                    amount: amount / conversionRate,
                    additionalCurrencyAmount: {
                        amount: amount,
                        currency: currency.currency,
                        exchangeRateToPrimary: conversionRate,
                    },
                    additionalInfo: {
                        section: split.section,
                        analysisCode: (split === null || split === void 0 ? void 0 : split.analysisCode) || null,
                        budgetReferenceNumberSplit: costCenterBudgetReferenceSplit,
                    },
                };
            });
            splits.push(...costCenterSplits);
            return splits;
        }
        else if (splitType === enums_1.AMOUNT_SPLIT.GL_CODE_SPLIT) {
            return amountSplitData.map(split => ({
                objectId: null,
                objectTitle: `${split.segments.segment1}-${split.segments.segment2}-${split.segments.segment3}-${split.segments.segment4}-${split.segments.segment5}-${split.segments.segment6}-${split.segments.segment7}-${split.segments.segment8}`,
                afeProposalId: afeProposalId,
                type: splitType,
                currency: primaryCurrencyType,
                amount: split.amount / conversionRate,
                additionalCurrencyAmount: {
                    amount: split.amount,
                    currency: split.currency,
                    exchangeRateToPrimary: conversionRate,
                },
            }));
        }
        else if (splitType === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT) {
            const amountSplitObjects = [];
            for (const split of amountSplitData) {
                const { id, title, budgetedAmount, unbudgetedAmount, currency } = split;
                const commonProps = {
                    objectId: id,
                    objectTitle: title,
                    afeProposalId: afeProposalId,
                    type: splitType,
                    currency: primaryCurrencyType,
                };
                if (budgetedAmount) {
                    amountSplitObjects.push(Object.assign(Object.assign({}, commonProps), { budgetType: enums_1.BUDGET_TYPE.BUDGETED, amount: budgetedAmount / conversionRate, additionalCurrencyAmount: {
                            amount: budgetedAmount,
                            currency: currency,
                            exchangeRateToPrimary: conversionRate,
                        } }));
                }
                if (unbudgetedAmount) {
                    amountSplitObjects.push(Object.assign(Object.assign({}, commonProps), { budgetType: enums_1.BUDGET_TYPE.UNBUDGETED, amount: unbudgetedAmount / conversionRate, additionalCurrencyAmount: {
                            amount: unbudgetedAmount,
                            currency: currency,
                            exchangeRateToPrimary: conversionRate,
                        } }));
                }
            }
            return amountSplitObjects;
        }
        else {
            return amountSplitData.map(split => ({
                objectId: split.id,
                objectTitle: split.title || split.number,
                afeProposalId: afeProposalId,
                type: splitType,
                currency: primaryCurrencyType,
                amount: split.amount / conversionRate,
                additionalCurrencyAmount: {
                    amount: split.amount,
                    currency: split.currency,
                    exchangeRateToPrimary: conversionRate,
                },
            }));
        }
    }
    createStepUniqueKey(associatedLevel, associateRole, associateType, associatedColumn) {
        return `associatedLevel:${associatedLevel || ''}#associateRole:${associateRole || ''}#associateType:${associateType || ''}#associatedColumn:${associatedColumn || ''}`;
    }
    getCountOfApproverStepSkip(approvers) {
        let count = 0;
        for (const approver of approvers) {
            if (approver.isStartingPoint) {
                return count;
            }
            count += 1;
        }
        return 0;
    }
    saveAfeApproversList(approvers, proposalId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            approvers.sort((a, b) => a.sequenceNumber - b.sequenceNumber);
            const skipCount = this.getCountOfApproverStepSkip(approvers);
            let sequenceNumber = 1;
            const approversList = approvers.slice(skipCount).map(approver => ({
                assignedTo: approver.associateRole ||
                    approver.approvers[approver.approvers.length - 1].loginId.toLowerCase(),
                title: approver.title,
                afeProposalId: proposalId,
                userDetail: null,
                otherInfo: Object.assign({ usersDetail: approver.approvers, approvalType: enums_1.APPROVAL_TYPE.APPROVAL }, (approver.associateType === associated_type_enum_1.ASSOCIATED_TYPE.COST_CENTER &&
                    approver.section && { section: approver.section })),
                assignedLevel: approver.associateLevel,
                assginedType: approver.associateType || enums_1.ASSIGNED_TYPE.USER,
                assignedEntityId: approver.associatedLevelEntityId || null,
                associatedColumn: approver.associatedColumn,
                parallelIdentifier: approver.parallelIdentifier,
                approvalSequence: sequenceNumber++,
                workflowMasterStepsId: approver.stepId,
                associatedCostCenterId: (approver === null || approver === void 0 ? void 0 : approver.associatedCostCenterId) || null,
            }));
            return this.afeProposalApproverRepository.bulkApproversInsert(approversList, currentContext);
        });
    }
    getAfeDetailUrl(proposalId) {
        const config = this.configService.getAppConfig();
        return `${config.uiClient.baseUrl}/afe/afe-detail/${proposalId}`;
    }
    sendAfeCreationNotificationToSubmitter(afeProposal, submitterEmail, templateName) {
        return __awaiter(this, void 0, void 0, function* () {
            const placeholdersValues = {
                afeDetailLink: this.getAfeDetailUrl(afeProposal.id),
            };
            yield this.sharedNotificationService.sendNotificationForAfeProposal(afeProposal.id, afeProposal.id, enums_1.NOTIFICATION_ENTITY_TYPE.AFE_PROPOSAL_CREATION_NOTIFICATION, { to: [submitterEmail] }, templateName, false, placeholdersValues);
        });
    }
    sendAfeCreationNotificationToOthers(afeProposal, templateName) {
        return __awaiter(this, void 0, void 0, function* () {
            const placeholdersValues = {
                afeDetailLink: this.getAfeDetailUrl(afeProposal.id),
            };
            const { readers, data } = afeProposal;
            let readersEmail = [];
            if (readers === null || readers === void 0 ? void 0 : readers.length) {
                readersEmail = readers.filter(r => !!r.mail).map(r => r.mail);
            }
            const receiverEmails = [...new Set([data.projectDetails.projectLeader.mail, ...readersEmail])];
            yield this.sharedNotificationService.sendNotificationForAfeProposal(afeProposal.id, afeProposal.id, enums_1.NOTIFICATION_ENTITY_TYPE.AFE_PROPOSAL_CREATION_NOTIFICATION, { to: receiverEmails }, templateName, false, placeholdersValues);
        });
    }
    moveAttachmentsFromSourceToDestination(sourceEntityType, sourceId, destinationEntityType, destinationId) {
        return __awaiter(this, void 0, void 0, function* () {
            const payload = {
                source_entity_type: sourceEntityType,
                source_entity_id: sourceId,
                destination_entity_type: destinationEntityType,
                destination_entity_id: +destinationId,
                destination_folder_path: (0, helpers_1.replaceUrlVariable)(constants_1.ATTACHMENT_REL_PATH.AFE_SUBMIT, {
                    entity_id: destinationId,
                }),
                meta_data_1: destinationId.toString(),
            };
            yield this.attachmentApiClient.moveAttachments(payload);
        });
    }
    copyHistoryFromOriginalAfeToResubmitAfe(sourceEntityId, destinationEntityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const sourceHistories = yield this.historyApiClient.getRequestHistory(sourceEntityId, enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL);
            const destinationHistoriesPromise = sourceHistories.map(history => {
                return this.historyApiClient.addRequestHistory({
                    created_by: history.created_by,
                    entity_id: destinationEntityId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: history.action_performed,
                    comments: history.action_comments,
                    additional_info: history.additional_info,
                    action_date: history.action_date,
                });
            });
            yield Promise.all(destinationHistoriesPromise);
        });
    }
    toggleAfeNotificationSubscription(afeProposalId, toggleValue, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
            }
            if (proposal.createdBy === currentContext.user.username) {
                throw new exceptions_1.HttpException(`Afe submitter can't perform this action.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (toggleValue === enums_1.TOGGLE_ON_OFF.ON) {
                yield this.afeProposalRepository.addContextUserToSubscriberList(afeProposalId, currentContext);
                return;
            }
            else if (toggleValue === enums_1.TOGGLE_ON_OFF.OFF) {
                yield this.afeProposalRepository.removeContextUserFromSubscibersList(afeProposalId, currentContext);
                return;
            }
            throw new exceptions_1.HttpException(`Toggle value should be 'ON' or 'OFF`, enums_1.HttpStatus.BAD_REQUEST);
        });
    }
    updateProposalDetail(updateAfeDetailRequestDTO, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const generalUpdatePermission = yield this.adminApiClient.hasPermissionToUser(currentContext.user.username, enums_1.PERMISSIONS.AFE_GENERAL_UPADTES, updateAfeDetailRequestDTO.entityId);
            const financeUpdatePermission = yield this.adminApiClient.hasPermissionToUser(currentContext.user.username, enums_1.PERMISSIONS.AFE_ALLOW_FINANCE_UPDATES, updateAfeDetailRequestDTO.entityId);
            if (!financeUpdatePermission &&
                ((updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.glCodes) ||
                    (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.naturalAccountNumbers) ||
                    (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.analysisCodeSplits))) {
                throw new exceptions_1.HttpException(`You are not authorized to update GL Code, Natural Account & Analysis Code.`, enums_1.HttpStatus.UNAUTHORIZED);
            }
            if (!generalUpdatePermission &&
                ((updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.projectName) ||
                    (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.projectLeaderNumber) ||
                    (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.projectJustification) ||
                    (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.budgetTypeJustification) ||
                    ((_a = updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.budgetRefNos) === null || _a === void 0 ? void 0 : _a.length) ||
                    (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.natureType))) {
                throw new exceptions_1.HttpException(`You are not authorized to update general AFE details.`, enums_1.HttpStatus.UNAUTHORIZED);
            }
            if (generalUpdatePermission || financeUpdatePermission) {
                const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(updateAfeDetailRequestDTO.proposalId);
                if (proposal) {
                    return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                        var _b, _c, _d, _e, _f;
                        if (generalUpdatePermission) {
                            if (proposal.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.IN_PROGRESS) {
                                throw new exceptions_1.HttpException(enums_1.AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus] + ` AFE can't be modified.`, enums_1.HttpStatus.NOT_ACCEPTABLE);
                            }
                            let proposalData = proposal.data;
                            let projectName = proposal.name;
                            let splitData = (proposal === null || proposal === void 0 ? void 0 : proposal.afeProposalAmountSplits) || [];
                            if (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.projectName) {
                                projectName = updateAfeDetailRequestDTO.projectName;
                                proposalData.projectDetails.projectName = updateAfeDetailRequestDTO.projectName;
                            }
                            if (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.projectJustification) {
                                proposalData.projectDetails.projectJustification =
                                    updateAfeDetailRequestDTO.projectJustification;
                            }
                            if (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.projectLeaderNumber) {
                                if (!((_b = proposalData.projectDetails) === null || _b === void 0 ? void 0 : _b.projectLeaderNumber)) {
                                    proposalData.projectDetails = Object.assign(Object.assign({}, proposalData.projectDetails), { projectLeaderNumber: '' });
                                }
                                proposalData.projectDetails.projectLeaderNumber =
                                    updateAfeDetailRequestDTO.projectLeaderNumber;
                            }
                            if (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.budgetTypeJustification) {
                                proposalData.budgetTypeJustification =
                                    updateAfeDetailRequestDTO.budgetTypeJustification;
                            }
                            if (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.natureType) {
                                proposalData.natureType = updateAfeDetailRequestDTO.natureType;
                            }
                            let updatedData = {
                                name: projectName,
                                data: proposalData,
                            };
                            yield this.afeProposalRepository.updateAfeProposalById(updateAfeDetailRequestDTO.proposalId, currentContext, updatedData);
                            if ((_c = updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.budgetRefNos) === null || _c === void 0 ? void 0 : _c.length) {
                                const updatedBudgetRefNos = (updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.budgetRefNos) || [];
                                const prevCostCenterSplits = splitData.filter(splitDetail => {
                                    return splitDetail.type === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT;
                                });
                                const costCenterObj = prevCostCenterSplits.map(costCenterDetail => {
                                    var _a;
                                    return {
                                        splitId: costCenterDetail.id,
                                        budgetReferenceNumberSplit: ((_a = costCenterDetail === null || costCenterDetail === void 0 ? void 0 : costCenterDetail.additionalInfo) === null || _a === void 0 ? void 0 : _a.budgetReferenceNumberSplit) || [],
                                        additionalInfo: (costCenterDetail === null || costCenterDetail === void 0 ? void 0 : costCenterDetail.additionalInfo) || {},
                                    };
                                });
                                const mergedCCData = costCenterObj.map(costCenterDetail => {
                                    const brnSplit = costCenterDetail.budgetReferenceNumberSplit.map(budgetReferenceNumberSplitDetail => {
                                        const newNumber = updatedBudgetRefNos.find(updatedBudgetRefDetail => {
                                            if ((0, lodash_1.toNumber)(updatedBudgetRefDetail.id) ===
                                                (0, lodash_1.toNumber)(costCenterDetail.splitId) &&
                                                updatedBudgetRefDetail.oldNumber === budgetReferenceNumberSplitDetail.number) {
                                                return updatedBudgetRefDetail;
                                            }
                                        });
                                        return Object.assign(Object.assign({}, budgetReferenceNumberSplitDetail), { number: (newNumber === null || newNumber === void 0 ? void 0 : newNumber.number) || '' });
                                    });
                                    return Object.assign(Object.assign({}, costCenterDetail), { additionalInfo: Object.assign(Object.assign({}, costCenterDetail.additionalInfo), { budgetReferenceNumberSplit: brnSplit }) });
                                });
                                if (mergedCCData === null || mergedCCData === void 0 ? void 0 : mergedCCData.length) {
                                    for (let i = 0; i < mergedCCData.length; i++) {
                                        const additionalInfo = mergedCCData[i].additionalInfo;
                                        const id = mergedCCData[i].splitId;
                                        yield this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(id, currentContext, {
                                            additionalInfo,
                                        });
                                    }
                                }
                            }
                        }
                        if (financeUpdatePermission && ((_d = updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.glCodes) === null || _d === void 0 ? void 0 : _d.length)) {
                            for (let i = 0; i < updateAfeDetailRequestDTO.glCodes.length; i++) {
                                const glcode = updateAfeDetailRequestDTO.glCodes[i].glcode;
                                const id = updateAfeDetailRequestDTO.glCodes[i].id;
                                yield this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(id, currentContext, {
                                    objectTitle: glcode,
                                });
                            }
                        }
                        if (financeUpdatePermission && ((_e = updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.analysisCodeSplits) === null || _e === void 0 ? void 0 : _e.length)) {
                            for (let i = 0; i < updateAfeDetailRequestDTO.analysisCodeSplits.length; i++) {
                                const objectId = updateAfeDetailRequestDTO.analysisCodeSplits[i].analysisId;
                                const title = updateAfeDetailRequestDTO.analysisCodeSplits[i].title;
                                const id = updateAfeDetailRequestDTO.analysisCodeSplits[i].id;
                                yield this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(id, currentContext, {
                                    objectTitle: title,
                                    objectId,
                                });
                            }
                        }
                        if (financeUpdatePermission && ((_f = updateAfeDetailRequestDTO === null || updateAfeDetailRequestDTO === void 0 ? void 0 : updateAfeDetailRequestDTO.naturalAccountNumbers) === null || _f === void 0 ? void 0 : _f.length)) {
                            for (let i = 0; i < updateAfeDetailRequestDTO.naturalAccountNumbers.length; i++) {
                                const objectId = updateAfeDetailRequestDTO.naturalAccountNumbers[i].naturalAccountId;
                                const title = updateAfeDetailRequestDTO.naturalAccountNumbers[i].title;
                                const id = updateAfeDetailRequestDTO.naturalAccountNumbers[i].id;
                                yield this.afeProposalAmountSplitRepository.updateAfeProposalAmountById(id, currentContext, {
                                    objectTitle: title,
                                    objectId,
                                });
                            }
                        }
                        const addHistoryPayload = {
                            created_by: currentContext.user.username,
                            entity_id: updateAfeDetailRequestDTO.proposalId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                            additional_info: {
                                newUpdate: updateAfeDetailRequestDTO,
                            },
                        };
                        yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                        return { message: 'AFE detail has been updated successfully.' };
                    }));
                }
                else {
                    throw new exceptions_1.HttpException(`AFE detail is not available.`, enums_1.HttpStatus.NOT_FOUND);
                }
            }
            throw new exceptions_1.HttpException(`You are not authorized to update AFE detail.`, enums_1.HttpStatus.UNAUTHORIZED);
        });
    }
    addNewReader(proposalId, addNewReadersRequestDto, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            let { readers } = addNewReadersRequestDto;
            const proposal = yield this.afeProposalRepository.getAfeProposalById(proposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if ((_a = proposal === null || proposal === void 0 ? void 0 : proposal.readers) === null || _a === void 0 ? void 0 : _a.length) {
                readers = [...proposal.readers, ...readers];
            }
            yield this.afeProposalRepository.updateAfeProposalById(proposalId, currentContext, {
                readers,
            });
            const addHistoryPayload = {
                created_by: currentContext.user.username,
                entity_id: proposalId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATED,
                comments: 'Reader addedd.',
            };
            yield this.historyApiClient.addRequestHistory(addHistoryPayload);
            return { message: 'New Reader has been added successfully!' };
        });
    }
    uploadEvidence(proposalId, evidencesRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalById(proposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const { evidences } = evidencesRequestDto;
            if (evidences === null || evidences === void 0 ? void 0 : evidences.length) {
                yield this.sharedAttachmentService.supportingDocumentsActivity(evidences, proposalId, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT, constants_1.ATTACHMENT_REL_PATH.AFE_SUBMIT, currentContext.user.username, proposalId);
                const addHistoryPayload = {
                    created_by: currentContext.user.username,
                    entity_id: proposalId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATED,
                    comments: 'The evidence/document has been uploaded to the supporting documents section.',
                };
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                return { message: 'Evidence has been uploaded successfully!' };
            }
            throw new exceptions_1.HttpException(`Evidence required.`, enums_1.HttpStatus.NOT_FOUND);
        });
    }
    withdrawAfeProposal(withdrawAfeProposalRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, comments } = withdrawAfeProposalRequestDto;
            const { user } = currentContext;
            const proposal = yield this.afeProposalRepository.getAfeProposalById(id);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (proposal.submitterId.toLowerCase() !== user.username.toLowerCase() &&
                !this.adminApiClient.hasPermissionToUser(user.username.toLowerCase(), enums_1.PERMISSIONS.AFE_ADMINISTRATION, proposal.entityId)) {
                throw new exceptions_1.HttpException(`You are not authorized to withraw this AFE`, enums_1.HttpStatus.UNAUTHORIZED);
            }
            if (proposal.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.IN_PROGRESS &&
                proposal.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.SENT_BACK) {
                throw new exceptions_1.HttpException(`It is not possible to withdraw the proposal since this AFE has already been ${enums_1.AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]}.`, enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const approvers = yield this.afeProposalApproverRepository.getInProgressApproversListByProposalId(id);
                let modifiedComment = '';
                if (proposal.submitterId.toLowerCase() === user.username.toLowerCase()) {
                    modifiedComment = `Proposal has been withdrawn by the submitter with the reason '${comments}'`;
                }
                else {
                    modifiedComment = `Proposal has been withdrawn by the AFE Support Team on user request with the reason '${comments}'`;
                }
                if (approvers === null || approvers === void 0 ? void 0 : approvers.length) {
                    let approverIds = [];
                    for (let i = 0; i < approvers.length; i++) {
                        approverIds.push(approvers[i].id);
                        yield this.taskApiClient.cancelAllTasks({
                            entity_id: approvers[i].id,
                            entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                            comments: modifiedComment,
                            created_by: user.username.toLowerCase(),
                        });
                    }
                    yield this.afeProposalApproverRepository.updateStatusOnActionByIds(approverIds, enums_1.APPROVER_STATUS.DISCARDED, currentContext, modifiedComment);
                }
                let internalStatus = enums_1.AFE_PROPOSAL_STATUS.CANCELLED;
                let userStatus = constants_1.AFE_USER_STATUS.CANCELLED;
                yield this.afeProposalRepository.changeStatus(id, internalStatus, userStatus, currentContext);
                yield this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(id, enums_1.AFE_LIMIT_DEDUCATION_STATUS.CANCELLED, currentContext);
                const addHistoryPayload = {
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CANCELLED,
                    comments: modifiedComment,
                };
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                return { message: 'Proposal has been successfully withdrawn!' };
            }));
        });
    }
    getAfeProposalAmountSplitData(afePropsal) {
        return __awaiter(this, void 0, void 0, function* () {
            const amountSplits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalId(afePropsal.id);
            let budgetTypeSplit = [];
            let projectComponentSplit = [];
            let budgetBasedProjectSplit = [];
            for (let split of amountSplits) {
                const { objectId, objectTitle, additionalCurrencyAmount, amount, currency } = split;
                const splitData = {
                    id: objectId,
                    title: objectTitle,
                    amount: amount,
                    currency: currency,
                    additionalCurrencyAmount,
                };
                switch (split.type) {
                    case enums_1.AMOUNT_SPLIT.BUDGET_TYPE_SPLIT:
                        budgetTypeSplit.push(Object.assign({}, splitData));
                        break;
                    case enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT:
                        projectComponentSplit.push(Object.assign({}, splitData));
                        break;
                }
            }
            const projectComponentSplitByBudgetType = amountSplits.filter(split => split.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_BY_BUDGET_TYPE_SPLIT);
            if (projectComponentSplitByBudgetType.length && afePropsal.budgetType === enums_1.BUDGET_TYPE.MIXED) {
                const secondaryBudgetBasedProjectSplit = (0, helpers_1.serializeBudgetBasedProjectAmountSplits)(projectComponentSplitByBudgetType);
                const inPrimaryCurrencyBudgetBasedProjectSplit = (0, helpers_1.serializeBudgetBasedProjectAmountSplits)(projectComponentSplitByBudgetType, false);
                for (let split of inPrimaryCurrencyBudgetBasedProjectSplit) {
                    const { id } = split;
                    const additionalCurrencyAmountForBudgetBasedProjectSplit = secondaryBudgetBasedProjectSplit.find(s => s.id === id);
                    budgetBasedProjectSplit.push(Object.assign(Object.assign({}, split), { additionalCurrencyAmountForBudgetBasedProjectSplit }));
                }
            }
            const splits = {
                budgetTypeSplit,
                projectComponentSplit,
                budgetBasedProjectSplit,
            };
            return splits;
        });
    }
    calculateDeltaAmounts(lastApprovedAfe, currentAfeProposal) {
        return __awaiter(this, void 0, void 0, function* () {
            const currentAfeAmountSplits = yield this.getAfeProposalAmountSplitData(currentAfeProposal);
            const lastApprovedAfeAmountSplits = yield this.getAfeProposalAmountSplitData(lastApprovedAfe);
            const { totalAmount: currentAfeTotalAmount, additionalCurrencyAmount: currentAfeAdditionalCurrencyAmount, marketValue: currentAfeMarketValue, marketValueAdditionalCurrencyAmount: currentAfeMarketValueAdditionalCurrencyAmount, currencyType, } = currentAfeProposal;
            const { totalAmount, additionalCurrencyAmount, marketValue, marketValueAdditionalCurrencyAmount, } = lastApprovedAfe;
            const returnDeltaAmountsObject = (lastApprovedSplit, currentSplits) => {
                var _a;
                const deltaAmounts = [];
                if (currentSplits) {
                    for (const split of currentSplits) {
                        const supplementalSplit = lastApprovedSplit === null || lastApprovedSplit === void 0 ? void 0 : lastApprovedSplit.find(s => s.id === split.id);
                        if (supplementalSplit) {
                            deltaAmounts.push(Object.assign(Object.assign({}, supplementalSplit), { amount: split.amount - supplementalSplit.amount, additionalCurrencyAmount: Object.assign(Object.assign({}, split.additionalCurrencyAmount), { amount: split.additionalCurrencyAmount.amount -
                                        supplementalSplit.additionalCurrencyAmount.amount }) }));
                        }
                        else {
                            deltaAmounts.push(split);
                        }
                    }
                }
                const onlyInLastSupplementalAfe = (_a = lastApprovedSplit === null || lastApprovedSplit === void 0 ? void 0 : lastApprovedSplit.filter(obj2 => !(currentSplits === null || currentSplits === void 0 ? void 0 : currentSplits.some(obj1 => obj1.id === obj2.id)))) === null || _a === void 0 ? void 0 : _a.map(split => (Object.assign(Object.assign({}, split), { amount: split.amount * -1, additionalCurrencyAmount: Object.assign(Object.assign({}, split.additionalCurrencyAmount), { amount: split.additionalCurrencyAmount.amount * -1 }) })));
                if (onlyInLastSupplementalAfe) {
                    deltaAmounts.push(...onlyInLastSupplementalAfe);
                }
                return deltaAmounts;
            };
            const calculateDeltaAmountForProjectBudgetBased = () => {
                var _a, _b, _c;
                const deltaEntries = [];
                let primaryLastApprovedAmountSplits = lastApprovedAfeAmountSplits.budgetBasedProjectSplit;
                let primaryCurrentAmountSplits = currentAfeAmountSplits.budgetBasedProjectSplit;
                if (currentAfeProposal.budgetType === enums_1.BUDGET_TYPE.MIXED &&
                    !(primaryLastApprovedAmountSplits === null || primaryLastApprovedAmountSplits === void 0 ? void 0 : primaryLastApprovedAmountSplits.length)) {
                    primaryLastApprovedAmountSplits = (_a = lastApprovedAfeAmountSplits === null || lastApprovedAfeAmountSplits === void 0 ? void 0 : lastApprovedAfeAmountSplits.projectComponentSplit) === null || _a === void 0 ? void 0 : _a.map(split => {
                        const { id, title, amount, currency, additionalCurrencyAmount } = split;
                        return {
                            id,
                            title,
                            currency,
                            totalAmount: amount,
                            budgetedAmount: lastApprovedAfe.budgetType === enums_1.BUDGET_TYPE.BUDGETED ? amount : 0,
                            unbudgetedAmount: lastApprovedAfe.budgetType === enums_1.BUDGET_TYPE.UNBUDGETED ? amount : 0,
                            additionalCurrencyAmountForBudgetBasedProjectSplit: {
                                id,
                                title,
                                currency: additionalCurrencyAmount.currency,
                                totalAmount: additionalCurrencyAmount.amount,
                                budgetedAmount: lastApprovedAfe.budgetType === enums_1.BUDGET_TYPE.BUDGETED
                                    ? additionalCurrencyAmount.amount
                                    : 0,
                                unbudgetedAmount: lastApprovedAfe.budgetType === enums_1.BUDGET_TYPE.UNBUDGETED
                                    ? additionalCurrencyAmount.amount
                                    : 0,
                            },
                        };
                    });
                }
                if (lastApprovedAfe.budgetType === enums_1.BUDGET_TYPE.MIXED && !(primaryCurrentAmountSplits === null || primaryCurrentAmountSplits === void 0 ? void 0 : primaryCurrentAmountSplits.length)) {
                    primaryCurrentAmountSplits = (_b = currentAfeAmountSplits === null || currentAfeAmountSplits === void 0 ? void 0 : currentAfeAmountSplits.projectComponentSplit) === null || _b === void 0 ? void 0 : _b.map(split => {
                        const { id, title, amount, currency, additionalCurrencyAmount } = split;
                        return {
                            id,
                            title,
                            currency,
                            totalAmount: amount,
                            budgetedAmount: currentAfeProposal.budgetType === enums_1.BUDGET_TYPE.BUDGETED ? amount : 0,
                            unbudgetedAmount: currentAfeProposal.budgetType === enums_1.BUDGET_TYPE.UNBUDGETED ? amount : 0,
                            additionalCurrencyAmountForBudgetBasedProjectSplit: {
                                id,
                                title,
                                currency: additionalCurrencyAmount.currency,
                                totalAmount: additionalCurrencyAmount.amount,
                                budgetedAmount: currentAfeProposal.budgetType === enums_1.BUDGET_TYPE.BUDGETED
                                    ? additionalCurrencyAmount.amount
                                    : 0,
                                unbudgetedAmount: currentAfeProposal.budgetType === enums_1.BUDGET_TYPE.UNBUDGETED
                                    ? additionalCurrencyAmount.amount
                                    : 0,
                            },
                        };
                    });
                }
                if ((primaryCurrentAmountSplits === null || primaryCurrentAmountSplits === void 0 ? void 0 : primaryCurrentAmountSplits.length) || (primaryLastApprovedAmountSplits === null || primaryLastApprovedAmountSplits === void 0 ? void 0 : primaryLastApprovedAmountSplits.length)) {
                    if (primaryCurrentAmountSplits === null || primaryCurrentAmountSplits === void 0 ? void 0 : primaryCurrentAmountSplits.length) {
                        for (const currentAmountSplit of primaryCurrentAmountSplits) {
                            const { id, budgetedAmount, unbudgetedAmount, totalAmount, currency, additionalCurrencyAmountForBudgetBasedProjectSplit, title, } = currentAmountSplit;
                            const lastApprovedEntry = primaryLastApprovedAmountSplits === null || primaryLastApprovedAmountSplits === void 0 ? void 0 : primaryLastApprovedAmountSplits.find(split => split.id === id);
                            if (lastApprovedEntry) {
                                const { budgetedAmount: lastBudgetedAmount, unbudgetedAmount: lastUnbudgetedAmount, totalAmount: lastTotalAmount, additionalCurrencyAmountForBudgetBasedProjectSplit: lastAdditionalCurrencyAmount, } = lastApprovedEntry;
                                deltaEntries.push({
                                    id,
                                    title,
                                    budgetedAmount: budgetedAmount - lastBudgetedAmount,
                                    unbudgetedAmount: unbudgetedAmount - lastUnbudgetedAmount,
                                    totalAmount: totalAmount - lastTotalAmount,
                                    currency: currency,
                                    additionalCurrencyAmountForBudgetBasedProjectSplit: {
                                        budgetedAmount: additionalCurrencyAmountForBudgetBasedProjectSplit.budgetedAmount -
                                            lastAdditionalCurrencyAmount.budgetedAmount,
                                        unbudgetedAmount: additionalCurrencyAmountForBudgetBasedProjectSplit.unbudgetedAmount -
                                            lastAdditionalCurrencyAmount.unbudgetedAmount,
                                        totalAmount: additionalCurrencyAmountForBudgetBasedProjectSplit.totalAmount -
                                            lastAdditionalCurrencyAmount.totalAmount,
                                        currency: currency,
                                    },
                                });
                            }
                            else {
                                deltaEntries.push(currentAmountSplit);
                            }
                        }
                    }
                    const onlyInLastApprovedAfe = (_c = primaryLastApprovedAmountSplits === null || primaryLastApprovedAmountSplits === void 0 ? void 0 : primaryLastApprovedAmountSplits.filter(obj2 => !(primaryCurrentAmountSplits === null || primaryCurrentAmountSplits === void 0 ? void 0 : primaryCurrentAmountSplits.some(obj1 => obj1.id === obj2.id)))) === null || _c === void 0 ? void 0 : _c.map(split => (Object.assign(Object.assign({}, split), { budgetedAmount: split.budgetedAmount * -1, unbudgetedAmount: split.unbudgetedAmount * -1, totalAmount: split.totalAmount * -1, additionalCurrencyAmountForBudgetBasedProjectSplit: Object.assign(Object.assign({}, split.additionalCurrencyAmount), { budgetedAmount: split.additionalCurrencyAmountForBudgetBasedProjectSplit.budgetedAmount * -1, unbudgetedAmount: split.additionalCurrencyAmountForBudgetBasedProjectSplit.unbudgetedAmount * -1, totalAmount: split.additionalCurrencyAmountForBudgetBasedProjectSplit.totalAmount * -1 }) })));
                    if (onlyInLastApprovedAfe) {
                        deltaEntries.push(...onlyInLastApprovedAfe);
                    }
                }
                return deltaEntries;
            };
            const budgetTypeSplit = returnDeltaAmountsObject(lastApprovedAfeAmountSplits.budgetTypeSplit, currentAfeAmountSplits.budgetTypeSplit);
            const projectComponentSplit = returnDeltaAmountsObject(lastApprovedAfeAmountSplits.projectComponentSplit, currentAfeAmountSplits.projectComponentSplit);
            const budgetBasedProjectSplit = calculateDeltaAmountForProjectBudgetBased();
            const deltaAmounts = Object.assign(Object.assign(Object.assign({ totalAmount: currentAfeTotalAmount - totalAmount, additionalCurrencyAmount: Object.assign(Object.assign({}, currentAfeAdditionalCurrencyAmount), { amount: currentAfeAdditionalCurrencyAmount.amount - additionalCurrencyAmount.amount }) }, (marketValue && { marketValue: currentAfeMarketValue - marketValue })), (marketValueAdditionalCurrencyAmount && {
                marketValueAdditionalCurrencyAmount: Object.assign(Object.assign({}, currentAfeMarketValueAdditionalCurrencyAmount), { amount: currentAfeMarketValueAdditionalCurrencyAmount.amount -
                        marketValueAdditionalCurrencyAmount.amount }),
            })), { currency: currencyType, afeProposalAmountSplits: {
                    budgetTypeSplit,
                    projectComponentSplit,
                    budgetBasedProjectSplit,
                } });
            return deltaAmounts;
        });
    }
    updateParentAfeSupplementalsDeltaAmounts(parentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const allAfes = yield this.afeProposalRepository.getAllSupplementalIdsWithVersion(parentId);
            if (allAfes.length > 1) {
                yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    let startIndex = 0;
                    let lastApprovedAfeId = allAfes[startIndex].id;
                    while (startIndex < allAfes.length - 1) {
                        startIndex += 1;
                        yield this.updateDeltaAmountPayloadForSupplemental(lastApprovedAfeId, allAfes[startIndex].id, constants_1.SYSTEM_USER);
                        if (allAfes[startIndex].internalStatus === enums_1.AFE_PROPOSAL_STATUS.APPROVED) {
                            lastApprovedAfeId = allAfes[startIndex].id;
                        }
                    }
                }));
            }
            return { message: `Update all the supplementals.` };
        });
    }
    updateDeltaAmountPayloadForSupplemental(previousApprovedAfeId, currentAfeId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const previousApprovedAfe = yield this.afeProposalRepository.getAfeProposalById(previousApprovedAfeId);
            const currentAfe = yield this.afeProposalRepository.getAfeProposalById(currentAfeId);
            const supplementalDeltaAmounts = yield this.calculateDeltaAmounts(previousApprovedAfe, currentAfe);
            yield this.afeProposalRepository.updateAfeProposalById(currentAfeId, currentContext, {
                supplementalDeltaAmounts,
            });
        });
    }
    sendBackAfeProposal(sendBackAfeProposalRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, comments } = sendBackAfeProposalRequestDto;
            const { user } = currentContext;
            const proposal = yield this.afeProposalRepository.getAfeProposalById(id);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (proposal.submitterId.toLowerCase() !== user.username.toLowerCase() &&
                !this.adminApiClient.hasPermissionToUser(user.username.toLowerCase(), enums_1.PERMISSIONS.AFE_ADMINISTRATION, proposal.entityId)) {
                throw new exceptions_1.HttpException(`You are not authorized to send back this AFE`, enums_1.HttpStatus.UNAUTHORIZED);
            }
            if (proposal.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.IN_PROGRESS) {
                throw new exceptions_1.HttpException(`It is not possible to send back the proposal since this AFE has already been ${enums_1.AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]}.`, enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                var _a, _b, _c, _d, _e, _f;
                const approvers = yield this.afeProposalApproverRepository.getInProgressApproversListByProposalId(id);
                let modifiedComment = '';
                if (proposal.submitterId.toLowerCase() === user.username.toLowerCase()) {
                    modifiedComment = `Proposal has been send back by the submitter with the reason '${comments}'`;
                }
                else {
                    modifiedComment = `Proposal has been send back by the AFE Support Team on user request with the reason '${comments}'`;
                }
                if (approvers === null || approvers === void 0 ? void 0 : approvers.length) {
                    let approverIds = [];
                    for (let i = 0; i < approvers.length; i++) {
                        approverIds.push(approvers[i].id);
                        yield this.taskApiClient.cancelAllTasks({
                            entity_id: approvers[i].id,
                            entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                            comments: modifiedComment,
                            created_by: user.username.toLowerCase(),
                        });
                    }
                    yield this.afeProposalApproverRepository.updateStatusOnActionByIds(approverIds, enums_1.APPROVER_STATUS.DISCARDED, currentContext, modifiedComment);
                }
                yield this.afeProposalLimitDeductionRepository.changeInProgressStatusByProposalId(id, enums_1.AFE_LIMIT_DEDUCATION_STATUS.CANCELLED, currentContext);
                const { submitterId: afeCreatorId } = proposal;
                const currentApprover = approvers[approvers.length - 1];
                const afeCreatorApprover = {
                    assignedTo: afeCreatorId.toLowerCase(),
                    title: afeCreatorId,
                    afeProposalId: id,
                    userDetail: null,
                    otherInfo: {
                        usersDetail: [{ loginId: afeCreatorId }],
                        approvalType: enums_1.APPROVAL_TYPE.RESUBMISSION,
                    },
                    assignedLevel: null,
                    assginedType: enums_1.ASSIGNED_TYPE.USER,
                    associatedColumn: null,
                    parallelIdentifier: null,
                    approvalSequence: currentApprover.approvalSequence,
                    workflowMasterStepsId: null,
                    assignedEntityId: null,
                };
                yield this.afeProposalApproverRepository.createAfeProposalApprover(afeCreatorApprover, currentContext);
                yield this.taskService.createNextTasks(id, enums_1.TASK_ACTION.SEND_BACK, currentContext);
                yield this.taskService.createQueueLogOnApprovalAction(enums_1.TASK_ACTION.SEND_BACK, proposal, true, approvers, currentContext);
                const submitterDetails = yield this.mSGraphApiClient.getUserDetails(afeCreatorId);
                const config = this.configService.getAppConfig();
                const placeholdersValues = {
                    afeDetailLink: `${config.uiClient.baseUrl}/afe/afe-detail/${proposal.id}`,
                    approvers: `${currentContext.user.name}`,
                    approver: `${currentApprover.title}`,
                    comments: comments || '-',
                };
                if (submitterDetails === null || submitterDetails === void 0 ? void 0 : submitterDetails.mail) {
                    let emailUser = { to: [submitterDetails.mail] };
                    if ((_c = (_b = (_a = proposal === null || proposal === void 0 ? void 0 : proposal.data) === null || _a === void 0 ? void 0 : _a.projectDetails) === null || _b === void 0 ? void 0 : _b.projectLeader) === null || _c === void 0 ? void 0 : _c.mail) {
                        const leaderDetails = yield this.mSGraphApiClient.getUserDetails((_f = (_e = (_d = proposal === null || proposal === void 0 ? void 0 : proposal.data) === null || _d === void 0 ? void 0 : _d.projectDetails) === null || _e === void 0 ? void 0 : _e.projectLeader) === null || _f === void 0 ? void 0 : _f.mail);
                        if (leaderDetails === null || leaderDetails === void 0 ? void 0 : leaderDetails.mail) {
                            emailUser.cc = [leaderDetails.mail];
                        }
                    }
                    yield this.sharedNotificationService.sendNotificationForAfeProposal(proposal.id, currentApprover.id, enums_1.NOTIFICATION_ENTITY_TYPE.AFE_TASK_APPROVAL_NOTIFICATION, emailUser, task_action_with_email_template_mapping_1.TASK_ACTION_WITH_EMAIL_TEMPLATE[enums_1.TASK_ACTION.SEND_BACK], false, placeholdersValues);
                }
                const addHistoryPayload = {
                    created_by: currentContext.user.username,
                    entity_id: id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.SENT_BACK,
                    comments: modifiedComment,
                };
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                return { message: 'Proposal has been sent back successfully!' };
            }));
        });
    }
    reopenAfeProposal(reopenAfeProposalRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { approverId, proposalId, comments } = reopenAfeProposalRequestDto;
            const { user } = currentContext;
            const proposal = yield this.afeProposalRepository.getAfeProposalById(proposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (proposal.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.REJECTED) {
                throw new exceptions_1.HttpException(`It is not possible to re-open the proposal since this AFE is currently in ${enums_1.AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]} status.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const approverDetail = yield this.afeProposalApproverRepository.getApproverById(approverId);
            if (!approverDetail) {
                throw new exceptions_1.HttpException(`Invalid approver.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (approverDetail.actionStatus !== enums_1.APPROVER_STATUS.REJECTED) {
                throw new exceptions_1.HttpException(`The AFE was not rejected by the selected approver.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const parallelIdentifier = approverDetail.parallelIdentifier;
                yield this.afeProposalApproverRepository.reopenApproverByProposalIdAndStatus(proposalId, parallelIdentifier, enums_1.APPROVER_STATUS.REJECTED, currentContext);
                yield this.afeProposalLimitDeductionRepository.changeRejectedStatusByProposalId(proposalId, enums_1.AFE_LIMIT_DEDUCATION_STATUS.IN_PROGRESS, currentContext);
                yield this.taskService.changeAfeStatusToInprogress(proposalId, currentContext);
                yield this.taskService.recreateTask(proposalId, proposal, mappings_1.TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING.APPROVE, true);
                const modifiedComment = `Proposal has been re-open by AFE Support Team on user request with the reason '${comments}'`;
                const addHistoryPayload = {
                    created_by: currentContext.user.username,
                    entity_id: proposalId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.REOPEN,
                    comments: modifiedComment,
                };
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                return { message: 'Proposal has been re-open successfully!' };
            }));
        });
    }
    revertSentBackAction(requestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { approverId, proposalId, comments } = requestDto;
            const proposal = yield this.afeProposalRepository.getAfeProposalById(proposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (proposal.internalStatus !== enums_1.AFE_PROPOSAL_STATUS.SENT_BACK) {
                throw new exceptions_1.HttpException(`It is not possible to revert the last action as a Sent Back since this AFE is currently in ${enums_1.AFE_PROPOSAL_STATUS_DISPLAY[proposal.internalStatus]} status.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const approverDetail = yield this.afeProposalApproverRepository.getApproverById(approverId);
            if (!approverDetail) {
                throw new exceptions_1.HttpException(`Invalid approver.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (approverDetail.actionStatus !== enums_1.APPROVER_STATUS.IN_PROGRESS) {
                throw new exceptions_1.HttpException(`The AFE is not pending with the selected approver.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const sendBackApproverDetail = yield this.afeProposalApproverRepository.getSentBackApproverByProposalId(proposalId);
            if (!sendBackApproverDetail) {
                throw new exceptions_1.HttpException(`No approver has sent back this AFE.`, enums_1.HttpStatus.NOT_FOUND);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const approvers = yield this.afeProposalApproverRepository.getInProgressApproversListByProposalId(proposalId);
                const modifiedComment = `Last action has been reverted by AFE Support Team on user request with the reason '${comments}'`;
                for (let i = 0; i < approvers.length; i++) {
                    yield this.taskApiClient.cancelAllTasks({
                        entity_id: approvers[i].id,
                        entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                        comments: modifiedComment,
                        created_by: currentContext.user.username.toLowerCase(),
                    });
                }
                const parallelIdentifier = sendBackApproverDetail.parallelIdentifier;
                yield this.afeProposalApproverRepository.deleteApproversByIds([approverId], currentContext);
                yield this.afeProposalApproverRepository.reopenApproverByProposalIdAndStatus(proposalId, parallelIdentifier, enums_1.APPROVER_STATUS.SEND_BACK, currentContext);
                yield this.taskService.changeAfeStatusToInprogress(proposalId, currentContext);
                yield this.taskService.recreateTask(proposalId, proposal, mappings_1.TASK_ACTION_WITH_APPROVAL_TYPE_MAPPING.APPROVE, true);
                const addHistoryPayload = {
                    created_by: currentContext.user.username,
                    entity_id: proposalId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.SENT_BACK_REVERTED,
                    comments: modifiedComment,
                };
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                return { message: 'Action has been reverted successfully!' };
            }));
        });
    }
    updateApproverUser(proposalId, updateApproverUserRequestDTO, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { approverId, userDetail } = updateApproverUserRequestDTO;
            const proposalData = yield this.afeProposalRepository.getAfeProposalById(proposalId);
            if (!proposalData) {
                throw new exceptions_1.HttpException(`Afe proposal doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const approverDetail = yield this.afeProposalApproverRepository.getApproverById(approverId);
            if (!approverDetail) {
                throw new exceptions_1.HttpException(`Approver doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            if (approverDetail.actionStatus !== enums_1.APPROVER_STATUS.IN_PROGRESS) {
                throw new exceptions_1.HttpException(`The AFE is not pending with the selected approver.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (approverDetail.assginedType !== associated_type_enum_1.ASSOCIATED_TYPE.USER) {
                throw new exceptions_1.HttpException(`The AFE is not pending with the selected approver.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                var _a, _b;
                if (approverDetail) {
                    yield this.taskApiClient.cancelAllTasks({
                        entity_id: approverDetail.id,
                        entity_type: enums_1.TASK_ENTITY_TYPE.AFE_PROPOSAL_APPROVER_TASK,
                        comments: `User has been changed`,
                        created_by: currentContext.user.username.toLowerCase(),
                    });
                }
                yield this.afeProposalApproverRepository.updateCustomUserByApproverId(approverId, userDetail, currentContext);
                yield this.afeProposalRepository.updateAfeProposalById(proposalId, currentContext, {
                    userStatus: `Pending with ${(userDetail === null || userDetail === void 0 ? void 0 : userDetail.title) ? userDetail.title : userDetail.loginId}`,
                });
                yield this.taskService.recreateTask(proposalId, proposalData, approverDetail.otherInfo.approvalType, true);
                let prevUserLoginId = '';
                if (((_a = approverDetail === null || approverDetail === void 0 ? void 0 : approverDetail.otherInfo) === null || _a === void 0 ? void 0 : _a.usersDetail.length) &&
                    ((_b = approverDetail.otherInfo.usersDetail[0]) === null || _b === void 0 ? void 0 : _b.loginId)) {
                    prevUserLoginId = approverDetail.otherInfo.usersDetail[0].loginId;
                }
                const addHistoryPayload = {
                    created_by: currentContext.user.username,
                    entity_id: proposalId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.AFE_PROPOSAL,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.APPROVERS_LIST_UPDATED,
                    comments: 'User has been changed ' +
                        (prevUserLoginId ? 'from ' + prevUserLoginId : '') +
                        ' to ' +
                        userDetail.loginId,
                    additional_info: {
                        previousUser: approverDetail.otherInfo.usersDetail,
                        newUser: userDetail,
                    },
                };
                yield this.historyApiClient.addRequestHistory(addHistoryPayload);
                return { message: 'User has been updated successfully!' };
            }));
        });
    }
};
AfeProposalService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_3.AfeProposalRepository,
        helpers_1.DatabaseHelper,
        draft_afe_repository_1.DraftAfeRepository,
        services_4.WorkflowService,
        repositories_3.AfeProposalAmountSplitRepository,
        repositories_3.AfeProposalApproverRepository,
        clients_1.AttachmentApiClient,
        clients_1.AdminApiClient,
        clients_1.RequestApiClient,
        config_service_1.ConfigService,
        clients_1.HistoryApiClient,
        repositories_3.AfeProposalLimitDeductionRepository,
        services_3.TaskService,
        services_2.SharedAttachmentService,
        clients_1.TaskApiClient,
        services_1.FinanceService,
        repositories_1.NotificationRepository,
        clients_1.MSGraphApiClient,
        validators_1.AfeProposalValidator,
        repositories_2.QueueLogRepository,
        services_2.SharedNotificationService])
], AfeProposalService);
exports.AfeProposalService = AfeProposalService;
//# sourceMappingURL=afe-proposal.service.js.map