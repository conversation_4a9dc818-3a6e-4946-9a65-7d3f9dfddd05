"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeConfigController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
const dtos_1 = require("../dtos");
const enums_1 = require("../../shared/enums");
let AfeConfigController = class AfeConfigController {
    constructor(afeConfigService) {
        this.afeConfigService = afeConfigService;
    }
    getNavBarByPosition(position) {
        return this.afeConfigService.getNavBarByPosition(position);
    }
    getAfeRequestTypes() {
        return this.afeConfigService.getAfeRequestTypes();
    }
    getParallelIdentifiers() {
        return this.afeConfigService.getParallelIdentifiers();
    }
    getAfeNatureTypesByRequestTypeAndLocation(requestTypeId, locationId) {
        return this.afeConfigService.getAfeNatureTypesByRequestTypeAndLocation(requestTypeId, locationId);
    }
    getAfeTypesByRequestType(requestTypeId) {
        return this.afeConfigService.getAfeTypesByRequestType(requestTypeId);
    }
    getAllAfeTypes() {
        return this.afeConfigService.getAllAfeTypes();
    }
    getAllBudgetType(requestTypeId, typeId) {
        return this.afeConfigService.getBudgetTypes(requestTypeId, typeId);
    }
    getQuestionsByRequestTypeAndLocation(requestTypeId, locationId, questionType) {
        return this.afeConfigService.getQuestionsByRequestTypeAndLocation(requestTypeId, locationId, questionType);
    }
    getAfeSubTypes(typeId, entityId) {
        return this.afeConfigService.getAfeSubTypes(typeId, entityId);
    }
    getLocations(requestTypeId, entityId) {
        return this.afeConfigService.getLocations(entityId, requestTypeId);
    }
    getLocationsByEntityIds(getLocationByEntitiesRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.afeConfigService.getLocationsByEntityIds(getLocationByEntitiesRequestDto);
        });
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get nativation bar menue by its position on the UI.',
        type: [dtos_1.NavBarResponseDto],
    }),
    (0, common_1.Get)('/nav-bar/:position'),
    __param(0, (0, common_1.Param)('position')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getNavBarByPosition", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return all the active AFE request types.',
        type: [dtos_1.AfeRequestTypeResponseDto],
    }),
    (0, common_1.Get)('/request-types'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getAfeRequestTypes", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return all the active AFE request types.',
        type: [dtos_1.ParallelIdentifierResponseDto],
    }),
    (0, common_1.Get)('/parallel-identifier'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getParallelIdentifiers", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE nature types by request type and location.',
        type: [dtos_1.AfeNatureTypeResponseDto],
    }),
    (0, common_1.Get)('/nature-types'),
    __param(0, (0, common_1.Query)('requestTypeId')),
    __param(1, (0, common_1.Query)('locationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getAfeNatureTypesByRequestTypeAndLocation", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE types by request type.',
        type: [dtos_1.AfeTypeResponseDto],
    }),
    (0, common_1.Get)('/request-types/:requestTypeId/afe-types'),
    __param(0, (0, common_1.Param)('requestTypeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getAfeTypesByRequestType", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get  all AFE types.',
        type: [dtos_1.AfeTypeResponseDto],
    }),
    (0, common_1.Get)('/afe-types'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getAllAfeTypes", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all AFE budget types.',
        type: [dtos_1.AfeBudgetTypeResponseDto],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'requestTypeId',
        type: Number,
        description: 'Afe request type id.',
        required: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'typeId',
        type: Number,
        description: 'Afe type id (optional).',
        required: false,
    }),
    (0, common_1.Get)('/budget-types'),
    __param(0, (0, common_1.Query)('requestTypeId')),
    __param(1, (0, common_1.Query)('typeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getAllBudgetType", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return questions by request type and entity or location id.',
        type: [dtos_1.QuestionResponseDto],
    }),
    (0, common_1.Get)('/questions'),
    __param(0, (0, common_1.Query)('requestTypeId')),
    __param(1, (0, common_1.Query)('locationId')),
    __param(2, (0, common_1.Query)('questionType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getQuestionsByRequestTypeAndLocation", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get AFE sub types types by AFE type and business entity.',
        type: [dtos_1.AfeSubTypeResponseDto],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'typeId',
        type: Number,
        description: 'Afe type id.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Business Entity Id.',
        required: false,
    }),
    (0, common_1.Get)('/sub-types'),
    __param(0, (0, common_1.Query)('typeId')),
    __param(1, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getAfeSubTypes", null);
__decorate([
    (0, swagger_1.ApiQuery)({
        name: 'requestTypeId',
        type: Number,
        description: 'Request type id.',
        required: true,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'entityId',
        type: Number,
        description: 'Business Entity Id.',
        required: true,
    }),
    (0, common_1.Get)('/get-locations'),
    __param(0, (0, common_1.Query)('requestTypeId')),
    __param(1, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", void 0)
], AfeConfigController.prototype, "getLocations", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Get Locations By Entity Ids.',
        type: [dtos_1.LocationsWithEntityIdResponseDto],
    }),
    (0, common_1.Post)('/get-locations-by-entity-ids'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.GetLocationByEntitiesRequestDto]),
    __metadata("design:returntype", Promise)
], AfeConfigController.prototype, "getLocationsByEntityIds", null);
AfeConfigController = __decorate([
    (0, swagger_1.ApiTags)('AFE Congfiguration & MetaData APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('afe-config'),
    __metadata("design:paramtypes", [services_1.AfeConfigService])
], AfeConfigController);
exports.AfeConfigController = AfeConfigController;
//# sourceMappingURL=afe-config.controller.js.map