"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SftpService = void 0;
const common_1 = require("@nestjs/common");
const fs = __importStar(require("fs"));
const fastcsv = __importStar(require("fast-csv"));
const path = __importStar(require("path"));
const ssh2_sftp_client_1 = __importDefault(require("ssh2-sftp-client"));
const repositories_1 = require("../afe-proposal/repositories");
const lodash_1 = require("lodash");
const clients_1 = require("../shared/clients");
const data_sharing_scheduler_repository_1 = require("./repositories/data_sharing_scheduler.repository");
const services_1 = require("../business-entity/services");
const constants_1 = require("../shared/constants");
const enums_1 = require("../shared/enums");
let SftpService = class SftpService {
    constructor(sftpClient, dataSharingSchedulerRepository, afeProposalRepository, afeProposalAmountSplitRepository, afeProposalApproverRepository, notificationApiClient, businessEntityService) {
        this.sftpClient = sftpClient;
        this.dataSharingSchedulerRepository = dataSharingSchedulerRepository;
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalAmountSplitRepository = afeProposalAmountSplitRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.notificationApiClient = notificationApiClient;
        this.businessEntityService = businessEntityService;
    }
    run() {
        return __awaiter(this, void 0, void 0, function* () {
            const readyToShareRuns = yield this.dataSharingSchedulerRepository.getAllReadyToShareScheduler();
            if (!readyToShareRuns.length) {
                console.log('No Data Sharing Scheduled For Now!');
                return;
            }
            for (const scheduler of readyToShareRuns) {
                try {
                    const { id, title, entities, recipients, frequency, monthDay, rule } = scheduler;
                    const emailIds = recipients.join(',');
                    const allChildEntitiesIds = yield this.businessEntityService.getBusinessEntitiesChildIds(entities);
                    if (allChildEntitiesIds.length === 0) {
                        console.log('ERROR - No Business Unit Found for ' + title);
                        return;
                    }
                    console.log('[______________ Data Sharing Service For ' + title + ' _____________]');
                    const timestamp = Date.now().toString();
                    const folderPath = path.join(__dirname, '..', 'uploads', timestamp);
                    fs.mkdirSync(folderPath, { recursive: true });
                    const afeProposals = yield this.afeProposalRepository.getAfeProposalsByEntities(allChildEntitiesIds, rule);
                    yield this.createAndUploadCsvFile(path.join(folderPath, timestamp + '_afeProposals.csv'), afeProposals);
                    const afeProposalIds = afeProposals.map((proposalDetail) => (0, lodash_1.toNumber)(proposalDetail.id));
                    const afeProposalSplit = yield this.afeProposalAmountSplitRepository.getAfeProposalSplitByProposalIds(afeProposalIds);
                    yield this.createAndUploadCsvFile(path.join(folderPath, timestamp + '_afeProposalSplit.csv'), afeProposalSplit);
                    const afeProposalApprovers = yield this.afeProposalApproverRepository.getAfeProposalApproverByProposalIds(afeProposalIds);
                    yield this.createAndUploadCsvFile(path.join(folderPath, timestamp + '_afeProposalApprovers.csv'), afeProposalApprovers);
                    if (emailIds) {
                        yield this.sendFileViaEmail(title, folderPath, emailIds);
                    }
                    this.deleteAllLocalFolder(folderPath);
                    console.log('>> Deleted All Local Files');
                    console.log('[______________ Data Sharing Service ' + title + ' END ______________]');
                    let currentDate = new Date();
                    let nextRunAt = new Date();
                    if (frequency === enums_1.FREQUENCY.MONTHLY) {
                        nextRunAt.setMonth(currentDate.getMonth() + 1);
                        nextRunAt.setDate(monthDay);
                    }
                    else {
                        nextRunAt.setDate(currentDate.getDate() + 1);
                    }
                    yield this.dataSharingSchedulerRepository.updateLastRunAt(id, new Date(), nextRunAt, constants_1.SYSTEM_USER);
                }
                catch (error) {
                    console.error(`Error processing scheduler for "${scheduler.title}":`, error);
                }
            }
        });
    }
    flatBuHierarchyFromTree(businessUnits, includeChildren = false) {
        let result = [];
        function traverse(node) {
            if ((0, lodash_1.toNumber)(node.id) !== -1) {
                result.push(Object.assign(Object.assign({}, node), { children: includeChildren ? node.children : [] }));
            }
            if (node.children && node.children.length > 0) {
                node.children.forEach(child => traverse(child));
            }
        }
        traverse(businessUnits);
        return result;
    }
    getLastLevelEntities(businessUnit) {
        var _a;
        if (!((_a = businessUnit.children) === null || _a === void 0 ? void 0 : _a.length)) {
            return [businessUnit];
        }
        return businessUnit.children.flatMap(this.getLastLevelEntities.bind(this));
    }
    createAndUploadCsvFile(filePath, data) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                console.log('>> Generating Data File For ' + filePath);
                return new Promise((resolve, reject) => {
                    const ws = fs.createWriteStream(filePath);
                    fastcsv
                        .write(data, { headers: true })
                        .pipe(ws)
                        .on('finish', resolve)
                        .on('error', reject);
                });
            }
            catch (error) {
                console.error('----- Error during CSV creation or upload ------');
                console.error(error);
                throw error;
            }
        });
    }
    uploadToSftp(localPath, sftpConfig, timestamp) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { folderName } = sftpConfig, connectionDetail = __rest(sftpConfig, ["folderName"]);
                console.log('----- Start Uploading Files To Sftp -----');
                yield this.sftpClient.connect(connectionDetail);
                const putConfig = {
                    flags: 'w',
                    encoding: null,
                    mode: 0o666,
                    autoClose: true
                };
                const files = fs.readdirSync(localPath);
                for (const file of files) {
                    const filePath = path.join(localPath, file);
                    const remoteDir = `/${folderName}`;
                    const remotePath = `${remoteDir}/${file}`;
                    yield this.sftpClient.put(filePath, remotePath, putConfig);
                }
                console.log('Files Transfered To SFTP Folder Successfully.');
                return yield this.sftpClient.end();
            }
            catch (error) {
                console.error('>>>> Error SFTP Upload <<<<');
                console.error(error);
                throw error;
            }
        });
    }
    sendFileViaEmail(title, localPath, emailId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                console.log('>> Started Sending Data Via Email');
                const files = fs.readdirSync(localPath);
                const attachments = files.map(file => {
                    const filePath = path.join(localPath, file);
                    const data = fs.readFileSync(filePath);
                    return {
                        name: file,
                        data: data,
                        relUrl: filePath
                    };
                });
                const payload = {
                    entity_id: 1,
                    entity_type: 'test',
                    subject: title,
                    is_approval_email: false,
                    receiver: emailId,
                    body: 'PFA AFE latest data.',
                    attachments,
                };
                yield this.notificationApiClient.sendNotification(payload);
                console.log('>> Email Send Success');
            }
            catch (error) {
                console.error('----- Error - Email Notification ------');
                console.error(error);
                throw error;
            }
        });
    }
    ensureRemoteDirExists(remoteDir) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                console.log('Ensure Remote Dir Exists - ' + remoteDir);
                const dirParts = remoteDir.split('/');
                let currentDir = '';
                for (const part of dirParts) {
                    if (part) {
                        currentDir += `/${part}`;
                        try {
                            yield this.sftpClient.mkdir(currentDir, true);
                        }
                        catch (error) {
                            if (error.code !== 4 && error.message !== 'Failure') {
                                throw error;
                            }
                        }
                    }
                }
                console.log('Remote Dir is now setup.');
            }
            catch (error) {
                console.error('----- Error Ensuring Remote Directory Exists ------');
                console.error(error);
                throw error;
            }
        });
    }
    deleteAllLocalFolder(folderPath) {
        console.log('>> Deleting All Local Files');
        fs.readdirSync(folderPath).forEach(file => {
            const filePath = path.join(folderPath, file);
            fs.unlinkSync(filePath);
        });
        return fs.rmdirSync(folderPath);
    }
    deleteAllPreviousFolders(baseFolderPath) {
        const folders = fs.readdirSync(baseFolderPath)
            .map(folderName => {
            const fullPath = path.join(baseFolderPath, folderName);
            return {
                name: folderName,
                time: fs.statSync(fullPath).ctime.getTime(),
                path: fullPath
            };
        })
            .filter(item => fs.statSync(item.path).isDirectory())
            .sort((a, b) => b.time - a.time);
        if (folders.length > 1) {
            folders.slice(1).forEach(folder => {
                fs.readdirSync(folder.path).forEach(file => {
                    fs.unlinkSync(path.join(folder.path, file));
                });
                fs.rmdirSync(folder.path);
            });
        }
    }
};
SftpService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof ssh2_sftp_client_1.default !== "undefined" && ssh2_sftp_client_1.default) === "function" ? _a : Object, data_sharing_scheduler_repository_1.DataSharingSchedulerRepository,
        repositories_1.AfeProposalRepository,
        repositories_1.AfeProposalAmountSplitRepository,
        repositories_1.AfeProposalApproverRepository,
        clients_1.NotificationApiClient,
        services_1.BusinessEntityService])
], SftpService);
exports.SftpService = SftpService;
//# sourceMappingURL=sftp.service.js.map