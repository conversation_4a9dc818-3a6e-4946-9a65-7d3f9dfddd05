"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeDraftModule = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../shared/clients");
const attachment_api_client_1 = require("../shared/clients/attachment-api.client");
const helpers_1 = require("../shared/helpers");
const services_1 = require("../shared/services");
const controllers_1 = require("./controllers");
const draft_afe_repository_1 = require("./repositories/draft-afe-repository");
const services_2 = require("./services");
const repositories_1 = require("../afe-proposal/repositories");
let AfeDraftModule = class AfeDraftModule {
};
AfeDraftModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.AfeDraftController],
        providers: [
            services_2.AfeDraftService,
            draft_afe_repository_1.DraftAfeRepository,
            helpers_1.DatabaseHelper,
            clients_1.AdminApiClient,
            attachment_api_client_1.AttachmentApiClient,
            services_1.SharedAttachmentService,
            services_1.SharedPermissionService,
            repositories_1.UserProjectComponentMappingRepository,
            repositories_1.UserCostCenterMappingRepository
        ],
    })
], AfeDraftModule);
exports.AfeDraftModule = AfeDraftModule;
//# sourceMappingURL=afe-draft.module.js.map