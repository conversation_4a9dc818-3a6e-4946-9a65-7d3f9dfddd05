"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Scheduler = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
let Scheduler = class Scheduler extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Scheduler.prototype, "title", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'rule', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], Scheduler.prototype, "rule", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entities', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Scheduler.prototype, "entities", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'actions', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Scheduler.prototype, "actions", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'final_approval',
        type: sequelize_typescript_1.DataType.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    }),
    __metadata("design:type", Boolean)
], Scheduler.prototype, "finalApproval", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'recipients', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], Scheduler.prototype, "recipients", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'template_name', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], Scheduler.prototype, "templateName", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'for_level', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], Scheduler.prototype, "forLevel", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'last_run_at', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], Scheduler.prototype, "lastRunAt", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'placeholder_fields', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], Scheduler.prototype, "placeholderFields", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'type',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.SCHEDULER_TYPE)),
        allowNull: false,
    }),
    __metadata("design:type", String)
], Scheduler.prototype, "type", void 0);
Scheduler = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'schedulers' })
], Scheduler);
exports.Scheduler = Scheduler;
//# sourceMappingURL=scheduler.model.js.map