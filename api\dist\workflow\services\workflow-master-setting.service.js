"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowMasterSettingService = void 0;
const common_1 = require("@nestjs/common");
const class_transformer_1 = require("class-transformer");
const lodash_1 = require("lodash");
const repositories_1 = require("../../afe-proposal/repositories");
const pagination_1 = require("../../core/pagination");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const mappings_1 = require("../../shared/mappings");
const services_1 = require("../../shared/services");
const dtos_1 = require("../dtos");
const get_history_response_dto_1 = require("../dtos/response/get-history-response.dto");
const get_master_setting_response_dto_1 = require("../dtos/response/get-master-setting-response.dto");
const get_overriden_setting_list_response_dto_1 = require("../dtos/response/get-overriden-setting-list-response.dto");
const repositories_2 = require("../repositories");
const workflow_master_step_service_1 = require("./workflow-master-step.service");
let WorkflowMasterSettingService = class WorkflowMasterSettingService {
    constructor(workflowMasterSettingRepository, workflowMasterStepRepository, workflowSharedChildLimitRepository, workflowSharedBucketLimitRepository, adminApiClient, limitDeductionRepository, databaseHelper, sequlizeOperator, historyApiClient, excelSheetService, workflowMasterStepService) {
        this.workflowMasterSettingRepository = workflowMasterSettingRepository;
        this.workflowMasterStepRepository = workflowMasterStepRepository;
        this.workflowSharedChildLimitRepository = workflowSharedChildLimitRepository;
        this.workflowSharedBucketLimitRepository = workflowSharedBucketLimitRepository;
        this.adminApiClient = adminApiClient;
        this.limitDeductionRepository = limitDeductionRepository;
        this.databaseHelper = databaseHelper;
        this.sequlizeOperator = sequlizeOperator;
        this.historyApiClient = historyApiClient;
        this.excelSheetService = excelSheetService;
        this.workflowMasterStepService = workflowMasterStepService;
        this.updateInBatches = (updates, currentContext) => __awaiter(this, void 0, void 0, function* () {
            console.log('updates.length', updates.length);
            console.log(updates);
            const BATCH_SIZE = 500;
            const chunks = (0, lodash_1.chunk)(updates, BATCH_SIZE);
            for (const batch of chunks) {
                const updateQueries = batch.filter((batchData) => batchData).map(({ condition, payload }) => {
                    if (condition && payload) {
                        return this.workflowMasterStepRepository.updateWorkflowByCondition(condition, payload, currentContext);
                    }
                    return null;
                }).filter(Boolean);
                yield Promise.all(updateQueries);
            }
            console.log('All Promise resolved');
        });
    }
    getAllMasterSetting(limit = null, page = null, filter) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getAllMasterSetting(limit, page, filter);
            const records = (0, helpers_1.multiObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result.rows);
            return new pagination_1.Pagination({ records, total: result.count });
        });
    }
    getOverrideAllSettingsDetail(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            const workflowSettingDetail = yield this.workflowMasterSettingRepository.getWorkflowSettingById(workflowSettingId);
            if (workflowSettingDetail) {
                const parentWorkflowId = workflowSettingDetail.parentId ? workflowSettingDetail.parentId : workflowSettingDetail.id;
                const result = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition(this.sequlizeOperator.orOperator({
                    id: parentWorkflowId,
                    parentId: parentWorkflowId
                }));
                return (0, helpers_1.multiObjectToInstance)(get_overriden_setting_list_response_dto_1.GetOverridenSettingListResponseDTO, result);
            }
            return [];
        });
    }
    getAllAssignedUniqueRolesForWorkflowSetting(workflowSettingId) {
        return __awaiter(this, void 0, void 0, function* () {
            const workflowSettingDetail = yield this.workflowMasterSettingRepository.getWorkflowSettingById(workflowSettingId);
            if (workflowSettingDetail) {
                const parentWorkflowId = workflowSettingDetail.parentId ? workflowSettingDetail.parentId : workflowSettingDetail.id;
                const result = yield this.workflowMasterStepRepository.getUnpublishedWorkflowUniqueRolesAssigned(parentWorkflowId);
                const uniqueRoles = [];
                result.forEach((data) => {
                    if (data.associate_role || data.associated_column || data.associated_user) {
                        uniqueRoles.push(data.associate_role || data.associated_column || data.associated_user);
                    }
                });
                return uniqueRoles;
            }
            return [];
        });
    }
    getAllUnpublishedOverriden(parentId) {
        return __awaiter(this, void 0, void 0, function* () {
            const overriddens = yield this.workflowMasterSettingRepository.getAllUnpublishedOverriden(parentId);
            return (0, helpers_1.multiObjectToInstance)(get_overriden_setting_list_response_dto_1.GetOverridenSettingListResponseDTO, overriddens);
        });
    }
    getAllMasterSettingList(limit = null, page = null, filter, listMasterWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getAllMasterSettingList(limit, page, filter, listMasterWorkflowRequestDto);
            const records = (0, helpers_1.multiObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result.rows);
            return new pagination_1.Pagination({ records, total: result.count });
        });
    }
    addNewMasterWorkflowSetting(newMasterSettingRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const allBusinessEntity = yield this.adminApiClient.getAllBusinessHierarchy();
            if (allBusinessEntity && allBusinessEntity.id) {
                const existingWorkflow = yield this.workflowMasterSettingRepository.getAllMasterSetting(1, 1, {
                    parentId: null,
                    year: newMasterSettingRequestDto.year,
                    requestTypeId: newMasterSettingRequestDto.requestTypeId,
                    budgetType: newMasterSettingRequestDto.budgetType,
                    entityId: allBusinessEntity.id,
                    projectComponentId: newMasterSettingRequestDto.projectComponentId,
                });
                if (existingWorkflow.rows.length) {
                    throw new exceptions_1.HttpException('Master workflow setting already exist.', enums_1.HttpStatus.CONFLICT);
                }
                const entityDetail = {
                    entityId: allBusinessEntity.id,
                    entityCode: allBusinessEntity.code,
                    entityTitle: allBusinessEntity.short_name,
                    entityType: allBusinessEntity.entity_type,
                };
                const result = yield this.workflowMasterSettingRepository.addNewMasterWorkflowSetting(Object.assign(Object.assign({}, newMasterSettingRequestDto), entityDetail), currentContext);
                yield this.historyApiClient.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: result.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                    comments: 'Workflow setting created.',
                    additional_info: {
                        masterSettingDetail: newMasterSettingRequestDto,
                        entityDetail
                    },
                });
                return (0, helpers_1.singleObjectToInstance)(dtos_1.NewMasterWorkflowResponseDto, result);
            }
            else {
                throw new exceptions_1.HttpException('Group entity not found.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getWorkflowDetailByWorkflowSettingId(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getWorkflowDetailByWorkflowSettingId(id);
            if (result) {
                const newVersionAvailable = result.unpublishedVersion ? true : false;
                return (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.WorkflowDetailResponseDTO, Object.assign(Object.assign({}, result), { newVersionAvailable }));
            }
            else {
                throw new exceptions_1.HttpException('Workflow detail is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getPolicyDetailByWorkflowSettingId(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getWorkflowDetailByWorkflowSettingId(id);
            if (result) {
                return (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.WorkflowDetailResponseDTO, result);
            }
            else {
                throw new exceptions_1.HttpException('Workflow detail is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    downloadPolicyDetailByWorkflowSettingId(id) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getWorkflowDetailByWorkflowSettingId(id);
            if (result && result.workflowMasterStep.length) {
                let reportList = [];
                for (let i = 0; i < result.workflowMasterStep.length; i++) {
                    const exceptionSteps = yield this.workflowMasterStepService.getExceptionalOverriddenSteps(result.workflowMasterStep[i].id);
                    const parentSharedLimitDetail = result.workflowMasterStep.find((workflowMasterStep) => {
                        if (workflowMasterStep.sharedLimitMasterStepChildId === result.workflowMasterStep[i].id) {
                            return workflowMasterStep;
                        }
                    });
                    let data = {
                        'Associate Role': result.workflowMasterStep[i].title,
                        'Single Proposal': parentSharedLimitDetail ?
                            'Can be determined for each ' + result.workflowMasterStep[i].title + ' by the relevant ' + parentSharedLimitDetail.title + ' subordinate to his/her own limits.' :
                            ((0, lodash_1.toNumber)(result.workflowMasterStep[i].singleLimit) === -1 ?
                                'Unlimited' :
                                ((0, lodash_1.toNumber)(result.workflowMasterStep[i].singleLimit) === 0 ?
                                    'Approval Required' :
                                    result.workflowMasterStep[i].singleLimit.toString())),
                        mergeColCount: 0,
                        isExceptionStep: false
                    };
                    if (result.isAggregateLimitApplicable) {
                        data['Annual Aggregate'] = parentSharedLimitDetail ?
                            'Can be determined for each ' + result.workflowMasterStep[i].title + ' by the relevant ' + parentSharedLimitDetail.title + ' subordinate to his/her own limits.' :
                            ((0, lodash_1.toNumber)(result.workflowMasterStep[i].aggregateLimit) === -1 ?
                                'Unlimited' :
                                ((0, lodash_1.toNumber)(result.workflowMasterStep[i].aggregateLimit) === 0 ?
                                    'Approval Required' :
                                    result.workflowMasterStep[i].aggregateLimit.toString()));
                        data.mergeColCount = parentSharedLimitDetail ? (data.mergeColCount + 1) : 0;
                    }
                    if (result.isCommitmentLengthApplicable) {
                        data['Length Of Commitment'] = parentSharedLimitDetail ? 'Can be determined for each ' + result.workflowMasterStep[i].title + ' by the relevant ' + parentSharedLimitDetail.title + ' subordinate to his/her own limits.' : ((0, lodash_1.toNumber)(result.workflowMasterStep[i].lengthOfCommitment) === -1 ?
                            'Unlimited' :
                            ((0, lodash_1.toNumber)(result.workflowMasterStep[i].lengthOfCommitment) === 0 ?
                                'Approval Required' :
                                result.workflowMasterStep[i].lengthOfCommitment.toString()));
                        data.mergeColCount = parentSharedLimitDetail ? (data.mergeColCount + 1) : 0;
                    }
                    reportList.push(data);
                    exceptionSteps.forEach((exceptionStep) => {
                        let data = {
                            'Associate Role': exceptionStep.title +
                                ' (' +
                                exceptionStep.entityTitle +
                                ((exceptionStep.entityTitle !== exceptionStep.entityCode) ?
                                    ' - ' + exceptionStep.entityCode :
                                    '') +
                                ')',
                            'Single Proposal': ((0, lodash_1.toNumber)(exceptionStep.singleLimit) === -1 ?
                                'Unlimited' :
                                ((0, lodash_1.toNumber)(exceptionStep.singleLimit) === 0 ?
                                    'Approval Required' :
                                    exceptionStep.singleLimit.toString())),
                            mergeColCount: 0,
                            isExceptionStep: true
                        };
                        if (result.isAggregateLimitApplicable) {
                            data['Annual Aggregate'] = ((0, lodash_1.toNumber)(exceptionStep.aggregateLimit) === -1 ?
                                'Unlimited' :
                                ((0, lodash_1.toNumber)(exceptionStep.aggregateLimit) === 0 ?
                                    'Approval Required' :
                                    exceptionStep.aggregateLimit.toString()));
                        }
                        if (result.isCommitmentLengthApplicable) {
                            data['Length Of Commitment'] = ((0, lodash_1.toNumber)(exceptionStep.lengthOfCommitment) === -1 ?
                                'Unlimited' :
                                ((0, lodash_1.toNumber)(exceptionStep.lengthOfCommitment) === 0 ?
                                    'Approval Required' :
                                    exceptionStep.lengthOfCommitment.toString()));
                        }
                        reportList.push(data);
                    });
                }
                const SHEET_NAME = 'Authority Limit';
                const FILE_NAME = mappings_1.AFE_REQUEST_TYPE_ID_WITH_LABEL_MAPPING[result.requestTypeId] + (result.budgetType ? (' ' + mappings_1.BUDGET_TYPE_NAME_MAPPING[result.budgetType]) : '') + (((_a = result === null || result === void 0 ? void 0 : result.projectComponent) === null || _a === void 0 ? void 0 : _a.title) ? (' ' + result.projectComponent.title) : '') + ' Authority Limit ' + result.year;
                const report = yield this.excelSheetService.createPolicyExcelSheet(reportList, SHEET_NAME);
                return { report, filename: FILE_NAME };
            }
            else {
                throw new exceptions_1.HttpException('Workflow detail is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getWorkflowSettingHistory(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.historyApiClient.getRequestHistory(id, enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING);
            return result.map(d => (0, class_transformer_1.instanceToPlain)(new get_history_response_dto_1.GetHistoryResponseDTO(d)));
        });
    }
    publishWorkflow(workflowSettingId, unpublishedVersion, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { masterSettingDetail, isPublished, historyRequest } = yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                let result;
                if (!unpublishedVersion) {
                    result = yield this.workflowMasterSettingRepository.getMasterSettingById(workflowSettingId);
                }
                else {
                    result = yield this.workflowMasterSettingRepository.getUnpublishedVersionWorkflowDetailOnlyBySettingId(workflowSettingId);
                }
                if (!result) {
                    if (unpublishedVersion) {
                        throw new exceptions_1.HttpException('Currently, there is no new version available to publish.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                    throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
                }
                const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result);
                if (!unpublishedVersion) {
                    if (masterSettingDetail.published) {
                        throw new exceptions_1.HttpException('Workflow has already been published.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                    if ((0, lodash_1.toNumber)(masterSettingDetail.totalAddedSteps) === 0) {
                        throw new exceptions_1.HttpException('At least one workflow step should be added before publish.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                    }
                }
                if (masterSettingDetail.parentId) {
                    let parentResult;
                    if (!unpublishedVersion) {
                        parentResult = yield this.workflowMasterSettingRepository.getMasterSettingById(masterSettingDetail.parentId);
                    }
                    else {
                        parentResult = yield this.workflowMasterSettingRepository.getUnpublishedVersionWorkflowDetailBySettingId(masterSettingDetail.parentId);
                    }
                    if (!parentResult) {
                        if (!unpublishedVersion) {
                            throw new exceptions_1.HttpException('Master Workflow setting is unavailable for this overriden.', enums_1.HttpStatus.NOT_FOUND);
                        }
                    }
                    if (parentResult) {
                        const parentSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, parentResult);
                        if (unpublishedVersion) {
                            if (parentResult.workflowMasterStep.length) {
                                throw new exceptions_1.HttpException('A new version master workflow is unpublished. You need to publish the new version of master before publishing the new version of overidden workflow.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                            }
                        }
                        else {
                            if (!parentSettingDetail.published) {
                                throw new exceptions_1.HttpException('Master workflow is unpublished. You need to publish the master before publishing the overidden workflow.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                            }
                        }
                    }
                }
                if (!unpublishedVersion) {
                    yield this.workflowMasterSettingRepository.updateWorkflowSetting([workflowSettingId], [workflowSettingId], { published: true }, currentContext);
                    yield this.historyApiClient.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: masterSettingDetail.id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.PUBLISHED,
                        comments: 'Workflow setting has been published.',
                        additional_info: masterSettingDetail,
                    });
                    return { masterSettingDetail, isPublished: true, historyRequest: [] };
                }
                else {
                    const unpublishedWorkflows = yield this.workflowMasterSettingRepository.getAllUnpublishedNewVersionSettingDetailWithSteps(masterSettingDetail.id);
                    let historyRequest = [];
                    let unpublishWorkflowIds = [];
                    let activatingNewVersionStepPromises = [];
                    let deactivatingOldVersionStepPromises = [];
                    for (let i = 0; i < unpublishedWorkflows.length; i++) {
                        activatingNewVersionStepPromises.push({
                            condition: {
                                workflowMasterSettingId: unpublishedWorkflows[i].id,
                                version: ((0, lodash_1.toNumber)(unpublishedWorkflows[i].unpublishedVersion)),
                                deleted: false,
                                active: false
                            },
                            data: {
                                active: true
                            },
                            currentContext,
                            params: {
                                includeDeleted: true,
                                includeInactive: true,
                                throwException: true,
                            }
                        });
                        if ((0, lodash_1.toNumber)(unpublishedWorkflows[i].version) !== (0, lodash_1.toNumber)(unpublishedWorkflows[i].unpublishedVersion)) {
                            deactivatingOldVersionStepPromises.push({
                                condition: {
                                    workflowMasterSettingId: unpublishedWorkflows[i].id,
                                    version: (0, lodash_1.toNumber)(unpublishedWorkflows[i].version),
                                    deleted: false,
                                    active: true
                                },
                                data: {
                                    active: false,
                                    deleted: true
                                },
                                currentContext,
                                params: {
                                    includeDeleted: true,
                                    includeInactive: false,
                                    throwException: true,
                                }
                            });
                        }
                        unpublishWorkflowIds.push(unpublishedWorkflows[i].id);
                    }
                    yield Promise.all(activatingNewVersionStepPromises.map(activatingNewVersionStep => this.workflowMasterStepRepository.updateWorkflowByCondition(activatingNewVersionStep.condition, activatingNewVersionStep.data, activatingNewVersionStep.currentContext, activatingNewVersionStep.params)));
                    yield Promise.all(deactivatingOldVersionStepPromises.map(deactivatingOldVersionStep => this.workflowMasterStepRepository.updateWorkflowByCondition(deactivatingOldVersionStep.condition, deactivatingOldVersionStep.data, deactivatingOldVersionStep.currentContext, deactivatingOldVersionStep.params)));
                    activatingNewVersionStepPromises.length = 0;
                    deactivatingOldVersionStepPromises.length = 0;
                    if (unpublishWorkflowIds.length) {
                        yield this.workflowMasterSettingRepository.updateWorkflowSetting(unpublishWorkflowIds, [], {
                            version: this.sequlizeOperator.sequelizeLiteral('unpublished_version'),
                        }, currentContext);
                        yield this.workflowMasterSettingRepository.updateWorkflowSetting(unpublishWorkflowIds, [], {
                            unpublishedVersion: null
                        }, currentContext);
                        if (!masterSettingDetail.published) {
                            yield this.workflowMasterSettingRepository.updateWorkflowSetting([workflowSettingId], [], { published: true }, currentContext);
                        }
                        for (let i = 0; i < unpublishedWorkflows.length; i++) {
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: unpublishedWorkflows[i].id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_SETTING_NEW_VERSION,
                                comments: 'Version ' + (unpublishedWorkflows[i].unpublishedVersion) + ' changes has been activated.',
                                additional_info: {
                                    version: ((0, lodash_1.toNumber)(unpublishedWorkflows[i].unpublishedVersion))
                                },
                            });
                        }
                    }
                    let removeChildLimitParentIds = [];
                    unpublishedWorkflows.forEach((unpublishedWorkflow) => {
                        unpublishedWorkflow.workflowMasterStep.forEach((workflowMasterStep) => {
                            if (workflowMasterStep.removeChildLimit) {
                                removeChildLimitParentIds.push(workflowMasterStep.workflowMasterStepId);
                            }
                        });
                    });
                    if (removeChildLimitParentIds.length) {
                        yield this.workflowSharedChildLimitRepository.deleteSharedLimitByCondition({
                            workflowMasterParentStepId: this.sequlizeOperator.inOperator(removeChildLimitParentIds)
                        }, currentContext);
                        if (!masterSettingDetail.parentId) {
                            yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                                workflowMasterStepId: this.sequlizeOperator.inOperator(removeChildLimitParentIds)
                            }, {
                                removeChildLimit: false
                            }, currentContext);
                        }
                    }
                    return { masterSettingDetail, isPublished: true, historyRequest };
                }
            }));
            if (isPublished) {
                if (masterSettingDetail.parentId) {
                    return { message: 'New version workflow setting has been published.' };
                }
                else {
                    return { message: 'New version for all master and overriden workflow setting has been synced. Note - Any previous unpublished overridden setting need to be published seperately.' };
                }
            }
            else {
                throw new exceptions_1.HttpException('Something went wrong while publishing!', enums_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        });
    }
    publishOverriddenWorkflowSetting(workflowMasterSettingId, publishedOverriddenWorkflowRequest, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { copyToAllOverridens, selectedOveriddenWorkflows } = publishedOverriddenWorkflowRequest;
            let condition = null;
            if (copyToAllOverridens) {
                condition = {
                    parentId: workflowMasterSettingId,
                    unpublishedVersion: null,
                    published: false
                };
            }
            if (selectedOveriddenWorkflows && selectedOveriddenWorkflows.length) {
                condition = Object.assign(Object.assign({}, condition), { id: this.sequlizeOperator.inOperator(selectedOveriddenWorkflows) });
            }
            const allUnpublishedOverriddens = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition(condition);
            const idsToPublish = [];
            let history = [];
            allUnpublishedOverriddens.forEach((unpublishedOverridden) => {
                const workflowSettingId = (0, lodash_1.toNumber)(unpublishedOverridden.id);
                idsToPublish.push(workflowSettingId);
                history.push({
                    created_by: currentContext.user.username,
                    entity_id: workflowSettingId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.PUBLISHED,
                    comments: 'Workflow setting has been published.'
                });
            });
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                if (idsToPublish.length) {
                    yield this.workflowMasterSettingRepository.updateWorkflowSetting(idsToPublish, [], { published: true }, currentContext);
                }
                if (history.length) {
                    yield this.historyApiClient.addBulkRequestHistory(history);
                }
                return { message: 'Selected workflow settings has been published.' };
            }));
        });
    }
    unpublishWorkflow(workflowSettingId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getMasterSettingById(workflowSettingId);
            if (result) {
                const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result);
                if (!masterSettingDetail.published) {
                    throw new exceptions_1.HttpException('Workflow has already been unpublished.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                yield this.workflowMasterSettingRepository.updateWorkflowSetting([workflowSettingId], masterSettingDetail.parentId ? [] : [workflowSettingId], { published: false }, currentContext);
                yield this.historyApiClient.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: masterSettingDetail.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.UNPUBLISHED,
                    comments: 'Workflow has been unpublished successfully.',
                    additional_info: masterSettingDetail,
                });
                return { message: 'Workflow setting has been unpublished.' };
            }
            else {
                throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    validateOverrideWorkflow(workflowSettingId, overrideWorkflowRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityDetails } = overrideWorkflowRequestDto;
            let messages = [];
            for (let i = 0; i < entityDetails.length; i++) {
                const overridenEntityDetail = entityDetails[i];
                const childList = yield this.adminApiClient.getChildernListOfBusinessEntity(overridenEntityDetail.id);
                if (childList.length) {
                    const result = yield this.workflowMasterSettingRepository.getWorkflowDetailByWorkflowSettingId(workflowSettingId);
                    if (result) {
                        if ((result.version !== overrideWorkflowRequestDto.version) && (result.unpublishedVersion !== overrideWorkflowRequestDto.version)) {
                            throw new exceptions_1.HttpException('Invalid Version Selected.', enums_1.HttpStatus.NOT_FOUND);
                        }
                        const overridenList = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition({
                            entityId: this.sequlizeOperator.inOperator(childList),
                            parentId: result.parentId ? result.parentId : result.id
                        });
                        if (overridenList.length) {
                            let entityNames = '';
                            overridenList.forEach((overriden, key) => {
                                if (key) {
                                    entityNames = entityNames + ', ';
                                }
                                entityNames = entityNames + overriden.entityTitle + '(' + overriden.entityType + ')';
                            });
                            messages.push({
                                message: 'Overrides for child entities ' + entityNames + ' for selected entity ' + overridenEntityDetail.name + ' also exist which may conflict with this overridden. You still want to override the workflow for the selected entity or you want to cancel and review the child overridden\'s?'
                            });
                        }
                    }
                    else {
                        throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
                    }
                }
            }
            if (messages.length) {
                return messages;
            }
            else {
                messages.push({ message: 'You want to override this workflow?' });
                return messages;
            }
        });
    }
    overrideWorkflow(workflowSettingId, overrideWorkflowRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityDetails } = overrideWorkflowRequestDto;
            let result;
            let isUnpublishedVersion = false;
            const workflowDetail = yield this.workflowMasterSettingRepository.getWorkflowDetailByWorkflowSettingId(workflowSettingId);
            if (workflowDetail) {
                if (workflowDetail.version === overrideWorkflowRequestDto.version) {
                    isUnpublishedVersion = false;
                }
                if (workflowDetail.unpublishedVersion === overrideWorkflowRequestDto.version) {
                    isUnpublishedVersion = true;
                }
            }
            if (!isUnpublishedVersion) {
                result = yield this.workflowMasterSettingRepository.getWorkflowDetailBySettingIdNVersion(workflowSettingId, overrideWorkflowRequestDto.version);
            }
            else {
                result = yield this.workflowMasterSettingRepository.getUnpublishedVersionWorkflowDetailBySettingIdNVersion(workflowSettingId, overrideWorkflowRequestDto.version);
            }
            if (result) {
                if (!isUnpublishedVersion) {
                    let isUnpublishedWorkflowAvailable;
                    if (result.parentId) {
                        isUnpublishedWorkflowAvailable = yield this.workflowMasterSettingRepository.ifUnpublishedNewVersionAvailable(result.parentId, true, false, [result.id]);
                        if (isUnpublishedWorkflowAvailable) {
                            throw new exceptions_1.HttpException('Override is not possible since unpublished version is available for either master or this overridden setting.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                    }
                    else {
                        isUnpublishedWorkflowAvailable = yield this.workflowMasterSettingRepository.ifUnpublishedNewVersionAvailable(result.id, false);
                        if (isUnpublishedWorkflowAvailable) {
                            throw new exceptions_1.HttpException('Override is not possible since unpublished version is available for master setting.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                        }
                    }
                }
                const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.WorkflowDetailResponseDTO, result);
                if (!masterSettingDetail.workflowMasterStep.length) {
                    throw new exceptions_1.HttpException('The setting does not have steps for overriding it.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                for (let i = 0; i < entityDetails.length; i++) {
                    const entityDetail = entityDetails[i];
                    const existingWorkflow = yield this.workflowMasterSettingRepository.getAllMasterSetting(1, 1, {
                        parentId: result.parentId ? result.parentId : result.id,
                        entityId: entityDetail.id,
                    });
                    if (existingWorkflow.rows.length) {
                        throw new exceptions_1.HttpException(entityDetail.name + ' overridden already exist.', enums_1.HttpStatus.CONFLICT);
                    }
                }
                let historyRequest = [];
                const newWorkflowResponse = yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    let newWorkflows = [];
                    for (let i = 0; i < entityDetails.length; i++) {
                        const entityDetail = entityDetails[i];
                        const overrideWorkflowDetail = {
                            requestTypeId: masterSettingDetail.requestTypeId,
                            year: masterSettingDetail.year,
                            isCommitmentLengthApplicable: masterSettingDetail.isCommitmentLengthApplicable,
                            isAggregateLimitApplicable: masterSettingDetail.isAggregateLimitApplicable,
                            projectComponentId: masterSettingDetail.projectComponentId,
                            budgetType: masterSettingDetail.budgetType,
                            entityId: entityDetail.id,
                            entityCode: entityDetail.code,
                            entityTitle: entityDetail.name,
                            entityType: entityDetail.type,
                            parentId: (masterSettingDetail.parentId ? masterSettingDetail.parentId : masterSettingDetail.id),
                            unpublishedVersion: (isUnpublishedVersion ? 1 : null)
                        };
                        const newWorkflowResponse = yield this.workflowMasterSettingRepository.addNewMasterWorkflowSetting(overrideWorkflowDetail, currentContext);
                        const { workflowMasterStep } = masterSettingDetail, masterSetting = __rest(masterSettingDetail, ["workflowMasterStep"]);
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: newWorkflowResponse.id,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_SETTING_OVERRIDE,
                            comments: 'This setting has been overriden for ' + entityDetail.type + ' - ' + entityDetail.name + ' ( ' + entityDetail.code + ' ) from ' + masterSettingDetail.entityType + ' - ' + masterSettingDetail.entityTitle + ' ( ' + masterSettingDetail.entityCode + ' ).' + (isUnpublishedVersion ? ' from unpublished version' : '') + '.',
                            additional_info: {
                                masterSettingDetail: masterSetting,
                                overridenEntity: entityDetail
                            },
                        });
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: workflowSettingId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_SETTING_OVERRIDE,
                            comments: 'The Workflow setting for ' + entityDetail.type + ' - ' + entityDetail.name + ' ( ' + entityDetail.code + ' ) has been overriden' + (isUnpublishedVersion ? ' from unpublished version' : '') + '.',
                            additional_info: {
                                masterSettingDetail: masterSetting,
                                overridenEntity: entityDetail
                            },
                        });
                        const workflowMasterStepDetails = [];
                        masterSettingDetail.workflowMasterStep.forEach(workflowMasterStep => {
                            workflowMasterStepDetails.push({
                                title: workflowMasterStep.title,
                                workflowMasterSettingId: newWorkflowResponse.id,
                                associateLevel: workflowMasterStep.associateLevel,
                                associateRole: workflowMasterStep.associateRole,
                                associateType: workflowMasterStep.associateType,
                                associatedColumn: workflowMasterStep.associatedColumn,
                                associatedUser: workflowMasterStep.associatedUser,
                                parallelIdentifier: workflowMasterStep.parallelIdentifier,
                                singleLimit: workflowMasterStep.singleLimit,
                                aggregateLimit: workflowMasterStep.aggregateLimit,
                                lengthOfCommitment: workflowMasterStep.lengthOfCommitment,
                                skipLimitRuleId: workflowMasterStep.skipLimitRuleId,
                                canWorkflowStart: workflowMasterStep.canWorkflowStart,
                                canRemovedAtChildLevel: workflowMasterStep.canRemovedAtChildLevel,
                                canShareLimitToChild: workflowMasterStep.canShareLimitToChild,
                                isMandatory: workflowMasterStep.isMandatory,
                                includeBelowSteps: workflowMasterStep.includeBelowSteps,
                                sharedLimitMasterStepChildId: workflowMasterStep.sharedLimitMasterStepChildId,
                                approvalSequence: workflowMasterStep.approvalSequence,
                                ruleId: workflowMasterStep.ruleId,
                                workflowMasterStepId: workflowMasterStep.workflowMasterStepId,
                                inherited: true,
                                active: (isUnpublishedVersion ? false : true)
                            });
                        });
                        if (workflowMasterStepDetails.length) {
                            const copiedSteps = yield this.workflowMasterStepRepository.addBulkWorkflowSteps(workflowMasterStepDetails, currentContext);
                            for (let i = 0; i < copiedSteps.length; i++) {
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: copiedSteps[i].id,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_STEPS_OVERRIDE,
                                    comments: copiedSteps[i].title + ' step has been overriden from ' + masterSettingDetail.entityType + ' - ' + masterSettingDetail.entityTitle + ' ( ' + masterSettingDetail.entityCode + ' ).',
                                    additional_info: {
                                        stepsDetail: copiedSteps
                                    },
                                });
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: copiedSteps[i].workflowMasterSettingId,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                                    comments: copiedSteps[i].title + ' step has been added.',
                                    additional_info: {
                                        stepDetail: copiedSteps[i]
                                    },
                                });
                            }
                        }
                        newWorkflows.push(newWorkflowResponse);
                    }
                    return newWorkflows;
                }));
                if (historyRequest.length) {
                    yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                }
                return (0, helpers_1.multiObjectToInstance)(dtos_1.NewMasterWorkflowResponseDto, newWorkflowResponse);
            }
            else {
                throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    cloneWorkflowStep(workflowSettingId, cloneWorkflowRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            console.log('Clone Start', new Date());
            const [currentWorkflowResponse, cloneWorkflowDetail] = yield Promise.all([
                this.workflowMasterSettingRepository.getMasterSettingById(workflowSettingId),
                this.workflowMasterSettingRepository.getWorkflowSettingWithStepById(cloneWorkflowRequestDto.cloneWorkflowSettingId),
            ]);
            console.log('[currentWorkflowResponse, cloneWorkflowDetail]', new Date());
            const currentWorkflowDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, currentWorkflowResponse);
            if (!currentWorkflowDetail) {
                throw new exceptions_1.HttpException('Current workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
            if ((0, lodash_1.toNumber)(currentWorkflowDetail.totalAddedSteps) > 0) {
                throw new exceptions_1.HttpException('Workflow already contains steps, cloning not allowed.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!cloneWorkflowDetail) {
                throw new exceptions_1.HttpException('Cloning workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
            if (!cloneWorkflowDetail.workflowMasterStep.length) {
                throw new exceptions_1.HttpException('No steps available to clone in the target workflow.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            console.log('[INITIAL VALIDATION DONE]', new Date());
            const cloneMasterWorkflowDetail = cloneWorkflowDetail.parentId
                ? yield this.workflowMasterSettingRepository.getWorkflowSettingWithStepById(cloneWorkflowDetail.parentId)
                : null;
            console.log('[Fetched cloneMasterWorkflowDetail]', new Date());
            const clonedStepList = cloneWorkflowDetail.workflowMasterStep.map((workflowMasterStep) => {
                const { id, createdOn, createdBy, updatedOn, updatedBy, version } = workflowMasterStep, workflowStepDetail = __rest(workflowMasterStep, ["id", "createdOn", "createdBy", "updatedOn", "updatedBy", "version"]);
                return Object.assign(Object.assign({}, workflowStepDetail), { inherited: false, workflowMasterSettingId: currentWorkflowDetail.id, aggregateLimit: currentWorkflowDetail.isAggregateLimitApplicable ? workflowStepDetail.aggregateLimit : 0, lengthOfCommitment: currentWorkflowDetail.isCommitmentLengthApplicable ? workflowStepDetail.lengthOfCommitment : null });
            });
            console.log('[Mapped clonedStepList]', new Date());
            const childLimitShareMapping = cloneWorkflowDetail.workflowMasterStep.reduce((acc, workflowStepDetail) => {
                if (workflowStepDetail.sharedLimitMasterStepChildId) {
                    acc.push({
                        parentApprovalSequence: workflowStepDetail.approvalSequence,
                        childApprovalSequence: this.getSequenceNumberWithStepId((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, cloneMasterWorkflowDetail ? cloneMasterWorkflowDetail.workflowMasterStep : cloneWorkflowDetail.workflowMasterStep), workflowStepDetail.sharedLimitMasterStepChildId),
                        parentStepId: workflowStepDetail.workflowMasterStepId,
                        childStepId: workflowStepDetail.sharedLimitMasterStepChildId
                    });
                }
                return acc;
            }, []);
            console.log('[Reduced childLimitShareMapping]', new Date());
            let historyRequest = [];
            const newWorkflowResponse = yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                console.log('[Start Transaction]', new Date());
                let bulkStepsAddedResponse = yield this.workflowMasterStepRepository.addBulkWorkflowSteps(clonedStepList, currentContext);
                console.log('[Bulk Steps Added Response]', new Date());
                yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                    workflowMasterSettingId: currentWorkflowDetail.id
                }, {
                    workflowMasterStepId: this.sequlizeOperator.columnOperator('id')
                }, currentContext);
                console.log('//9 [Update Workflow By Condition]', new Date());
                if (childLimitShareMapping.length) {
                    console.log('[Inside childLimitShareMapping if condition and start Promise]', new Date());
                    const parentChildStepMappings = yield Promise.all(childLimitShareMapping.map((mapping) => __awaiter(this, void 0, void 0, function* () {
                        const parentStepId = this.getStepIdWithSequenceNumber((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, bulkStepsAddedResponse), mapping.parentApprovalSequence);
                        const childStepId = this.getStepIdWithSequenceNumber((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, bulkStepsAddedResponse), mapping.childApprovalSequence);
                        const oldParentStepId = mapping.parentStepId;
                        const oldChildStepId = mapping.childStepId;
                        yield this.workflowMasterStepRepository.updateWorkflowByCondition({ id: parentStepId }, { sharedLimitMasterStepChildId: childStepId }, currentContext);
                        bulkStepsAddedResponse = bulkStepsAddedResponse.map((stepDetail) => {
                            if (stepDetail.id === parentStepId) {
                                stepDetail.sharedLimitMasterStepChildId = childStepId;
                            }
                            return stepDetail;
                        });
                        return { parentStepId, childStepId, parentApprovalSequence: mapping.parentApprovalSequence, childApprovalSequence: mapping.childApprovalSequence, oldParentStepId, oldChildStepId };
                    })));
                    console.log('[Promise.all parentChildStepMappings Done]', new Date());
                    bulkStepsAddedResponse.forEach((stepDetail) => {
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: stepDetail.id,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_STEP_CLONE,
                            comments: `${stepDetail.title} step has been cloned.`,
                            additional_info: {
                                stepDetail: stepDetail,
                                clonedWorkflowSettingId: cloneWorkflowDetail.id,
                            },
                        });
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: stepDetail.workflowMasterSettingId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                            comments: `${stepDetail.title} step has been cloned.`,
                            additional_info: {
                                stepDetail: stepDetail,
                            },
                        });
                        if (stepDetail.sharedLimitMasterStepChildId) {
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: stepDetail.id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                comments: 'Child limit setting has been updated for this step.',
                                additional_info: {
                                    requestPayload: stepDetail,
                                },
                            });
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: stepDetail.sharedLimitMasterStepChildId,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                comments: `${stepDetail.title} is now linked with this step to share the limit.`,
                            });
                        }
                    });
                    console.log('[Update history for each bulk step added]', new Date());
                    if (cloneWorkflowRequestDto.cloneAllChildLimit) {
                        console.log('[Inside cloneAllChildLimit if condition]', new Date());
                        const cloneChildLimitList = [];
                        console.log('[Starting loop for parentChildStepMappings in if condition]', new Date());
                        for (const mapping of parentChildStepMappings) {
                            const childLimits = yield this.workflowSharedChildLimitRepository.findChildSharedLimitsByCondition({
                                workflowMasterParentStepId: mapping.oldParentStepId,
                                workflowMasterStepId: mapping.oldChildStepId,
                            });
                            if (childLimits.length) {
                                const parentStepId = this.getStepIdWithSequenceNumber((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, bulkStepsAddedResponse), mapping.parentApprovalSequence);
                                const childStepId = this.getStepIdWithSequenceNumber((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, bulkStepsAddedResponse), mapping.childApprovalSequence);
                                childLimits.forEach((childLimit) => {
                                    const { id, createdOn, createdBy, updatedOn, updatedBy } = childLimit, childLimitDetail = __rest(childLimit, ["id", "createdOn", "createdBy", "updatedOn", "updatedBy"]);
                                    cloneChildLimitList.push(Object.assign(Object.assign({}, childLimitDetail), { workflowMasterStepId: childStepId, workflowMasterParentStepId: parentStepId, aggregateLimit: currentWorkflowDetail.isAggregateLimitApplicable ? childLimitDetail.aggregateLimit : 0 }));
                                });
                            }
                        }
                        console.log('[Ending loop for parentChildStepMappings in if condition]', new Date());
                        if (cloneChildLimitList.length) {
                            console.log('[addBulkSharedLimitStep Start]', new Date());
                            const bulkChildSharedLimit = yield this.workflowSharedChildLimitRepository.addBulkSharedLimitStep(cloneChildLimitList, currentContext);
                            bulkChildSharedLimit === null || bulkChildSharedLimit === void 0 ? void 0 : bulkChildSharedLimit.forEach((childLimit) => {
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: childLimit.workflowMasterStepId,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_CLONE,
                                    comments: `Limit Cloned - Limit added by parent step for ${childLimit.entityTitle}(${childLimit.entityCode}) entity.`,
                                    additional_info: {
                                        limitDetail: childLimit,
                                    },
                                });
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: childLimit.workflowMasterParentStepId,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_CHILD_LIMIT_CLONE,
                                    comments: `Limit shared to ${childLimit.entityTitle}(${childLimit.entityCode}) entity for child step.`,
                                    additional_info: {
                                        limitDetail: childLimit,
                                    },
                                });
                            });
                            console.log('[addBulkSharedLimitStep Done]', new Date());
                        }
                    }
                }
                if (cloneWorkflowRequestDto.cloneAllBucketLimit) {
                    console.log('[Inside if of cloneAllBucketLimit]', new Date());
                    let sharedBucketList = yield this.workflowSharedBucketLimitRepository.getSharedBucketListBySettingId(cloneWorkflowDetail.parentId ? cloneWorkflowDetail.parentId : cloneWorkflowDetail.id);
                    if (sharedBucketList.length) {
                        sharedBucketList = sharedBucketList.map((sharedBucketDetail) => {
                            sharedBucketDetail.workflowMasterSettingId = currentWorkflowDetail.id;
                            const sequenceNumber = this.getSequenceNumberWithStepId((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, cloneWorkflowDetail.workflowMasterStep), sharedBucketDetail.workflowMasterStepId);
                            const workflowMasterStepId = this.getStepIdWithSequenceNumber((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, bulkStepsAddedResponse), sequenceNumber);
                            sharedBucketDetail.workflowMasterStepId = workflowMasterStepId;
                            const { id, createdOn, createdBy, updatedOn, updatedBy } = sharedBucketDetail, clonesharedBucketDetail = __rest(sharedBucketDetail, ["id", "createdOn", "createdBy", "updatedOn", "updatedBy"]);
                            return clonesharedBucketDetail;
                        });
                        const bulkBucketLimit = yield this.workflowSharedBucketLimitRepository.addBulkSharedBucketLimit(sharedBucketList, currentContext);
                        console.log('[Inside if of cloneAllBucketLimit End]', new Date());
                        if (bulkBucketLimit.length) {
                            for (let i = 0; i < bulkBucketLimit.length; i++) {
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: bulkBucketLimit[i].workflowMasterStepId,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_BUCKET_LIMIT_CLONED,
                                    comments: 'Bucket Cloned - ' + bulkBucketLimit[i].entityCode + ' will share the same bucket of ' + bulkBucketLimit[i].bucketEntityCode,
                                    additional_info: {
                                        bucketDetail: bulkBucketLimit[i],
                                    }
                                });
                            }
                        }
                    }
                }
                if (!cloneWorkflowDetail.parentId && cloneWorkflowRequestDto.cloneOverriddenWorkflow) {
                    console.log('[Inside if of get all overriden workflow and clone everything]', new Date());
                    let overriddenSettings = yield this.workflowMasterSettingRepository.getAllWorkflowSettingWithStepByCondition({
                        parentId: cloneWorkflowDetail.id
                    });
                    console.log('[Got all overriden workflow]', new Date());
                    if (overriddenSettings.length) {
                        let overridenCurrentSettings = [];
                        console.log('[Looping all overriden workflow]');
                        for (let overridenIndex = 0; overridenIndex < overriddenSettings.length; overridenIndex++) {
                            const overridenWorkflowSettingDetail = overriddenSettings[overridenIndex];
                            const { entityId, entityCode, entityTitle, entityType, workflowMasterStep } = overridenWorkflowSettingDetail;
                            overridenCurrentSettings.push({
                                entityId,
                                entityCode,
                                entityTitle,
                                entityType,
                                isCommitmentLengthApplicable: currentWorkflowDetail.isCommitmentLengthApplicable,
                                isAggregateLimitApplicable: currentWorkflowDetail.isAggregateLimitApplicable,
                                parentId: currentWorkflowDetail.id,
                                year: currentWorkflowDetail.year,
                                published: false,
                                deleted: false,
                                active: true,
                                requestTypeId: currentWorkflowDetail.requestTypeId,
                                budgetType: currentWorkflowDetail.budgetType,
                                projectComponentId: currentWorkflowDetail.projectComponentId,
                            });
                        }
                        console.log('[Looping done for overriden workflow]');
                        console.log('[Starting bulk insert for overriden workflow]');
                        const overridenClonedSettingList = yield this.workflowMasterSettingRepository.addBulkMasterWorkflowSetting(overridenCurrentSettings, currentContext);
                        console.log('[End of bulk insert for overriden workflow]');
                        if (overridenClonedSettingList.length) {
                            console.log('[Inside overridenClonedSettingList.length if condition ]');
                            overriddenSettings = overriddenSettings.map((overriddenSetting) => {
                                const updatedOverriddenWorkflowDetail = overridenClonedSettingList.find((overridenClonedSetting) => {
                                    if ((overridenClonedSetting.entityId === overriddenSetting.entityId)) {
                                        return overridenClonedSetting;
                                    }
                                });
                                overriddenSetting.id = updatedOverriddenWorkflowDetail.id;
                                return overriddenSetting;
                            });
                            for (let i = 0; i < overridenClonedSettingList.length; i++) {
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: overridenClonedSettingList[i].id,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_SETTING_CLONE,
                                    comments: 'Workflow Setting has been cloned.',
                                    additional_info: {
                                        clonedSettingDetail: overridenClonedSettingList[i],
                                    }
                                });
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: workflowSettingId,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_SETTING_OVERRIDE,
                                    comments: 'The Workflow setting for ' + overridenClonedSettingList[i].entityType + ' - ' + overridenClonedSettingList[i].entityTitle + ' ( ' + overridenClonedSettingList[i].entityCode + ' ) has been overriden while cloning.',
                                    additional_info: {
                                        workfloSetting: overridenClonedSettingList[i]
                                    },
                                });
                            }
                            let overriddenClonedStepList = [];
                            for (let overridenIndex = 0; overridenIndex < overriddenSettings.length; overridenIndex++) {
                                overriddenSettings[overridenIndex].workflowMasterStep.forEach((overridenWorkflowMasterStep) => {
                                    const { id, createdOn, createdBy, updatedOn, updatedBy, version } = overridenWorkflowMasterStep, workflowStepDetail = __rest(overridenWorkflowMasterStep, ["id", "createdOn", "createdBy", "updatedOn", "updatedBy", "version"]);
                                    overriddenClonedStepList.push(Object.assign(Object.assign({}, workflowStepDetail), { workflowMasterSettingId: overriddenSettings[overridenIndex].id, aggregateLimit: currentWorkflowDetail.isAggregateLimitApplicable ? workflowStepDetail.aggregateLimit : 0, lengthOfCommitment: currentWorkflowDetail.isCommitmentLengthApplicable ? workflowStepDetail.lengthOfCommitment : null, workflowMasterStepId: workflowStepDetail.workflowMasterStepId }));
                                });
                            }
                            console.log('[bulkOverridenSteps start ]');
                            const bulkOverridenSteps = yield this.workflowMasterStepRepository.addBulkWorkflowSteps(overriddenClonedStepList, currentContext);
                            console.log('[bulkOverridenSteps end ]');
                            const allOverridenWorkflowId = [];
                            overridenClonedSettingList.forEach((overridenClonedSetting) => {
                                allOverridenWorkflowId.push(overridenClonedSetting.id);
                            });
                            console.log('[start updateWorkflowByCondition for overriden workflowMasterStepId ]');
                            yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                                workflowMasterSettingId: this.sequlizeOperator.inOperator(allOverridenWorkflowId),
                                inherited: false
                            }, {
                                workflowMasterStepId: this.sequlizeOperator.columnOperator('id')
                            }, currentContext);
                            console.log('[end updateWorkflowByCondition for overriden workflowMasterStepId ]');
                            if (bulkOverridenSteps && bulkOverridenSteps.length) {
                                console.log('[Inside bulkOverridenSteps if condition ]');
                                const sourceMasterClonedSteps = (0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, cloneWorkflowDetail.workflowMasterStep);
                                const currentMasterSteps = (0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, bulkStepsAddedResponse);
                                const inheritedSteps = bulkOverridenSteps.filter((bulkOverridenStep) => {
                                    return bulkOverridenStep.inherited;
                                });
                                let updateIdDirectly = [];
                                let alreadyMappedIds = [];
                                const updatePromises = inheritedSteps.map((step) => {
                                    if (alreadyMappedIds.includes(step.workflowMasterStepId)) {
                                        return null;
                                    }
                                    alreadyMappedIds.push(step.workflowMasterStepId);
                                    const prevMasterSequenceNumber = this.getSequenceNumberWithStepId(sourceMasterClonedSteps, step.workflowMasterStepId);
                                    const currentOverridenStepId = prevMasterSequenceNumber ? this.getStepIdWithSequenceNumber(currentMasterSteps, prevMasterSequenceNumber) : step.id;
                                    if (!prevMasterSequenceNumber) {
                                        updateIdDirectly.push(step.id);
                                        return null;
                                    }
                                    let updatePayload = {
                                        workflowMasterStepId: currentOverridenStepId
                                    };
                                    if (step.sharedLimitMasterStepChildId) {
                                        const prevChilLimitSequenceNumber = this.getSequenceNumberWithStepId(sourceMasterClonedSteps, step.sharedLimitMasterStepChildId);
                                        const currentChildLimitStepId = this.getStepIdWithSequenceNumber(currentMasterSteps, prevChilLimitSequenceNumber);
                                        updatePayload.sharedLimitMasterStepChildId = currentChildLimitStepId;
                                    }
                                    return {
                                        condition: {
                                            workflowMasterSettingId: this.sequlizeOperator.inOperator(allOverridenWorkflowId),
                                            workflowMasterStepId: step.workflowMasterStepId
                                        },
                                        payload: updatePayload
                                    };
                                });
                                const validUpdates = updatePromises.filter(update => update !== null);
                                console.log('[Start updateInBatches for bulkOverridenSteps]');
                                if (validUpdates.length > 0) {
                                    yield this.updateInBatches(validUpdates, currentContext);
                                }
                                console.log('[End updateInBatches for bulkOverridenSteps]');
                                if (updateIdDirectly.length) {
                                    console.log('[Start updateWorkflowByCondition for bulkOverridenSteps]');
                                    yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                                        id: this.sequlizeOperator.inOperator(updateIdDirectly)
                                    }, {
                                        workflowMasterStepId: this.sequlizeOperator.columnOperator('id'),
                                        inherited: false
                                    }, currentContext);
                                    console.log('[End updateWorkflowByCondition for bulkOverridenSteps]');
                                }
                            }
                            if (bulkOverridenSteps && bulkOverridenSteps.length) {
                                for (let i = 0; i < bulkOverridenSteps.length; i++) {
                                    historyRequest.push({
                                        created_by: currentContext.user.username,
                                        entity_id: bulkOverridenSteps[i].id,
                                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                        action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_STEP_CLONE,
                                        comments: bulkOverridenSteps[i].title + ' step has been cloned.',
                                        additional_info: {
                                            clonedStepDetail: bulkOverridenSteps[i],
                                        }
                                    });
                                    historyRequest.push({
                                        created_by: currentContext.user.username,
                                        entity_id: bulkOverridenSteps[i].workflowMasterSettingId,
                                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                        action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                                        comments: bulkOverridenSteps[i].title + ' step has been added.',
                                        additional_info: {
                                            stepDetail: bulkOverridenSteps[i]
                                        },
                                    });
                                }
                            }
                        }
                    }
                }
                console.log('[updatedWorkflowDetailWithSteps at last start]');
                const updatedWorkflowDetailWithSteps = yield this.workflowMasterSettingRepository.getWorkflowDetailByWorkflowSettingId(currentWorkflowDetail.id);
                console.log('[updatedWorkflowDetailWithSteps at last end]');
                return updatedWorkflowDetailWithSteps;
            }));
            console.log('[All Done now return]');
            if (newWorkflowResponse) {
                if (historyRequest.length) {
                }
                return (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.WorkflowDetailResponseDTO, newWorkflowResponse);
            }
            else {
                throw new exceptions_1.HttpException('Something went wrong, please try again!.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    createNewVersionWorkflow(workflowSettingId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const currentVersionWorkflowDetail = yield this.workflowMasterSettingRepository.getWorkflowSettingWithStepById(workflowSettingId);
            if (!currentVersionWorkflowDetail) {
                throw new exceptions_1.HttpException('Workflow is unavailable to create a new version.', enums_1.HttpStatus.NOT_FOUND);
            }
            if (!currentVersionWorkflowDetail.published) {
                throw new exceptions_1.HttpException('Can\'t create a new version for unpublished workflow.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            if (!currentVersionWorkflowDetail.workflowMasterStep.length) {
                throw new exceptions_1.HttpException('Can\'t create a new version of this workflow as the steps are not available.', enums_1.HttpStatus.NOT_ACCEPTABLE);
            }
            let isUnpublishedWorkflowAvailable;
            if (currentVersionWorkflowDetail.parentId) {
                isUnpublishedWorkflowAvailable = yield this.workflowMasterSettingRepository.ifUnpublishedNewVersionAvailableByIds([currentVersionWorkflowDetail.parentId, currentVersionWorkflowDetail.id]);
            }
            else {
                isUnpublishedWorkflowAvailable = yield this.workflowMasterSettingRepository.ifUnpublishedNewVersionAvailable(currentVersionWorkflowDetail.id);
            }
            if (isUnpublishedWorkflowAvailable) {
                throw new exceptions_1.HttpException('The master workflow or any overridden workflow already exists in an unpublished version.', enums_1.HttpStatus.CONFLICT);
            }
            let currentVersionMasterWorkflowDetail;
            if (currentVersionWorkflowDetail.parentId) {
                currentVersionMasterWorkflowDetail = yield this.workflowMasterSettingRepository.getWorkflowSettingWithStepById(currentVersionWorkflowDetail.parentId);
            }
            let newVersionHistoryRequest = [];
            let historyRequest = [];
            const newWorkflowResponse = yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                let clonedStepList = [];
                let childLimitShareMapping = [];
                currentVersionWorkflowDetail.workflowMasterStep.forEach((workflowMasterStep) => {
                    const { id, updatedOn, updatedBy, version } = workflowMasterStep, workflowStepDetail = __rest(workflowMasterStep, ["id", "updatedOn", "updatedBy", "version"]);
                    clonedStepList.push(Object.assign(Object.assign({}, workflowStepDetail), { version: ((0, lodash_1.toNumber)(version) + 1), active: false, deleted: false }));
                    if (workflowStepDetail.sharedLimitMasterStepChildId) {
                        childLimitShareMapping.push({
                            parentApprovalSequence: workflowStepDetail.approvalSequence,
                            childApprovalSequence: this.getSequenceNumberWithStepId((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, currentVersionMasterWorkflowDetail ? currentVersionMasterWorkflowDetail.workflowMasterStep : currentVersionWorkflowDetail.workflowMasterStep), workflowStepDetail.sharedLimitMasterStepChildId),
                            parentStepId: workflowStepDetail.workflowMasterStepId,
                            childStepId: workflowStepDetail.sharedLimitMasterStepChildId
                        });
                    }
                });
                let newVersionStepsResponse = yield this.workflowMasterStepRepository.addBulkWorkflowSteps(clonedStepList, currentContext);
                if (newVersionStepsResponse.length) {
                    for (let i = 0; i < newVersionStepsResponse.length; i++) {
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: newVersionStepsResponse[i].id,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_STEP_NEW_VERSION,
                            comments: 'Version ' + newVersionStepsResponse[i].version + ' has been created for ' + newVersionStepsResponse[i].title,
                            additional_info: {
                                stepDetail: newVersionStepsResponse[i],
                                version: newVersionStepsResponse[i].version
                            }
                        });
                        currentVersionWorkflowDetail.workflowMasterStep.forEach((prevStep) => {
                            if (newVersionStepsResponse[i].workflowMasterStepId === prevStep.workflowMasterStepId) {
                            }
                        });
                    }
                    historyRequest.push({
                        created_by: currentContext.user.username,
                        entity_id: currentVersionWorkflowDetail.id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_SETTING_NEW_VERSION,
                        comments: 'Version ' + (currentVersionWorkflowDetail.version + 1) + ' has been created.',
                        additional_info: {
                            version: (currentVersionWorkflowDetail.version + 1)
                        },
                    });
                }
                if (childLimitShareMapping.length) {
                    for (let i = 0; i < childLimitShareMapping.length; i++) {
                        const parentStepId = this.getStepIdWithSequenceNumber((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, newVersionStepsResponse), childLimitShareMapping[i].parentApprovalSequence);
                        const childStepId = this.getStepIdWithSequenceNumber((0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, newVersionStepsResponse), childLimitShareMapping[i].childApprovalSequence);
                        yield this.workflowMasterStepRepository.updateWorkflowByCondition({
                            id: parentStepId
                        }, {
                            sharedLimitMasterStepChildId: childStepId
                        }, currentContext);
                        newVersionStepsResponse = newVersionStepsResponse.map((stepDetail) => {
                            if (stepDetail.id === parentStepId) {
                                stepDetail.sharedLimitMasterStepChildId = childStepId;
                            }
                            return stepDetail;
                        });
                    }
                }
                if (!currentVersionWorkflowDetail.parentId) {
                    let overriddenSettings = yield this.workflowMasterSettingRepository.getAllWorkflowSettingWithStepByCondition({
                        parentId: currentVersionWorkflowDetail.id
                    });
                    if (overriddenSettings.length) {
                        let overriddenClonedStepList = [];
                        let overriddenClonedAllDetailStepList = [];
                        for (let overridenIndex = 0; overridenIndex < overriddenSettings.length; overridenIndex++) {
                            overriddenSettings[overridenIndex].workflowMasterStep.forEach((overridenWorkflowMasterStep) => {
                                overriddenClonedAllDetailStepList.push(overridenWorkflowMasterStep);
                                const { id, updatedOn, updatedBy, version } = overridenWorkflowMasterStep, workflowStepDetail = __rest(overridenWorkflowMasterStep, ["id", "updatedOn", "updatedBy", "version"]);
                                overriddenClonedStepList.push(Object.assign(Object.assign({}, workflowStepDetail), { version: ((0, lodash_1.toNumber)(version) + 1), active: false, deleted: false }));
                            });
                        }
                        const newVersionOverriddenSteps = yield this.workflowMasterStepRepository.addBulkWorkflowSteps(overriddenClonedStepList, currentContext);
                        const allOverridenWorkflowId = [];
                        overriddenSettings.forEach((overridenClonedSetting) => {
                            allOverridenWorkflowId.push(overridenClonedSetting.id);
                        });
                        if (newVersionOverriddenSteps && newVersionOverriddenSteps.length) {
                            const updateSharedLimitPromises = [];
                            for (let updateMasterStepIndex = 0; newVersionOverriddenSteps.length > updateMasterStepIndex; updateMasterStepIndex++) {
                                if (newVersionOverriddenSteps[updateMasterStepIndex].inherited) {
                                    const masterClonedSteps = (0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, currentVersionWorkflowDetail.workflowMasterStep);
                                    const currentMasterSteps = (0, helpers_1.multiObjectToInstance)(dtos_1.GetMasterStepsResponseDTO, newVersionStepsResponse);
                                    let updatePayload = {};
                                    if (newVersionOverriddenSteps[updateMasterStepIndex].sharedLimitMasterStepChildId) {
                                        const prevChilLimitSequenceNumber = this.getSequenceNumberWithStepId(masterClonedSteps, newVersionOverriddenSteps[updateMasterStepIndex].sharedLimitMasterStepChildId);
                                        const currentChildLimitStepId = this.getStepIdWithSequenceNumber(currentMasterSteps, prevChilLimitSequenceNumber);
                                        updatePayload.sharedLimitMasterStepChildId = currentChildLimitStepId;
                                    }
                                    if (updatePayload === null || updatePayload === void 0 ? void 0 : updatePayload.sharedLimitMasterStepChildId) {
                                        updateSharedLimitPromises.push({
                                            condition: {
                                                workflowMasterSettingId: this.sequlizeOperator.inOperator(allOverridenWorkflowId),
                                                workflowMasterStepId: newVersionOverriddenSteps[updateMasterStepIndex].workflowMasterStepId,
                                                version: newVersionOverriddenSteps[updateMasterStepIndex].version
                                            },
                                            updatePayload,
                                            currentContext
                                        });
                                    }
                                }
                            }
                            yield Promise.all(updateSharedLimitPromises.map(updateSharedLimit => this.workflowMasterStepRepository.updateWorkflowByCondition(updateSharedLimit.condition, updateSharedLimit.updatePayload, updateSharedLimit.currentContext)));
                            updateSharedLimitPromises.length = 0;
                        }
                        if (newVersionOverriddenSteps && newVersionOverriddenSteps.length) {
                            for (let i = 0; i < newVersionOverriddenSteps.length; i++) {
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: newVersionOverriddenSteps[i].id,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_STEP,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_STEP_NEW_VERSION,
                                    comments: 'Version ' + newVersionOverriddenSteps[i].version + ' has been created for ' + newVersionOverriddenSteps[i].title,
                                    additional_info: {
                                        clonedStepDetail: newVersionOverriddenSteps[i],
                                        version: newVersionOverriddenSteps[i].version
                                    }
                                });
                                overriddenClonedAllDetailStepList.forEach((overriddenClonedStep) => {
                                    if ((overriddenClonedStep.workflowMasterSettingId === newVersionOverriddenSteps[i].workflowMasterSettingId) &&
                                        (overriddenClonedStep.workflowMasterStepId === newVersionOverriddenSteps[i].workflowMasterStepId)) {
                                    }
                                });
                            }
                            for (let i = 0; i < overriddenSettings.length; i++) {
                                newVersionHistoryRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: overriddenSettings[i].id,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.WORKFLOW_SETTING_NEW_VERSION,
                                    comments: 'Version ' + (overriddenSettings[i].version + 1) + ' has been created.',
                                    additional_info: {
                                        version: (overriddenSettings[i].version + 1)
                                    }
                                });
                            }
                        }
                    }
                }
                yield this.workflowMasterSettingRepository.updateWorkflowSetting([workflowSettingId], [workflowSettingId], { unpublishedVersion: this.sequlizeOperator.sequelizeLiteral('version + 1') }, currentContext);
                return true;
            }));
            if (newWorkflowResponse) {
                if (historyRequest.length) {
                }
                if (newVersionHistoryRequest.length) {
                }
                return { message: 'New version has been created successfully.' };
            }
            else {
                throw new exceptions_1.HttpException('Something went wrong, please try again!.', enums_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        });
    }
    insertWorkflowStepsInBatches(steps, batchSize, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const totalSteps = steps.length;
            const numBatches = Math.ceil(totalSteps / batchSize);
            const promises = [];
            for (let i = 0; i < numBatches; i++) {
                const startIdx = i * batchSize;
                const endIdx = Math.min((i + 1) * batchSize, totalSteps);
                const batchSteps = steps.slice(startIdx, endIdx);
                const promise = this.workflowMasterStepRepository.addBulkWorkflowSteps(batchSteps, currentContext);
                promises.push(promise);
            }
            const batchResults = yield Promise.all(promises);
            let allNewRecords = [];
            batchResults.forEach(newRecords => {
                allNewRecords.push(...newRecords);
            });
            return allNewRecords;
        });
    }
    getSequenceNumberWithStepId(stepList, stepid) {
        const stepDetail = stepList.find((stepDetail) => {
            return (stepDetail.workflowMasterStepId === stepid);
        });
        if (stepDetail) {
            return stepDetail.approvalSequence;
        }
        return null;
    }
    getStepIdWithSequenceNumber(stepList, sequenceNumber) {
        const stepDetail = stepList.find((stepDetail) => {
            return (stepDetail.approvalSequence === sequenceNumber);
        });
        if (stepDetail) {
            return stepDetail.id;
        }
        return null;
    }
    deleteWorkflow(workflowSettingId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getMasterSettingById(workflowSettingId);
            if (result) {
                const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result);
                const limitDeduction = yield this.limitDeductionRepository.getMasterSettingLimitDeductionCount(workflowSettingId);
                if (limitDeduction > 0) {
                    throw new exceptions_1.HttpException('Unable to delete as limit got deducted for this workflow setting before.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                let allOverridenSettings = [];
                if (!masterSettingDetail.parentId) {
                    allOverridenSettings = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition({
                        parentId: masterSettingDetail.id
                    });
                }
                const deleteRes = yield this.workflowMasterSettingRepository.deleteWorkflowSetting([workflowSettingId], masterSettingDetail.parentId ? [] : [workflowSettingId], currentContext);
                if (deleteRes) {
                    let historyRequest = [];
                    historyRequest.push({
                        created_by: currentContext.user.username,
                        entity_id: workflowSettingId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                        comments: 'Workflow setting has been deleted successfully.',
                    });
                    if (!masterSettingDetail.parentId && allOverridenSettings.length) {
                        for (let i = 0; i < allOverridenSettings.length; i++) {
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: allOverridenSettings[i].id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                                comments: 'Workflow setting has been deleted successfully.'
                            });
                        }
                    }
                    if (historyRequest.length) {
                        yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                    }
                    return { message: 'Workflow setting has been deleted successfully.' };
                }
                throw new exceptions_1.HttpException('Unable to delete the workflow setting.', enums_1.HttpStatus.BAD_REQUEST);
            }
            else {
                throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    deleteMultipleOverridenWorkflowSetting(overridenWorkflowSettingIds, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getMasterWorkflowSettingsByIds(overridenWorkflowSettingIds);
            if (!(result === null || result === void 0 ? void 0 : result.length)) {
                return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    const deleteRes = yield this.workflowMasterSettingRepository.deleteWorkflowSetting(overridenWorkflowSettingIds, [], currentContext);
                    let historyRequest = [];
                    for (let i = 0; i < overridenWorkflowSettingIds.length; i++) {
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: overridenWorkflowSettingIds[i],
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                            comments: 'Workflow setting has been deleted successfully.'
                        });
                    }
                    if (historyRequest.length) {
                        yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                    }
                    return { message: 'Selected workflow setting has been deleted successfully.' };
                }));
            }
            else {
                throw new exceptions_1.HttpException('Multi select option does not allow deleting master workflow settings.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    deleteUnpublishedNewVersionWorkflow(workflowSettingId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getWorkflowSettingById(workflowSettingId);
            const isUnpublishedWorkflowAvailable = result.unpublishedVersion ? true : false;
            if (!isUnpublishedWorkflowAvailable) {
                throw new exceptions_1.HttpException('Unpublished workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
            if (result) {
                let historyRequest = [];
                const deleteResponse = yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result);
                    const deleteRes = yield this.workflowMasterStepRepository.deleteWorkflowStepByCondition({
                        workflowMasterSettingId: masterSettingDetail.id,
                        version: (masterSettingDetail.unpublishedVersion),
                        active: false
                    }, currentContext, {
                        includeDeleted: false,
                        includeInactive: true,
                        throwException: true
                    });
                    if (deleteRes) {
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: workflowSettingId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                            comments: 'Version ' + (masterSettingDetail.unpublishedVersion) + ' workflow setting has been deleted.',
                        });
                    }
                    let allOverridenSettings = [];
                    if (!masterSettingDetail.parentId) {
                        allOverridenSettings = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition({
                            parentId: masterSettingDetail.id
                        });
                        for (let i = 0; i < allOverridenSettings.length; i++) {
                            const deleteOveRes = yield this.workflowMasterStepRepository.deleteWorkflowStepByCondition({
                                workflowMasterSettingId: allOverridenSettings[i].id,
                                version: (allOverridenSettings[i].unpublishedVersion),
                                active: false
                            }, currentContext, {
                                includeDeleted: false,
                                includeInactive: true,
                                throwException: true
                            });
                            if (deleteOveRes) {
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: allOverridenSettings[i].id,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                                    comments: 'Version ' + (allOverridenSettings[i].unpublishedVersion) + ' workflow setting has been deleted.',
                                });
                            }
                        }
                    }
                    if (deleteRes) {
                        yield this.workflowMasterSettingRepository.updateWorkflowSetting([workflowSettingId], [workflowSettingId], { unpublishedVersion: null }, currentContext);
                        return true;
                    }
                    return false;
                }));
                if (deleteResponse) {
                    if (historyRequest.length) {
                        yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                    }
                    return { message: 'Unpublishd workflow setting version has been deleted successfully.' };
                }
                throw new exceptions_1.HttpException('Unable to delete the workflow setting.', enums_1.HttpStatus.BAD_REQUEST);
            }
            else {
                throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    deleteOverriddenWorkflow(workflowSettingId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getMasterSettingById(workflowSettingId);
            if (result) {
                const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result);
                if (masterSettingDetail.parentId) {
                    throw new exceptions_1.HttpException('Unable to process this request for overridden workflow.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                let allOverridenSettings = [];
                if (!masterSettingDetail.parentId) {
                    allOverridenSettings = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition({
                        parentId: masterSettingDetail.id
                    });
                }
                const deleteRes = yield this.workflowMasterSettingRepository.deleteWorkflowSetting([], [workflowSettingId], currentContext);
                if (deleteRes) {
                    let historyRequest = [];
                    if (!masterSettingDetail.parentId && allOverridenSettings.length) {
                        for (let i = 0; i < allOverridenSettings.length; i++) {
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: allOverridenSettings[i].id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                                comments: 'Workflow setting has been deleted successfully.'
                            });
                        }
                    }
                    if (historyRequest.length) {
                        yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                    }
                    return { message: 'Workflow setting has been deleted successfully.' };
                }
                throw new exceptions_1.HttpException('Unable to delete the workflow setting.', enums_1.HttpStatus.BAD_REQUEST);
            }
            else {
                throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    deleteUnpublishedOverriddenWorkflowSetting(workflowSettingId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const isUnpublishedWorkflowAvailable = yield this.workflowMasterSettingRepository.ifUnpublishedNewVersionAvailable(workflowSettingId, false, true);
            if (!isUnpublishedWorkflowAvailable) {
                throw new exceptions_1.HttpException('Unpublished overriden workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
            const result = yield this.workflowMasterSettingRepository.getWorkflowSettingById(workflowSettingId);
            if (result) {
                const masterSettingDetail = (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.GetMasterSettingResponseDTO, result);
                if (masterSettingDetail.parentId) {
                    throw new exceptions_1.HttpException('Unable to process this request for overridden workflow.', enums_1.HttpStatus.NOT_ACCEPTABLE);
                }
                return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    let allOverridenSettings = [];
                    let historyRequest = [];
                    if (!masterSettingDetail.parentId) {
                        allOverridenSettings = yield this.workflowMasterSettingRepository.getMasterFlowSettingByCondition({
                            parentId: masterSettingDetail.id
                        });
                        for (let i = 0; i < allOverridenSettings.length; i++) {
                            const deleteOveRes = yield this.workflowMasterStepRepository.deleteWorkflowStepByCondition({
                                workflowMasterSettingId: allOverridenSettings[i].id,
                                version: (allOverridenSettings[i].unpublishedVersion),
                                active: false
                            }, currentContext, {
                                includeDeleted: false,
                                includeInactive: true,
                                throwException: true
                            });
                            if (deleteOveRes) {
                                historyRequest.push({
                                    created_by: currentContext.user.username,
                                    entity_id: allOverridenSettings[i].id,
                                    entity_type: enums_1.HISTORY_ENTITY_TYPE.WORKFLOW_SETTING,
                                    action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                                    comments: 'Version ' + (allOverridenSettings[i].unpublishedVersion) + ' workflow setting has been deleted.',
                                });
                            }
                        }
                        yield this.workflowMasterSettingRepository.updateWorkflowSetting([], [workflowSettingId], { unpublishedVersion: null }, currentContext);
                        if (historyRequest.length) {
                            yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                        }
                        return { message: 'Unpublishd workflow setting version has been deleted successfully.' };
                    }
                    throw new exceptions_1.HttpException('Unable to delete the workflow setting.', enums_1.HttpStatus.BAD_REQUEST);
                }));
            }
            else {
                throw new exceptions_1.HttpException('Workflow setting is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
    getUnpublishedVersionWorkflowDetailBySettingId(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.workflowMasterSettingRepository.getUnpublishedVersionWorkflowDetailBySettingId(id);
            if (result) {
                result.published = false;
                return (0, helpers_1.singleObjectToInstance)(get_master_setting_response_dto_1.WorkflowDetailResponseDTO, result);
            }
            else {
                throw new exceptions_1.HttpException('Workflow detail is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
        });
    }
};
WorkflowMasterSettingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_2.WorkflowMasterSettingRepository,
        repositories_2.WorkflowMasterStepRepository,
        repositories_2.WorkflowSharedChildLimitRepository,
        repositories_2.WorkflowSharedBucketLimitRepository,
        clients_1.AdminApiClient,
        repositories_1.AfeProposalLimitDeductionRepository,
        helpers_1.DatabaseHelper,
        helpers_1.SequlizeOperator,
        clients_1.HistoryApiClient,
        services_1.ExcelSheetService,
        workflow_master_step_service_1.WorkflowMasterStepService])
], WorkflowMasterSettingService);
exports.WorkflowMasterSettingService = WorkflowMasterSettingService;
//# sourceMappingURL=workflow-master-setting.service.js.map