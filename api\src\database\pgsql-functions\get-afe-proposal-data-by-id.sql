
DROP FUNCTION dev_hosted.get_afe_proposal_data(bigint);
CREATE OR REPLACE FUNCTION dev_hosted.get_afe_proposal_data(bigint)
 RETURNS TABLE(
  project_name character varying, 
  reference_number character varying, 
  total_expenditure text, 
  request_type character varying, 
  business_unit character varying,
 budget_type text,
 justification text,
 user_status text,
 submitted_on text,
 project_expenditures text,
 cost_center_expenditures text,
 gl_code text,
 last_approver_name text,
 last_approver_comment text
 )
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
     afp.name as "project_name", 
     afp.project_reference_number as "reference_number",
  case 
   when afp.market_value IS NOT NULL then ('Net Value - ' || trim(LEADING from to_char(total_amount, '999,999,999,999,999.99')) || '; ' || 'Market Value - ' || trim(LEADING from to_char(market_value, '999,999,999,999,999.99')))
   else trim(LEADING from to_char(total_amount, '999,999,999,999,999.99'))  
  end as "total_expenditure",
     art.title as "request_type",
     afp.entity_title  as "business_unit",
     case 
      when afp.budget_type = 'BUDGETED' then 'Budgeted'
      when afp.budget_type = 'UNBUDGETED' then 'Unbudgeted'
      when afp.budget_type = 'MIXED' then 'Budgeted & Unbudgeted'
      else 'N/A'
     end as "budget_type",
     afp."data" -> 'projectDetails'->>'projectJustification'::text as "justification",
  (
            SELECT COALESCE(
                (
                    afp.user_status || '(' || (
                        SELECT
                            STRING_AGG((u.value->>'firstName') || ' ' || (u.value->>'lastName'), ', ')
                        FROM
                            dev_hosted.afe_proposal_approvers AS apa, jsonb_array_elements(apa.other_info -> 'usersDetail') u
                        WHERE
                            apa.afe_proposal_id = afp.id
                            AND apa.action_status = 'IN_PROGRESS'
                        GROUP BY
                            afp.id
                    ) || ')'
                ),
                afp.user_status
            )
        ) AS "user_status",
     to_char(afp.created_on, 'Mon DD YYYY HH:MIAM') as "submitted_on",
  COALESCE(
      (
          SELECT string_agg(object_title || ' - ' || trim(LEADING from to_char(amount, '999,999,999,999,999.99')), '; ') 
          FROM dev_hosted.afe_proposal_amount_splits 
          WHERE "type" = 'PROJECT_COMPONENT_SPLIT' and afe_proposal_id = $1 and object_title is not null and amount is not null
      ), 
      '-'
  ) as "project_expenditures",
  COALESCE(
   (
    SELECT string_agg(object_title || ' - ' || trim(LEADING from to_char(amount, '999,999,999,999,999.99')), '; ') 
    FROM dev_hosted.afe_proposal_amount_splits 
    WHERE "type" = 'COST_CENTER_SPLIT' and afe_proposal_id = $1 and object_title is not null and object_title != '' and amount is not null
   ), 
   '-'
  ) as "cost_center_expenditures",
  COALESCE(
      (
    SELECT string_agg(object_title || ' - ' || trim(LEADING from to_char(amount, '999,999,999,999,999.99')), '; ') 
          FROM dev_hosted.afe_proposal_amount_splits 
    WHERE "type" = 'GL_CODE_SPLIT' and afe_proposal_id = $1 and object_title is not null and object_title != '' and amount is not null
      ), 
      '-'
  ) as "gl_code",
  COALESCE (
   ( 
    SELECT (user_detail->'originalApprover'->>'firstName')::text || ' ' || (user_detail->'originalApprover'->>'lastName')::text FROM dev_hosted.afe_proposal_approvers WHERE afe_proposal_id = afp.id AND action_date IS NOT NULL AND user_detail IS NOT NULL AND action_status != 'AUTO_APPROVED' ORDER BY action_date DESC LIMIT 1
   ), '-'
  ) AS "last_approver_name",
  COALESCE (
   ( 
    SELECT comment FROM dev_hosted.afe_proposal_approvers WHERE afe_proposal_id = afp.id AND action_date IS NOT NULL AND user_detail IS NOT NULL AND action_status != 'AUTO_APPROVED' ORDER BY action_date DESC LIMIT 1
   ), '-'
  ) AS "last_approver_comment"
    FROM 
     dev_hosted.afe_proposals as afp
    inner join dev_hosted.afe_request_types as art 
     on afp.afe_request_type_id = art.id 
    WHERE afp.id = $1;
END;
$function$;