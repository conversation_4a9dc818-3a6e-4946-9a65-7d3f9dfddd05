"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceAdminService = void 0;
const common_1 = require("@nestjs/common");
const class_transformer_1 = require("class-transformer");
const lodash_1 = require("lodash");
const pagination_1 = require("../../core/pagination");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const mappings_1 = require("../../shared/mappings");
const services_1 = require("../../shared/services");
const dtos_1 = require("../dtos");
const get_history_response_dto_1 = require("../dtos/response/get-history-response.dto");
const repositories_1 = require("../repositories");
let FinanceAdminService = class FinanceAdminService {
    constructor(naturalAccountNumberRepository, costCenterRepository, companyCodeRepository, analysisCodeRepository, adminApiClient, databaseHelper, excelSheetService, mSGraphApiClient, historyApiClient) {
        this.naturalAccountNumberRepository = naturalAccountNumberRepository;
        this.costCenterRepository = costCenterRepository;
        this.companyCodeRepository = companyCodeRepository;
        this.analysisCodeRepository = analysisCodeRepository;
        this.adminApiClient = adminApiClient;
        this.databaseHelper = databaseHelper;
        this.excelSheetService = excelSheetService;
        this.mSGraphApiClient = mSGraphApiClient;
        this.historyApiClient = historyApiClient;
    }
    createCompanyCode(createCompanyCodeRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { code, name, entityId } = createCompanyCodeRequestDto;
            const existingCompanyCode = yield this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
            if (existingCompanyCode) {
                throw new exceptions_1.HttpException(`Company code is already exist for this business entity.`, enums_1.HttpStatus.CONFLICT);
            }
            const businessEntity = yield this.adminApiClient.getBusinessEntityDetailsById(entityId);
            if (!businessEntity) {
                throw new exceptions_1.HttpException('Invalid business entity id.', enums_1.HttpStatus.BAD_REQUEST);
            }
            if (!businessEntity.is_node) {
                throw new exceptions_1.HttpException(`Company code ${code} can only be added at node level.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const { code: entityCode, entity_type: entityType } = businessEntity;
            let company = null;
            let historyRequest = [];
            yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                company = yield this.companyCodeRepository.createCompanyCode({
                    code: code,
                    name,
                    entityId,
                    entityCode,
                    entityType,
                    fusionIntegrationForRequestTypeIds: []
                }, currentContext);
                const defaultValue = {
                    title: 'DEFAULT',
                    companyCodeId: company.id,
                    requestTypeIds: [mappings_1.AFE_REQUEST_TYPE_WITH_ID_MAPPING[enums_1.AFE_REQUEST_TYPE.CAPEX], mappings_1.AFE_REQUEST_TYPE_WITH_ID_MAPPING[enums_1.AFE_REQUEST_TYPE.OPEX]]
                };
                yield this.naturalAccountNumberRepository.createNaturalAccountNumber(Object.assign(Object.assign({}, defaultValue), { number: '********' }), currentContext);
                yield this.analysisCodeRepository.createAnalysisCode(Object.assign(Object.assign({}, defaultValue), { code: '000000' }), currentContext);
                historyRequest.push({
                    created_by: currentContext.user.username,
                    entity_id: entityId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.COMPANY,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                    comments: 'A new company ' + createCompanyCodeRequestDto.name + ' (' + createCompanyCodeRequestDto.code + ') ' + ' has been created.',
                    additional_info: {
                        companyDetail: createCompanyCodeRequestDto
                    }
                });
            }));
            if (historyRequest.length) {
                yield this.historyApiClient.addBulkRequestHistory(historyRequest);
            }
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CompanyCodeResponseDto, company);
        });
    }
    addCostCenter(addCostCenterRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCodeId, code } = addCostCenterRequestDto;
            const isCompanyCodeExists = yield this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
            if (!isCompanyCodeExists) {
                throw new exceptions_1.HttpException('Company does\'t exist or deactivated. ', enums_1.HttpStatus.BAD_REQUEST);
            }
            const isCodeExistsForCompanyCode = yield this.costCenterRepository.isCostCenterExistForCompanyCode(code, companyCodeId);
            if (isCodeExistsForCompanyCode) {
                throw new exceptions_1.HttpException(`Cost center code ${code} already exists in company code.`, enums_1.HttpStatus.CONFLICT);
            }
            this.isDuplicateSectionExist(addCostCenterRequestDto.sectionHead);
            const response = yield this.costCenterRepository.createCostCenter(addCostCenterRequestDto, currentContext);
            yield this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: response.id,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.COST_CENTER,
                action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                comments: 'A new cost center ' + addCostCenterRequestDto.name + ' (' + addCostCenterRequestDto.code + ') ' + ' has been created.',
                additional_info: {
                    costCenterDetail: addCostCenterRequestDto
                }
            });
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CostCenterResponseDto, response);
        });
    }
    isDuplicateSectionExist(sectionHeads) {
        const titles = sectionHeads.map(obj => obj.title);
        const uniqueTitles = new Set(titles);
        if (titles.length !== uniqueTitles.size) {
            throw new exceptions_1.HttpException(`Duplicate section available.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        return false;
    }
    createAnalysisCode(createAnalysisCodeRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCodeId, code } = createAnalysisCodeRequestDto;
            const isCompanyCodeExists = yield this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
            if (!isCompanyCodeExists) {
                throw new exceptions_1.HttpException('Company does\'t exist or deactivated. ', enums_1.HttpStatus.BAD_REQUEST);
            }
            const isCodeExist = yield this.analysisCodeRepository.isAnalysisCodeExistForCompanyCode(code, companyCodeId);
            if (isCodeExist) {
                throw new exceptions_1.HttpException(`Analysis code ${code} already exists for company code.`, enums_1.HttpStatus.CONFLICT);
            }
            const response = yield this.analysisCodeRepository.createAnalysisCode(createAnalysisCodeRequestDto, currentContext);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.AnalysisCodeResponseDto, response);
        });
    }
    createNatualAccountNumber(createNaturalAccountNumberRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCodeId, number } = createNaturalAccountNumberRequestDto;
            const isCompanyCodeExists = yield this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
            if (!isCompanyCodeExists) {
                throw new exceptions_1.HttpException('Company does\'t exist or deactivated. ', enums_1.HttpStatus.BAD_REQUEST);
            }
            const isNumberExist = yield this.naturalAccountNumberRepository.isNaturalAccountNumberExistForCompanyCode(number, companyCodeId);
            if (isNumberExist) {
                throw new exceptions_1.HttpException(`Natural account number ${number} already exists for company code.`, enums_1.HttpStatus.CONFLICT);
            }
            const response = yield this.naturalAccountNumberRepository.createNaturalAccountNumber(createNaturalAccountNumberRequestDto, currentContext);
            return (0, helpers_1.singleObjectToInstance)(dtos_1.NaturalAccountResponseDto, response);
        });
    }
    updateCompanyCodeDetail(updateCompanyCodeRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, code, entityId } = updateCompanyCodeRequestDto;
            const businessEntity = yield this.adminApiClient.getBusinessEntityDetailsById(entityId);
            if (!businessEntity) {
                throw new exceptions_1.HttpException('Invalid business entity id.', enums_1.HttpStatus.BAD_REQUEST);
            }
            if (!businessEntity.is_node) {
                throw new exceptions_1.HttpException(`Company code ${code} can only be added at node level.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const { code: entityCode, entity_type: entityType } = businessEntity;
            yield this.companyCodeRepository.updateCompanyCodeDetailById(id, { code: code, entityId, entityCode, entityType }, currentContext);
            yield this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: entityId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.COMPANY,
                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                comments: 'Company detail has been updated.',
                additional_info: {
                    updatedCompanyDetail: updateCompanyCodeRequestDto
                }
            });
            return { message: 'Company code detail has been updated successfully.' };
        });
    }
    updateCostCenterDetail(updateCostCenterRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, companyCodeId, code } = updateCostCenterRequestDto;
            const costCenterDetails = yield this.costCenterRepository.getCostCenterById(id);
            if (!costCenterDetails) {
                throw new exceptions_1.HttpException(`Invalid cost center id.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (costCenterDetails.code !== code) {
                const isCodeExistsForCompanyCode = yield this.costCenterRepository.isCostCenterExistForCompanyCode(code, companyCodeId);
                if (isCodeExistsForCompanyCode) {
                    throw new exceptions_1.HttpException(`Cost center code ${code} already exists in company code.`, enums_1.HttpStatus.CONFLICT);
                }
            }
            this.isDuplicateSectionExist(updateCostCenterRequestDto.sectionHead);
            yield this.costCenterRepository.updateCostCenterDetailById(id, updateCostCenterRequestDto, currentContext);
            yield this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: id,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.COST_CENTER,
                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                comments: 'Cost center detail has been updated.',
                additional_info: {
                    updatedCostCenterDetail: updateCostCenterRequestDto,
                    currentCostCenterDetail: costCenterDetails
                }
            });
            return { message: 'Cost center detail has been updated successfully.' };
        });
    }
    updateFusionIntegration(UpdateFusionIntegrationRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, requestTypeIds: fusionIntegrationForRequestTypeIds } = UpdateFusionIntegrationRequestDto;
            const companyCodeDetails = yield this.companyCodeRepository.getCompanyCodeById(id);
            if (!companyCodeDetails) {
                throw new exceptions_1.HttpException(`Invalid company code id.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            yield this.companyCodeRepository.updateCompanyCodeDetailById(id, { fusionIntegrationForRequestTypeIds }, currentContext);
            yield this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: companyCodeDetails.entityId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.COMPANY,
                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                comments: 'Fusion Integration has been updated',
                additional_info: {
                    updatedCompanyDetail: UpdateFusionIntegrationRequestDto
                }
            });
            return { message: 'Fusion Integration Updated Successfully.' };
        });
    }
    updateAnalysisCodeDetail(updateAnalysisCodeRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, companyCodeId, code } = updateAnalysisCodeRequestDto;
            const analysisCodeDetails = yield this.analysisCodeRepository.getAnalysisCodeById(id);
            if (!analysisCodeDetails) {
                throw new exceptions_1.HttpException(`Invalid analysis code id.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (analysisCodeDetails.code !== code) {
                const isCodeExist = yield this.analysisCodeRepository.isAnalysisCodeExistForCompanyCode(code, companyCodeId);
                if (isCodeExist) {
                    throw new exceptions_1.HttpException(`Analysis code ${code} already exists for company code.`, enums_1.HttpStatus.CONFLICT);
                }
            }
            yield this.analysisCodeRepository.updateAnalysisCodeDetailById(id, updateAnalysisCodeRequestDto, currentContext);
            return { message: 'Analysis code detail has been updated successfully.' };
        });
    }
    updateNaturalAccountNumber(updateNaturalAccountNumberRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id, companyCodeId, number } = updateNaturalAccountNumberRequestDto;
            const naturalAccNoDetails = yield this.naturalAccountNumberRepository.getNaturalAccountNumberById(id);
            if (!naturalAccNoDetails) {
                throw new exceptions_1.HttpException(`Invalid natural account number id.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (naturalAccNoDetails.number !== number) {
                const isNumberExist = yield this.naturalAccountNumberRepository.isNaturalAccountNumberExistForCompanyCode(number, companyCodeId);
                if (isNumberExist) {
                    throw new exceptions_1.HttpException(`Natural account number ${number} already exists for company code.`, enums_1.HttpStatus.CONFLICT);
                }
            }
            yield this.naturalAccountNumberRepository.updateNaturalAccountNumberDetailById(id, updateNaturalAccountNumberRequestDto, currentContext);
            return { message: 'Natural account number detail has been updated successfully.' };
        });
    }
    deleteCompanyCodeById(companyCodeId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const companyDetail = yield this.companyCodeRepository.getCompanyCodeById(companyCodeId);
            if (companyDetail) {
                const result = yield this.companyCodeRepository.deleteCompanyCodeById(companyCodeId, currentContext);
                if (result) {
                    yield this.historyApiClient.addRequestHistory({
                        created_by: currentContext.user.username,
                        entity_id: companyDetail.entityId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.COMPANY,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                        comments: 'Company has been deleted',
                        additional_info: null
                    });
                    return { message: 'Company code has been deleted successfully.' };
                }
                throw new exceptions_1.HttpException('Unable to delete the company code.', enums_1.HttpStatus.BAD_REQUEST);
            }
            throw new exceptions_1.HttpException('Company not found or already deleted.', enums_1.HttpStatus.NOT_FOUND);
        });
    }
    deleteCostCenterById(costCenterId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.costCenterRepository.deleteCostCenterById(costCenterId, currentContext);
            if (result) {
                yield this.historyApiClient.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: costCenterId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.COST_CENTER,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                    comments: 'Cost center detail has been deleted.',
                    additional_info: null
                });
                return { message: 'Cost center has been deleted successfully.' };
            }
            throw new exceptions_1.HttpException('Unable to delete the cost center.', enums_1.HttpStatus.BAD_REQUEST);
        });
    }
    deleteAnalysisCodeById(analysisCodeId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const analyisCode = yield this.analysisCodeRepository.getAnalysisCodeById(analysisCodeId);
            if (!analyisCode) {
                throw new exceptions_1.HttpException('Analysis code doesn\'t exist', enums_1.HttpStatus.BAD_REQUEST);
            }
            const analyisCodesCountForCompany = yield this.analysisCodeRepository.countAnalysisCodesForCompany(analyisCode.companyCodeId);
            if (analyisCodesCountForCompany === 1) {
                throw new exceptions_1.HttpException('Aleast one analysis code should be associate with the company', enums_1.HttpStatus.BAD_REQUEST);
            }
            const result = yield this.analysisCodeRepository.deleteAnalysisCodeById(analysisCodeId, currentContext);
            if (result) {
                return { message: 'Analysis code has been deleted successfully.' };
            }
            throw new exceptions_1.HttpException('Unable to delete the analysis code.', enums_1.HttpStatus.BAD_REQUEST);
        });
    }
    deleteNaturalAccountNumberById(naturalAccountNoId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const naturalAccountNo = yield this.naturalAccountNumberRepository.getNaturalAccountNumberById(naturalAccountNoId);
            if (!naturalAccountNo) {
                throw new exceptions_1.HttpException('Natural account number doesn\'t exist', enums_1.HttpStatus.BAD_REQUEST);
            }
            const naturalAccountNumbersCountForCompany = yield this.naturalAccountNumberRepository.countNaturalAccountNumbersForCompany(naturalAccountNo.companyCodeId);
            if (naturalAccountNumbersCountForCompany === 1) {
                throw new exceptions_1.HttpException('Aleast one natural account number should be associate with the company', enums_1.HttpStatus.BAD_REQUEST);
            }
            const result = yield this.naturalAccountNumberRepository.deleteNaturalAccountNumberById(naturalAccountNoId, currentContext);
            if (result) {
                return { message: 'Natural account number has been deleted successfully.' };
            }
            throw new exceptions_1.HttpException('Unable to delete the natural account number.', enums_1.HttpStatus.BAD_REQUEST);
        });
    }
    getCompanyCodesListByEntityId(entityId, limit, page) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.companyCodeRepository.getCompanyCodesListByEntityId(entityId, limit, page);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.CompanyCodeResponseDto, rows);
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    getCostCentersByCompanyCodeId(companyCodeId, limit, page) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.costCenterRepository.getCostCentersListByCompanyCodeId(companyCodeId, limit, page);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.CostCenterResponseDto, rows);
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    getAnalsysisCodesByCompanyCodeId(companyCodeId, limit, page) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.analysisCodeRepository.getAnalysisCodesListByCompanyCodeId(companyCodeId, limit, page);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.AnalysisCodeResponseDto, rows);
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    getNaturalAccountNumbersByCompanyCodeId(companyCodeId, limit, page) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.naturalAccountNumberRepository.getNaturalAccountNumbersListByCompanyCodeId(companyCodeId, limit, page);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.NaturalAccountResponseDto, rows);
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    getCapexNaturalAccountNumbersByCompanyCodeId(companyCodeId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield this.naturalAccountNumberRepository.getNaturalAccountNumbersByRequestType(1, companyCodeId);
        });
    }
    toggleActiveStateOfCompany(toggleActiveStateCompanyCodeRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCodeId, entityId, active } = toggleActiveStateCompanyCodeRequestDto;
            const isCompanyExist = yield this.companyCodeRepository.isCompanyCodeExistByIdAndEntityId(companyCodeId, entityId);
            if (isCompanyExist) {
                throw new exceptions_1.HttpException('Company code doesn\'t exist.', enums_1.HttpStatus.NOT_FOUND);
            }
            if (active) {
                yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                    yield this.companyCodeRepository.markEntityCompanyCodeInactiveExceptSpecificId(companyCodeId, entityId, currentContext);
                    yield this.companyCodeRepository.markCompanyCodeActive(companyCodeId, currentContext);
                }));
            }
            else {
                yield this.companyCodeRepository.markCompanyCodeInactiveById(companyCodeId, currentContext);
            }
            yield this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: entityId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.COMPANY,
                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                comments: `Company has been mark ${active ? 'active' : 'inactive'}.`,
                additional_info: {
                    companyDetail: toggleActiveStateCompanyCodeRequestDto
                }
            });
            return { message: `Company code has been mark ${active ? 'active' : 'inactive'} successfully.` };
        });
    }
    getEntityActiveCompanyCodeDetails(entityId, throwError = true) {
        return __awaiter(this, void 0, void 0, function* () {
            const companyCode = yield this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
            if (!companyCode && throwError) {
                throw new exceptions_1.HttpException('Company code doesn\'t exist.', enums_1.HttpStatus.NOT_FOUND);
            }
            return (0, helpers_1.singleObjectToInstance)(dtos_1.CompanyCodeResponseDto, companyCode);
        });
    }
    deactivateCompanyCode(companyCodeId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const companyCode = yield this.companyCodeRepository.getCompanyCodeById(companyCodeId);
            if (!companyCode) {
                throw new exceptions_1.HttpException('Company code doesn\'t exist or deactivated', enums_1.HttpStatus.NOT_FOUND);
            }
            yield this.companyCodeRepository.markCompanyCodeInactiveById(companyCodeId, currentContext);
            yield this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: companyCode.entityId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.COMPANY,
                action_performed: enums_1.HISTORY_ACTION_TYPE.DEACTIVATED,
                comments: 'Company ' + companyCode.name + ' (' + companyCode.code + ') has been deactivated.',
                additional_info: {
                    companyDetail: companyCode
                }
            });
            return { message: `Company code has been deactivated successfully.` };
        });
    }
    importAnalysisCode(importDataRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCodeId, bufferData } = importDataRequestDto;
            const isCompanyCodeExists = yield this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
            if (!isCompanyCodeExists) {
                throw new exceptions_1.HttpException('Company does\'t exist or deactivated. ', enums_1.HttpStatus.BAD_REQUEST);
            }
            const arrayBufferObj = new ArrayBuffer(Object.keys(bufferData).length);
            const arrayBuffer = new Uint8Array(arrayBufferObj);
            for (let i = 0; i < Object.keys(bufferData).length; ++i) {
                arrayBuffer[i] = bufferData[i];
            }
            const analysisCodeList = yield this.excelSheetService.readAnalysisCodeSheet(arrayBuffer);
            let deletedList = [];
            let newList = [];
            let addedCodes = [];
            let updateList = [];
            for (let i = 0; i < analysisCodeList.length; i++) {
                const analysisCodeDetail = analysisCodeList[i];
                const prevAnalysisCodeDetail = yield this.analysisCodeRepository.getAnalysisCodeForCompanyCode(analysisCodeDetail.code.toString(), companyCodeId);
                if (!prevAnalysisCodeDetail && analysisCodeDetail.isDeleted) {
                    throw new exceptions_1.HttpException('Analysis code ' + analysisCodeDetail.code + ' is not available to delete. Issue at [B' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                }
                if (addedCodes.includes(analysisCodeDetail.code)) {
                    throw new exceptions_1.HttpException('Analysis code ' + analysisCodeDetail.code + ' already added, duplicate code at [B' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                }
                if (prevAnalysisCodeDetail && analysisCodeDetail.isDeleted) {
                    deletedList.push(analysisCodeDetail.code.toString());
                }
                else {
                    const detail = {
                        title: analysisCodeDetail.title,
                        code: analysisCodeDetail.code.toString(),
                        companyCodeId,
                        requestTypeIds: analysisCodeDetail.requestTypes ? mappings_1.REQUEST_TYPE[analysisCodeDetail.requestTypes.toUpperCase()] : []
                    };
                    if (prevAnalysisCodeDetail) {
                        let updateRequired = true;
                        if ((detail.title === prevAnalysisCodeDetail.title)) {
                            if ((detail.requestTypeIds.length === prevAnalysisCodeDetail.requestTypeIds.length) &&
                                detail.requestTypeIds.every((val, index) => val === prevAnalysisCodeDetail.requestTypeIds[index])) {
                                updateRequired = false;
                            }
                        }
                        if (updateRequired) {
                            updateList.push(detail);
                        }
                    }
                    else {
                        newList.push(detail);
                    }
                }
                addedCodes.push(analysisCodeDetail.code);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                if (newList.length) {
                    yield this.analysisCodeRepository.addBulkAnalysisCode(newList, currentContext);
                }
                if (deletedList.length) {
                    yield this.analysisCodeRepository.bulkDeleteAnalysisCode(deletedList, companyCodeId, currentContext);
                }
                if (updateList.length) {
                    for (let i = 0; i < updateList.length; i++) {
                        yield this.analysisCodeRepository.updateAnalysisCodeDetailByCode(updateList[i].code, companyCodeId, {
                            title: updateList[i].title,
                            requestTypeIds: updateList[i].requestTypeIds
                        }, currentContext);
                    }
                }
                return { message: `Data has been imported successfully.` };
            }));
        });
    }
    importNaturalAccount(importDataRequestDto, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCodeId, bufferData } = importDataRequestDto;
            const isCompanyCodeExists = yield this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
            if (!isCompanyCodeExists) {
                throw new exceptions_1.HttpException('Company does\'t exist or deactivated. ', enums_1.HttpStatus.BAD_REQUEST);
            }
            const arrayBufferObj = new ArrayBuffer(Object.keys(bufferData).length);
            const arrayBuffer = new Uint8Array(arrayBufferObj);
            for (let i = 0; i < Object.keys(bufferData).length; ++i) {
                arrayBuffer[i] = bufferData[i];
            }
            const naturalAccountList = yield this.excelSheetService.readNaturalAccountSheet(arrayBuffer);
            let deletedList = [];
            let newList = [];
            let addedNumbers = [];
            let updateList = [];
            for (let i = 0; i < naturalAccountList.length; i++) {
                const naturalAccountDetail = naturalAccountList[i];
                const prevNaturalAccountDetail = yield this.naturalAccountNumberRepository.getNaturalAccountNumberForCompanyCode(naturalAccountDetail.number.toString(), companyCodeId);
                if (!prevNaturalAccountDetail && naturalAccountDetail.isDeleted) {
                    throw new exceptions_1.HttpException('Natural account number ' + naturalAccountDetail.number + ' is not available to delete. Issue at [B' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                }
                if (addedNumbers.includes(naturalAccountDetail.number)) {
                    throw new exceptions_1.HttpException('Natural account number ' + naturalAccountDetail.number + ' already added, duplicate number at [B' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                }
                if (prevNaturalAccountDetail && naturalAccountDetail.isDeleted) {
                    deletedList.push(naturalAccountDetail.number.toString());
                }
                else {
                    const detail = {
                        title: naturalAccountDetail.title,
                        number: naturalAccountDetail.number.toString(),
                        companyCodeId,
                        requestTypeIds: naturalAccountDetail.requestTypes ? mappings_1.REQUEST_TYPE[naturalAccountDetail.requestTypes.toUpperCase()] : []
                    };
                    if (prevNaturalAccountDetail) {
                        let updateRequired = true;
                        if ((detail.title === prevNaturalAccountDetail.title)) {
                            if ((detail.requestTypeIds.length === prevNaturalAccountDetail.requestTypeIds.length) &&
                                detail.requestTypeIds.every((val, index) => val === prevNaturalAccountDetail.requestTypeIds[index])) {
                                updateRequired = false;
                            }
                        }
                        if (updateRequired) {
                            updateList.push(detail);
                        }
                    }
                    else {
                        newList.push(detail);
                    }
                }
                addedNumbers.push(naturalAccountDetail.number);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                if (newList.length) {
                    yield this.naturalAccountNumberRepository.addBulkNaturalAccountNumbers(newList, currentContext);
                }
                if (deletedList.length) {
                    yield this.naturalAccountNumberRepository.bulkDeleteNaturalAccountNumbers(deletedList, companyCodeId, currentContext);
                }
                if (updateList.length) {
                    for (let i = 0; i < updateList.length; i++) {
                        yield this.naturalAccountNumberRepository.updateNaturalAccountNumberDetailByCode(updateList[i].number, companyCodeId, {
                            title: updateList[i].title,
                            requestTypeIds: updateList[i].requestTypeIds
                        }, currentContext);
                    }
                }
                return { message: `Data has been imported successfully.` };
            }));
        });
    }
    importCostCenter(importDataRequestDto, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCodeId, bufferData } = importDataRequestDto;
            const isCompanyCodeExists = yield this.companyCodeRepository.isCompanyCodeExistById(companyCodeId);
            if (!isCompanyCodeExists) {
                throw new exceptions_1.HttpException('Company does\'t exist or deactivated. ', enums_1.HttpStatus.BAD_REQUEST);
            }
            const arrayBufferObj = new ArrayBuffer(Object.keys(bufferData).length);
            const arrayBuffer = new Uint8Array(arrayBufferObj);
            for (let i = 0; i < Object.keys(bufferData).length; ++i) {
                arrayBuffer[i] = bufferData[i];
            }
            const costCenterList = yield this.excelSheetService.readCostCenterSheet(arrayBuffer);
            let deletedList = [];
            let newList = [];
            let addedCodes = [];
            let updateList = [];
            let historyRequest = [];
            for (let i = 0; i < costCenterList.length; i++) {
                const costCenterDetail = Object.assign(Object.assign({}, costCenterList[i]), { sectionHead: costCenterList[i].sections });
                const prevCostCenterDetail = yield this.costCenterRepository.gerCostCenterForCompanyCode(costCenterDetail.code.toString(), companyCodeId);
                if (!prevCostCenterDetail && costCenterDetail.isDeleted) {
                    throw new exceptions_1.HttpException('Cose center code ' + costCenterDetail.code + ' is not available to delete. Issue at [B' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                }
                if (addedCodes.includes(costCenterDetail.code)) {
                    throw new exceptions_1.HttpException('Cose center code ' + costCenterDetail.code + ' already added, duplicate code at [B' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                }
                if (prevCostCenterDetail && costCenterDetail.isDeleted) {
                    deletedList.push(costCenterDetail.code.toString());
                    historyRequest.push({
                        created_by: currentContext.user.username,
                        entity_id: costCenterDetail.id,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.COST_CENTER,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.DELETE,
                        comments: 'Cost center detail has been deleted.',
                        additional_info: null
                    });
                }
                else {
                    const detail = {
                        name: costCenterDetail.name,
                        code: costCenterDetail.code.toString(),
                        operating: costCenterDetail.operating,
                        departmentHead: (0, lodash_1.isObject)(costCenterDetail.departmentHead) ? costCenterDetail.departmentHead.text : costCenterDetail.departmentHead,
                        sectionHead: costCenterDetail.sectionHead ? ((0, lodash_1.isObject)(costCenterDetail.sectionHead) ? costCenterDetail.sectionHead.text : costCenterDetail.sectionHead) : '',
                        companyCodeId,
                    };
                    if (prevCostCenterDetail) {
                        let updateDetail = null;
                        if ((detail.name !== prevCostCenterDetail.name)) {
                            updateDetail = Object.assign(Object.assign({}, updateDetail), { name: detail.name });
                        }
                        if ((detail.operating !== prevCostCenterDetail.operating)) {
                            updateDetail = Object.assign(Object.assign({}, updateDetail), { operating: detail.operating });
                        }
                        if ((detail.departmentHead.toLowerCase() !== prevCostCenterDetail.departmentHead.mail.toLowerCase()) && (detail.departmentHead.toLowerCase() !== prevCostCenterDetail.departmentHead.userPrincipalName.toLowerCase())) {
                            const userDetail = yield this.mSGraphApiClient.getUserDetails(detail.departmentHead);
                            if (!userDetail) {
                                throw new exceptions_1.HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [D' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                            }
                            updateDetail = Object.assign(Object.assign({}, updateDetail), { departmentHead: Object.assign(Object.assign({}, userDetail), { loginId: userDetail.userType === enums_1.AD_USER_TYPE.GUEST ? userDetail === null || userDetail === void 0 ? void 0 : userDetail.mail : userDetail.userPrincipalName }) });
                        }
                        const prevSectionHead = ((_a = prevCostCenterDetail === null || prevCostCenterDetail === void 0 ? void 0 : prevCostCenterDetail.sectionHead) === null || _a === void 0 ? void 0 : _a.length) ? prevCostCenterDetail === null || prevCostCenterDetail === void 0 ? void 0 : prevCostCenterDetail.sectionHead.map((entry) => `${entry.title}#${(entry.user.userType === enums_1.AD_USER_TYPE.GUEST) ? entry.user.mail.toLowerCase() : entry.user.userPrincipalName.toLowerCase()}`).join(";") : '';
                        if (detail.sectionHead.toLowerCase() !== prevSectionHead.toLowerCase()) {
                            const colText = detail.sectionHead;
                            const sectionText = detail.sectionHead;
                            const sections = sectionText ? ((0, lodash_1.isObject)(sectionText) ? colText.text.toString().split(';') : colText.toString().split(';')) : '';
                            let sectionList = [];
                            if (detail.sectionHead && sections.length) {
                                for (let si = 0; si <= sections.length; si++) {
                                    const sectionDetail = sections[si] ? sections[si].split('#') : '';
                                    if (sections[si] && (0, lodash_1.isArray)(sectionDetail)) {
                                        const userDetail = yield this.mSGraphApiClient.getUserDetails(sectionDetail[1]);
                                        if (!userDetail) {
                                            throw new exceptions_1.HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [E' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                                        }
                                        sectionList.push({
                                            title: sectionDetail[0],
                                            user: userDetail
                                        });
                                    }
                                }
                            }
                            updateDetail = Object.assign(Object.assign({}, updateDetail), { sectionHead: sectionList });
                        }
                        if (updateDetail) {
                            updateList.push(Object.assign(Object.assign({}, updateDetail), { code: detail.code, companyCodeId }));
                            historyRequest.push({
                                created_by: currentContext.user.username,
                                entity_id: prevCostCenterDetail.id,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.COST_CENTER,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.UPDATE,
                                comments: 'Cost center detail has been updated.',
                                additional_info: {
                                    updatedCostCenterDetail: Object.assign(Object.assign({}, updateDetail), { code: detail.code, companyCodeId }),
                                    currentCostCenterDetail: prevCostCenterDetail
                                }
                            });
                        }
                    }
                    else {
                        if (0) {
                            const userDetail = yield this.mSGraphApiClient.getUserDetails(detail.departmentHead);
                            if (!userDetail) {
                                throw new exceptions_1.HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [D' + (i + 2) + '] & [E' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                            }
                            detail.departmentHead = Object.assign(Object.assign({}, userDetail), { loginId: userDetail.userType === enums_1.AD_USER_TYPE.GUEST ? userDetail === null || userDetail === void 0 ? void 0 : userDetail.mail : userDetail.userPrincipalName });
                        }
                        else {
                            const departmentHeadUser = yield this.mSGraphApiClient.getUserDetails(detail.departmentHead);
                            if (!departmentHeadUser) {
                                throw new exceptions_1.HttpException('Invalid email id ' + detail.departmentHead + '. Issue at [D' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                            }
                            const colText = detail.sectionHead;
                            const sectionText = detail.sectionHead;
                            const sections = sectionText ? ((0, lodash_1.isObject)(sectionText) ? colText.text.toString().split(';') : colText.toString().split(';')) : '';
                            let sectionList = [];
                            if (detail.sectionHead && sections.length) {
                                for (let si = 0; si <= sections.length; si++) {
                                    const sectionDetail = sections[si] ? sections[si].split('#') : '';
                                    if (sections[si] && (0, lodash_1.isArray)(sectionDetail)) {
                                        const userDetail = yield this.mSGraphApiClient.getUserDetails(sectionDetail[1]);
                                        if (!userDetail) {
                                            throw new exceptions_1.HttpException('Invalid email id ' + sectionDetail[1] + '. Issue at [E' + (i + 2) + '].', enums_1.HttpStatus.BAD_REQUEST);
                                        }
                                        sectionList.push({
                                            title: sectionDetail[0],
                                            user: userDetail
                                        });
                                    }
                                }
                            }
                            detail.departmentHead = Object.assign(Object.assign({}, departmentHeadUser), { loginId: departmentHeadUser.userType === enums_1.AD_USER_TYPE.GUEST ? departmentHeadUser === null || departmentHeadUser === void 0 ? void 0 : departmentHeadUser.mail : departmentHeadUser.userPrincipalName });
                            detail.sectionHead = sectionList;
                        }
                        newList.push(detail);
                    }
                }
                addedCodes.push(costCenterDetail.code);
            }
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                if (newList.length) {
                    const addedCostCenters = yield this.costCenterRepository.addBulkCostCenters(newList, currentContext);
                    addedCostCenters.forEach((addedCostCenter) => {
                        historyRequest.push({
                            created_by: currentContext.user.username,
                            entity_id: addedCostCenter.id,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.COST_CENTER,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.ADD,
                            comments: 'A new cost center ' + addedCostCenter.name + ' (' + addedCostCenter.code + ') ' + ' has been created.',
                            additional_info: {
                                costCenterDetail: addedCostCenter
                            }
                        });
                    });
                }
                if (deletedList.length) {
                    yield this.costCenterRepository.bulkDeleteCostCenters(deletedList, companyCodeId, currentContext);
                }
                if (updateList.length) {
                    for (let ui = 0; ui < updateList.length; ui++) {
                        const _b = updateList[ui], { code, companyCodeId } = _b, updateDetail = __rest(_b, ["code", "companyCodeId"]);
                        yield this.costCenterRepository.updateCostCenterDetailByCode(code, companyCodeId, updateDetail, currentContext);
                    }
                }
                if (historyRequest.length) {
                    yield this.historyApiClient.addBulkRequestHistory(historyRequest);
                }
                return { message: `Data has been imported successfully.` };
            }));
        });
    }
    getCompanyHistory(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.historyApiClient.getRequestHistory(entityId, enums_1.HISTORY_ENTITY_TYPE.COMPANY);
            return result.map(d => (0, class_transformer_1.instanceToPlain)(new get_history_response_dto_1.GetHistoryResponseDTO(d)));
        });
    }
    getCostCenterHistory(costCenterId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.historyApiClient.getRequestHistory(costCenterId, enums_1.HISTORY_ENTITY_TYPE.COST_CENTER);
            return result.map(d => (0, class_transformer_1.instanceToPlain)(new get_history_response_dto_1.GetHistoryResponseDTO(d)));
        });
    }
};
FinanceAdminService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.NaturalAccountNumberRepository,
        repositories_1.CostCenterRepository,
        repositories_1.CompanyCodeRepository,
        repositories_1.AnalysisCodeRepository,
        clients_1.AdminApiClient,
        helpers_1.DatabaseHelper,
        services_1.ExcelSheetService,
        clients_1.MSGraphApiClient,
        clients_1.HistoryApiClient])
], FinanceAdminService);
exports.FinanceAdminService = FinanceAdminService;
//# sourceMappingURL=finance-admin.service.js.map