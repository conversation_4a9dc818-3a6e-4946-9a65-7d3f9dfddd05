"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OneAppService = void 0;
const common_1 = require("@nestjs/common");
const lodash_1 = require("lodash");
const repositories_1 = require("../../afe-proposal/repositories");
const config_service_1 = require("../../config/config.service");
const repositories_2 = require("../../finance/repositories");
const clients_1 = require("../../shared/clients");
const enums_1 = require("../../shared/enums");
const associated_type_enum_1 = require("../../shared/enums/associated-type.enum");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const validators_1 = require("../../shared/validators");
const services_1 = require("../../task/services");
const constants_1 = require("../constants");
const dtos_1 = require("../dtos");
let OneAppService = class OneAppService {
    constructor(afeProposalRepository, afeProposalAmountSplitRepository, taskApiClient, mSGraphApiClient, attachmentService, configService, adminApiClient, afeProposalValidator, costCenterRepository, afeProposalApproverRepository, taskService, companyCodeRepository) {
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalAmountSplitRepository = afeProposalAmountSplitRepository;
        this.taskApiClient = taskApiClient;
        this.mSGraphApiClient = mSGraphApiClient;
        this.attachmentService = attachmentService;
        this.configService = configService;
        this.adminApiClient = adminApiClient;
        this.afeProposalValidator = afeProposalValidator;
        this.costCenterRepository = costCenterRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
        this.taskService = taskService;
        this.companyCodeRepository = companyCodeRepository;
    }
    getAfeTasks(userDetail) {
        return __awaiter(this, void 0, void 0, function* () {
            let emailId = yield this.getUserEmail(userDetail.email);
            const taskList = yield this.taskApiClient.getAllPendingUserTasks(emailId);
            if (taskList.length) {
                let newTaskList = [];
                taskList.forEach(taskDetail => {
                    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
                    if (((_a = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _a === void 0 ? void 0 : _a.proposal_entity) &&
                        !((_c = (_b = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.task_rel_url) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === null || _c === void 0 ? void 0 : _c.includes('resubmit'))) {
                        const taskDetailResponse = {
                            AFEInitiator: ((_d = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _d === void 0 ? void 0 : _d.proposal_created_by) || '',
                            AFELink: taskDetail.task_base_url +
                                (0, helpers_1.replaceUrlVariable)(taskDetail.task_rel_url, { taskId: (0, lodash_1.toNumber)(taskDetail.id) }),
                            AFEType: ((_e = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _e === void 0 ? void 0 : _e.proposal_request_type) || '',
                            ActionType: ((_f = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _f === void 0 ? void 0 : _f.approval_type)
                                ? taskDetail.additional_info.approval_type
                                : 'APPROVAL',
                            ContentType: constants_1.ONE_APP_DATA.TASK_CONTENT_TYPE,
                            Created: taskDetail.created_on,
                            Department: ((_g = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _g === void 0 ? void 0 : _g.proposal_entity) || '',
                            ID: (0, lodash_1.toNumber)(taskDetail.id),
                            SubmissionDate: ((_h = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _h === void 0 ? void 0 : _h.proposal_submitted_on) || '',
                            TechID: ((_j = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _j === void 0 ? void 0 : _j.proposal_id)
                                ? (0, lodash_1.toNumber)(taskDetail.additional_info.proposal_id)
                                : null,
                            Title: ((_k = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _k === void 0 ? void 0 : _k.proposal_project_name) || '',
                        };
                        newTaskList.push(taskDetailResponse);
                    }
                });
                return (0, helpers_1.multiObjectToInstance)(dtos_1.TaskListResponseDTO, newTaskList);
            }
            return [];
        });
    }
    getAfeDetail(afeDetailRequest) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        return __awaiter(this, void 0, void 0, function* () {
            let emailId = yield this.getUserEmail(afeDetailRequest.useremail);
            const afeDetail = yield this.afeProposalRepository.getAfeProposalWithSplitById((0, lodash_1.toNumber)(afeDetailRequest.afeid));
            if (afeDetail) {
                const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(afeDetail, {
                    user: {
                        username: emailId,
                        unique_name: emailId,
                    },
                }, ((afeDetailRequest === null || afeDetailRequest === void 0 ? void 0 : afeDetailRequest.taskid) || null));
                if (!hasUserPermission) {
                    throw new exceptions_1.HttpException(`You are not authorized to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                }
                const attachmentList = yield this.attachmentService.getAllAttachments((0, lodash_1.toNumber)(afeDetail.id), enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT);
                let attachmentListPayload = [];
                if (attachmentList.length) {
                    const config = this.configService.getAppConfig();
                    attachmentList.forEach(attachmentDetail => {
                        attachmentListPayload.push({
                            AfeID: attachmentDetail.meta_data_1,
                            AppType: constants_1.ONE_APP_DATA.TASK_CONTENT_TYPE,
                            AttachmentID: (0, lodash_1.toNumber)(attachmentDetail.id),
                            AttachmentPath: config.webClientConfig.apiBaseUrl + '/attachment/content/' + attachmentDetail.file_id,
                            ContentType: attachmentDetail.attachment_content_type,
                            FileName: attachmentDetail.attachment_name,
                        });
                    });
                }
                const entityHierarchy = yield this.adminApiClient.getParentsOfEntity(afeDetail.entityId);
                let groupEntity = null;
                let divisionalEntity = null;
                let regionEntity = null;
                let businessUnitEntity = null;
                if (entityHierarchy && entityHierarchy.length) {
                    groupEntity = entityHierarchy.find(entityDetail => {
                        if (!entityDetail.parent_id) {
                            return entityDetail;
                        }
                    });
                    if (groupEntity) {
                        divisionalEntity = entityHierarchy.find(entityDetail => {
                            if ((0, lodash_1.toNumber)(entityDetail.parent_id) === (0, lodash_1.toNumber)(groupEntity.id)) {
                                return entityDetail;
                            }
                        });
                    }
                    if (divisionalEntity) {
                        regionEntity = entityHierarchy.find(entityDetail => {
                            if ((0, lodash_1.toNumber)(entityDetail.parent_id) === (0, lodash_1.toNumber)(divisionalEntity.id)) {
                                return entityDetail;
                            }
                        });
                    }
                    if (regionEntity) {
                        businessUnitEntity = entityHierarchy.find(entityDetail => {
                            if ((0, lodash_1.toNumber)(entityDetail.parent_id) === (0, lodash_1.toNumber)(regionEntity.id)) {
                                return entityDetail;
                            }
                        });
                    }
                }
                const proposalAmountSplits = yield this.afeProposalAmountSplitRepository.getAmountSplitsByProposalIdAndTypes(afeDetail.id, [
                    enums_1.AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT,
                    enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT,
                    enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT,
                ], ['objectTitle', 'type', 'currency', 'amount', 'objectId']);
                let budgetRefNos = '';
                let departments = '';
                let projectSplits = [];
                let costCenterIds = [];
                proposalAmountSplits.forEach(proposalAmountSplit => {
                    if (proposalAmountSplit.type === enums_1.AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT) {
                        budgetRefNos = budgetRefNos + (budgetRefNos ? ',' : '') + proposalAmountSplit.objectTitle;
                    }
                    if (proposalAmountSplit.type === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT) {
                        departments = departments + (departments ? ',' : '') + proposalAmountSplit.objectTitle;
                        costCenterIds.push(proposalAmountSplit.objectId);
                    }
                    if (proposalAmountSplit.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT) {
                        projectSplits.push({
                            AmountInUSD: proposalAmountSplit.amount,
                            Project: proposalAmountSplit.objectTitle,
                        });
                    }
                });
                let costCenters = '';
                if (costCenterIds.length) {
                    const costCenterList = yield this.costCenterRepository.getCostCenterByIds(costCenterIds);
                    costCenterList.forEach(costCenter => {
                        costCenters = costCenters + (costCenters ? ',' : '') + costCenter.code;
                    });
                }
                let expenditureAmount = (afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.totalAmount) ? (0, lodash_1.toNumber)(afeDetail.totalAmount) : null;
                if (afeDetail.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL) {
                    const prevSuppDetail = yield this.afeProposalRepository.getPreviousSupplementalAfeByIdAndVersion(afeDetail.parentAfeId, afeDetail.version);
                    if (prevSuppDetail) {
                        expenditureAmount =
                            (0, lodash_1.toNumber)(afeDetail.totalAmount) - (0, lodash_1.toNumber)(prevSuppDetail.totalAmount);
                    }
                }
                let responsePayload = {
                    ID: (0, lodash_1.toNumber)(afeDetail.id),
                    AFEHistory: null,
                    AFEInitiator: afeDetail.createdBy,
                    AFENature: ((_b = (_a = afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.data) === null || _a === void 0 ? void 0 : _a.natureType) === null || _b === void 0 ? void 0 : _b.title) || null,
                    AFEStatus: (afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.userStatus) || null,
                    AFEType: ((_c = afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.data) === null || _c === void 0 ? void 0 : _c.type) || null,
                    Approved_Date: afeDetail.internalStatus === enums_1.AFE_PROPOSAL_STATUS.APPROVED ? afeDetail.updatedOn : null,
                    BudgetType: afeDetail.budgetType ? enums_1.BUDGET_TYPE_TITLE[afeDetail.budgetType] : null,
                    IsSupplementary: afeDetail.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL,
                    ParentAFEId: (afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.parentAfeId) ? (0, lodash_1.toNumber)(afeDetail.parentAfeId) : null,
                    ParentProjectReferenceNumber: afeDetail.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL
                        ? afeDetail.supplementalData.parentAfeNo
                        : null,
                    ProjectJustification: ((_e = (_d = afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.data) === null || _d === void 0 ? void 0 : _d.projectDetails) === null || _e === void 0 ? void 0 : _e.projectJustification) || null,
                    ProjectLeader: afeDetail.data.projectDetails.projectLeader.userType === enums_1.AD_USER_TYPE.GUEST
                        ? afeDetail.data.projectDetails.projectLeader.mail.toLowerCase()
                        : afeDetail.data.projectDetails.projectLeader.userPrincipalName.toLowerCase(),
                    ProjectLeaderContactNumber: ((_g = (_f = afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.data) === null || _f === void 0 ? void 0 : _f.projectDetails) === null || _g === void 0 ? void 0 : _g.projectLeaderNumber) || null,
                    ProjectName: (afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.name) || '',
                    ProjectReferenceNumber: (afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.projectReferenceNumber) || '',
                    Submission_Date: (afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.createdOn) || null,
                    UnbudgetedReason: ((_h = afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.data) === null || _h === void 0 ? void 0 : _h.budgetTypeJustification) || null,
                    Attachments: attachmentListPayload,
                    ExpenditureAmountInUSD: expenditureAmount,
                    TotalExpenditureAmountinUSD: (afeDetail === null || afeDetail === void 0 ? void 0 : afeDetail.totalAmount)
                        ? (0, lodash_1.toNumber)(afeDetail.totalAmount)
                        : null,
                    SupplementaryAFEs: '',
                    AFERegionType: businessUnitEntity.full_name,
                    BusinessDivision: divisionalEntity.full_name,
                    RegionName: regionEntity.full_name,
                    TerminalName: businessUnitEntity.full_name,
                    DepartmentName: departments,
                    CostCenter: costCenters,
                    BudgetReferenceNumber: budgetRefNos,
                    ExpenseSummary: projectSplits,
                };
                return (0, helpers_1.singleObjectToInstance)(dtos_1.GetAfeDetailResponseDTO, responsePayload);
            }
            throw new exceptions_1.HttpException('AFE detail is unavailable.', enums_1.HttpStatus.NOT_FOUND);
        });
    }
    getAfeAttachment(getAfeAttachmentRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const afeDetail = yield this.afeProposalRepository.getAfeProposalWithSplitById((0, lodash_1.toNumber)(getAfeAttachmentRequestDto.afeid));
            if (afeDetail) {
                let emailId = yield this.getUserEmail(getAfeAttachmentRequestDto.email);
                const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(afeDetail, {
                    user: {
                        username: emailId,
                        unique_name: emailId,
                    },
                }, ((getAfeAttachmentRequestDto === null || getAfeAttachmentRequestDto === void 0 ? void 0 : getAfeAttachmentRequestDto.taskid) || null));
                if (!hasUserPermission) {
                    throw new exceptions_1.HttpException(`You are not authorized to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                }
                const attachmentList = yield this.attachmentService.getAllAttachments((0, lodash_1.toNumber)(getAfeAttachmentRequestDto.afeid), enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT);
                if (attachmentList && attachmentList.length) {
                    const attachmentRequestedData = attachmentList.find(attachment => {
                        return (0, lodash_1.toNumber)(attachment.id) === (0, lodash_1.toNumber)(getAfeAttachmentRequestDto.attachmentid);
                    });
                    if (attachmentRequestedData) {
                        const attachmentContent = yield this.attachmentService.getContentByFileId(attachmentRequestedData.file_id);
                        if (attachmentContent) {
                            return {
                                AttachmentID: (0, lodash_1.toNumber)(attachmentContent.id),
                                FileName: attachmentContent.attachment_name,
                                FileType: attachmentContent.attachment_content_type,
                                Length: (0, lodash_1.toNumber)(attachmentContent.meta_data_3),
                                FileByteStream: Buffer.from(attachmentContent.contents.data).toString('base64'),
                            };
                        }
                    }
                }
                throw new exceptions_1.HttpException('AFE Attachment is unavailable.', enums_1.HttpStatus.NOT_FOUND);
            }
            throw new exceptions_1.HttpException('Invalid AFE.', enums_1.HttpStatus.NOT_FOUND);
        });
    }
    getAfeHistory(getAfeHistoryDto) {
        return __awaiter(this, void 0, void 0, function* () {
            let emailId = yield this.getUserEmail(getAfeHistoryDto.useremail);
            const afeDetail = yield this.afeProposalRepository.getAfeProposalWithSplitById((0, lodash_1.toNumber)(getAfeHistoryDto.afeid));
            if (afeDetail) {
                const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(afeDetail, {
                    user: {
                        username: emailId,
                        unique_name: emailId,
                    },
                }, ((getAfeHistoryDto === null || getAfeHistoryDto === void 0 ? void 0 : getAfeHistoryDto.taskid) || null));
                if (!hasUserPermission) {
                    throw new exceptions_1.HttpException(`You are not authorized to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                }
                const afeApprovers = yield this.afeProposalApproverRepository.getApproversListByProposalId(afeDetail.id);
                let approverHistory = [];
                afeApprovers.forEach(approver => {
                    var _a, _b, _c, _d, _e, _f;
                    let userNames = '';
                    if (approver === null || approver === void 0 ? void 0 : approver.userDetail) {
                        userNames =
                            ((_b = (_a = approver.userDetail) === null || _a === void 0 ? void 0 : _a.originalApprover) === null || _b === void 0 ? void 0 : _b.firstName) +
                                ' ' +
                                ((_d = (_c = approver.userDetail) === null || _c === void 0 ? void 0 : _c.originalApprover) === null || _d === void 0 ? void 0 : _d.lastName);
                    }
                    else if ((_f = (_e = approver === null || approver === void 0 ? void 0 : approver.otherInfo) === null || _e === void 0 ? void 0 : _e.usersDetail) === null || _f === void 0 ? void 0 : _f.length) {
                        approver.otherInfo.usersDetail.forEach(usersDetail => {
                            userNames =
                                userNames +
                                    (userNames ? ',' : '') +
                                    (usersDetail.firstName + ' ' + usersDetail.lastName);
                        });
                    }
                    approverHistory.push({
                        Action: enums_1.APPROVER_STATUS_DISPLAY_TITLE[approver.actionStatus],
                        AssignedDate: approver.createdOn,
                        Comments: approver.comment,
                        CompletionDate: approver.actionDate,
                        RowID: (0, lodash_1.toNumber)(approver.approvalSequence),
                        Title: approver.title + ' (' + userNames + ')',
                        WorkflowProcessID: (0, lodash_1.toNumber)(approver.id),
                    });
                });
                return (0, helpers_1.multiObjectToInstance)(dtos_1.GetAFEHistoryResponseDTO, approverHistory);
            }
            throw new exceptions_1.HttpException('Invalid AFE.', enums_1.HttpStatus.NOT_FOUND);
        });
    }
    getUserEmail(emailId) {
        return __awaiter(this, void 0, void 0, function* () {
            const submitterDetails = yield this.mSGraphApiClient.getUserDetailsByEmail(emailId);
            if (submitterDetails) {
                const { mail: email, userPrincipalName, userType } = submitterDetails;
                emailId =
                    userType === enums_1.AD_USER_TYPE.GUEST ? email.toLowerCase() : userPrincipalName.toLowerCase();
                return emailId;
            }
            return '';
        });
    }
    getTaskApprovalActions(afeId, taskId) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const task = yield this.taskApiClient.getTaskById(+taskId);
            if (!task || (task === null || task === void 0 ? void 0 : task.task_status) !== 'Not Started') {
                throw new exceptions_1.HttpException(`Task doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const { additional_info, task_rel_url } = task;
            if (+additional_info.proposal_id !== afeId) {
                throw new exceptions_1.HttpException("Task id doesn't belong to AFE proposal", enums_1.HttpStatus.BAD_REQUEST);
            }
            if ((additional_info === null || additional_info === void 0 ? void 0 : additional_info.approval_type) === 'MOREDETAIL' ||
                ((_a = task_rel_url === null || task_rel_url === void 0 ? void 0 : task_rel_url.toLowerCase()) === null || _a === void 0 ? void 0 : _a.includes('resubmit'))) {
                return [];
            }
            return ['Approve', 'Reject', 'Delegate', 'More Details', 'Send Back', 'Reassign'];
        });
    }
    getApproversForMoreDetailTaskAction(afeId) {
        return __awaiter(this, void 0, void 0, function* () {
            const approvers = yield this.afeProposalApproverRepository.getCompletedActionApprovers(afeId);
            const afeDetail = yield this.afeProposalRepository.getAfeProposalById(afeId);
            if (!afeDetail) {
                throw new exceptions_1.HttpException(`Invalid afe id.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const approversList = approvers.map(approver => {
                var _a, _b, _c, _d;
                const commonProp = { ID: 0, UserUPN: approver.actionBy };
                if (approver.assginedType === associated_type_enum_1.ASSOCIATED_TYPE.USER) {
                    return Object.assign({ DisplayValue: `${((_b = (_a = approver.userDetail) === null || _a === void 0 ? void 0 : _a.originalApprover) === null || _b === void 0 ? void 0 : _b.firstName) || ''} ${((_d = (_c = approver.userDetail) === null || _c === void 0 ? void 0 : _c.originalApprover) === null || _d === void 0 ? void 0 : _d.lastName) || ''}` }, commonProp);
                }
                else {
                    return Object.assign({ DisplayValue: approver.title }, commonProp);
                }
            });
            return (0, helpers_1.multiObjectToInstance)(dtos_1.GetApproversForMoreDetailsResponseDTO, [
                {
                    ID: 0,
                    DisplayValue: 'Initiator',
                    UserUPN: afeDetail.submitterId,
                },
                ...approversList,
            ]);
        });
    }
    taskApprovalByTaskId(afeId, taskId, userId, action, comments, assigneeId) {
        var _a, _b, _c;
        return __awaiter(this, void 0, void 0, function* () {
            userId = yield this.getUserEmail(userId);
            if ((action === enums_1.TASK_ACTION.MORE_DETAIL ||
                action === enums_1.TASK_ACTION.DELEGATE ||
                action === enums_1.TASK_ACTION.REASSIGNE) &&
                !assigneeId) {
                throw new exceptions_1.HttpException(`Assignee is required.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const task = yield this.taskApiClient.getTaskById(+taskId);
            if (!task || (task === null || task === void 0 ? void 0 : task.task_status) !== 'Not Started') {
                throw new exceptions_1.HttpException(`Task doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if ((((_a = task.additional_info) === null || _a === void 0 ? void 0 : _a.approval_type) === 'MOREDETAIL' &&
                action !== enums_1.TASK_ACTION.MORE_DETAIL_SUBMITTED) ||
                (((_c = (_b = task === null || task === void 0 ? void 0 : task.task_rel_url) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === null || _c === void 0 ? void 0 : _c.includes('resubmit')) && action !== enums_1.TASK_ACTION.RESUBMIT)) {
                throw new exceptions_1.HttpException(`Invalid action.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const { additional_info, is_group_assignment, assigned_to, business_entity_id } = task;
            if (+additional_info.proposal_id !== afeId) {
                throw new exceptions_1.HttpException("Task id doesn't belong to AFE proposal", enums_1.HttpStatus.BAD_REQUEST);
            }
            const taskApprovalStep = yield this.afeProposalApproverRepository.getApproverById(task.entity_id);
            if (taskApprovalStep.actionStatus !== enums_1.APPROVER_STATUS.IN_PROGRESS) {
                throw new exceptions_1.HttpException(`Task is not in progress state.`, enums_1.HttpStatus.CONFLICT);
            }
            if (is_group_assignment) {
                const isUserInGroup = yield this.adminApiClient.hasUserRole(userId, assigned_to, business_entity_id);
                if (!isUserInGroup) {
                    throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
                }
            }
            else if (assigned_to !== userId) {
                throw new exceptions_1.HttpException(`User doesn't has permission to perform this action.`, enums_1.HttpStatus.FORBIDDEN);
            }
            let userLoginIds = [userId];
            if (assigneeId) {
                userLoginIds.push(assigneeId);
            }
            const users = yield this.mSGraphApiClient.getUsersDetails(userLoginIds);
            const currentUser = users.find(user => user.userPrincipalName.toLowerCase() === userId.toLowerCase() ||
                user.mail.toLowerCase() === userId.toLowerCase());
            if (!currentUser) {
                throw new exceptions_1.HttpException(`User doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const currentUsername = currentUser.userType == enums_1.AD_USER_TYPE.GUEST
                ? currentUser.mail.toLowerCase()
                : currentUser.userPrincipalName.toLowerCase();
            const currentContextUser = {
                family_name: currentUser.surname,
                given_name: currentUser.givenName,
                name: currentUser.displayName,
                unique_name: currentUsername,
                username: currentUsername,
                upn: currentUser.userPrincipalName,
            };
            let assigneeInfo = null;
            if (assigneeId) {
                const assignee = users.find(user => user.userPrincipalName.toLowerCase() === (assigneeId === null || assigneeId === void 0 ? void 0 : assigneeId.toLowerCase()) ||
                    user.mail.toLowerCase() === (assigneeId === null || assigneeId === void 0 ? void 0 : assigneeId.toLowerCase()));
                if (assigneeId && !assignee) {
                    throw new exceptions_1.HttpException(`Assignee doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
                }
                assigneeInfo = {
                    firstName: assignee.givenName,
                    lastName: assignee.surname,
                    loginId: assignee.userType == enums_1.AD_USER_TYPE.GUEST
                        ? assignee.mail.toLowerCase()
                        : assignee.userPrincipalName.toLowerCase(),
                    title: assignee.jobTitle,
                };
            }
            const approvers = yield this.afeProposalApproverRepository.getApproversByProposalId(taskApprovalStep.afeProposalId);
            action =
                task.additional_info.approval_type === enums_1.APPROVAL_TYPE.MORE_DETAIL
                    ? enums_1.TASK_ACTION.MORE_DETAIL_SUBMITTED
                    : action;
            const afeDetail = yield this.afeProposalRepository.getAfeProposalById(afeId);
            yield this.taskService.performApprovalAction(action, afeDetail, taskApprovalStep, approvers, { user: currentContextUser }, task, comments, assigneeInfo);
            return 'success';
        });
    }
    getUserSubmittedAFEProposal(userId, pageSize, activePage) {
        return __awaiter(this, void 0, void 0, function* () {
            userId = yield this.getUserEmail(userId);
            const afeProposals = yield this.afeProposalRepository.getUserSubmittedAFEProposals(userId, pageSize, activePage);
            return this.transformAfeProposalsList(afeProposals);
        });
    }
    getUserActionedAFEProposals(userId, pageSize, activePage, type) {
        return __awaiter(this, void 0, void 0, function* () {
            userId = yield this.getUserEmail(userId);
            const afeProposals = yield this.afeProposalRepository.getUserActionedAFEProposals(userId, pageSize, activePage, type);
            const afeProposalIds = [...new Set(afeProposals.map(a => a.id))];
            const budgetRefNumberSplits = yield this.afeProposalAmountSplitRepository.getBudgetRefByProposalIds(afeProposalIds);
            const response = yield afeProposals.map(afe => ({
                ID: afe.id,
                AFEModule: 'AFE',
                AFEType: afe['afeRequestType.title'],
                BusinessUnit: afe.entityTitle,
                BudgetType: enums_1.BUDGET_TYPE_TITLE[afe.budgetType],
                ProjectName: afe.name,
                ProjectReferenceNumber: afe.projectReferenceNumber,
                TotalAmount: `USD ${afe.totalAmount}`,
                BudgetReferenceNumber: (budgetRefNumberSplits === null || budgetRefNumberSplits === void 0 ? void 0 : budgetRefNumberSplits.get(afe.id)) || null,
                SubmissionDate: afe.createdOn,
                ActionDateTime: afe['afeProposalApprovers.actionDate'],
                AFEStatus: afe.userStatus,
                ApproverRole: afe['afeProposalApprovers.title'],
                Action: afe['afeProposalApprovers.actionStatus'],
                Comments: afe['afeProposalApprovers.comment'],
                TotalCount: null,
            }));
            return response;
        });
    }
    transformAfeProposalsList(afeProposals) {
        return __awaiter(this, void 0, void 0, function* () {
            const businessEntitiesHeirarchy = yield this.adminApiClient.getAllBusinessHierarchy();
            const entityHierarchies = afeProposals.map(afeProposal => this.adminApiClient.getPath([businessEntitiesHeirarchy], `${afeProposal.entityId}`));
            let afeProposalsList = yield Promise.all(afeProposals.map((afeProposal, index) => __awaiter(this, void 0, void 0, function* () {
                var _a, _b, _c, _d, _e, _f, _g, _h;
                const entityHierarchy = entityHierarchies[index];
                let [groupEntity, divisionalEntity, regionEntity, businessUnitEntity] = [
                    null,
                    null,
                    null,
                    null,
                ];
                if (entityHierarchy) {
                    [groupEntity, divisionalEntity, regionEntity, businessUnitEntity] = [
                        ...entityHierarchy,
                    ].reduce(([groupEntity, divisionalEntity, regionEntity, businessUnitEntity], entityDetail) => {
                        if (!entityDetail.parent_id) {
                            groupEntity = entityDetail;
                        }
                        else if ((0, lodash_1.toNumber)(entityDetail.parent_id) === (0, lodash_1.toNumber)(groupEntity.id)) {
                            divisionalEntity = entityDetail;
                        }
                        else if ((0, lodash_1.toNumber)(entityDetail.parent_id) === (0, lodash_1.toNumber)(divisionalEntity.id)) {
                            regionEntity = entityDetail;
                        }
                        else if ((0, lodash_1.toNumber)(entityDetail.parent_id) === (0, lodash_1.toNumber)(regionEntity.id)) {
                            businessUnitEntity = entityDetail;
                        }
                        return [groupEntity, divisionalEntity, regionEntity, businessUnitEntity];
                    }, [null, null, null, null]);
                }
                const budgetRefNos = afeProposal.afeProposalAmountSplits
                    .filter(proposalAmountSplit => proposalAmountSplit.type === enums_1.AMOUNT_SPLIT.BUDGET_REFERENCE_SPLIT)
                    .map(proposalAmountSplit => proposalAmountSplit.objectTitle)
                    .join(',');
                const departments = afeProposal.afeProposalAmountSplits
                    .filter(proposalAmountSplit => proposalAmountSplit.type === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT)
                    .map(proposalAmountSplit => proposalAmountSplit.objectTitle)
                    .join(',');
                const costCenterIds = afeProposal.afeProposalAmountSplits
                    .filter(proposalAmountSplit => proposalAmountSplit.type === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT)
                    .map(proposalAmountSplit => proposalAmountSplit.objectId);
                const projectSplits = afeProposal.afeProposalAmountSplits
                    .filter(proposalAmountSplit => proposalAmountSplit.type === enums_1.AMOUNT_SPLIT.PROJECT_COMPONENT_SPLIT)
                    .map(proposalAmountSplit => ({
                    AmountInUSD: proposalAmountSplit.amount,
                    Project: proposalAmountSplit.objectTitle,
                }));
                let costCenters = '';
                if (costCenterIds.length) {
                    const costCenterList = yield this.costCenterRepository.getCostCenterByIdsIncludingInactiveAndDeleted(costCenterIds);
                    costCenterList.forEach(costCenter => {
                        costCenters = costCenters + (costCenters ? ',' : '') + costCenter.code;
                    });
                }
                let expenditureAmount = (afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.totalAmount) ? (0, lodash_1.toNumber)(afeProposal.totalAmount) : null;
                if (afeProposal.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL) {
                    const prevSuppDetail = yield this.afeProposalRepository.getPreviousSupplementalAfeByIdAndVersion(afeProposal.parentAfeId, afeProposal.version);
                    if (prevSuppDetail) {
                        expenditureAmount =
                            (0, lodash_1.toNumber)(afeProposal.totalAmount) - (0, lodash_1.toNumber)(prevSuppDetail.totalAmount);
                    }
                }
                return {
                    ID: (0, lodash_1.toNumber)(afeProposal.id),
                    AFEHistory: null,
                    AFEInitiator: afeProposal.createdBy,
                    AFENature: ((_b = (_a = afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.data) === null || _a === void 0 ? void 0 : _a.natureType) === null || _b === void 0 ? void 0 : _b.title) || null,
                    AFEStatus: (afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.userStatus) || null,
                    AFEType: ((_c = afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.data) === null || _c === void 0 ? void 0 : _c.type) || null,
                    Approved_Date: afeProposal.internalStatus === enums_1.AFE_PROPOSAL_STATUS.APPROVED
                        ? afeProposal.updatedOn
                        : null,
                    BudgetType: afeProposal.budgetType ? enums_1.BUDGET_TYPE_TITLE[afeProposal.budgetType] : null,
                    IsSupplementary: afeProposal.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL,
                    ParentAFEId: (afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.parentAfeId) ? (0, lodash_1.toNumber)(afeProposal.parentAfeId) : null,
                    ParentProjectReferenceNumber: afeProposal.category === enums_1.AFE_CATEGORY.SUPPLEMENTAL
                        ? afeProposal.supplementalData.parentAfeNo
                        : null,
                    ProjectJustification: ((_e = (_d = afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.data) === null || _d === void 0 ? void 0 : _d.projectDetails) === null || _e === void 0 ? void 0 : _e.projectJustification) || null,
                    ProjectLeader: afeProposal.data.projectDetails.projectLeader.userType === enums_1.AD_USER_TYPE.GUEST
                        ? afeProposal.data.projectDetails.projectLeader.mail.toLowerCase()
                        : afeProposal.data.projectDetails.projectLeader.userPrincipalName.toLowerCase(),
                    ProjectLeaderContactNumber: ((_g = (_f = afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.data) === null || _f === void 0 ? void 0 : _f.projectDetails) === null || _g === void 0 ? void 0 : _g.projectLeaderNumber) || null,
                    ProjectName: (afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.name) || '',
                    ProjectReferenceNumber: (afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.projectReferenceNumber) || '',
                    Submission_Date: (afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.createdOn) || null,
                    UnbudgetedReason: ((_h = afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.data) === null || _h === void 0 ? void 0 : _h.budgetTypeJustification) || null,
                    ExpenditureAmountInUSD: expenditureAmount,
                    TotalExpenditureAmountinUSD: (afeProposal === null || afeProposal === void 0 ? void 0 : afeProposal.totalAmount)
                        ? (0, lodash_1.toNumber)(afeProposal.totalAmount)
                        : null,
                    SupplementaryAFEs: '',
                    AFERegionType: (businessUnitEntity === null || businessUnitEntity === void 0 ? void 0 : businessUnitEntity.full_name) || afeProposal.entityTitle,
                    BusinessDivision: (divisionalEntity === null || divisionalEntity === void 0 ? void 0 : divisionalEntity.full_name) || afeProposal.entityTitle,
                    RegionName: (regionEntity === null || regionEntity === void 0 ? void 0 : regionEntity.full_name) || afeProposal.entityTitle,
                    TerminalName: (businessUnitEntity === null || businessUnitEntity === void 0 ? void 0 : businessUnitEntity.full_name) || afeProposal.entityTitle,
                    DepartmentName: departments,
                    CostCenter: costCenters,
                    BudgetReferenceNumber: budgetRefNos,
                    ExpenseSummary: projectSplits,
                };
            })));
            afeProposalsList = afeProposalsList.filter(s => !!s);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.GetAfeDetailResponseDTO, afeProposalsList);
        });
    }
    getAFECompleteDetail(requestData) {
        return __awaiter(this, void 0, void 0, function* () {
            const { companyCode, costCenters, startDate } = requestData;
            const companyDetail = yield this.companyCodeRepository.getCompanyByCode(companyCode);
            if (!companyDetail) {
                throw new exceptions_1.HttpException(`Invalid Company Code.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const costCenterList = yield this.costCenterRepository.gerCostCentersForCompanyCode(costCenters, companyDetail.id);
            if (!(costCenterList === null || costCenterList === void 0 ? void 0 : costCenterList.length)) {
                throw new exceptions_1.HttpException(`Invalid Cost Centers.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const costCenterIds = costCenterList.map(costCenterDetail => { var _a; return (_a = costCenterDetail === null || costCenterDetail === void 0 ? void 0 : costCenterDetail.id) === null || _a === void 0 ? void 0 : _a.toString(); });
            const afeList = yield this.afeProposalRepository.getAfeListForMaximoIntegration(startDate);
            const maximoEnabledAfes = afeList.filter(afeDetail => {
                const { afeProposalAmountSplits } = afeDetail;
                if (afeProposalAmountSplits === null || afeProposalAmountSplits === void 0 ? void 0 : afeProposalAmountSplits.length) {
                    const costCenterSplits = afeProposalAmountSplits.filter(afeProposalAmountSplitDetail => afeProposalAmountSplitDetail.type === enums_1.AMOUNT_SPLIT.COST_CENTER_SPLIT);
                    if (costCenterSplits === null || costCenterSplits === void 0 ? void 0 : costCenterSplits.length) {
                        return costCenterSplits.some(costCenterSplitDetail => { var _a; return costCenterIds.includes((_a = costCenterSplitDetail === null || costCenterSplitDetail === void 0 ? void 0 : costCenterSplitDetail.objectId) === null || _a === void 0 ? void 0 : _a.toString()); });
                    }
                }
                return false;
            });
            return (maximoEnabledAfes === null || maximoEnabledAfes === void 0 ? void 0 : maximoEnabledAfes.length) ? maximoEnabledAfes : [];
        });
    }
};
OneAppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [repositories_1.AfeProposalRepository,
        repositories_1.AfeProposalAmountSplitRepository,
        clients_1.TaskApiClient,
        clients_1.MSGraphApiClient,
        clients_1.AttachmentApiClient,
        config_service_1.ConfigService,
        clients_1.AdminApiClient,
        validators_1.AfeProposalValidator,
        repositories_2.CostCenterRepository,
        repositories_1.AfeProposalApproverRepository,
        services_1.TaskService,
        repositories_2.CompanyCodeRepository])
], OneAppService);
exports.OneAppService = OneAppService;
//# sourceMappingURL=one-app.service.js.map