"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSequelizeOrmConfig = void 0;
const models_1 = require("../afe-config/models");
const afe_request_type_model_1 = require("../afe-config/models/afe-request-type.model");
const afe_type_model_1 = require("../afe-config/models/afe-type.model");
const questions_model_1 = require("../afe-config/models/questions.model");
const afe_draft_model_1 = require("../afe-draft/models/afe-draft.model");
const models_2 = require("../afe-proposal/models");
const afe_proposal_model_1 = require("../afe-proposal/models/afe-proposal.model");
const entity_setup_model_1 = require("../business-entity/models/entity-setup.model");
const models_3 = require("../finance/models");
const models_4 = require("../project-component/models");
const models_5 = require("../settings/models");
const models_6 = require("../vacation/models");
const models_7 = require("../workflow/models");
const models_8 = require("../notification/models");
const models_9 = require("../queue/models");
const scheduler_model_1 = require("../scheduler/models/scheduler.model");
const models_10 = require("../sftp-service/models");
const models = [
    models_1.AfeBudgetTypeMapping,
    models_1.AfeBudgetType,
    models_1.AfeCommitmentLength,
    models_1.AfeNatureTypeMapping,
    models_1.AfeNatureType,
    afe_request_type_model_1.AfeRequestType,
    models_1.ParallelIdentifier,
    afe_type_model_1.AfeType,
    afe_proposal_model_1.AfeProposal,
    models_2.AfeProposalAmountSplit,
    models_2.AfeProposalApprover,
    models_2.AfeProposalLimitDeduction,
    questions_model_1.Question,
    models_3.CompanyCode,
    models_3.CostCenter,
    models_3.CurrencyType,
    models_3.NaturalAccountNumber,
    entity_setup_model_1.EntitySetup,
    models_4.ProjectComponent,
    models_6.VactionSetup,
    models_7.WorkflowMasterSetting,
    models_7.WorkflowMasterStep,
    models_7.WorkflowSharedBucketLimit,
    models_7.WorkflowRule,
    models_7.WorkflowSharedChildLimit,
    afe_draft_model_1.AfeDraft,
    models_3.AnalysisCode,
    models_5.Settings,
    models_8.Notification,
    models_1.AfeSubType,
    scheduler_model_1.Scheduler,
    models_9.QueueLog,
    models_2.UserCostCenterMapping,
    models_2.UserProjectComponentMapping,
    models_2.Location,
    models_10.DataSharingSchedulers
];
const getSequelizeOrmConfig = (enableSSL) => {
    return Object.assign({ synchronize: false, autoLoadModels: true, models }, (enableSSL && {
        ssl: true,
        dialectOptions: {
            ssl: {
                require: true,
            },
        },
    }));
};
exports.getSequelizeOrmConfig = getSequelizeOrmConfig;
//# sourceMappingURL=orm-config.js.map