// USA
export const locale = {
  lang: 'en',
  data: {
    TRANSLATOR: {
      SELECT: 'Select your language',
    },
    COMMON: {
      CHECK_NOTIFICATION: 'Check Notification',
      MY_PROFILE: 'My Profile',
      DASHBOARD: 'Dashboard',
      MY_TASK: 'My Task',
      REPORT: 'Report',
      VACATION_SETTING: 'Setup Delegation',
      UPCOMING_DELEGATION: 'Upcoming Delegation',
      REPRESENTATIVE_SETUP: 'Setup Representative',
      AFE: 'AFE',
      EXCHANGE_RATE: 'Exchange Rate',
      AMOUNT: 'Amount',
      IN: 'in',
      OR: 'or',
      BY: 'by',
      IS: 'is',
      ON: 'on',
      TO: 'to',
      OF: 'of',
      LIST: 'List',
      ROLE: 'Role',
      NAME: 'Name',
      ADD: 'Add',
      REMOVE: 'Remove',
      SNO: 'S. No.',
      NEW: 'New',
      BEFORE: 'Before',
      AFTER: 'After',
      READER: 'Reader',
      ADDITIONAL: 'Additional',
      REQUEST: 'Request',
      SUBMISSION: 'Submission',
      ATTACHMENTS: 'Attachments',
      WORKFLOW_START: 'Workflow Start',
      NO_READER: 'No reader added.',
      SUBMITTED_BY: 'Submitted by',
      CURRENT_STATUS: 'Current Status',
      ERROR: 'Error',
      ERROR_404: 'Error 404',
      ERROR_403: 'Error 403',
      ERROR_500: 'Error 500',
      ADMIN: 'Admin',
      WORKFLOW: 'Workflow',
      COMPANY: 'Company Setup',
      TOTAL_RECORD: 'Total Record',
      RECORD: 'Record',
      SHOWING: 'Showing',
      SHARED_LIMIT_MESSAGE: 'Can be determined for each {{childTitle}} by the relevant {{parentTitle}} subordinate to his/her own limits.',
      NETBOOK_VALUE: 'Netbook Value',
      MARKET_VALUE: 'Market Value',
      SUPPLEMENTARY_AMOUNT: 'Supplementary Amount',
      DRY_RUN_WORKFLOW: 'Dry Run Workflow',
      USERS_LIST: 'Users List',
      GENERATING_APPROVERS: 'Generating Approvers Flow...',
      APPROVER_LEVEL: 'Approver Level',
      TOTAL_APPROVER_NEEDED: 'Total User Approval Required',
      CONDITIONS: 'Conditions',
      TASK_OVERVIEW: 'Task Overview',
      NOTIFICATION: 'Notifications',
      VIEW_MORE: 'View More',
      VIEW_ALL_TASK: 'View All Task',
      VIEW_ALL_NOTIFICATION: 'View All Notification',
      REMOVE_NOTIFICATION: 'Remove Notification',
      CLEAR_ALL_NOTIFICATIONS: 'Clear All Notifications',
      TOTAL_SUBMITTED_AFE: 'Total Submitted AFE',
      MY_SUBMITTED_AFE: 'My Submitted AFE',
      TOTAL_APPROVED_AFE: 'Total Approved AFE',
      MY_APPROVED_AFE: 'My Approved AFE',
      APPROVED: 'Approved',
      APPROVED_DATE: 'Approved On',
      VIEW_AFE: 'View AFE',
      REJECTED: 'Rejected',
      TOTAL_REJECTED_AFE: 'Total Rejected AFE',
      MY_REJECTED_AFE: 'My Rejected AFE',
      MY_SENT_BACK_AFE: 'My Sent Back AFE',
      TOTAL_INPROGRESS_AFE: 'Total In-Progress AFE',
      MY_INPROGRESS_AFE: 'My In-Progress AFE',
      TOTAL_SENT_BACK_AFE: 'Total Sent Back AFE',
      INPROGRESS: 'In-Progress',
      SENT_BACK: 'Sent Back',
      TOTAL_APPROVED_AMOUNT: 'Total Approved Amount',
      MY_APPROVED_AMOUNT: 'My Approved Amount',
      MY_DRAFTED_AFE: 'My Drafted AFE',
      MY_PENDING_TASK: 'My Pending Task',
      TASK: 'Task',
      REQUEST_AFE_COUNT: 'Request Wise AFE Count',
      BOTH: 'Both',
      PUBLISHED: 'Published',
      UNPUBLISHED: 'Unpublished',
      PUBLISHED_AND_UNPUBLISHED: 'Published & Unpublished',
      WORKFLOW_STATUS: 'Workflow Status',
      FUSSION_INTEGRATION: 'Fusion Integration',
      BUDGETED: 'Budgeted',
      UNBUDGETED: 'Unbudgeted',
      BUDGET_BASED_PROJECT_SPLIT: 'Budget Based Project Component',
      IMPORT: 'Import Data',
      ADD_MANUALLY: 'Add Manually',
      IMPORT_INFO: '*In order to import Excel files, the format must be the same as in the export Excel.',
      COST_CENTER_SECTION_FORMAT: '*Follow this format to add sections: SectionTitle1#UserEmail1;SectionTitle2#UserEmail2;',
      WORKFLOW_SETTING: 'Workflow Setting',
      ADVANCE_SETTING: 'Show Advance Setting',
      HIDE_ADVANCE_SETTING: 'Hide Advance Setting',
      PUBISHED_VERSION: 'Published Version',
      UNPUBISHED_VERSION: 'Unpublished Version',
      BUDGET_YEAR: 'Budget Year',
      USER: 'User',
      SEND_REMINDER: 'Send Reminder',
      ACTION: {
        APPROVE: 'Approve',
        REJECT: 'Reject',
        REASSIGNE: 'Reassign',
        MORE_DETAIL: 'More Detail',
        DELEGATE: 'Delegate',
        SEND_BACK: 'Send Back',
        ASK_DETAIL: 'Ask more detail from AFE submitter',
      }
    },
    LIST: {
      ID: 'Id',
      TITLE: 'Title',
      ENTITY: 'Entity',
      ENTITY_NAME: 'Entity Name',
      ENTITY_CODE: 'Entity Code',
      PROJECT_NAME: 'Project Name',
      CREATED_BY: 'Created By',
      UPDATED_BY: 'Updated By',
      ACTION: 'Actions',
      CREATED_AT: 'Created At',
      UPDATED_AT: 'Updated At',
      APPROVAL_PENDING_WITH: 'Pending With',
      PROJECT_REFERENCE_NUMBER: 'Project Ref. No.',
      REQUEST_TYPE: 'Request Type',
      TOTAL_AMOUNT: 'Total Amount',
      TOTAL_NETBOOK_VALUE: 'Total Netbook Value',
      TOTAL_MARKET_VALUE: 'Total Market Value',
      STATUS: 'Status',
      TOTAL_EXPENSE: 'Total Expense',
      EXPENSE_AMOUNT: 'Expense Amount',
      BUDGET_TYPE: 'Budget Type',
      COST_CENTER_OPERATING: 'Cost Center Operating',
      IS_MASTER_SETTING: 'Is Master Setting',
      YEAR: 'Year',
      LENGTH_OF_COMMITMENT: 'Length of Commitment',
      AGGREGATE_LIMIT: 'Aggregate Limit',
      REQUIRED: 'Required',
      TASK_ID: 'Task Id',
      AFE_PROPOSAL_ID: 'Afe Proposal Id',
      ASSIGNED_TO: 'Assigned To',
      APPROVAL: 'Approval',
      BALANCE_LEFT: 'Balance Left',
      STEP_TITLE: 'Step Title',
      COMPANY_CODE: 'Company Code',
      ACTIVE: 'Active',
      CODE: 'Code',
      SECTION_HEAD: 'Section Head',
      DEPARTMENT_HEAD: 'Department Head',
      REQUEST_TYPES: 'Request Types',
      NUMBER: 'Number',
      NAME: 'Name',
      DELEGATED_FOR: 'Delegate For',
      DELEGATED_TO: 'Delegate To',
      REPRESENTATIVE_FOR: 'Representative For',
      REPRESENTATIVE: 'Representative',
      FROM_DATE: 'From Date',
      TO_DATE: 'To Date',
      USER_NAME: 'User Name',
      NO_APPROVER_FOUND: 'No Approver Available'
    },
    ERROR: {
      NO_DATA_FOUND: 'No Data Found',
      NO_DRAFT_FOUND: 'No Draft Found',
      NO_TASK_FOUND: 'No Task Found',
      NO_HISTORY_FOUND: 'No History Found',
      NO_RECORD_FOUND: 'No Record Found',
      NO_WORKFLOW_ADDED: 'No Workflow Available',
      NO_WORKFLOW_TO_CLONE: 'No Workflow Available To Clone',
      NO_OVERRIDE_ADDED: 'No Override Workflow Available',
      NO_WORKFLOW_AVAILABLE: 'Workflow Detail Not Available',
      NO_SHARE_LIMIT_AVAILABLE: 'Limit Not Shared With Selected Entity',
      NO_STEP_AVAILABLE: 'Workflow Step Not Available',
      SELECT_WORKFLOW: 'Select Any Workflow For Detail.',
      SELECT_ENTITY: 'Select Entity To Check Detail.',
      NO_BUCKET_SHARE: 'No Bucket Limit Available.',
      NO_STEPS_ADDED: 'No Steps Added',
      NO_UNPUBLISHED_OVERRIDDEN_FOUND: 'No Unpublished Overridden Available.',
      NO_LIMIT_FOUND: 'No Limit Found',
      NO_EXCEPTION_STEP_FOUND: 'All {{title}} have the same limit.',
      NO_CHILD_LIMIT_ADDED: 'No Limit Shared To ',
      NO_RULE_AVAILABLE: 'Rule Does Not Exist',
      NO_TASK_AVAILABLE: 'No task has been assigned to you',
      NO_VACATION_PLANNED: 'No upcoming delegation found.',
      NO_REPRESENTATIVE_ADDED: 'No representative added.',
      NO_NOTIFICATION_AVAILABLE: 'No notification available',
      STEP_LIMIT_SHARE_NOT_ALLOWED: 'Limit sharing is not allowed for this step.',
      STEP_LIMIT_SHARE_DELETE_CONFIRMATION: 'If you decrease the single or aggregate limit for this step, all child limits will be deleted and the link to share child limits will be removed.',
      NO_APPROVER_USER_FOUND: 'No User Found',
      GENERATE_APPROVERS: 'Fill out the form to generate the list of approvers.',
      NO_DRYRUN_APPROVER: 'We were unable to find any approvers for the options you selected.'
    },
    EMPTY_STATE: {
      EMPTY_COMPANY_CODE_LIST: 'No company found for the {{entityName}}',
      EMPTY_USER_LIST: 'No user found for the {{selectedRole}}',
      EMPTY_COST_CENTER_LIST: 'No cost center found in the company.',
      EMPTY_ANALYSIS_CODE_LIST: 'No analysis code found in the company.',
      EMPTY_NATURAL_ACCOUNT_NUMBER_LIST: 'No natural account number found in the company.',
      EMPTY_COMPANY_CODE: 'Please select business unit to setup a company.',
      EMPTY_ROLE_LIST: 'On the left, select any entity.',
      SELECT_ROLE: 'Select any of the roles above.'
    },
    AFE_MODULE: {
      SECTION_TITLE: {
        PROJECT_INFORMATION: 'Project Information',
        PROPOSAL_DETAILS: 'Proposal Details',
        PROJECT_COMPONENT: 'Project Component',
        PROJECT_COMPONENT_AMOUNT: 'Project Component Amount',
        FINANCE_DETAILS: 'Finance Details',
        APPROVER_DETAILS: 'Approver Details',
        GLOBAL_PROCUREMENT_INFO: 'Global Procurement Information',
        SUPPORTING_DOCUMENTS: 'Supporting Documents',
        APPROVAL_DOCUMENTS: 'Approver Documents',
        WORKFLOW_RULE_INFORMATION: 'Rule Information',
        ADD_WORKFLOW_RULE: 'Add New Workflow Rule',
        UPDATE_WORKFLOW_RULE: 'Update Workflow Rule',
        SUMMARY: 'Summary',
        AFE_APPROVED_VALUE: 'Value of AFE/s Approved Till Date',
        OPERATING_ENTITIES: 'DP World Operating Entities',
      },
      SECTION_DESCRIPTION: {
        PROJECT_INFORMATION: 'Location & Project Information',
        PROPOSAL_DETAILS: 'Budget Types & Other Types Information',
        PROJECT_COMPONENT_AMOUNT: 'Project Component Based Amount Information',
        FINANCE_DETAILS: 'Finance Related Information',
        APPROVER_DETAILS: 'Approvers Related Information',
        GLOBAL_PROCUREMENT_INFO: 'Question Answer For Global Procurement Information',
        SUPPORTING_DOCUMENTS: 'Upload Supporting Documents',
        SUMMARY: 'Review & Submit',
      }
    },
    WARNING: {
      STEP_UPDATE_WARNING: ' Warning - Any changes to this step will also affect all other steps that override it.',
      CHILD_SHARRING_UPDATE_WARNING: 'After the update, all the shared limits with the current child step will be removed, and the limits cannot be restored again.',
      UNPUBLISHED_VERSION_AVAILABLE: 'There is a new version of this workflow that has not yet been published. Click on continue to proceed further and publish the new version.',
      MV_NBV_AMOUNT_MESSAGE: 'Total Market Value is {{type}} than Net Book Value By {{amount}}.',
      OPEX_PROJECT_COMPONENT_RESTRICTION: 'For Supplemental Opex, changing or removing project components is temporarily restricted.',
      PUBLISH_OVERRIDDEN_MESSAGE: 'Publish any overridden version that has not been published yet and does not apply to a new version in this way.',
      DELETE_MULTIPLE_STEP_MESSAGE: 'Steps will be deleted from the unpublished overridden workflows where they are inherited, and have the ability to be removed at the child level.'
    },
    MENU: {
      SUBMIT_AFE: 'Submit AFE',
      SUBMIT_AFE_REQUEST: 'Submit AFE Request',
      LIST_AFE: 'List AFE',
      MY_TASKS: 'My Tasks',
      APPROVAL_HISTORY: 'Approval History',
      RELATED_AFE: 'Related AFE Proposals',
      AFE_APPROVAL_HISTORY: 'AFE Approval History',
      REPORTS: 'Reports',
      BU_LEVEL_WORKFLOW: 'BU Level Workflow Report',
      AFE_REPORTS: 'AFE Reports',
      EMAIL_REPORTS: 'Email Reports',
      HISTORY_REPORTS: 'AFE History Reports',
      ADMINISTRATION: 'Administration',
      MANAGE_PROJECT: 'Manage Project',
      MANAGE_WORKFLOW: 'Manage Workflow',
      MANAGE_SETTINGS: 'Manage Settings',
      MANAGE_WORKFLOW_SETTING: 'Manage Workflow Settings',
      WORKFLOW_SETTING: 'Workflow Settings',
      UNPUBLISHED_VERSION_WORKFLOW_SETTING: 'Workflow Settings (Unpublished Version)',
      MASTER_SETTING_LIST: 'Master Setting List',
      WORKFLOW_SETTING_DETAIL: 'Workflow Setting Detail',
      MANAGE_SHARED_CHILD_LIMIT: 'Manage Shared Child Limits',
      MANAGE_SHARED_BUCKET_LIMIT: 'Manage Shared Bucket Limits',
      LINKS_DOCUMENTS: 'Links & Documents',
      POLICY_DOCUMENTS: 'AFE Policy Document',
      MY_AFE_SUBMISSIONS: "My AFE Submissions",
      MANAGE_WORKFLOW_RULES: "Manage Workflow Rules",
      DRAFTED_AFE: 'Drafted AFE',
      MY_AFE_LIST: 'AFE List',
      AFE_DETAIL: 'AFE Detail',
      WORKFLOW: 'Workflow',
      ADD_WORKFLOW: 'New Master Workflow Setting',
      ALL_MASTER_SETTINGS: 'All Master Setting List',
      PUBLISH_MULTIPLE_OVERRIDDEN: 'Publish Overridden',
      OVERRIDDEN_SETTING_LIST: 'Overridden Setting List',
      OVERRIDDEN_WORKFLOW: 'Overridden Workflow List',
      UNPUBLISHED_OVERRIDDEN_WORKFLOW: 'Unpublished Overridden Workflow List',
      OVERRIDE_WORKFLOW: 'Override Workflow',
      WORKFLOW_MASTER_STEPS: 'Workflow Master Steps',
      NEW_WORKFLOW: 'Add New Master Workflow',
      VIEW_LIST: 'View List',
      QUICK_ACTION: 'Quick Actions',
      FILTER_OPTIONS: 'Filter Options',
      PARENT_WORKFLOW_DETAIL: 'Parent Workflow Detail',
      UPDATE_STEP_DETAIL: 'Update Step Detail',
      MANAGE_CHILD_LIMIT_SHARE: 'Manage Child Limit Share',
      CHILD_LIMIT_SHARE_STEPS: 'Manage Child Limit Share',
      STEP_LIST: 'Step List',
      APPROVAL: 'Approval',
      MORE_DETAIL_REQUIRED: 'More Detail Required',
      AUTHORITY_LIMIT: 'Authority Limits for Approvals',
      MANAGE_CHILD_LIMIT: 'Manage Child Limit',
      MANAGE_BUCKET_LIMIT: 'Manage Bucket Sharing',
      CHILD_LIMITS: 'Child Limits',
      CHECK_BALANCE: 'Check Balance',
      AGGREGATE_LIMIT_BALANCE: 'Aggregate Limit Balance',
      DEDUCTION_LIST: 'Utilizations',
      AFE_TYPES: 'AFE Types',
      SUBMISSION_TYPES: 'Submission Types',
      CURRENCIES: 'Currencies Settings',
      COMMITMENT_LENGTHS: 'Commitment Lengths',
      NATURE_TYPES: 'Nature Type',
      PROJECT_COMPONENTS: 'Project Components',
      NATURAL_ACCOUNT_NUMBERS: 'Natural Account Numbers',
      COMPANIES: 'Company Setup',
      ADD_COMPANY: 'Add Company',
      UPDATE_COMPANY: 'Update Company',
      ADD_COST_CENTER: 'Add Cost Center',
      UPDATE_COST_CENTER: 'Update Cost Center',
      ADD_ANALYSIS_CODE: 'Add Analysis Code',
      UPDATE_ANALYSIS_CODE: 'Update Analysis Code',
      ADD_NATURAL_ACCOUNT_NUMBER: 'Add Natural Account Number',
      UPDATE_NATURAL_ACCOUNT_NUMBER: 'Update Natural Account Number',
      NATURAL_ACCOUNT_NUMBER_LIST: 'Natural Account Numbers',
      ANALYSIS_CODE_LIST: 'Analysis Codes',
      COST_CENTER_LIST: 'Cost Centers',
      COMPANY_CODE: 'Company Code',
      FILTER: 'Filter',
      CHECK_AFE_DETAIL: 'Check AFE Detail',
      CHOOSE_COLUMNS_FOR_EXCEL_EXPORT: 'Choose Columns for Excel Export',
      CHOOSE_REQUEST_TYPES_FOR_FUSION_INTEGRATION: 'Select AFE Request Types for Fusion Integration',
      USER_LIST: 'User List',
      UPDATE_LOGIN_ID: 'Update Login Id',
      REPLACE_LOGIN_ID: 'Replace User Login Id',
      OLD_LINK: 'Link to Legacy System',
      USER_GUIDE: 'User Guide'
    },
    HEADER: {
      ACCOUNT_SETTINGS: 'Account Settings',
      SIGN_OUT: 'Sign Out'
    },
    FORM: {
      BUTTON: {
        SUBMIT_BUTTON: 'Submit',
        BACK_BUTTON: 'Back',
        COPY_BUTTON: 'Copy',
        CONTINUE_BUTTON: 'Continue',
        DOWNLOAD_ALL_BUTTON: 'Download All',
        ADD_BUTTON: 'Add',
        ADD_COMBINATION_BUTTON: 'Add New Combination',
        SEND_BUTTON: 'Send',
        EDIT_BUTTON: 'Edit',
        EDIT_DETAIL_BUTTON: 'Edit AFE Detail',
        WITHDRAW_BUTTON: 'Withdraw AFE',
        SEND_BACK_BUTTON: 'Send Back',
        CANCEL_BUTTON: 'Cancel',
        DELETE_BUTTON: 'Delete',
        DRAFT_BUTTON: 'Save Draft',
        ADD_MORE_BUDGET_REFBUTTON: 'Add More Budget Ref. Number',
        DISCARD: 'Discard',
        BEFORE_BUTTON: 'Before',
        AFTER_BUTTON: 'After',
        ADD_READER_BUTTON: 'Add Reader',
        UPLOAD_EVIDENCE_BUTTON: 'Upload Evidence',
        UPLOAD_BUTTON: 'Upload',
        BUDGETED: 'Budgeted',
        UNBUDGETED: 'Unbudgeted',
        CLOSE: 'Close',
        SAVE_BUTTON: 'Save',
        DISCARD_BUTTON: 'Discard',
        PUBLISH_BUTTON: 'Publish Workflow',
        PUBLISH_NEW_VERSION_BUTTON: 'Publish New Version',
        UNPUBLISH_BUTTON: 'Unpublish Workflow',
        NEW_VERSION_BUTTON: 'Create New Version',
        NEW_VERSION_DETAIL_BUTTON: 'New Version Detail',
        OVERRIDE_BUTTON: 'Override Workflow',
        OVERRIDE: 'Override',
        MANAGE_STEP_BUTTON: 'Manage Steps',
        ADD_STEP_BUTTON: 'Add New Step',
        WORKFLOW_HISTORY_BUTTON: 'Workflow Setting History',
        HISTORY_BUTTON: 'History',
        STEP_HISTORY_BUTTON: 'Workflow Step History',
        COMPANY_HISTORY_BUTTON: 'Company History',
        COST_CENTER_HISTORY_BUTTON: 'Cost Center History',
        STEPS_HISTORY_BUTTON: 'Step History',
        CLONE_STEP_BUTTON: 'Clone Steps',
        EDIT_STEP_BUTTON: 'Edit Step',
        DELETE_STEP_BUTTON: 'Delete Step',
        SHARE_LIMIT_STEP_BUTTON: 'Child Limit Sharing',
        MOVE_STEP_UP: 'Move Step Up',
        MOVE_STEP_DOWN: 'Move Step Down',
        ADD_STEP_TO_OTHER_OVERRIDDEN: 'Copy Step',
        DELETE_STEP_TO_OTHER_OVERRIDDEN: 'Delete Step From Overridden',
        ADD_WORKFLOW_BUTTON: 'Add Workflow',
        ADD_VACATION_BUTTON: 'Add Delegation',
        ADD_REPRESENTATIVE_BUTTON: 'Add Representative',
        AMOUNT_DIFFERENCE_BUTTON: 'Amount Difference',
        CHECK_AVAILABLE_LIMIT: 'Check Available Limits',
        SETUP_VACATION_BUTTON: 'Setup Delegation',
        UPDATE_VACATION_BUTTON: 'Update Delegation',
        UPDATE_REPRESENTATIVE_BUTTON: 'Update Representative',
        SETUP_REPRESENTATIVE_BUTTON: 'Setup Representative',
        VIEW_DETAIL_BUTTON: 'View Detail',
        VIEW_UNPUBISHED_VERSION_BUTTON: 'New Version',
        UPDATE_BUTTON: 'Update',
        DELETE_MASTER: 'Delete Master Workflow',
        DELETE_UNPUBLISHED_WORKFLOW: 'Delete Unpublished Version Workflow',
        DELETE_WORKFLOW: 'Delete Workflow',
        DELETE_OVERRIDDEN: 'Delete All Overrides',
        DELETE_UNPUBLISHED_OVERRIDDEN: 'Delete All Unpublished Version Overrides',
        DELETE_ALL_DELEGATION: 'Delete All',
        SHARE_LIMIT: 'Share Limit',
        LINK_BUTTON: 'Link',
        REVIEW: 'Review',
        DEACTIVATE: 'Deactivate',
        APPLY: 'Apply',
        RESET: 'Reset',
        DOWNLOAD: 'Download',
        FILTER: 'Filter',
        CLEAR_FILTERS: 'Clear Filters',
        SUBSCRIBE: 'Subscribe',
        UNSUBSCRIBE: 'Unsubscribe',
        EXPORT_ALL_TO_EXCEL: 'Export All To Excel',
        CLEAR: 'Clear',
        EXPORT_TO_EXCEL: 'Export to Excel',
        EXPORT: 'Export',
        PRINT: 'Print',
        EXPORT_FORMAT: 'Export Format',
        EDIT_APPROVERS_LIST: 'Edit Approvers List',
        CREATE: 'Create',
        ADD_COST_CENTER_BUTTON: 'Add Cost Center',
        ADD_REMOVED_PROJECT: 'Add Removed Projects',
        RE_OPEN: 'Re-Open',
        REVERT_SENT_BACK: 'Revert Send Back',
        DELETE_SELECTED_WORKFLOW: 'Delete Selected Workflow'
      },
      INPUT: {
        EMAIL: 'Email',
        FULLNAME: 'Fullname',
        PASSWORD: 'Password',
        CONFIRM_PASSWORD: 'Confirm Password',
        USERNAME: 'Username',
        COMMENTS: 'Comments'
      },
      LABEL: {
        ACCOUNT_NUMBER: 'Account Number',
        BUSINESS_ENTITY: 'Business Entity',
        CLONE_STEPS: 'Clone Workflow Steps',
        SUBMISSION_TYPE: 'Submission Type',
        NEW_AFE: 'New AFE',
        NEW_AFE_DESCRIPTION: 'Select to submit a fresh AFE',
        SUPPLEMENTAL_AFE: 'Supplemental AFE',
        PUBLISH_TYPE: 'Publish Type (which is not published yet)',
        SUPPLEMENTAL_AFE_DESCRIPTION: 'Changes in scope/value of parent AFE.',
        SCOPE_CHANGE: 'Scope Change',
        REASON_SCOPE_CHANGE: 'Reason for change in scope',
        VALUE_CHANGE: 'Value Change',
        PARENT_AFE: 'Parent AFE (Ref. Number)',
        PARENT_PROJECT_AMOUNT: 'Parent Project Amount',
        SECTIONS: 'Sections',
        SECTION: 'Section',
        PROJECT_INFORMATION: 'Project Information',
        OLD_AFE_DETAIL: 'View Parent AFE Detail',
        PROPOSAL_DETAILS: 'Proposal Details',
        PROJECT_NAME: 'Project Name',
        LOCATION: 'Location',
        TITLE: 'Title',
        PROJECT_JUSTIFICATION: 'Project Justification',
        JUSTIFICATION: 'Justification',
        PROJECT_LEADER: 'Project Leader',
        CONTACT_PERSON: 'Contact Person',
        CONTACT_NUMBER: 'Contact Number',
        KEY_CONTACT: 'Key Contact',
        NATURE_TYPE: 'Nature Type',
        TYPE: 'Type',
        SUB_TYPE: 'Sub Type',
        BUDGET: 'Budget',
        POSITION: 'Position',
        BOARD_SECRETARY_QUESTION: 'Has this proposal already been approved by the DPW Ltd Board of Directors/ExCom in Dubai?',
        UNBUDGETED_REASON: 'Why was this expense not included on the budget?',
        PROJECT_COMPONENT: 'Project Component',
        NATURAL_ACCOUNT: 'Natural Account Number',
        NATURAL_ACCOUNTS: 'Natural Accounts',
        COST_CENTER: 'Cost Center',
        AMOUNT_SPLIT: 'Amount Split',
        BUDGET_REFERENCE_NUMBER: 'Budget Reference Number',
        TOTAL: 'Total',
        EXPENDITURE: 'Expenditure',
        APPROVERS_LIST: 'Approvers List',
        APPROVERS: 'Approvers',
        APPROVER: 'Approver',
        DESCRIPTION: 'Description',
        UPLOAD_ATTACHMENT: 'Upload Attachment',
        UPLOAD_ANALYSIS_CODE: 'Upload Analysis Code',
        UPLOAD_NATURAL_ACCOUNT: 'Upload Natural Account',
        UPLOAD_COST_CENTER: 'Upload Cost Center',
        UPLOADED_PHOTOS: 'Uploaded Images',
        DRAG_N_DROP: 'Drag & Drop',
        BROWSE_FILE: 'Browse File',
        UPLOADED_DOCUMENTS: 'Uploaded Documents',
        UPLOADED_ATTACHMENT: 'Uploaded Attachments',
        ANALYSIS_CODE: 'Analysis Code',
        ACCOUNT_DETAILS: 'Account Details',
        COST_CENTER_TITLE: 'Cost Center Title',
        CHART_OF_ACCOUNT: 'Chart of Account',
        CHARGE_AMMOUNT: 'Total Chargeable Amount',
        CHARGABLE_AMMOUNT: 'Chargeable Amount',
        ASSOCIATE_LEVEL: 'Associate Level',
        ASSOCIATE_ROLE: 'Associate Role',
        ASSOCIATE_TYPE: 'Associate Type',
        ASSOCIATED_COLUMN: 'Associated Column',
        ASSOCIATED_USER: 'Associated User',
        PARALLEL_IDENTIFIER: 'Parallel Identifier',
        SINGLE_LIMIT: 'Single Limit',
        AGGREGATE_LIMIT: 'Aggregate Limit',
        CAN_WORKFLOW_START: 'Can Workflow Start?',
        IS_MANDATORY: 'Is Mandatory Step?',
        INCLUDE_CHILD_STEPS: 'Include All Below Steps Also?',
        CAN_REMOVED_AT_CHILD_LEVEL: 'Can Removed At Child Level?',
        CAN_SHARE_LIMIT_TO_CHILD: 'Can Share Limit to Child?',
        COPY_STEP_TO_OVERRIDDEN: 'Copy To?',
        SHARED_LIMIT_MASTER_STEP_CHILD: 'Shared Limit Master Step Child',
        SHARE_LIMIT_CHILD: 'Shared Limit With Child Step',
        CHILD_STEP: 'Child Step',
        RULE: 'Rule',
        GENERATE_BU_LEVEL_REPORT: 'Generate BU Level Workflow Report',
        SELECT_BUSINESS_ENTITY: 'Select Business Entity',
        STEP_TITLE: 'Step Title',
        IS_INHERITED: 'Is Inherited',
        SKIP_LIMIT_CHECK: 'Skip Limit Check Rule',
        COMMENTS: 'Comments',
        AGREE_WITH_AFE_DETAILS: 'Agree with the AFE proposal details?',
        ASK_MORE_DETAIL_TO: 'Ask more detail to',
        REASSIGN_TO: 'Reassign to',
        DELEGATE_TO: 'Delegate to',
        REPRESENTATIVE: 'Representative',
        USER: 'User',
        AGGREGATE_LIMIT_REMAINING: 'Aggregate Limit Sharing Balance',
        PARENT_ENTITY: 'Parent Entity',
        CHILD_ENTITY: 'Child Entity',
        SOURCE_ENTITY: 'Source Entity',
        LINK_NEW_ENTITY: 'Link New Entity',
        SHARED_ENTITY: 'Shared Entity',
        BALANCE_LEFT: 'Balance Left',
        TOTAL_DEDUCTION: 'Total Utilization',
        DISCARD_AFE: 'Discard AFE',
        APPROVAL_ACTION_DETAILS: 'Approval Action Details',
        ACTION: 'Action',
        ACTION_BY: 'Action By',
        ACTION_ON: 'Action On',
        CREATED_BY: 'Created By',
        LAST_UPADTED_ON: 'Last Updated At',
        CREATED_ON: 'Created At',
        STATUS: 'Status',
        CLONE_OVERRIDEN_WORKFLOW: 'Clone All Overriden Workflow Setting With Associated Steps.',
        CLONE_CHILD_LIMIT_SHARE: 'Clone All Child Limit Share.',
        CLONE_BUCKET_LIMIT: 'Clone All Bucket Limit Share.',
        CLONE_SELECTED_STEP: 'Clone All Selected Workflow Steps.',
        COMPANY_CODE: 'Company Code',
        COMPANY_FUSSION_NUMBER: 'Company (Fusion Number)',
        FUSSION_NUMBER: 'Fusion Number',
        COMPANY_NAME: 'Company Name',
        COST_CENTER_CODE: 'Cost Center Code',
        CODE: 'Code',
        NUMBER: 'Number',
        SECTION_HEAD: 'Section Head',
        DEPARTMENT_HEAD: 'Department Head',
        IS_COST_CENTER_OPERATING: 'Is cost center operating?',
        NAME: 'Name',
        SELECT_AFE_REQUEST_TYPES: 'Select AFE request types',
        NATURAL_ACCOUNT_NUMBER: 'Natural account number',
        ADD_NATURAL_ACCOUNT_NUMBER: 'Add Natural Account Number',
        ADD_ANALYSIS_CODE: 'Add Analysis Code',
        SETUP_COST_CENTER: 'Setup a Cost Center',
        SETUP_COMPANY: 'Setup a Company',
        ENTITY_NAME: 'Entity Name',
        ENTITY_CODE: 'Entity Code',
        PROPOSAL_TYPE: 'Proposal Type',
        BUDGET_TYPE: 'Budget Type',
        TOTAL_AMOUNT: 'Total Amount',
        SUBMISSION_DATE_FROM: 'Submission Date From',
        SUBMISSION_DATE_TO: 'Submission Date To',
        APPROVAL_DATE_FROM: 'Approval Date From',
        APPROVAL_DATE_TO: 'Approval Date To',
        VACATION_DATE_FROM: 'Delegation Date From',
        VACATION_DATE_TO: 'Delegation Date To',
        REPRESENTATIVE_DATE_FROM: 'Representative Date From',
        REPRESENTATIVE_DATE_TO: 'Representative Date To',
        DELEGATE_FOR: 'Delegate For',
        DELEGATE_USER: 'Delegate For User',
        REPRESENTATIVE_USER: 'Representative For User',
        PROJECT_COMPONENTS: 'Project Components',
        AFE_REFERENCE_NUMBER: 'AFE Reference Number',
        REQUEST_TYPES: 'Request Types',
        FROM_TOTAL_AMOUNT: 'Total Amount From',
        TO_TOTAL_AMOUNT: 'Total Amount To',
        FROM_EXPENSE_AMOUNT: 'Expense Amount From',
        TO_EXPENSE_AMOUNT: 'Expense Amount To',
        COMPANY: 'Company',
        ACCOUNT: 'Account',
        INTERCOMPANY: 'Intercompany',
        SERVICE: 'Service',
        ANALYSIS: 'Analysis',
        FUTURE_USE_1: 'Future Use 1',
        FUTURE_USE_2: 'Future Use 2',
        SUBMISSION_YEAR: 'Submission Year',
        SUBMIT_FOR_YEAR: 'Submit for Year',
        AFE_TYPE: 'AFE Type',
        SUBMITTED_BY: 'Submitted By',
        UPDATE_AFE_DETAIL: 'Update AFE Detail',
        ADD_NEW_READER: 'Add New Reader',
        UPDATE_APPROVERS_LIST: 'Update Approvers List',
        NO_INFO_FOUND: 'No information found',
        SUPPLEMENTAL_AMOUNT_DIFFERENCE: 'Supplemental Amount Difference',
        AGGREGATE_LIMIT_BALANCE: 'Available Aggregate Limit',
        REPRESENTATIVE_FOR: 'Representative For',
        ASSIGNED_DATE_FROM: 'Assigned Date From',
        ASSIGNED_DATE_TO: 'Assigned Date To',
        FUSION_INTEGRATION_ENABLED_FOR: 'Fusion Integration Enabled For',
        SETTING_NEW_FFO: 'Does this relate to the setting up of a new Freight Forwarding Office?',
        OVERRIDE_WORKFLOW: 'Override Workflow',
        OVERRIDE_FOR: 'Select entity for which you want to override.',
        OVERRIDE_FROM: 'Select workflow from where you want to override.',
        SECTION_VIEW: 'Section View',
        TABULAR_VIEW: 'Tabular View',
        OVERRIDDENS: 'Overridens',
        REFERENCE_STEP: 'Reference Step',
        SOURCE_LOGIN_ID: 'Source Login ID',
        TARGET_LOGIN_ID: 'Target Login ID',
        SOURCE_LOGIN_ID_INFO: 'Id is case sensitive so use both forms of ids (all small letter and original form) to update all possible places.<br/> All Small Letter - <EMAIL> & Original Form - <EMAIL>'
      },
      PLACEHOLDER: {
        ENTER: 'Enter',
        SEARCH: 'Search',
        SELECT: 'Select',
        SCOPE_CHANGE: 'Enter reason for scope change',
        REASON: 'Enter reason',
        ANSWER: 'Enter your answer',
        COMMENTS: 'Enter your comments',
        SEARCH_FILTER: 'Type to search'
      },
      VALIDATION: {
        INVALID: '{{name}} is not valid',
        REQUIRED: '{{name}} is required',
        MIN_LENGTH: '{{name}} minimum length is {{min}}',
        MAX_LENGTH_REQUIRED: 'Max length allowed is {{max}}',
        LENGTH_ERROR: '{{name}} length must be {{length}}.',
        NUMERIC_LENGTH_ERROR: 'Only numeric of length {{min}} is allowed.',
        REQUIRED_FIELD: 'Field is required',
        NAME_ALREADY_EXIST: 'Name already exist.',
        FILE_TYPE_ERROR: 'Invalid file selected',
        TO_FROM_DATE_ERROR: 'To date must be greater than or equal to from date.',
        INVALID_FIELD: 'Field is not valid',
        ACTION_REQUIRED: 'Action Required',
        MIN_AMOUNT: 'Amount should be greater than 0',
        MIN_OR_EQUAL_AMOUNT: 'Amount should be greater than or equal to {{minAmount}}',
        MAX_AMOUNT: 'Amount should be less than 999999999999999',
        MIN_LOC: 'Length of Commitment should be greater than 0',
        MIN_PROJECT_COMPONENT: 'One project component amount is compulsory',
        TOTAL_SUM_AMOUNT: 'Sum of total amount should be equal to {{amount}}',
        TOTAL_BUDGETED_SUM_AMOUNT: 'Sum of total budgeted amount should be equal to {{amount}}',
        TOTAL_UNBUDGETED_SUM_AMOUNT: 'Sum of total unbudgeted amount should be equal to {{amount}}',
        TOTAL_BUDGET_REF_AMOUNT: 'Sum of total budget reference number amount should be equal to {{amount}}',
        NO_MATCHING_USER_FOUND: 'No user found.',
        NO_MATCHING_AFE_FOUND: 'No afe found.',
        NO_ATTACHMENTS_FOUND: 'No attachement found.',
        NO_DOCUMENTS_FOUND: 'No documents found.',
        NO_DOCUMENTS_ADDED: 'No documents added.',
        NO_INTERCOMPANY: 'No Intercompany found.',
        NO_ANALYSIS_CODE: 'No Analysis Code found.',
        NO_NATURAL_ACCOUNT: 'No Natural Account found.',
        NO_ROLE_FOUND: 'No Role found.',
        NO_WORKFLOW_SETTING: 'No workflow found.',
        NO_COST_CENTER: 'No Cost Center found',
        NO_SECTION: 'No Section found',
        NO_LOCATION: 'No Location found',
        COMBINATION_AVAILABLE: 'This combination already added',
        ADD_COMBINATION_ERROR: 'Click on Add New Combination.',
      }
    },
    REQUEST_TYPE: {
      CAPEX: 'Capex',
      CAPEX_REQUEST: 'Capex Request',
      OPEX: 'Opex',
      OPEX_REQUEST: 'Opex Request',
      EQUITY_INFUSION: 'Equity Infusion',
      RECEIVABLE_WRITE_OFFS: 'Receivable Write Offs / Waivers and Settlements of Claims',
      SALES_WRITE_OFF: 'Sales/Write Off - Tangible Assets',
      SALE_BUSINESS: 'Sale of Business',
      SALES_WRITE_OFF_MV_GT_NBV: 'Sale/Write Off - Tangible Assets (Market Value > Net Book Value)',
      SALES_WRITE_OFF_MV_LT_NBV: 'Sale/Write Off - Tangible Assets (Market Value <= Net Book Value)',
      DISPOSAL: 'Disposal'

    },
    WORKFLOW: {
      LABEL: {
        TITLE: 'Title',
        RULE: 'Rule',
        CREATED_BY: 'Created By',
        LAST_UPADTED_ON: 'Last Updated At',
        CREATED_ON: 'Created At',
        STATUS: 'Status',
      },
      MENUE: {
        RULE_ADD_TITLE: 'Add Rule',
        RULE_UPDATE_TITLE: 'Update Rule',
        RULE_VIEW_TITLE: 'Rule Detail',
        RULE_EDITOR_TITLE: 'Manage Rule',
      },
      RULE: {
        QUERY_BUILDER: {
          ADD_RULE: 'Add Rule',
          ADD_RULE_SET: 'Add Rule Set',
          REMOVE_RULE_SET: 'Remove Rule Set'
        },
        LIST: {
          ID: 'Id',
          TITLE: 'Title',
          CREATED_AT: 'Created At',
          UPDATED_AT: 'Updated At',
          ACTIVE: 'Active'
        },
        BUTTON: {
          ADD_RULE: 'Add Rule',
          EDIT: 'Edit',
          DELETE: 'Delete',
          CANCEL: 'Cancel'
        },
        FORM: {
          RULE_TITLE: 'Rule Title',
          RULE_DESCRIPTION: 'Rule Description',
          RULE_TITLE_PLACEHOLDER: 'Enter rule title',
          RULE_DESCRIPTION_PLACEHOLDER: 'Enter description',
          RULE_CONDITION_LABEL: 'Condition Expression'
        },
        ERROR: {
          TITLE_ERROR: 'Please enter a title.'
        }
      }
    },
    SWAL: {
      SOMETHING_WENT_WRONG: 'Something went wrong!',
      DOWNLAOD_SOMETHING_WENT_WRONG: 'Something went wrong while downloading!',
      SUCCESS: 'Success',
      ERROR: 'Error',
      OOPS: 'Oops...',
      SELECT_AT_LEAST_ONE_COLUMN: 'Please select at least one column',
      SERVICE_UNAVAILABLE: 'Service Unavailable',
      SITE_UNDERMAINTAINANCE: 'Sorry for the inconvenience but we’re performing some maintenance at the moment. We’ll be back online shortly!',
      RULE_ADDED: 'Rule has been added successfully!',
      RULE_UPDATED: 'Rule has been updated successfully!',
      ERROR_UPDATED: 'Unable to update a workflow rule.',
      ERROR_HISTORY_FETCH: 'Unable to fetch history.',
      RULE_ERROR: 'Unable to create a new workflow rule.',
      CONFIRMATION: 'Are you sure?',
      RULE_DELETE_CONFIRM: 'You want to delete the rule!',
      DELETE_BUTTON: 'Yes, Delete!',
      DEACTIVATE_BUTTON: 'Yes, Deactivate!',
      RULE_DELETE_TITLE: 'Rule Deleted',
      RULE_DELETE_SUCCESS: 'Rule has been deleted successfully!',
      WORKFLOW_ADD_TITLE: 'Workflow Added',
      WORKFLOW_ADD_SUCCESS: 'Master workflow successfully added!',
      ROLE_FETCH_ERROR: 'A problem occurred while fetching the roles!',
      RULE_FETCH_ERROR: 'A problem occurred while fetching the rules!',
      STEP_ADD_TITLE: 'Workflow Step Added',
      STEP_ADD_SUCCESS: 'Workflow step successfully added!',
      STEP_UPDATE_TITLE: 'Workflow Step Updated',
      STEP_UPDATE_SUCCESS: 'Workflow step successfully updated!',
      NO_DATA_CHANGE: 'The data has not changed since the last update.',
      PUBLISH_OVERRIDDEN_CONFIRM: 'You want to publish this workflow!',
      PUBLISH_MASTER_CONFIRM: 'The publishing of this workflow will lead to the publishing of all overridden workflows as well!',
      PUBLISH_NEW_VERSION_CONFIRM: 'You want to publish this new version!',
      PUBLISH_BUTTON: 'Yes, Publish!',
      UNPUBLISH_BUTTON: 'Yes, Unublish!',
      PUBLISH_SUCCESS_TITLE: 'Workflow Published',
      UNPUBLISH_SUCCESS_TITLE: 'Workflow Unpublished',
      NEW_VERSION_SUCCESS_TITLE: 'New Version Created',
      PUBLISH_SUCCESS: 'Workflow has been published successfully!',
      UNPUBLISH_SUCCESS: 'Workflow has been unpublished successfully!',
      NEW_VERSION_SUCCESS: 'New version has been created successfully!',
      UNPUBLISH_CONFIRM: "You want to unpublish this workflow!",
      NEW_VERSION_CONFIRM: "You want to create a new version of this workflow!",
      UNPUBLISH_MASTER_CONFIRM: "The unpublishing of this workflow will lead to the unpublishing of all overridden workflows as well!",
      NEW_VERSION_MASTER_CONFIRM: "You want to create a new version of the master workflow, along with all overrides!",
      UNPUBLISH_MASTER_SUCCESS: 'A successful unpublishing of the master and all overridden workflows has been accomplished!',
      NEW_VERSION_MASTER_SUCCESS: 'A successful creation of new version of the master and all overridden workflows has been accomplished!',
      WORKFLOW_OVERRIDE_TITLE: 'Workflow Override',
      WORKFLOW_OVERRIDE_SUCCESS: 'Workflow has been successfully override!',
      WORKFLOW_DELETE_WARNING_MASTER: 'Deleting this workflow will lead to the deletion of all overridden workflows as well!',
      WORKFLOW_DELETE_SUCCESS_MASTER: 'A successful deletion of the master and all overridden workflows has been accomplished!',
      WORKFLOW_OVERIDE_DELETE_WARNING: 'You want to delete this overriden workflow.',
      WORKFLOW_OVERIDE_DELETE_SUCCESS: 'Workflow has been deleted successfully.',
      ALL_OVERRIDDEN_DELETE_WARNING: 'There will be no changes to the main master workflow when you delete all the overridden workflows.',
      ALL_OVERRIDDEN_DELETE_SUCCESS: 'All overriden workflow deleted successfully.',
      WORKFLOW_DELETE_MESSAGE: 'Workflow has been deleted successfully!',
      LIMIT_SHARING_TITLE: 'Limit Sharing',
      LIMIT_SHARING_SUCCESS: 'Limit sharing has been updated successfully!',
      STEP_DELETE_WARNING_OVERRIDE: 'You want to delete this step.',
      STEP_DELETE_WARNING_MASTER: 'Deleting this step will lead to the deletion from all overridden steps as well!',
      STEP_DELETE_TITLE: 'Step Deleted',
      WORKFLOW_DELETE_TITLE: 'Workflow Deleted',
      STEP_DELETE_SUCCESS: 'Step has been deleted successfully.',
      DRAFT_DELETE_CONFIRMATION: "You want to delete the draft!",
      DRAFT_DELETE_TITLE: 'Draft Deleted',
      DRAFT_DELETE_SUCCESS: 'Draft has been deleted successfully!',
      APPROVER_LIST_FETCH_ERROR: 'A problem occurred while fetching the approval list.',
      CONFIRM: 'Confirmation',
      SAVE_DISCARD: "Do you want to save or discard your changes before navigating away?",
      AFE_DRAFTED: 'AFE Drafted',
      AFE_DRAFT_SUCCESS: 'Draft has been saved successfully!',
      PROPOSAL_SUBMITTED: 'Proposal Submitted Successfully!',
      SUBMIT_AFE_ERROR: 'Unable to submit the request, please try again',
      STEP_MOVEMENT_TITLE: 'Step Moved',
      STEP_MOVEMENT_SUCCESS: 'Step position has been changed successfully',
      STEP_COPY_SUCCESS_TITLE: 'Step Copied',
      STEP_COPY_SUCCESS_DESCRIPTION: 'Step has been copied successfully to selected workflows',
      STEP_DELETE_SUCCESS_TITLE: 'Step Deleted',
      STEP_DELETE_SUCCESS_DESCRIPTION: 'Step has been deleted successfully to selected workflows',
      TASK_ACTION_SUCCESS: 'Approval action has been completed successfully.',
      TASK_ACTION_ERROR: 'Unable to perform approval action.',
      DISCARD_ACTION_ERROR: 'Unable to discard.',
      LIMIT_SHARED_TITLE: 'Limit Shared',
      LIMIT_SHARED_SUCCESS: 'Limit has been shared successfully.',
      LIMIT_INVOKED_SUCCESS: 'Limit has been successfully invoked.',
      LIMIT_INVOKED_TITLE: 'Limit Invoked.',
      INVOKE_CONFIRMATION: 'You want to invoke the limit.',
      LIMIT_SHARED_UPDATE_TITLE: 'Limit Updated.',
      LIMIT_SHARED_UPDATE_SUCCESS: 'Limit has been updated successfully.',
      SAME_BUCKET_ERROR: 'Source & Shared Entity can\'t be same.',
      BUCKET_SHARED_TITLE: 'New Entity Linked.',
      BUCKET_SHARED_SUCCESS: 'Entity has been linked successfully.',
      DELETE_BUCKET_SHARED_TITLE: 'Bucket Entity Deleted.',
      DELETE_BUCKET_SHARED_SUCCESS: 'Bucket entity has been deleted successfully.',
      NO_USER_TITLE: 'No Approver',
      NO_APPROVER_DESC: 'No Approver found to approve this proposal',
      STEP_CLONE_TITLE: 'Workflow Step Clonned',
      STEP_CLONE_TITLE_SUCCESS: 'Workflow steps has been clonned successfully!',
      ADD_NEW_COMPANY_CODE_SUCCESS: 'Company has been created successfully.',
      FUSION_INTEGRATION_UPDATE_SUCCESS: 'Fusion integration has been updated successfully.',
      FUSION_INTEGRATION_UPDATE_ERROR: 'Unable to update fusion integration.',
      ADD_NEW_COMPANY_CODE_ERROR: 'Unable to add a company.',
      DEACTIVATE_COMPANY_CODE_ERROR: 'Unable to deactivate company.',
      UPDATE_COMPANY_CODE_SUCCESS: 'Company has been updated successfully.',
      UPDATE_COMPANY_CODE_ERROR: 'Unable to update a company.',
      COMPANY_CODE_ACTIVE_CONFIRM: 'Do you want to enable this company?',
      COMPANY_CODE_INACTIVE_CONFIRM: 'Do you want to deactivate this company?',
      MARK_COMPANY_CODE_ACTIVE: 'Company has been activated successfully.',
      MARK_COMPANY_CODE_INACTIVE: 'Company has been deactivated successfully.',
      COST_CENTER_DELETE_CONFIRM: 'Do you want to delete the cost center?',
      ANALYSIS_CODE_DELETE_CONFIRM: 'Do you want to delete the analysis code?',
      VACATION_DELETE_CONFIRM: 'Do you want to remove this delegation?',
      REMINDER_EMAIL_CONFIRM: 'Do you want to remind the user?',
      UPDATE_LOGIN_CONFIRM: 'You want to change {{sourceLoginId}} to {{targetLoginId}} throughout the application?',
      REPRESENTATIVE_DELETE_CONFIRM: 'Do you want to remove the representative?',
      NATURAL_ACCOUNT_NUMBER_DELETE_CONFIRM: 'Do you want to delete the natural account number?',
      ADD_COST_CENTER_CODE_SUCCESS: 'Cost center has been created successfully.',
      ADD_COST_CENTER_CODE_ERROR: 'Unable to create a cost center.',
      COST_CENTER_DELETE_TITLE: 'Cost Center Deleted',
      ANALYSIS_CODE_DELETE_TITLE: 'Analysis Code Deleted',
      NATURAL_ACCOUNT_NUMBER_DELETE_TITLE: 'Natural Account Number Deleted',
      COST_CENTER_DELETE_SUCCESS: 'Cost center has been deleted successfully.',
      ANALYSIS_CODE_DELETE_SUCCESS: 'Analysis code has been deleted successfully.',
      NATURAL_ACCOUNT_NUMBER_DELETE_SUCCESS: 'Natural account number has been deleted successfully.',
      ADD_ANALYSIS_CODE_SUCCESS: 'Analysis code has been created successfully.',
      ADD_ANALYSIS_CODE_ERROR: 'Unable to create a analysis code.',
      UPDATE_COST_CENTER_CODE_SUCCESS: 'Cost center has been updated successfully.',
      UPDATE_COST_CENTER_CODE_ERROR: 'Unable to update the cost center.',
      UPDATE_ANALYSIS_CODE_SUCCESS: 'Analysis code has been updated successfully.',
      IMPORT_ANALYSIS_CODE_SUCCESS: 'Analysis codes has been imported successfully.',
      IMPORT_NATURAL_ACCOUNT_SUCCESS: 'Natural account numbers has been imported successfully.',
      IMPORT_COST_CENTER_SUCCESS: 'Cost centers has been imported successfully.',
      UPDATE_ANALYSIS_CODE_ERROR: 'Unable to update the analysis code.',
      UPDATE_NATURAL_ACCOUNT_NUMBER_SUCCESS: 'Natural account number has been updated successfully.',
      UPDATE_NATURAL_ACCOUNT_NUMBER_ERROR: 'Unable to update natural account number.',
      ADD_NATURAL_ACCOUNT_NUMBER_SUCCESS: 'Natural account number has been created successfully.',
      ADD_NATURAL_ACCOUNT_NUMBER_ERROR: 'Unable to create natural account number.',
      ADD_DELEGATION_SUCCESS_TITLE: 'Delegation Added',
      ADD_DELEGATION_SUCCESS_DESCRIPTION: 'New delegation has been added successfully',
      DELETE_DELEGATION_SUCCESS_TITLE: 'Delegation Deleted',
      DELETE_DELEGATION_SUCCESS_DESCRIPTION: 'Delegation has been deleted successfully',
      UPDATE_DELEGATION_SUCCESS_TITLE: 'Delegation Updated',
      UPDATE_DELEGATION_SUCCESS_DESCRIPTION: 'Delegation has been updated successfully',
      UPDATE_AFE_DETAIL_SUCCESS: 'AFE detail has been updated successfully',
      ADD_REPRESENTATIVE_SUCCESS_TITLE: 'Representative Added',
      ADD_REPRESENTATIVE_SUCCESS_DESCRIPTION: 'Representative has been added successfully',
      DELETE_REPRESENTATIVE_SUCCESS_TITLE: 'Representative Deleted',
      DELETE_REPRESENTATIVE_SUCCESS_DESCRIPTION: 'Representative has been deleted successfully',
      UPDATE_REPRESENTATIVE_SUCCESS_TITLE: 'Representative Updated',
      UPDATE_REPRESENTATIVE_SUCCESS_DESCRIPTION: 'Representative has been updated successfully',
      UPDATE_APPROVERS_LIST_SUCCESS: 'Approvers list has been updated successfully',
      UPDATE_READERS_SUCCESS: 'New Reader has been added successfully',
      UPLOAD_EVIDENCE_SUCCESS: 'Evidence has been uploaded successfully',
      APPROVER_UPDATED_SUCCESS: 'User has been updated successfully',
      SEND_REMINDER_TITLE: 'Reminder Sent',
      SEND_REMINDER_DESCRIPTION: 'Reminder has been sent successfully',
      DELETE_SELECTED_WORKFLOW_CONFIRMATION: 'You want to delete all the selected workflow?',
      LOGIN_ID_UPDATE_TITLE: 'Login ID Updated',
      LOGIN_ID_UPDATE_SUCCESS: 'Login Id has been successfully updated!',
    },
    BADGES_TITLE: {
      BUSINESS_ENTITIES: 'Business Entities',
      PROJECTS: 'Projects',
      BUDGET_TYPES: 'Budget Types',
      REQUEST_TYPES: 'Request Types',
      STATUES: 'Status',
      REFERENCE_NUMBER: 'Reference Number',
      FROM_AMOUNT: 'From Amont',
      TO_AMOUNT: 'To Amount',
      PROJECT_NAME: 'Project Name',
      FROM_SUBMISSION_DATE: 'From Submission Date',
      TO_SUBMISSION_DATE: 'To Submission Date',
      FROM_APPROVAL_DATE: 'From Approval Date',
      TO_APPROVAL_DATE: 'To Approval Date',
      SUBMISSION_TYPES: 'Submission Types',
      AFE_TYPES: 'AFE Types',
      SUBMITTED_BY: 'Submitted By',
      IS_PUBLISHED: 'Is Published',
      REPRESENTATIVE_FOR: 'Representative For',
      ASSIGNED_DATE_FROM: 'Assigned Date From',
      ASSIGNED_DATE_TO: 'Assigned Date To',
      COST_CENTERS: 'Çost Centers',
      LOCATIONS: 'Locations',
    },
    ACTION_ALERT: {
      SEND_BACK: 'Please note that when you use the "send back" option, the system assumes that you are asking the submitter to change the details (e.g. amount, proposal type etc). Doing so will result for the approval chain to restart from the beginning. If you intend to ask for more information only, please use "More Details" instead.',
      MORE_DETAIL: 'Please note that when you use the "More Detail" option, the system assumes that you are asking more information from the submitter or any other user. In this case the user can only provide required information to the existing proposals and once information is submitted by that user AFE will return back to you again',
      REASSIGNE: 'Please note that when you use the "Reassign" option, the system assumes that you are assigning this AFE to any other user to take action and once it is approved by reassigned user AFE will return back to you again.',
      DELEGATE: 'Please note that when you use the "Delegate" option, the system assumes that you are delegating this AFE to any other user to take action with all the same authority as you and once it is approved by delegated user AFE will not return back to you again.'
      
    }
  }
  
};
