<div *ngIf="selectedCompanyCode; else noDataMessage" class="card mb-5 mb-xl-8">
  <!-- begin::Header -->
  <div class="card-header border-0 pt-5">
    <h3 class="card-title align-items-start flex-column">
      <span class="card-label fw-bolder fs-3 mb-1">{{
        "FORM.LABEL.COMPANY" | translate
      }}</span>
    </h3>

    <div class="row">
      <div class="d-flex justify-content-end">
        <div class="menu-item px-3">
          <button
            (click)="openEvidenceModel()"
            class="btn btn-sm btn-primary fw-bold btnRounded px-6 py-2 mx-1"
          >
            <span translate="FORM.BUTTON.UPLOAD_EVIDENCE_BUTTON"></span>
          </button>
        </div>
        <button
          appToggleProfileMenu
          [transform]="'-30px, 50.5px, 0px'"
          type="button"
          class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary"
        >
          <img
            width="45px"
            src="./assets/media/svg/icons/dpw-icons/menu.png"
            alt="next"
          />
        </button>
        <span
          class="fs-6 fw-bold menu menu-column menu-gray-600 menu-rounded menu-state-bg menu-state-primary menu-sub menu-sub-dropdown py-4 w-275px"
        >
          <div class="menu-item px-3">
            <div
              class="menu-content fs-6 text-dark fw-bolder px-3 py-4"
              translate="MENU.QUICK_ACTION"
            ></div>
          </div>

          <div class="separator mb-3 opacity-75"></div>

          <div class="menu-item px-3 mb-2">
            <a
              title="{{ 'FORM.BUTTON.DEACTIVATE' | translate }}"
              (click)="deactivateCompanyCode()"
              class="menu-link px-3 cursor-pointer"
            >
              {{ "FORM.BUTTON.DEACTIVATE" | translate }}
              {{ "FORM.LABEL.COMPANY" | translate }}
            </a>
          </div>

          <div class="menu-item px-3">
            <a
              title="{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}"
              (click)="openHistoryModel()"
              class="menu-link px-3 cursor-pointer"
              translate="{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}"
            >
            </a>
          </div>
        </span>
      </div>
    </div>
  </div>
  <!-- end::Header -->
  <!-- begin::Body -->
  <div class="card-body py-3">
    <div class="row">
      <div class="col-lg-3 col-sm-6 mb-4 mb-lg-5">
        <label
          class="fw-bold text-muted"
          translate="FORM.LABEL.COMPANY_FUSSION_NUMBER"
        ></label>
        <div class="h4 text-gray-800">
          <div>
            {{ selectedCompanyCode.code }}
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-sm-6 mb-4 mb-lg-5">
        <label
          class="fw-bold text-muted"
          translate="FORM.LABEL.COMPANY_NAME"
        ></label>
        <div class="h4 text-gray-800">
          <div>
            {{ selectedCompanyCode.name }}
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-sm-6 mb-4 mb-lg-5">
        <label
          class="fw-bold text-muted"
          translate="FORM.LABEL.ENTITY_NAME"
        ></label>
        <div class="h4 text-gray-800">
          <div>
            {{ selectedEntity.entityName }}
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-sm-6 mb-4 mb-lg-5">
        <label
          class="fw-bold text-muted"
          translate="FORM.LABEL.ENTITY_CODE"
        ></label>
        <div class="h4 text-gray-800">
          <div>
            {{ selectedCompanyCode.entityCode }}
          </div>
        </div>
      </div>

      <div class="col-lg-6 col-sm-6 mb-4 mb-lg-5">
        <label
          class="fw-bold text-muted"
          translate="FORM.LABEL.FUSION_INTEGRATION_ENABLED_FOR"
        ></label>
        <button
          (click)="openFusionIntegrationModal()"
          type="button"
          class="btn btn-sm mb-2 editBtn btnRounded fw-bold ps-4 pe-4 py-1 mx-2"
          translate="FORM.BUTTON.EDIT_BUTTON"
        ></button>
        <div class="h4 text-gray-800">
          <div *ngIf="fusionIntegrationEnableFor.length; else disable">
            <ng-container
              *ngFor="let requestType of fusionIntegrationEnableFor"
            >
              <span class="badge badge-success badge-space">
                {{ requestType }}
              </span>
            </ng-container>
          </div>
          <ng-template #disable>
            <div>
              <span class="badge badge-danger"> Disabled </span>
            </div>
          </ng-template>
        </div>
      </div>

      <div class="col-lg-6 col-sm-6 mb-4 mb-lg-5">
        <!-- Toggle Button to enable disable natural account -->
        <div>
          <label
            class="fw-bold text-muted"
            translate="FORM.LABEL.NATURAL_ACCOUNT_CONFIGURATION"
          ></label>

          <div class="fw-bold mt-2">
            <!-- {{ "FORM.LABEL.SINGLE_NATURAL_ACCOUNT" | translate }} -->
            <div class="form-switch form-check mt-2">
              <input
                (click)="updateMultiNaturalAccount()"
                [(ngModel)]="enableMultiNatualAccount"
                [value]="enableMultiNatualAccount"
                type="checkbox"
                class="form-check-input"
                id="site_state"
              />
            </div>
            <!-- <label
              for="site_state"
              class="form-check-label"
              translate="FORM.LABEL.MULTIPLE_NATURAL_ACCOUNTS"
            ></label> -->
          </div>
        </div>
      </div>
    </div>
    <!-- begin::Body -->
  </div>
</div>
<ng-template #noDataMessage>
  <app-empty-state
    [message]="
      'EMPTY_STATE.EMPTY_COMPANY_CODE_LIST'
        | translate : { entityName: selectedEntity.entityName }
    "
    [showImage]="true"
  >
    <div class="history d-flex justify-content-end">
      <div class="outline-btn-light">
        <button
          type="button"
          title="{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}"
          (click)="openHistoryModel()"
          class="btn btn-sm outline-btn-light mx-2"
          translate="{{ 'FORM.BUTTON.HISTORY_BUTTON' | translate }}"
        ></button>
      </div>
    </div>

    <span class="action position-relative d-inline-block text-danger">
      <a
        (click)="openCompanyCodeEditorModal()"
        class="text-danger opacity-75-hover cursor-pointer"
      >
        {{ "FORM.LABEL.SETUP_COMPANY" | translate }}
      </a>
      <span
        class="position-absolute opacity-15 bottom-0 start-0 border-4 border-danger border-bottom w-100"
      >
      </span>
    </span>
  </app-empty-state>
</ng-template>

<app-modal #companyCodeEditorModal [modalConfig]="companyCodeEditorModalConfig">
  <div class="w-100 px-5 bg-body rounded" [formGroup]="companyCodeFormGroup">
    <div class="row">
      <div class="col-lg-6 col-md-6 col-6 mb-6 mb-lg-5">
        <label class="fw-bold text-muted" translate="LIST.ENTITY_NAME"></label>
        <div class="h4 text-gray-800">
          {{ selectedEntity.entityName }}
        </div>
      </div>
      <div class="col-lg-6 col-md-6 col-6 mb-6 mb-lg-5">
        <label class="fw-bold text-muted" translate="LIST.ENTITY_CODE"></label>
        <div class="h4 text-gray-800">
          {{ selectedEntity.entityCode }}
        </div>
      </div>
    </div>
    <div class="separator my-2"></div>
    <div class="row">
      <div class="col-md-12 mb-5">
        <label class="form-label required">
          {{ "FORM.PLACEHOLDER.ENTER" | translate }}
          {{ "FORM.LABEL.COMPANY_FUSSION_NUMBER" | translate }}
        </label>
        <input
          name="companyCode"
          placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
            'FORM.LABEL.COMPANY_FUSSION_NUMBER' | translate | lowercase
          }}"
          class="form-control form-control-lg form-control-solid"
          formControlName="companyCode"
          pattern="[a-zA-Z0-9]{4}"
        />
        <ng-container *ngIf="isRequiredError('companyCode')">
          <app-input-error-message
            errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
          >
          </app-input-error-message>
        </ng-container>
        <ng-container *ngIf="isPatternError('companyCode')">
          <app-input-error-message
            errorMessage="{{
              'FORM.VALIDATION.LENGTH_ERROR'
                | translate
                  : { name: 'FORM.LABEL.FUSSION_NUMBER' | translate, length: 4 }
            }}"
          >
          </app-input-error-message>
        </ng-container>
      </div>

      <div class="col-md-12 mb-5">
        <label class="form-label required">
          {{ "FORM.PLACEHOLDER.ENTER" | translate }}
          {{ "FORM.LABEL.COMPANY_NAME" | translate }}
        </label>
        <input
          name="companyName"
          placeholder="{{ 'FORM.PLACEHOLDER.ENTER' | translate }} {{
            'FORM.LABEL.COMPANY_NAME' | translate | lowercase
          }}"
          class="form-control form-control-lg form-control-solid"
          formControlName="companyName"
        />
        <ng-container *ngIf="isRequiredError('companyName')">
          <app-input-error-message
            errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
          >
          </app-input-error-message>
        </ng-container>
      </div>
    </div>
  </div>
</app-modal>

<app-modal #historyModal [modalConfig]="historyModalConfig">
  <div class="w-100 px-1 bg-body rounded py-1">
    <app-history-logs
      [title]="'FORM.BUTTON.COMPANY_HISTORY_BUTTON' | translate"
      [historyLogs]="companyHistory"
    >
    </app-history-logs>
  </div>
</app-modal>

<app-modal #fusionIntegrationModal [modalConfig]="fusionIntegrationModalConfig">
  <ng-container>
    <div
      class="w-100 p-5 bg-body rounded"
      [formGroup]="fusionIntegrationFormGroup"
    >
      <div class="row p-1 p-lg-3 p-md-3 p-sm-3">
        <div class="col-12 col-md-12 p-0 m-0">
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              value=""
              formControlName="selectAll"
            />
            <label class="form-label fw-bold"> Select/Deselect all </label>
          </div>
        </div>
      </div>
      <div class="row p-1 p-lg-3 p-md-3 p-sm-3">
        <div
          class="col-6 p-0 m-0"
          formArrayName="requestTypes"
          *ngFor="let col of getRequestTypesControls(); let i = index"
        >
          <div class="form-check mt-3">
            <input
              class="form-check-input"
              type="checkbox"
              [formControlName]="i"
            />
            <label class="form-label fw-bold">
              {{ fusionIntegrationColumns[i].col }}
            </label>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</app-modal>

<ng-container>
  <app-modal #uploadEvidence [modalConfig]="uploadEvidenceConfig">
    <div *ngIf="evidenceFormReady">
      <div class="row">
        <div class="fv-row mb-5 mb-lg-7">
          <app-upload-attachment
            [isBoxShadow]="true"
            [labelHeader]="
              'AFE_MODULE.SECTION_TITLE.SUPPORTING_DOCUMENTS' | translate
            "
            [labelSubHeader]="'COMMON.ATTACHMENTS' | translate"
            [required]="false"
            [isShowHeader]="true"
            [isDescriptionRequired]="false"
            [isAdd]="true"
            [attachments]="evidenceDocuments"
            (attachmentDelete)="onAttachmentDelete($event)"
            (attachmentEdited)="onAttachmentEdited($event)"
            [isViewOnly]="false"
          >
          </app-upload-attachment>
          <ng-container *ngIf="evidenceError">
            <app-input-error-message
              errorMessage="{{ 'FORM.VALIDATION.REQUIRED_FIELD' | translate }}"
            >
            </app-input-error-message>
          </ng-container>
        </div>
      </div>
    </div>
  </app-modal>
</ng-container>
