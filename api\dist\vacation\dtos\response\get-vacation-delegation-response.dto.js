"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetVacationDelegationResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class FilterParam {
}
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'requestTypeId' }),
    (0, class_transformer_1.Expose)({ name: 'requestTypeId' }),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], FilterParam.prototype, "request_type_id", void 0);
class AdditionalInfo {
}
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'filterParam' }),
    (0, class_transformer_1.Expose)({ name: 'filterParam' }),
    (0, class_transformer_1.Type)(() => FilterParam),
    __metadata("design:type", FilterParam)
], AdditionalInfo.prototype, "filter_param", void 0);
class GetVacationDelegationResponseDto {
    constructor(partial = {}) {
        Object.assign(this, partial);
    }
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetVacationDelegationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'delegateForUsername' }),
    __metadata("design:type", String)
], GetVacationDelegationResponseDto.prototype, "delegate_for_username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'delegateToUsername' }),
    __metadata("design:type", String)
], GetVacationDelegationResponseDto.prototype, "delegate_to_username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'delegateFromDate' }),
    __metadata("design:type", Date)
], GetVacationDelegationResponseDto.prototype, "delegate_from_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'delegateToDate' }),
    __metadata("design:type", Date)
], GetVacationDelegationResponseDto.prototype, "delegate_to_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'createdOn' }),
    __metadata("design:type", Date)
], GetVacationDelegationResponseDto.prototype, "created_on", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    __metadata("design:type", String)
], GetVacationDelegationResponseDto.prototype, "created_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'modifiedBy' }),
    __metadata("design:type", String)
], GetVacationDelegationResponseDto.prototype, "modified_by", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)({ name: 'modifiedOn' }),
    __metadata("design:type", Date)
], GetVacationDelegationResponseDto.prototype, "modified_on", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ name: 'additionalInfo', required: false, type: AdditionalInfo }),
    (0, class_transformer_1.Expose)({ name: 'additionalInfo' }),
    (0, class_transformer_1.Type)(() => AdditionalInfo),
    __metadata("design:type", AdditionalInfo)
], GetVacationDelegationResponseDto.prototype, "additional_info", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)({ name: 'businessEntityId' }),
    __metadata("design:type", Number)
], GetVacationDelegationResponseDto.prototype, "business_entity_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)({ name: 'delegateComments' }),
    __metadata("design:type", String)
], GetVacationDelegationResponseDto.prototype, "delegate_comments", void 0);
exports.GetVacationDelegationResponseDto = GetVacationDelegationResponseDto;
//# sourceMappingURL=get-vacation-delegation-response.dto.js.map