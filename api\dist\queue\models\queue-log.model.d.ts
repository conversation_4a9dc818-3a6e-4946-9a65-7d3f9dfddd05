import { QUEUE_LOG_ACTION } from 'src/shared/enums';
import { BaseModel } from 'src/shared/models';
import { QueueLogData } from 'src/shared/types';
export declare class QueueLog extends BaseModel<QueueLog> {
    entityId?: number | null;
    action?: QUEUE_LOG_ACTION | null;
    finalApproval: boolean;
    data: QueueLogData;
    processed: boolean;
    retryCount: number;
    errors?: string[] | null;
}
