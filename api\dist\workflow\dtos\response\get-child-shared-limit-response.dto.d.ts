import { GetMasterSettingResponseDTO } from './get-master-setting-response.dto';
import { GetMasterStepsResponseDTO } from './get-master-steps-response.dto';
export declare class GetChildSharedLimitResponseDTO {
    id: number;
    entityId: number;
    entityCode: string;
    entityTitle: string;
    singleLimit: number;
    aggregateLimit: number;
    createdOn: Date;
    updatedOn: Date;
    createdBy: string;
    updatedBy: string;
    constructor(partial?: Partial<GetChildSharedLimitResponseDTO>);
}
export declare class GetChildSharedLimitWithParentDetailDTO {
    title: string;
    associateLevel: string;
    associateRole: string;
    associateType: string;
    associatedColumn: string;
    associatedUser: string;
    singleLimit: number;
    aggregateLimit: number;
    projectComponentTitle: string;
    canShareLimitToChild: boolean;
    isMandatory: boolean;
    includeBelowSteps: boolean;
    workflowMasterSetting: GetMasterSettingResponseDTO;
    sharedLimitMasterStepChild: GetMasterStepsResponseDTO;
}
