import { APPROVAL_TYPE } from "@core/enums";

export interface TaskAdditionalInfo {
    proposalId: number;
    proposalProjectReferenceNumber: string;
    proposal_created_by: string;
    proposal_project_name: string;
    totalAmount: number;
}

export interface TaskDetailModel {
    id: number;
    tennantId: string;
    applicationId: string;
    businessEntityId?: number;
    entityType?: string;
    title?: string;
    details?: string;
    assignedTo?: string;
    isGroupAssignment?: boolean;
    dueDate?: Date;
    taskBaseUrl?: string;
    taskRelUrl?: string;
    taskStatus?: string;
    taskOutcome?: string;
    taskCompletionDate?: Date;
    taskCompletionComments?: string;
    additionalInfo: TaskAdditionalInfo;
    createdBy?: string;
    createdOn?: Date;
    modifiedBy?: string;
    modifiedOn?: Date;
    completedBy?: string;
}

