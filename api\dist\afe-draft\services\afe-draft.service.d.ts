import { MessageResponseDto } from 'src/shared/dtos';
import { CurrentContext } from 'src/shared/types';
import { DraftAfeRepository } from '../repositories/draft-afe-repository';
import { AfeDraftRequestDto, AfeDraftResponseDto, GetAfeDraftResponseDto, UpdateDraftRequestDto } from '../dtos';
import { AttachmentApiClient } from 'src/shared/clients/attachment-api.client';
import { SharedAttachmentService } from 'src/shared/services';
import { Pagination } from 'src/core/pagination';
export declare class AfeDraftService {
    private readonly draftAfeRepository;
    private attachmentApiClient;
    private sharedAttachmentService;
    constructor(draftAfeRepository: DraftAfeRepository, attachmentApiClient: AttachmentApiClient, sharedAttachmentService: SharedAttachmentService);
    getAllDraftsByActiveUser(currentContext: CurrentContext, limit?: number, page?: number): Promise<Pagination<GetAfeDraftResponseDto>>;
    getDraftsByIdAndActiveUser(id: number, currentContext: CurrentContext): Promise<GetAfeDraftResponseDto>;
    saveDraft(draftAfeDto: AfeDraftRequestDto, currentContext: CurrentContext): Promise<AfeDraftResponseDto>;
    updateDraftByIdAndActiveUser(updateDraftDto: UpdateDraftRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    deleteDraftById(id: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
}
