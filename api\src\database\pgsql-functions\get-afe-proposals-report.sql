DROP FUNCTION data_mig_301223.get_afe_proposals_report(json,text,integer[],json);
CREATE OR REPLACE FUNCTION data_mig_301223.get_afe_proposals_report(p_conditions json, p_login_id text, locations integer[], extra_permissions json)
 RETURNS TABLE(
  "Business Unit Code" character varying,
  "Business Unit Name" text, 
 	"Proposal Type" character varying, 
 	"AFE Number" character varying, 
 	"AFE Name" character varying, 
 	"Budget Type" text, 
 	"Type" text, 
 	"Total Expenditure (In USD)" text, 
 	"Submitter" character varying, 
 	"Submission Date" text, 
 	"Expense Summary" text,
 	"Budget Reference Number" text, 
 	"Cost Centers" text,
 	"GL Codes" text, 
 	"Status" text, 
 	"Approved / Rejected On" text,
 	"Workflow Year" integer
 )
 LANGUAGE plpgsql
AS $function$ 
DECLARE 
  v_request_types INTEGER[];
  v_budget_types text[];
  v_statuses text[];
  v_business_entities INTEGER[];
  v_from_submission_date timestamp;
  v_to_submission_date timestamp;
  v_from_approval_date timestamp;
  v_to_approval_date timestamp;
  v_submitted_by text[];
  v_submission_types text[];
  v_project_components INTEGER[];
  v_cost_centers INTEGER[];
  v_from_amount FLOAT;
  v_to_amount FLOAT;
  v_project_name text;
  v_afe_reference_number text;
  v_workflow_year INTEGER;
  v_afe_types text[];
  v_length_of_commitments text[];
BEGIN 
  v_request_types := (
    select array_agg(ele :: text :: int) 
    from json_array_elements(p_conditions -> 'requestTypes') ele
  );
  v_budget_types := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'budgetTypes') ele
  );
  v_statuses := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'statuses') ele
  );
  v_business_entities := (
    select array_agg(ele :: text :: int) 
    from json_array_elements(p_conditions -> 'businessEntities') ele
  );
  v_from_submission_date := (p_conditions ->> 'fromSubmissionDate'):: timestamp;
  v_to_submission_date := (p_conditions ->> 'toSubmissionDate'):: timestamp;
  v_from_approval_date := (p_conditions ->> 'fromApprovalDate'):: timestamp;
  v_to_approval_date := (p_conditions ->> 'toApprovalDate'):: timestamp;
  v_submitted_by := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'submittedBy') ele
  );
  v_afe_types := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'afeTypes') ele
  );
  v_submission_types := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'submissionTypes') ele
  );
  v_project_components := (
    select array_agg(ele :: text :: int) 
    from json_array_elements(p_conditions -> 'projectComponents') ele
  );
  v_cost_centers := (
    select array_agg(ele :: text :: int) 
    from json_array_elements(p_conditions -> 'costCenters') ele
  );
  v_from_amount := (p_conditions ->> 'fromAmount'):: FLOAT;
  v_to_amount := (p_conditions ->> 'toAmount'):: FLOAT;
  v_project_name := (p_conditions ->> 'projectName'):: text;
  v_afe_reference_number := (p_conditions ->> 'afeReferenceNumber'):: text;
  v_workflow_year := (p_conditions ->> 'workflowYear'):: int;
  v_length_of_commitments := (
    SELECT '{' || array_to_string(array_agg(ele :: text), ',')|| '}' 
    FROM json_array_elements(p_conditions -> 'lengthOfCommitments') ele
  );
  RETURN QUERY SELECT 
  	AfeProposal.entity_code AS "Business Unit Code",
    (AfeProposal.entity_title || ' ' || COALESCE(NULLIF(loc.title, ''), '')) as "Business Unit Name", 
    (
      select art.title 
      from data_mig_301223.afe_request_types as art 
      where AfeProposal.afe_request_type_id = art.id
    ) as "Proposal Type", 
    AfeProposal.project_reference_number AS "AFE Number", 
    AfeProposal.name AS "AFE Name", 
  CASE 
    WHEN AfeProposal.budget_type = 'BUDGETED' THEN 'Budgeted' 
    WHEN AfeProposal.budget_type = 'UNBUDGETED' THEN 'Unbudgeted' 
    WHEN AfeProposal.budget_type = 'MIXED' THEN 'Budgeted & Unbudgeted' 
    ELSE 'N/A' 
  END AS "Budget Type", 
  AfeProposal."data" ->> 'type' AS "Type", 
  case 
    when AfeProposal.category = 'SUPPLEMENTAL' then 
    TRIM(
        LEADING 
        FROM 
          TO_CHAR((AfeProposal.supplemental_delta_amounts ->> 'totalAmount')::float, '999,999,999,999,999.99')
    )
    else  
    TRIM(
        LEADING 
        FROM 
          TO_CHAR(AfeProposal.total_amount, '999,999,999,999,999.99')
    )
    end as "Total Expenditure (In USD)", 
  AfeProposal.submitter_id AS "Submitter", 
  TO_CHAR(AfeProposal.created_on, 'Mon DD YYYY HH:MIAM') AS "Submission Date", 
  case
    when AfeProposal.category = 'NEW' then  
        STRING_AGG(
            DISTINCT afeProposalAmountSplits.object_title || ' : ' || TRIM(
                LEADING 
                FROM 
                    TO_CHAR(afeProposalAmountSplits.amount, '999,999,999,999,999.99')
            ), 
            '; '
        )
      else  
        STRING_AGG(
            DISTINCT COALESCE(NULLIF(project_amount_delta.value->>'title', ''), 'N/A') || ' : ' || TRIM(
            LEADING 
            FROM 
              TO_CHAR((project_amount_delta.value->>'amount')::float, '999,999,999,999,999.99')
          ), 
          '; '
    )
    end as "Expense Summary", 
    STRING_AGG(
            distinct COALESCE(NULLIF(apmsa.value->>'number', ''), 'N/A') || ' : ' || TRIM(
            LEADING 
            FROM 
              TO_CHAR((apmsa.value->>'amount')::float, '999,999,999,999,999.99')
          ), 
          '; '
    ) AS "Budget Reference Number",
  STRING_AGG(
        DISTINCT apms.object_title || ' : ' || TRIM(
          LEADING 
            FROM 
              TO_CHAR(apms.amount, '999,999,999,999,999.99')
          ), 
          '; '
  ) AS "Cost Centers",
  STRING_AGG(
        DISTINCT glCodeAmountSplits.object_title, 
          '; '
  ) AS "GL Codes",
  COALESCE(
    AfeProposal.user_status || '(' ||  STRING_AGG(DISTINCT (u.value->>'firstName') || ' ' || (u.value->>'lastName'), ', ') || ')',
    AfeProposal.user_status 
  ) AS "Status",
  MAX(TO_CHAR(apl.action_date, 'Mon DD YYYY HH:MIAM')) AS "Approved / Rejected On",
  AfeProposal.workflow_year as "Workflow Year"
FROM 
  data_mig_301223.afe_proposals AS AfeProposal 
  LEFT OUTER JOIN data_mig_301223.afe_request_types AS afeRequestType ON AfeProposal.afe_request_type_id = afeRequestType.id 
  LEFT OUTER JOIN data_mig_301223.afe_proposal_amount_splits AS afeProposalAmountSplits ON AfeProposal.id = afeProposalAmountSplits.afe_proposal_id AND afeProposalAmountSplits.type = 'PROJECT_COMPONENT_SPLIT' 
  LEFT OUTER JOIN data_mig_301223.afe_proposal_amount_splits AS apms ON apms.afe_proposal_id = AfeProposal.id AND apms.type = 'COST_CENTER_SPLIT'
  LEFT OUTER JOIN data_mig_301223.afe_proposal_amount_splits AS glCodeAmountSplits ON glCodeAmountSplits.afe_proposal_id = AfeProposal.id AND glCodeAmountSplits.type = 'GL_CODE_SPLIT'
  LEFT OUTER JOIN data_mig_301223.afe_proposal_approvers AS apa ON apa.afe_proposal_id = AfeProposal.id AND apa.action_status = 'IN_PROGRESS' 
  LEFT OUTER JOIN data_mig_301223.afe_proposal_approvers AS apl ON apl.afe_proposal_id = AfeProposal.id AND apl.action_status in ('APPROVED', 'REJECTED', 'AUTO_APPROVED') AND apl.action_date is not null AND AfeProposal.internal_status in ('APPROVED', 'REJECTED')
  LEFT OUTER JOIN data_mig_301223.locations as loc on loc.id = AfeProposal.location_id
  LEFT OUTER JOIN LATERAL jsonb_array_elements(apa.other_info -> 'usersDetail') u ON true
  LEFT OUTER JOIN LATERAL jsonb_array_elements(apms.additional_info -> 'budgetReferenceNumberSplit') apmsa ON true
  LEFT OUTER JOIN LATERAL json_array_elements(extra_permissions -> 'projectLocationPermissions') projectPermissions ON true
  LEFT OUTER JOIN data_mig_301223.user_cost_center_mappings AS uccm ON uccm."login_id" = p_login_id
  LEFT OUTER JOIN LATERAL jsonb_array_elements(AfeProposal.supplemental_delta_amounts ->'afeProposalAmountSplits'-> 'projectComponentSplit') project_amount_delta ON true
WHERE
  (
    (
      (
        v_request_types IS NULL 
        OR AfeProposal.afe_request_type_id = ANY(v_request_types)
      ) 
      AND (
        v_budget_types IS NULL 
        OR AfeProposal.budget_type = ANY(array_append(cast(v_budget_types as data_mig_301223.enum_afe_proposals_budget_type[]), 'MIXED'))
      ) 
      AND (
        v_statuses IS NULL 
        OR AfeProposal.internal_status = ANY(cast(v_statuses as data_mig_301223.enum_afe_proposals_internal_status[]))
      ) 
      AND (
        v_business_entities IS NULL 
        OR AfeProposal.entity_id = ANY(v_business_entities)
      ) 
      AND (
        v_from_submission_date IS NULL 
        OR AfeProposal.created_on >= v_from_submission_date
      ) 
      AND (
        v_to_submission_date IS NULL 
        OR AfeProposal.created_on <= v_to_submission_date
      ) 
      AND (
        v_from_approval_date IS NULL 
        OR AfeProposal.updated_on >= v_from_approval_date
      ) 
      AND (
        v_to_approval_date IS NULL 
        OR AfeProposal.updated_on <= v_to_approval_date
      ) 
     AND (
        v_submitted_by IS NULL
        OR (
            (
                'ME' = ANY(v_submitted_by) AND AfeProposal.submitter_id = p_login_id
            ) 
            OR (
                'OTHERS' = ANY(v_submitted_by) AND AfeProposal.submitter_id != p_login_id
            )
        )
      )
      AND (
        v_submission_types IS NULL 
        OR AfeProposal.category = ANY(cast(v_submission_types as data_mig_301223.enum_afe_proposals_category[]))
      ) 
      AND (
        v_from_amount IS NULL 
        OR AfeProposal.total_amount >= v_from_amount
      ) 
      AND (
        v_to_amount IS NULL 
        OR AfeProposal.total_amount <= v_to_amount
      ) 
      AND (
        v_project_name IS NULL 
        OR AfeProposal.name ilike '%' || v_project_name || '%'
      ) 
      AND (
        v_afe_reference_number IS NULL 
        OR AfeProposal.project_reference_number ilike '%' || v_afe_reference_number || '%'
      )
      AND (
        v_project_components IS NULL 
        OR (afeProposalAmountSplits.type = 'PROJECT_COMPONENT_SPLIT' AND afeProposalAmountSplits.object_id = ANY(v_project_components))
      )
      AND (
        v_cost_centers IS NULL 
        OR (apms.type = 'COST_CENTER_SPLIT' AND apms.object_id = ANY(v_cost_centers))
      )
      AND (
        v_workflow_year IS NULL
        OR AfeProposal.workflow_year = v_workflow_year
      )
      AND (
        v_afe_types IS null
        OR (AfeProposal."data"->>'type') = ANY(v_afe_types)
      )
      AND (
        v_length_of_commitments IS NULL 
        OR AfeProposal."data"->>'lengthOfCommitmentTitle' = ANY(v_length_of_commitments)
      ) 
    ) 
    AND (
      AfeProposal."entity_id"= ANY(locations)
      OR AfeProposal."submitter_id" = p_login_id 
      OR AfeProposal."readers" @> jsonb_build_array(jsonb_build_object('loginId', p_login_id))
      OR AfeProposal."data" @> jsonb_build_object('projectDetails', jsonb_build_object('projectLeader', jsonb_build_object('loginId', p_login_id)))
      OR apms.object_id = uccm.cost_center_id
      OR ((projectPermissions.value->>'projectId')::INT = afeProposalAmountSplits."object_id" AND AfeProposal."entity_id" = ANY(ARRAY(SELECT json_array_elements_text(projectPermissions.value->'entityIds')::INTEGER)))
    )
   AND AfeProposal.deleted = false
   AND AfeProposal.active = true
  ) 
GROUP BY 
  AfeProposal.id,
  loc.title
ORDER BY 
  AfeProposal.created_on desc;
END;
$function$;