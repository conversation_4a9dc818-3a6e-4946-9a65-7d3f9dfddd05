"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const dtos_1 = require("../dtos");
const services_1 = require("../services");
let NotificationController = class NotificationController {
    constructor(notificationService) {
        this.notificationService = notificationService;
    }
    notificationsViewedByUser(request, viewNotificationRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.notificationService.notificationsViewedByUser(viewNotificationRequestDto.notificationsIds, request.currentContext);
        });
    }
    getNotificationsListForUser(request, limit, page) {
        return this.notificationService.getNotificationsListForUser(request.currentContext, limit, page);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Mark the notifications viewed by the user.',
    }),
    (0, common_1.Post)('viewed'),
    (0, common_1.HttpCode)(204),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ViewNotificationRequestDto]),
    __metadata("design:returntype", Promise)
], NotificationController.prototype, "notificationsViewedByUser", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the list of notifications for the user',
        type: [dtos_1.PaginatedNotificationResponseDto],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        type: Number,
        description: 'Number of records in response.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        type: Number,
        description: 'Page number of the paginated record.',
        required: false,
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], NotificationController.prototype, "getNotificationsListForUser", null);
NotificationController = __decorate([
    (0, swagger_1.ApiTags)('Notification APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('notifications'),
    __metadata("design:paramtypes", [services_1.NotificationService])
], NotificationController);
exports.NotificationController = NotificationController;
//# sourceMappingURL=notification.controller.js.map