import { Injectable } from '@nestjs/common';
import { EntitySetupRepository } from 'src/business-entity/repositories';
import { AdminApiClient } from 'src/shared/clients';
import { CURRENCY_TYPE, HttpStatus } from 'src/shared/enums';
import { HttpException } from 'src/shared/exceptions';
import { multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import {
	AllCostCenterResponseDto,
	AnalysisCodeResponseDto,
	CompanyCodeResponseDto,
	CostCenterFilterResponseDto,
	CostCenterMappedResponseDto,
	CostCenterResponseDto,
	CurrencyConversionResponseDto,
	NaturalAccountResponseDto,
} from '../dtos';
import {
	NaturalAccountNumberRepository,
	CostCenterRepository,
	CurrencyTypeRepository,
	CompanyCodeRepository,
	AnalysisCodeRepository,
} from '../repositories';
import { instanceToPlain } from 'class-transformer';

@Injectable()
export class FinanceService {
	constructor(
		private readonly naturalAccountNumberRepository: NaturalAccountNumberRepository,
		private readonly costCenterRepository: CostCenterRepository,
		private readonly currencyTypeRepository: CurrencyTypeRepository,
		private readonly entitySetupRepository: EntitySetupRepository,
		private readonly companyCodeRepository: CompanyCodeRepository,
		private readonly analysisCodeRepository: AnalysisCodeRepository,
		private readonly adminApiClient: AdminApiClient,
	) { }


	/**
	 * Check if company code exists for the business entity or not.
	 * @param entityId 
	 */
	public async checkCompanyCodeExistsForBusinessEntity(entityId: number): Promise<boolean> {
		return this.companyCodeRepository.isCompanyCodeExistEntityId(entityId);
	}

	/**
	 * Check if company code exists for the business entity or not.
	 * @param entityId 
	 */
	public async getCompanyDetailForBusinessEntity(entityId: number): Promise<CompanyCodeResponseDto> {
		const companyDetail = this.companyCodeRepository.getCompanyCodeByEntityId(entityId);

		return singleObjectToInstance(CompanyCodeResponseDto, companyDetail);
	}

	/**
	 * Get all company code list except passed id.
	 * @param entityId 
	 */
	public async getCompanyCodeListExcluding(excludedCode: string) {
		return this.companyCodeRepository.getAllCompanyCodeExcluding(excludedCode);
	}

	/**
	 * Return all the natural account number by requestTypeId and entityId.
	 * @param requestTypeId
	 * @param entityId
	 * @returns
	 */
	public async getNaturalAccountNumbersByRequestType(
		requestTypeId: number,
		entityId: number,
	): Promise<NaturalAccountResponseDto[]> {
		const company = await this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
		if (!company) {
			throw new HttpException(`Company doesn't exist for the business entity.`, HttpStatus.NOT_FOUND);
		}

		const accountNumbers =
			await this.naturalAccountNumberRepository.getNaturalAccountNumbersByRequestType(
				requestTypeId,
				company.id,
			);
		return multiObjectToInstance(NaturalAccountResponseDto, accountNumbers);
	}

	/**
	 * Return all the analysis codes by requestTypeId and entityId.
	 * @param requestTypeId
	 * @param entityId
	 * @returns
	 */
	public async getAnalysisCodesByRequestType(
		requestTypeId: number,
		entityId?: number
	): Promise<AnalysisCodeResponseDto[]> {
		const company = await this.companyCodeRepository.getCompanyCodeByEntityId(entityId);
		if (!company) {
			throw new HttpException(`Company doesn't exist for the business entity.`, HttpStatus.NOT_FOUND);
		}
		const accountNumbers = await this.analysisCodeRepository.getAnalysisCodesByRequestType(
			requestTypeId,
			company.id,
		);
		return multiObjectToInstance(AnalysisCodeResponseDto, accountNumbers);
	}

	/**
	 * Get all the cost centers with its company code by entity id.
	 * @param entityId
	 * @returns
	 */
	public async getCostCentersWithCompanyCodeByEntity(
		entityId: number,
	): Promise<CostCenterResponseDto[]> {
		const costCenters = await this.costCenterRepository.getCostCentersWithCompanyCodeByEntity(entityId);
		return multiObjectToInstance(CostCenterResponseDto, costCenters);
	}

	/**
	 * Return given currency conversion rate to primary currency.
	 * @param currencyType
	 * @returns
	 */
	public async getCurrencyConversionRateToPrimary(
		currencyType: CURRENCY_TYPE,
	): Promise<CurrencyConversionResponseDto> {
		const currency = await this.currencyTypeRepository.getCurrencyConversionRateToPrimary(
			currencyType,
		);
		const primaryCurrency = await this.currencyTypeRepository.getPrimaryCurrency();
		return singleObjectToInstance(CurrencyConversionResponseDto, {
			...currency,
			primaryCurrency: primaryCurrency.currency,
		});
	}

	/**
	 * Get currency type of an entity
	 * @param entityId
	 * @returns
	 */
	public async getCurrencyTypeForEntity(entityId: number): Promise<CurrencyConversionResponseDto> {
		const primaryCurrency = await this.currencyTypeRepository.getPrimaryCurrency();
		const entityParentIds = await this.adminApiClient.getParentIdsOfEntity(entityId);
		const entitySetup = await this.entitySetupRepository.getEntitySetupWithCurrency(
			entityParentIds,
		);
		const entityCurrency = !entitySetup ? primaryCurrency : entitySetup.currencyType;
		return singleObjectToInstance(CurrencyConversionResponseDto, {
			...entityCurrency,
			primaryCurrency: primaryCurrency.currency,
		});
	}

	public async getCostCenterByEntityIds(filter: { entityIds: number[] }) {
		const { entityIds } = filter;
		const costCenters = await this.costCenterRepository.getCostCentersWithCompanyCodeByEntityIds(entityIds);

		const mappedCostCenters = costCenters.map(costCenter =>
			instanceToPlain(new CostCenterMappedResponseDto(costCenter)),
		);

		return multiObjectToInstance(CostCenterFilterResponseDto, mappedCostCenters);
	}

	public async getAllCostCenters(): Promise<AllCostCenterResponseDto[]> {
		const costCenters = await this.costCenterRepository.getAllCostCenters();
		return multiObjectToInstance(AllCostCenterResponseDto, costCenters);
	}
}
