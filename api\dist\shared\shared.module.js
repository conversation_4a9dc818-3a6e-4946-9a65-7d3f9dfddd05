"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedModule = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../afe-proposal/repositories");
const repositories_2 = require("../finance/repositories");
const repositories_3 = require("../settings/repositories");
const services_1 = require("../settings/services");
const clients_1 = require("./clients");
const helpers_1 = require("./helpers");
const database_helper_1 = require("./helpers/database.helper");
const services_2 = require("./services");
const shared_notification_service_1 = require("./services/shared-notification.service");
const validators_1 = require("./validators");
const repositories = [
    repositories_2.CurrencyTypeRepository,
    repositories_2.CompanyCodeRepository,
    repositories_1.AfeProposalApproverRepository,
    repositories_2.CostCenterRepository,
    repositories_1.AfeProposalRepository,
    helpers_1.SequlizeOperator,
    repositories_3.SettingsRepository,
    repositories_1.UserCostCenterMappingRepository,
    repositories_1.UserProjectComponentMappingRepository
];
let SharedModule = class SharedModule {
};
SharedModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [
            database_helper_1.DatabaseHelper,
            clients_1.AdminApiClient,
            clients_1.AttachmentApiClient,
            services_2.EntityService,
            clients_1.MSGraphApiClient,
            services_2.SharedAttachmentService,
            clients_1.TaskApiClient,
            clients_1.NotificationApiClient,
            clients_1.FusionApiClient,
            clients_1.FusionUaeApiClient,
            clients_1.RequestApiClient,
            clients_1.HistoryApiClient,
            services_2.SharedPermissionService,
            shared_notification_service_1.SharedNotificationService,
            services_2.CurrencyService,
            validators_1.IsCompanyCodeIdExist,
            services_2.ExcelSheetService,
            validators_1.AfeProposalValidator,
            services_1.SettingsService,
            validators_1.WorkflowYearValidator,
            services_2.SharedDelegationService,
            ...repositories
        ],
    })
], SharedModule);
exports.SharedModule = SharedModule;
//# sourceMappingURL=shared.module.js.map