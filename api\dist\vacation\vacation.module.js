"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VacationModule = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../shared/clients");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
let VacationModule = class VacationModule {
};
VacationModule = __decorate([
    (0, common_1.Module)({
        controllers: [
            controllers_1.VacationController
        ],
        providers: [
            services_1.VacationService,
            clients_1.RequestApiClient,
            clients_1.AdminApiClient
        ],
    })
], VacationModule);
exports.VacationModule = VacationModule;
//# sourceMappingURL=vacation.module.js.map