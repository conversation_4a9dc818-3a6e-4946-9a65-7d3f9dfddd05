import { AdminApiClient, RequestApiClient } from 'src/shared/clients';
import { CurrentContext } from 'src/shared/types';
import { DeleteVacationDelegationRequestDto, SetupNewVacationDelegationRequestDto, UpdateVacationDelegationRequestDto } from '../dtos';
export declare class VacationService {
    private readonly requestApiClient;
    private readonly adminApiClient;
    constructor(requestApiClient: RequestApiClient, adminApiClient: AdminApiClient);
    getAllUpcomingDelegations(currentContext: CurrentContext, filterQuery?: any): Promise<any>;
    addUpcomingDelegation(delegationPayload: SetupNewVacationDelegationRequestDto, currentContext: CurrentContext): Promise<number>;
    updateUpcomingDelegation(delegationPayload: UpdateVacationDelegationRequestDto, currentContext: CurrentContext): Promise<any>;
    deleteUpcomingDelegation(id: number, currentContext: CurrentContext): Promise<any>;
    deleteAllUpcomingDelegation(deleteRequestDto: DeleteVacationDelegationRequestDto, currentContext: CurrentContext): Promise<any>;
    ifDelegationAlreadyAdded(username: string, fromDate: Date, toDate: Date, editDelegationId?: number, requestTypeId?: number): Promise<boolean>;
}
