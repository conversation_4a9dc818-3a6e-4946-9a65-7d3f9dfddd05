import { AfeBudgetType, AfeBudgetTypeMapping, AfeCommitmentLength, AfeNatureType, AfeNatureTypeMapping, AfeSubType, ParallelIdentifier } from 'src/afe-config/models';
import { AfeRequestType } from 'src/afe-config/models/afe-request-type.model';
import { AfeType } from 'src/afe-config/models/afe-type.model';
import { Question } from 'src/afe-config/models/questions.model';
import { AfeDraft } from 'src/afe-draft/models/afe-draft.model';
import { AfeProposalAmountSplit, AfeProposalApprover, AfeProposalLimitDeduction, Location, UserCostCenterMapping, UserProjectComponentMapping } from 'src/afe-proposal/models';
import { AfeProposal } from 'src/afe-proposal/models/afe-proposal.model';
import { EntitySetup } from 'src/business-entity/models/entity-setup.model';
import { AnalysisCode, CompanyCode, CostCenter, CurrencyType, NaturalAccountNumber } from 'src/finance/models';
import { ProjectComponent } from 'src/project-component/models';
import { Settings } from 'src/settings/models';
import { VactionSetup } from 'src/vacation/models';
import { WorkflowMasterSetting, WorkflowMasterStep, WorkflowRule, WorkflowSharedBucketLimit, WorkflowSharedChildLimit } from 'src/workflow/models';
import { Notification } from 'src/notification/models';
import { QueueLog } from 'src/queue/models';
import { Scheduler } from 'src/scheduler/models/scheduler.model';
import { DataSharingSchedulers } from 'src/sftp-service/models';
export declare const getSequelizeOrmConfig: (enableSSL: any) => {
    ssl: boolean;
    dialectOptions: {
        ssl: {
            require: boolean;
        };
    };
    synchronize: boolean;
    autoLoadModels: boolean;
    models: (typeof AfeBudgetTypeMapping | typeof AfeBudgetType | typeof AfeCommitmentLength | typeof AfeNatureTypeMapping | typeof AfeNatureType | typeof AfeRequestType | typeof ParallelIdentifier | typeof AfeType | typeof AfeProposal | typeof AfeProposalAmountSplit | typeof AfeProposalApprover | typeof AfeProposalLimitDeduction | typeof Question | typeof CompanyCode | typeof CostCenter | typeof CurrencyType | typeof NaturalAccountNumber | typeof EntitySetup | typeof ProjectComponent | typeof VactionSetup | typeof WorkflowMasterSetting | typeof WorkflowMasterStep | typeof WorkflowSharedBucketLimit | typeof WorkflowRule | typeof WorkflowSharedChildLimit | typeof AfeDraft | typeof AnalysisCode | typeof Settings | typeof Notification | typeof AfeSubType | typeof Scheduler | typeof QueueLog | typeof UserCostCenterMapping | typeof UserProjectComponentMapping | typeof Location | typeof DataSharingSchedulers)[];
};
