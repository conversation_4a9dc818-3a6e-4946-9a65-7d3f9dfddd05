"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedGetAfeDraftResponseDto = exports.GetAfeDraftResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class GetAfeDraftResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)({ type: Number }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], GetAfeDraftResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAfeDraftResponseDto.prototype, "projectName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: String }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], GetAfeDraftResponseDto.prototype, "entityName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: JSON }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], GetAfeDraftResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetAfeDraftResponseDto.prototype, "createdOn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: Date }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], GetAfeDraftResponseDto.prototype, "updatedOn", void 0);
exports.GetAfeDraftResponseDto = GetAfeDraftResponseDto;
class PaginatedGetAfeDraftResponseDto {
}
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PaginatedGetAfeDraftResponseDto.prototype, "pageTotal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ isArray: true, type: GetAfeDraftResponseDto }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], PaginatedGetAfeDraftResponseDto.prototype, "records", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PaginatedGetAfeDraftResponseDto.prototype, "total", void 0);
exports.PaginatedGetAfeDraftResponseDto = PaginatedGetAfeDraftResponseDto;
//# sourceMappingURL=get-afe-draft-response.dto.js.map