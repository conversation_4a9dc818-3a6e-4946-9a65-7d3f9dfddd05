"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VactionSetup = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
let VactionSetup = class VactionSetup extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'user_id', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], VactionSetup.prototype, "userId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'start_date', type: 'TIMESTAMP', allowNull: false }),
    __metadata("design:type", Date)
], VactionSetup.prototype, "startDate", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'end_date', type: 'TIMESTAMP', allowNull: false }),
    __metadata("design:type", Date)
], VactionSetup.prototype, "endDate", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'delegate_user_id', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], VactionSetup.prototype, "delegateUserId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'delegate_type',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.VACATION_DELEGATION_TYPE)),
        allowNull: false,
    }),
    __metadata("design:type", String)
], VactionSetup.prototype, "delegateType", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'source_role', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], VactionSetup.prototype, "sourceRole", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'source_location_id', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], VactionSetup.prototype, "sourceLocationId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'source_location_code', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], VactionSetup.prototype, "sourceLocationCode", void 0);
VactionSetup = __decorate([
    (0, sequelize_typescript_1.Table)({ tableName: 'vaction_setup' })
], VactionSetup);
exports.VactionSetup = VactionSetup;
//# sourceMappingURL=vaction-setup.model.js.map