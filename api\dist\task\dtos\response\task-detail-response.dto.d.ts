export declare class TaskAdditionalInfoResponseDto {
    proposal_id: number;
    proposal_project_reference_number: number;
}
export declare class TaskDetailResponseDto {
    id: number;
    tennant_id: string;
    application_id: string;
    business_entity_id: number;
    entity_id?: number;
    entity_type?: string;
    title?: string;
    details?: string;
    assigned_to?: string;
    is_group_assignment?: boolean;
    due_date?: Date;
    task_base_url?: string;
    task_rel_url?: string;
    task_status?: string;
    task_outcome?: string;
    task_completion_date?: Date;
    task_completion_comments?: string;
    additional_info?: TaskAdditionalInfoResponseDto;
    created_by?: string;
    created_on?: Date;
    modified_by?: string;
    modified_on?: Date;
    completed_by?: string;
    constructor(partial?: Partial<TaskDetailResponseDto>);
}
