import { ChangeDetectorRef, Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { RequestTypeData } from '@core/data/request-type';
import { ModalComponent, ModalConfig } from '@core/modules/partials';
import { ReportService } from '@core/services/api';
import { LocalStorageService } from '@core/services/common/localStorage.service';
import { TranslateService } from '@ngx-translate/core';
import { toNumber } from 'lodash';
import { BehaviorSubject, Observable, Subject, takeUntil } from 'rxjs';
import { PAGINATION_DEFAULT_LIMIT } from 'src/app/core/constants/contants';
import { GlobalUrls, ReplaceUrlVariable } from 'src/app/core/constants/urls.constants';
import { BudgetTypeEnum, BudgetTypeLabel } from 'src/app/core/enums/Metadata';
import { IPagination } from 'src/app/core/interfaces/api';
import { UserPermissionModel } from 'src/app/core/models/basic/userpermissions';
import { ProposalService } from 'src/app/core/services/api/proposal.service';
import { CommonService } from 'src/app/core/services/common/common.service';
import { PermissionService } from 'src/app/core/services/common/permission.service';
import { SpinnerService } from 'src/app/core/services/common/spinner.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-afe-list',
  templateUrl: './afe-list.component.html',
  styleUrls: ['./afe-list.component.scss']
})
export class AfeListComponent implements OnInit, OnDestroy {
  @Input('filterLocalStorageKey') filterLocalStorageKey: string;
  @Input('listTitle') listTitle: string;

  public afeList: any = [];
  public loading: boolean = true;
  private destroy$: Subject<boolean> = new Subject<boolean>();

  public pagination: IPagination = {
    limit: PAGINATION_DEFAULT_LIMIT,
    offset: 0,
    page: 1
  };

  public totalRecord: number = 0;

  public filterModalTitle: string = this.translateService.instant('MENU.FILTER');
  public modalDismissButtonLabel: string = this.translateService.instant('FORM.BUTTON.APPLY');
  public exportToExcelTitle: string = this.translateService.instant('MENU.CHOOSE_COLUMNS_FOR_EXCEL_EXPORT');
  public downloadButtonLabel: string = this.translateService.instant('FORM.BUTTON.DOWNLOAD');
  public filtersData: any;
  public storedFilterData: any | null;
  public filterChangeEvent = new BehaviorSubject<boolean>(true);
  public filterLocalStorage: Observable<any>;
  private forApprovalHistory: boolean;
  @Input() public showExport: boolean;

  @ViewChild('filterModal') private filterModal: ModalComponent;
  @ViewChild('exportModal') private exportModal: ModalComponent;

  isFilterModalReady: boolean = false;
  public filterModalConfig: ModalConfig = {
    modalTitle: this.filterModalTitle,
    dismissButtonLabel: this.modalDismissButtonLabel,
    closeButtonLabel: this.translateService.instant('FORM.BUTTON.RESET'),
    onDismiss: () => {
      this.isFilterModalReady = false;
      return true;
    },
    shouldClose: () => {
      this.isFilterModalReady = false;
      this.pagination.page = 1;
      this.pagination.offset = 0;
      //this.cdr.detectChanges();
      this.localStorageService.set(this.filterLocalStorageKey, '');
      this.isFilterModalReady = true;
      this.cdr.detectChanges();
      return false;
    },
    shouldDismiss: () => {
      this.pagination.page = 1;
      this.pagination.offset = 0;
      this.cdr.detectChanges();
      this.localStorageService.set(this.filterLocalStorageKey, this.filtersData);
      this.filterChangeEvent.next(true);
      this.isFilterModalReady = false;
      return true;
    },
    modalDialogConfig: { backdrop: 'static', size: 'lg', keyboard: false, centered: true }
  };


  public exportModalConfig: ModalConfig = {
    modalTitle: this.exportToExcelTitle,
    dismissButtonLabel: this.downloadButtonLabel,
    closeButtonLabel: this.translateService.instant('FORM.BUTTON.CANCEL_BUTTON'),
    onDismiss: () => {
      return true;
    },
    shouldClose: () => {
      return true;
    },
    shouldDismiss: () => {
      this.exportToExcelSheet();
      return false;
    },
    modalDialogConfig: { backdrop: 'static', size: 'md', keyboard: false, centered: true }
  };

  public exportColumnsForm: FormGroup;
  public exportColumns = [
    { id: 'Business Unit Code', col: 'Business Unit Code' },
    { id: 'Business Unit Name', col: 'Business Unit Name' },
    { id: 'Proposal Type', col: 'Proposal Type' },
    { id: 'AFE Number', col: 'AFE Number' },
    { id: 'AFE Name', col: 'AFE Name' },
    { id: 'Budget Type', col: 'Budget Type' },
    { id: 'Type', col: 'Type' },
    { id: 'Total Expenditure (In USD)', col: 'Total Expenditure' },
    { id: 'Submitter', col: 'Submitter' },
    { id: 'Submission Date', col: 'Submission Date' },
    { id: 'Expense Summary', col: 'Expense Summary' },
    { id: 'Cost Centers', col: 'Cost Centers' },
    { id: 'GL Codes', col: 'GL Codes' },
    { id: 'Budget Reference Number', col: 'Budget Reference Number' },
    { id: 'Status', col: 'Status' },
    { id: 'Approved / Rejected On', col: 'Approved / Rejected On' },
    { id: 'Workflow Year', col: 'Workflow Year' },
  ];

  constructor(
    private proposalService: ProposalService,
    private commonService: CommonService,
    private spinnerService: SpinnerService,
    private permissionService: PermissionService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private route: ActivatedRoute,
    private translateService: TranslateService,
    private readonly localStorageService: LocalStorageService,
    private readonly reportService: ReportService,
    private readonly requestTypeData: RequestTypeData,
    private readonly fb: FormBuilder
  ) {
    // Create a FormControl for each available colunm, initialize them as unchecked, and put them in an array
    const formControls = this.exportColumns.map(_ => new FormControl(true));

    // Create a FormControl for the select/unselect all checkbox
    const selectAllControl = new FormControl(true);
    this.exportColumnsForm = this.fb.group({
      exportColumns: new FormArray(formControls),
      selectAll: selectAllControl
    });
  }

  ngOnInit(): void {
    this.spinnerService.startSpinner();
    this.loading = true;

    this.route.data.subscribe((data) => {
      if (data.permissionRequired) {
        this.commonService.isPermissionFetched.subscribe((permissionReady) => {
          if (permissionReady) {
            const permissionList = this.commonService.permissionAll as UserPermissionModel[];
            if (permissionList.length) {
              const ifPermission = this.permissionService.checkPermissionByName(data.permissionRequired)
              if (ifPermission) {
                this.localStorageService.watch(this.filterLocalStorageKey)
                  .pipe(takeUntil(this.destroy$))
                  .subscribe({
                    next: (val) => {
                      this.forApprovalHistory = data.isApprovalHistory;
                      this.storedFilterData = val;
                      this.fetchList();
                    }
                  });
              } else {
                this.spinnerService.stopSpinner();
                this.router.navigate([GlobalUrls.ACCESS_DENIED]);
              }
            } else {
              this.spinnerService.stopSpinner();
              this.router.navigate([GlobalUrls.ACCESS_DENIED]);
            }
          }
        })
      } else {
        this.localStorageService.watch(this.filterLocalStorageKey)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (val) => {
              this.forApprovalHistory = data.isApprovalHistory;
              this.storedFilterData = val;
              this.fetchList();
            }
          });
      }
    });

    // Subscribe to changes on the selectAll checkbox
    this.exportColumnsForm?.get('selectAll')?.valueChanges.subscribe(bool => {
      this.exportColumnsForm
        ?.get('exportColumns')
        ?.patchValue(Array(this.exportColumns.length).fill(bool), { emitEvent: false });
    });

    // Subscribe to changes on the colunm name checkboxes
    this.exportColumnsForm.get('exportColumns')?.valueChanges.subscribe(val => {
      const allSelected = val.every((bool: any) => bool);
      if (this.exportColumnsForm.get('selectAll')?.value !== allSelected) {
        this.exportColumnsForm.get('selectAll')?.patchValue(allSelected, { emitEvent: false });
      }
    });
  }

  public getExportColumnsControls() {
    return (this.exportColumnsForm.get('exportColumns') as FormArray).controls;
  }

  public isAnyColunmSelected() {
    return this.getExportColumnsControls().some((control: any) => control.value);
  }

  /**
   * Creating body payload for afe list filter.
   * @param filterSelection 
   * @returns 
   */
  private filterPayload(filterSelection: any) {
    if (filterSelection) {
      const { obj: filterObject } = filterSelection;
      let filterRequest = {};
      for (let property in filterObject) {
        if (filterObject[property]) {
          switch (property) {
            case 'requestTypes':
            case 'projectComponents':
            case 'budgetTypes':
            case 'statuses':
            case 'businessEntities':
            case 'submissionTypes':
            case 'afeTypes':
            case 'submittedBy':
              if (filterObject[property]?.length) {
                filterRequest = { ...filterRequest, [property]: filterObject[property]?.map((f: any) => f.id) };
              }
              break;

            case 'lengthOfCommitments':
              if (filterObject[property]?.length) {
                filterRequest = { ...filterRequest, [property]: filterObject[property]?.map((f: any) => f.title) };
              }
              break;

            case 'costCenters':
            case 'additionalLocations':
              if (filterObject[property]?.length) {
                filterRequest = { ...filterRequest, [property]: filterObject[property]?.map((f: any) => toNumber(f.id)) };
              }
              break;

            case 'fromAmount':
            case 'toAmount':
            case 'projectName':
            case 'afeReferenceNumber':

              filterRequest = { ...filterRequest, [property]: filterObject[property] };
              break;

            case 'workflowYear':
              if (filterObject[property]) {
                filterRequest = { ...filterRequest, [property]: toNumber(filterObject[property]) };
              }
              break;

            case 'fromSubmissionDate':
            case 'toSubmissionDate':
            case 'fromApprovalDate':
            case 'toApprovalDate':

              const { year, month, day } = filterObject[property];
              filterRequest = { ...filterRequest, [property]: `${year}-${month}-${day}` };
              break;
          }
        }
      }
      return filterRequest;
    }
    return null;
  }

  resetEventTrigger() {
    this.pagination.page = 1;
    this.pagination.offset = 0;
    this.cdr.detectChanges();
  }

  public fetchList() {
    const filters = this.filterPayload(this.storedFilterData);
    this.spinnerService.startSpinner();

    console.log(filters);

    this.proposalService.getSubmittedApprovalList(this.pagination, this.forApprovalHistory, filters)
      .subscribe({
        next: (response: any) => {
          this.afeList = response.records.map((record: any) => ({
            ...record, afeRequestType: this.requestTypeData.getRequestDetailFromId(record.afeRequestTypeId, true)?.title
          }));
          this.totalRecord = response.total;
          this.spinnerService.stopSpinner();
          this.loading = false;
          this.cdr.detectChanges();
        },
        error: (_) => {
          Swal.fire({
            icon: 'error',
            title: this.translateService.instant('SWAL.OOPS'),
            text: _.message
          });
          this.afeList = [];
          this.loading = false;
          this.spinnerService.stopSpinner();
          this.cdr.detectChanges();
        }
      })
  }

  public exportToExcelSheet() {
    const selectedColumns = this.exportColumnsForm.value.exportColumns
      .map((checked: any, index: any) => checked ? this.exportColumns[index].id : null)
      .filter((value: any) => value !== null);

    if (!this.isAnyColunmSelected()) {
      Swal.fire({
        icon: 'warning',
        title: this.translateService.instant('SWAL.OOPS'),
        text: this.translateService.instant('SWAL.SELECT_AT_LEAST_ONE_COLUMN')
      });
      return;
    }

    const filters = this.filterPayload(this.storedFilterData);
    this.reportService.downloadAfeListExcelReport(filters, selectedColumns);
  }

  public nagivateToTaskAction(task: any) {
    const { relUrl, taskId } = task;
    this.router.navigateByUrl(ReplaceUrlVariable(`${relUrl}`, { taskId }));
  }

  public getBudgetTypeTitle(budgetType: BudgetTypeEnum) {
    return BudgetTypeLabel[budgetType];
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.unsubscribe();
  }

  public handlePageChange(event: number): void {
    this.spinnerService.startSpinner();
    this.loading = true;
    this.pagination.offset = +event - 1;
    this.pagination.page = event;
    this.fetchList();
  }

  public getFilterPayload(event: any) {
    this.filtersData = event;
  }

  async openFilterModal() {
    this.isFilterModalReady = true;
    this.cdr.detectChanges();
    return await this.filterModal.open();
  }

  async openExportModal() {
    this.cdr.detectChanges();
    return await this.exportModal.open();
  }
}
