"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentService = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../afe-proposal/repositories");
const attachment_api_client_1 = require("../../shared/clients/attachment-api.client");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const helpers_1 = require("../../shared/helpers");
const validators_1 = require("../../shared/validators");
const dtos_1 = require("../dtos");
const repositories_2 = require("../../afe-draft/repositories");
let AttachmentService = class AttachmentService {
    constructor(attachmentApiClient, afeProposalRepository, afeProposalValidator, draftAfeRepository, afeProposalApproverRepository) {
        this.attachmentApiClient = attachmentApiClient;
        this.afeProposalRepository = afeProposalRepository;
        this.afeProposalValidator = afeProposalValidator;
        this.draftAfeRepository = draftAfeRepository;
        this.afeProposalApproverRepository = afeProposalApproverRepository;
    }
    getContentByFileId(fileId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const fileContent = yield this.attachmentApiClient.getContentByFileId(fileId);
            const { username } = currentContext.user;
            const { meta_data_1: afeProposalId, entity_id: entityId, entity_type: entityType } = fileContent;
            if (entityType === enums_1.ATTACHMENT_ENTITY_TYPE.AFE_DRAFT) {
                const draft = yield this.draftAfeRepository.getDraftById(entityId);
                if (!draft) {
                    throw new exceptions_1.HttpException(`Draft doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
                }
                if (draft.createdBy.toLowerCase() !== username.toLowerCase()) {
                    throw new exceptions_1.HttpException(`You are not authorized to view this file id content.`, enums_1.HttpStatus.FORBIDDEN);
                }
            }
            else {
                const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(Number(afeProposalId));
                if (!proposal) {
                    throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
                }
                const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
                if (!hasUserPermission) {
                    throw new exceptions_1.HttpException(`You are not authorized to view this file id content.`, enums_1.HttpStatus.FORBIDDEN);
                }
            }
            if (!fileContent) {
                throw new exceptions_1.HttpException(`Content not available for file id ${fileId}`, enums_1.HttpStatus.NOT_FOUND);
            }
            return (0, helpers_1.singleObjectToInstance)(dtos_1.AttachmentContentResponseDto, fileContent);
        });
    }
    getProposalAttachmentMetaData(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const response = yield this.attachmentApiClient.getAllAttachments(afeProposalId, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_SUBMIT);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AttachmentContentResponseDto, response);
        });
    }
    getAllApproversAttachmentMetaData(afeProposalId, currentContext, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            const proposal = yield this.afeProposalRepository.getAfeProposalWithSplitById(afeProposalId);
            if (!proposal) {
                throw new exceptions_1.HttpException(`Afe doesn't exist.`, enums_1.HttpStatus.NOT_FOUND);
            }
            const hasUserPermission = yield this.afeProposalValidator.checkUserHasPermissionToViewAfeDetails(proposal, currentContext, taskId);
            if (!hasUserPermission) {
                throw new exceptions_1.HttpException(`You are not authorized to view this AFE information.`, enums_1.HttpStatus.FORBIDDEN);
            }
            const allAttachments = yield this.attachmentApiClient.getAttachmentsByMetadata({
                entity_type: enums_1.ATTACHMENT_ENTITY_TYPE.AFE_ACTION,
                meta_data_1: afeProposalId.toString()
            });
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AttachmentContentResponseDto, ((allAttachments === null || allAttachments === void 0 ? void 0 : allAttachments.length) ? allAttachments : []));
        });
    }
    getApproverAttachmentMetaData(approverId) {
        return __awaiter(this, void 0, void 0, function* () {
            const response = yield this.attachmentApiClient.getAllAttachments(approverId, enums_1.ATTACHMENT_ENTITY_TYPE.AFE_ACTION);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AttachmentContentResponseDto, response);
        });
    }
};
AttachmentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [attachment_api_client_1.AttachmentApiClient,
        repositories_1.AfeProposalRepository,
        validators_1.AfeProposalValidator,
        repositories_2.DraftAfeRepository,
        repositories_1.AfeProposalApproverRepository])
], AttachmentService);
exports.AttachmentService = AttachmentService;
//# sourceMappingURL=attachment.service.js.map