import { FREQUENCY } from 'src/shared/enums';
import { BaseRepository } from 'src/shared/repositories';
import { CurrentContext } from 'src/shared/types';
import { DataSharingSchedulers } from '../models';
export declare class DataSharingSchedulerRepository extends BaseRepository<DataSharingSchedulers> {
    constructor();
    getSchedulerByType(type: FREQUENCY): Promise<DataSharingSchedulers[]>;
    getAllReadyToShareScheduler(): Promise<any>;
    updateLastRunAt(id: number, lastRunAt: Date, nextRunAt: Date, currentContext: CurrentContext): Promise<number>;
}
