import { ApprovalActionOnTaskRequestDto } from '../dtos';
import { TaskService } from '../services';
export declare class TaskPrivateController {
    private readonly taskService;
    constructor(taskService: TaskService);
    approveTask(approvalActionOnTaskRequestDto: ApprovalActionOnTaskRequestDto): Promise<{
        message: string;
    }>;
    rejectTask(approvalActionOnTaskRequestDto: ApprovalActionOnTaskRequestDto): Promise<{
        message: string;
    }>;
}
