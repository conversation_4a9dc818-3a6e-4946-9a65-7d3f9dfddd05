import { MSGraphApiClient } from 'src/shared/clients';
export declare class GraphUserService {
    private readonly mSGraphApiClient;
    constructor(mSGraphApiClient: MSGraphApiClient);
    searchUsers(searchText: string, orderBy: string, count: boolean): Promise<any>;
    getUserDetailsInMsResponse(userId: string): Promise<any>;
    getUsersDetailsFromAdInMsResponse(userIds: string): Promise<any>;
}
