"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceAdminController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../core/guards");
const enums_1 = require("../../shared/enums");
const dtos_1 = require("../dtos");
const decorators_1 = require("../../core/decorators");
const dtos_2 = require("../../shared/dtos");
const finance_admin_service_1 = require("../services/finance-admin.service");
const services_1 = require("../../report/services");
const get_history_response_dto_1 = require("../dtos/response/get-history-response.dto");
const dtos_3 = require("../../afe-proposal/dtos");
let FinanceAdminController = class FinanceAdminController {
    constructor(financeAdminService, reportService) {
        this.financeAdminService = financeAdminService;
        this.reportService = reportService;
    }
    createCompanyCode(request, createCompanyCodeRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.createCompanyCode(createCompanyCodeRequestDto, request.currentContext);
        });
    }
    updateCompanyCodeDetail(updateCompanyCodeRequestDto, request) {
        return this.financeAdminService.updateCompanyCodeDetail(updateCompanyCodeRequestDto, request.currentContext);
    }
    deleteCompanyCodeById(id, request) {
        return this.financeAdminService.deleteCompanyCodeById(id, request.currentContext);
    }
    getCompanyCodesListByEntityId(entityId, limit = 10, page = 1) {
        return this.financeAdminService.getCompanyCodesListByEntityId(entityId, limit, page);
    }
    getEntityActiveCompanyCodeDetails(entityId) {
        return this.financeAdminService.getEntityActiveCompanyCodeDetails(entityId);
    }
    addCostCenter(request, addCostCenterRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.addCostCenter(addCostCenterRequestDto, request.currentContext);
        });
    }
    getCostCentersByCompanyCodeId(companyId, limit = 10, page = 1) {
        return this.financeAdminService.getCostCentersByCompanyCodeId(companyId, limit, page);
    }
    createAnalysisCode(request, createCompanyCodeRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.createAnalysisCode(createCompanyCodeRequestDto, request.currentContext);
        });
    }
    getAnalsysisCodesByCompanyCodeId(companyId, limit = 10, page = 1) {
        return this.financeAdminService.getAnalsysisCodesByCompanyCodeId(companyId, limit, page);
    }
    createNatualAccountNumber(request, createNaturalAccountNumberRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.createNatualAccountNumber(createNaturalAccountNumberRequestDto, request.currentContext);
        });
    }
    getNaturalAccountNumbersByCompanyCodeId(companyId, limit = 10, page = 1) {
        return this.financeAdminService.getNaturalAccountNumbersByCompanyCodeId(companyId, limit, page);
    }
    updateCostCenterDetail(updateCostCenterRequestDto, request) {
        return this.financeAdminService.updateCostCenterDetail(updateCostCenterRequestDto, request.currentContext);
    }
    updateFusionIntegration(updateFusionIntegrationRequestDto, request) {
        return this.financeAdminService.updateFusionIntegration(updateFusionIntegrationRequestDto, request.currentContext);
    }
    updateAnalysisCodeDetail(updateAnalysisCodeRequestDto, request) {
        return this.financeAdminService.updateAnalysisCodeDetail(updateAnalysisCodeRequestDto, request.currentContext);
    }
    updateNaturalAccountNumber(updateNaturalAccountNumberRequestDto, request) {
        return this.financeAdminService.updateNaturalAccountNumber(updateNaturalAccountNumberRequestDto, request.currentContext);
    }
    deleteCostCenterById(id, request) {
        return this.financeAdminService.deleteCostCenterById(id, request.currentContext);
    }
    deleteAnalysisCodeById(id, request) {
        return this.financeAdminService.deleteAnalysisCodeById(id, request.currentContext);
    }
    deleteNaturalAccountNumberById(id, request) {
        return this.financeAdminService.deleteNaturalAccountNumberById(id, request.currentContext);
    }
    toggleActiveStateOfCompany(request, toggleActiveStateCompanyCodeRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.toggleActiveStateOfCompany(toggleActiveStateCompanyCodeRequestDto, request.currentContext);
        });
    }
    deactivateCompanyCode(companyId, request) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.deactivateCompanyCode(companyId, request.currentContext);
        });
    }
    downloadAnalysisCode(res, companyId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { report, filename } = yield this.reportService.downloadAnalysisCode(companyId);
            res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
            res.header('Access-Control-Expose-Headers', 'Content-Disposition');
            res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            return res.send(report);
        });
    }
    downloadCostCenter(res, companyId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { report, filename } = yield this.reportService.downloadCostCenters(companyId);
            res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
            res.header('Access-Control-Expose-Headers', 'Content-Disposition');
            res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            return res.send(report);
        });
    }
    downloadNaturalAccount(res, companyId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { report, filename } = yield this.reportService.downloadNaturalAccounts(companyId);
            res.header('Content-disposition', `attachment; filename=${filename}.xlsx`);
            res.header('Access-Control-Expose-Headers', 'Content-Disposition');
            res.type('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            return res.send(report);
        });
    }
    importAnalysisCode(request, importDataRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.importAnalysisCode(importDataRequestDto, request.currentContext);
        });
    }
    importNaturalAccount(request, importDataRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.importNaturalAccount(importDataRequestDto, request.currentContext);
        });
    }
    importCostCenter(request, importDataRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.importCostCenter(importDataRequestDto, request.currentContext);
        });
    }
    getCompanyHistory(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.getCompanyHistory(entityId);
        });
    }
    getCostCenterHistory(costCenterId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.getCostCenterHistory(costCenterId);
        });
    }
    uploadEvidence(companyId, request, evidences) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.uploadEvidence(companyId, evidences, request.currentContext);
        });
    }
    updateMultiNaturalAccount(request, updateMultiNaturalAccountRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.updateMultiNaturalAccount(updateMultiNaturalAccountRequestDto, request.currentContext);
        });
    }
    getEvidences(companyId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeAdminService.getEvidences(companyId);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Create new company code for the business entity',
        type: dtos_1.CompanyCodeResponseDto,
    }),
    (0, common_1.Post)('company-code'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.CreateCompanyCodeRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "createCompanyCode", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update company code detail',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Put)('company-code'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.UpdateCompanyCodeRequestDto, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "updateCompanyCodeDetail", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete company code by its id.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Delete)('company-code/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "deleteCompanyCodeById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the company codes list for an business entity.',
        type: [dtos_1.PaginatedCompanyCodesResponseDto],
    }),
    (0, common_1.Get)('entities/:entityId/company-codes'),
    __param(0, (0, common_1.Param)('entityId')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getCompanyCodesListByEntityId", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the active company code details for an business entity.',
        type: dtos_1.CompanyCodeResponseDto,
    }),
    (0, common_1.Get)('entities/:entityId/company-code'),
    __param(0, (0, common_1.Param)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getEntityActiveCompanyCodeDetails", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add new cost center for a company code.',
        type: dtos_1.CostCenterResponseDto,
    }),
    (0, common_1.Post)('cost-center'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.AddCostCenterRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "addCostCenter", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the cost centers list for a company.',
        type: [dtos_1.PaginatedCostCentersResponseDto],
    }),
    (0, common_1.Get)('companies/:companyId/cost-centers'),
    __param(0, (0, common_1.Param)('companyId')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getCostCentersByCompanyCodeId", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Create new analysis code for a company.',
        type: dtos_1.AnalysisCodeResponseDto,
    }),
    (0, common_1.Post)('analysis-code'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.CreateAnalysisCodeRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "createAnalysisCode", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the analysis codes list for a company.',
        type: [dtos_1.PaginatedAnalysisCodeResponseDto],
    }),
    (0, common_1.Get)('companies/:companyId/analysis-codes'),
    __param(0, (0, common_1.Param)('companyId')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getAnalsysisCodesByCompanyCodeId", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Create new natural account number for a company',
        type: dtos_1.NaturalAccountResponseDto,
    }),
    (0, common_1.Post)('natural-account-number'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.CreateNaturalAccountNumberRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "createNatualAccountNumber", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the natural account numbers list for a company.',
        type: [dtos_1.PaginatedNaturalAccountNumberResponseDto],
    }),
    (0, common_1.Get)('companies/:companyId/natural-account-numbers'),
    __param(0, (0, common_1.Param)('companyId')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('page')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getNaturalAccountNumbersByCompanyCodeId", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update cost center detail',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Put)('cost-center'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.UpdateCostCenterRequestDto, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "updateCostCenterDetail", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update Fusion Integration',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Put)('fusion-integration'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.UpdateFusionIntegrationRequestDto, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "updateFusionIntegration", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update analysis code detail',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Put)('analysis-code'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.UpdateAnalysisCodeRequestDto, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "updateAnalysisCodeDetail", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Update natural account number detail',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Put)('natural-account-number'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dtos_1.UpdateNaturalAccountNumberRequestDto, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "updateNaturalAccountNumber", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete cost center by its id.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Delete)('cost-center/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "deleteCostCenterById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete analysis code by its id.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Delete)('analysis-code/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "deleteAnalysisCodeById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete natural account number by its id.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Delete)('natural-account-number/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "deleteNaturalAccountNumberById", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Toggle the state of the company code of an entity.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('toggle-company-state'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ToggleActiveStateCompanyCodeRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "toggleActiveStateOfCompany", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Deactivate the company code.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('/companies/:companyId/deactivate'),
    __param(0, (0, common_1.Param)('companyId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "deactivateCompanyCode", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Download analysis code list.' }),
    (0, common_1.Get)('analysis-codes/:companyId/download'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Param)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "downloadAnalysisCode", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Download cost center list.' }),
    (0, common_1.Get)('cost-centers/:companyId/download'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Param)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "downloadCostCenter", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Download natural account list.' }),
    (0, common_1.Get)('natural-accounts/:companyId/download'),
    __param(0, (0, common_1.Res)()),
    __param(1, (0, common_1.Param)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "downloadNaturalAccount", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Import analysis code for a company using xlsx file.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('import-analysis-code'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ImportDataRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "importAnalysisCode", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Import natural account number for a company using xlsx file.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('import-natural-account-number'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ImportDataRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "importNaturalAccount", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Import cost centers for a company using xlsx file.',
        type: dtos_2.MessageResponseDto,
    }),
    (0, common_1.Post)('import-cost-center'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.ImportDataRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "importCostCenter", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get company history by entity id.',
        type: [get_history_response_dto_1.GetHistoryResponseDTO],
    }),
    (0, common_1.Get)('/companies/:entityId/history'),
    __param(0, (0, common_1.Param)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getCompanyHistory", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get Cost Center history by cost center id.',
        type: [get_history_response_dto_1.GetHistoryResponseDTO],
    }),
    (0, common_1.Get)('/cost-center/:costCenterId/history'),
    __param(0, (0, common_1.Param)('costCenterId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getCostCenterHistory", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Upload evidence for company.',
        type: dtos_2.MessageResponseDto
    }),
    (0, common_1.Post)('/companies/:companyId/upload-evidence'),
    __param(0, (0, common_1.Param)('companyId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, dtos_3.UploadEvidenceRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "uploadEvidence", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Update multi natural account configuration.',
        type: dtos_2.MessageResponseDto
    }),
    (0, common_1.Post)('/companies/update-multi-natural-account'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_1.UpdateMultiNaturalAccountConfigRequestDto]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "updateMultiNaturalAccount", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.AFE_ADMINISTRATION),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get Evidence by compnay id.',
        type: [get_history_response_dto_1.GetHistoryResponseDTO],
    }),
    (0, common_1.Get)('/companies/:companyId/evidences'),
    __param(0, (0, common_1.Param)('companyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], FinanceAdminController.prototype, "getEvidences", null);
FinanceAdminController = __decorate([
    (0, swagger_1.ApiTags)('Finance Admin APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, common_1.Controller)('finance-admin'),
    __metadata("design:paramtypes", [finance_admin_service_1.FinanceAdminService,
        services_1.ReportService])
], FinanceAdminController);
exports.FinanceAdminController = FinanceAdminController;
//# sourceMappingURL=finance-admin.controller.js.map