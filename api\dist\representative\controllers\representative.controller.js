"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepresentativeController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const dtos_1 = require("../../shared/dtos");
const dtos_2 = require("../dtos");
const update_representative_request_dto_1 = require("../dtos/request/update-representative-request.dto");
const services_1 = require("../services");
let RepresentativeController = class RepresentativeController {
    constructor(representativeService) {
        this.representativeService = representativeService;
    }
    addRepresentative(request, addRepresentativeRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.representativeService.addRepresentative(addRepresentativeRequestDto, request.currentContext);
        });
    }
    getRepresentativeOfUser(request, filterQuery) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.representativeService.getRepresentativesOfUser(request.currentContext, filterQuery);
        });
    }
    deleteRepresentativeById(id, request) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.representativeService.deleteRepresentativeById(id, request.currentContext);
        });
    }
    updateRepresentative(request, updateRepresentativeRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.representativeService.updateUpcomingRepresentative(updateRepresentativeRequestDto, request.currentContext);
        });
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add representative for an user',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.AddRepresentativeRequestDto]),
    __metadata("design:returntype", Promise)
], RepresentativeController.prototype, "addRepresentative", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get representative of an user',
        type: [dtos_2.GetRepresentativeResponseDto],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'representativeFor',
        type: String,
        description: 'Get representative list for specific user. (Only For Admin)',
        required: false,
        allowEmptyValue: true,
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], RepresentativeController.prototype, "getRepresentativeOfUser", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Delete representative by id',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], RepresentativeController.prototype, "deleteRepresentativeById", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Add representative for an user',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Put)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_representative_request_dto_1.UpdateRepresentativeRequestDto]),
    __metadata("design:returntype", Promise)
], RepresentativeController.prototype, "updateRepresentative", null);
RepresentativeController = __decorate([
    (0, swagger_1.ApiTags)('Representatives APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('representatives'),
    __metadata("design:paramtypes", [services_1.RepresentativeService])
], RepresentativeController);
exports.RepresentativeController = RepresentativeController;
//# sourceMappingURL=representative.controller.js.map