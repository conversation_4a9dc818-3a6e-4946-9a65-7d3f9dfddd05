import { Inject, Injectable } from '@nestjs/common';
import * as msal from '@azure/msal-node';
import { AxiosRequestHeaders } from 'axios';
import { MS_GRAPH_API } from '../constants';
import { ConfigService } from 'src/config/config.service';
import { HttpService } from '../services';
import { ADUserDetails, AzureADConfig } from '../types';

@Injectable()
export class MSGraphApiClient {
	private tokenRequest: { scopes: string[] };
	private azureADConfig: AzureADConfig;

	constructor(
		@Inject(MS_GRAPH_API.MS_GRAPH_API_PROVIDER)
		private readonly msGraphApiProvider: msal.ConfidentialClientApplication,
		@Inject(MS_GRAPH_API.MS_GRAPH_HTTP_SERVICE_PROVIDER)
		private readonly msGraphApiHttpService: HttpService,
		@Inject(ConfigService) private readonly configService: ConfigService,
	) {
		this.azureADConfig = this.configService.getAppConfig().azureAD;
		const { graphApiUrl } = this.azureADConfig;
		this.tokenRequest = {
			scopes: [`${graphApiUrl}/.default`],
		};
	}

	private async getToken(): Promise<msal.AuthenticationResult> {
		return await this.msGraphApiProvider.acquireTokenByClientCredential(this.tokenRequest);
	}

	private async getHeaders(): Promise<AxiosRequestHeaders> {
		const { accessToken } = await this.getToken();
		return { Authorization: `Bearer ${accessToken}`, ConsistencyLevel: 'eventual' };
	}

	public async searchUsers(searchText: string, orderBy: string, count: boolean): Promise<any> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$search="displayName:${searchText}" OR "mail:${searchText}" OR "userPrincipalName:${searchText}"&$orderby=${orderBy}&$count=${count}&$filter=onPremisesExtensionAttributes%2FextensionAttribute9%20eq%20'DP%20World%20Guest'%20OR%20userType%20eq%20'Member'`,
			);
		data.value = data.value.filter(d => !d?.userPrincipalName?.toLowerCase()?.endsWith('au.dpworld.com'));
		return data;
	}

	public async getUserDetailsInMsResponse(userId: string): Promise<any> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=id,givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${userId}' OR userPrincipalName eq '${userId}'`,
			);
		return data;
	}

	public async getUsersDetailsFromAdInMsResponse(ids: string): Promise<any> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`,
			);
		return data;
	}

	public async getUserDetails(userId: string): Promise<ADUserDetails> {
		const data = await this.getUserDetailsInMsResponse(userId);
		return data?.value[data.value.length - 1] || null;
	}

	public async getUserDetailsByEmail(email: string): Promise<ADUserDetails> {
		const headers = await this.getHeaders();
		const { data } = await this.msGraphApiHttpService
			.withHeaders(headers)
			.get(
				`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType,displayName&$filter=mail eq '${email}'`,
			);
		return data?.value[data.value.length - 1] || null;
	}

	public async getUsersDetailsFromAd(userIds: string[]): Promise<ADUserDetails[]> {
		const headers = await this.getHeaders();
		const ids = userIds.map(id => `'${id}'`).join(',');
		const results: ADUserDetails[] = [];
		let nextLink:
			| string
			| null = `/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`;
		while (nextLink) {
			const response = await this.msGraphApiHttpService
				.withHeaders(headers)
				.get(
					`/users/?$select=givenName,jobTitle,surname,mail,userPrincipalName,userType&$filter=mail in (${ids}) OR userPrincipalName in (${ids})`,
				);
			const { data } = response;
			results.push(...data.value);
			nextLink =
				data['@odata.nextLink']?.replace(
					`${this.azureADConfig.graphApiUrl}/${this.azureADConfig.graphApiVersion}`,
					'',
				) || null;
		}
		return results;
	}

	public async getUsersDetails(ids: string[]): Promise<ADUserDetails[]> {
		const BATCH_SIZE = 6;
		const numberOfBatches = Math.ceil(ids.length / BATCH_SIZE);
		const result: ADUserDetails[] = [];
		// Loop through batches
		for (let batchIndex = 0; batchIndex < numberOfBatches; batchIndex++) {
			// Get the current batch
			const start = batchIndex * BATCH_SIZE;
			const end = start + BATCH_SIZE;
			const currentBatch = ids.slice(start, end);
			const usersDetails = await this.getUsersDetailsFromAd(currentBatch);
			result.push(...usersDetails);
		}
		return result;
	}
}
