"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttachmentController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const dtos_1 = require("../dtos");
const attachment_service_1 = require("../services/attachment.service");
let AttachmentController = class AttachmentController {
    constructor(attachmentService) {
        this.attachmentService = attachmentService;
    }
    getDraftsByIdAndActiveUser(fileId, taskId, request, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = yield this.attachmentService.getContentByFileId(fileId, request.currentContext, taskId);
            if (data === null || data === void 0 ? void 0 : data.attachment_name) {
                data.attachment_name = data.attachment_name.replace(/[^\w\s-]/g, '');
            }
            res.status(200);
            res.setHeader('Content-Type', data.attachment_content_type);
            res.setHeader('Content-Disposition', 'attachment; filename=' + data.attachment_name);
            const buffer = Buffer.from(data.contents.data);
            res.write(buffer);
            res.end();
        });
    }
    getProposalAttachmentMetaData(request, afeProposalId, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.attachmentService.getProposalAttachmentMetaData(afeProposalId, request.currentContext, taskId);
        });
    }
    getAllApproversAttachmentMetaData(request, afeProposalId, taskId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.attachmentService.getAllApproversAttachmentMetaData(afeProposalId, request.currentContext, taskId);
        });
    }
    getApproverAttachmentMetaData(afeProposalId) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.attachmentService.getApproverAttachmentMetaData(afeProposalId);
        });
    }
};
__decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get document content by id.',
        type: Buffer,
    }),
    (0, common_1.Get)('content/:fileId'),
    __param(0, (0, common_1.Param)('fileId')),
    __param(1, (0, common_1.Query)('taskId')),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Object, Object]),
    __metadata("design:returntype", Promise)
], AttachmentController.prototype, "getDraftsByIdAndActiveUser", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get meta data of Afe proposal attachments',
        type: [dtos_1.AttachmentContentResponseDto],
    }),
    (0, common_1.Get)(':afeProposalId/meta-data'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], AttachmentController.prototype, "getProposalAttachmentMetaData", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all approvers attachments meta data of Afe proposal',
        type: [dtos_1.AttachmentContentResponseDto],
    }),
    (0, common_1.Get)('/approver/:afeProposalId/all-meta-data'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('afeProposalId')),
    __param(2, (0, common_1.Query)('taskId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], AttachmentController.prototype, "getAllApproversAttachmentMetaData", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get meta data of Afe approver attachments',
        type: [dtos_1.AttachmentContentResponseDto],
    }),
    (0, common_1.Get)('/approver/:approverId/meta-data'),
    __param(0, (0, common_1.Param)('approverId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AttachmentController.prototype, "getApproverAttachmentMetaData", null);
AttachmentController = __decorate([
    (0, swagger_1.ApiTags)('Attachment API'),
    (0, common_1.Controller)('attachment'),
    __metadata("design:paramtypes", [attachment_service_1.AttachmentService])
], AttachmentController);
exports.AttachmentController = AttachmentController;
//# sourceMappingURL=attachment.controller.js.map