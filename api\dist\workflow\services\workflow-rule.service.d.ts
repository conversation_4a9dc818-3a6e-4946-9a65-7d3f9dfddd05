import { Pagination } from 'src/core/pagination';
import { MessageResponseDto } from 'src/shared/dtos';
import { CurrentContext } from 'src/shared/types';
import { CreateWorkflowRuleRequestDto, CreateWorkflowRuleResponseDto, GetWorkflowRuleResponseDto, UpdateWorkflowRuleRequestDto } from '../dtos';
import { WorkflowMasterStepRepository, WorkflowRuleRepository } from '../repositories';
export declare class WorkflowRuleService {
    private readonly workflowRuleRepository;
    private readonly workflowMasterStepRepository;
    constructor(workflowRuleRepository: WorkflowRuleRepository, workflowMasterStepRepository: WorkflowMasterStepRepository);
    createWorkflowRule(createWorkflowRuleRequestDto: CreateWorkflowRuleRequestDto, currentContext: CurrentContext): Promise<CreateWorkflowRuleResponseDto>;
    deleteWorkflowRuleById(ruleId: number, currentContext: CurrentContext): Promise<MessageResponseDto>;
    updateWorkflowRule(updateWorkflowRuleRequestDto: UpdateWorkflowRuleRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getWorkflowRuleById(ruleId: number): Promise<GetWorkflowRuleResponseDto>;
    getWorkflowRulesList(page?: number, limit?: number, searchTerm?: string, noLimit?: boolean): Promise<Pagination<GetWorkflowRuleResponseDto>>;
}
