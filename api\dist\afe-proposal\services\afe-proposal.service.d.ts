import { DraftAfeRepository } from 'src/afe-draft/repositories/draft-afe-repository';
import { ConfigService } from 'src/config/config.service';
import { CurrencyConversionResponseDto } from 'src/finance/dtos';
import { FinanceService } from 'src/finance/services';
import { NotificationRepository } from 'src/notification/repositories';
import { QueueLogRepository } from 'src/queue/repositories';
import { AdminApiClient, AttachmentApiClient, HistoryApiClient, MSGraphApiClient, RequestApiClient, TaskApiClient } from 'src/shared/clients';
import { TOGGLE_ON_OFF } from 'src/shared/enums';
import { DatabaseHelper } from 'src/shared/helpers';
import { SharedAttachmentService, SharedNotificationService } from 'src/shared/services';
import { ADUserDetails, CurrentContext } from 'src/shared/types';
import { AfeProposalValidator } from 'src/shared/validators';
import { TaskService } from 'src/task/services';
import { AfeApproversStepsResponseDto } from 'src/workflow/dtos';
import { WorkflowService } from 'src/workflow/services';
import { SubmitAfeProposalRequestDto, CreateAfeProposalResposeDto, ResubmitAfeProposalRequestDto, CreateAfeProposalDataDto, UpdateAfeDetailRequestDTO, WithdrawAfeProposalRequestDto, AddNewReadersRequestDto, SendBackAfeProposalRequestDto, ReopenAfeProposalRequestDto, UploadEvidenceRequestDto, UpdateApproverUserRequestDTO } from '../dtos';
import { AfeProposal } from '../models';
import { AfeProposalAmountSplitRepository, AfeProposalApproverRepository, AfeProposalLimitDeductionRepository, AfeProposalRepository } from '../repositories';
export declare class AfeProposalService {
    private readonly afeProposalRepository;
    private readonly databaseHelper;
    private readonly draftAfeRepository;
    private readonly workflowService;
    private readonly afeProposalAmountSplitRepository;
    private readonly afeProposalApproverRepository;
    private readonly attachmentApiClient;
    private readonly adminApiClient;
    private readonly requestApiClient;
    private readonly configService;
    private readonly historyApiClient;
    private readonly afeProposalLimitDeductionRepository;
    private readonly taskService;
    private readonly sharedAttachmentService;
    private readonly taskApiClient;
    private readonly financeService;
    private readonly notificationRepository;
    private readonly mSGraphApiClient;
    private readonly afeProposalValidator;
    private readonly queueLogRepository;
    private readonly sharedNotificationService;
    constructor(afeProposalRepository: AfeProposalRepository, databaseHelper: DatabaseHelper, draftAfeRepository: DraftAfeRepository, workflowService: WorkflowService, afeProposalAmountSplitRepository: AfeProposalAmountSplitRepository, afeProposalApproverRepository: AfeProposalApproverRepository, attachmentApiClient: AttachmentApiClient, adminApiClient: AdminApiClient, requestApiClient: RequestApiClient, configService: ConfigService, historyApiClient: HistoryApiClient, afeProposalLimitDeductionRepository: AfeProposalLimitDeductionRepository, taskService: TaskService, sharedAttachmentService: SharedAttachmentService, taskApiClient: TaskApiClient, financeService: FinanceService, notificationRepository: NotificationRepository, mSGraphApiClient: MSGraphApiClient, afeProposalValidator: AfeProposalValidator, queueLogRepository: QueueLogRepository, sharedNotificationService: SharedNotificationService);
    submitAfeProposal(submitAfeProposalRequestDto: SubmitAfeProposalRequestDto, currentContext: CurrentContext): Promise<CreateAfeProposalResposeDto>;
    resubmitAfeProposal(resubmitAfeProposalRequestDto: ResubmitAfeProposalRequestDto, currentContext: CurrentContext): Promise<CreateAfeProposalResposeDto>;
    private areCostCentersValid;
    private supplementalAfeValidation;
    createAfeProposal(createAfeProposalDto: CreateAfeProposalDataDto, projectReferenceNumber: string, currentContext: CurrentContext, subscribers: string[], submitterInfo: ADUserDetails, currencyDetail: CurrencyConversionResponseDto, parentAfeId?: number, version?: number, createdOn?: Date): Promise<{
        afeProposal: AfeProposal;
        steps: AfeApproversStepsResponseDto[];
    }>;
    private getUsersEmailByRoleAndEntityId;
    private createNotificationOnAfeSubmission;
    private createNotificationOnAfeResubmission;
    private createLimitDeductionEntries;
    private computeLastestApproversList;
    private isConflictExistInWorkflowSteps;
    private createProjectReferenceNumber;
    private createCostSplitPayload;
    private createStepUniqueKey;
    private getCountOfApproverStepSkip;
    private saveAfeApproversList;
    private getAfeDetailUrl;
    private sendAfeCreationNotificationToSubmitter;
    private sendAfeCreationNotificationToOthers;
    private moveAttachmentsFromSourceToDestination;
    private copyHistoryFromOriginalAfeToResubmitAfe;
    toggleAfeNotificationSubscription(afeProposalId: number, toggleValue: TOGGLE_ON_OFF, currentContext: CurrentContext): Promise<void>;
    updateProposalDetail(updateAfeDetailRequestDTO: UpdateAfeDetailRequestDTO, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    addNewReader(proposalId: number, addNewReadersRequestDto: AddNewReadersRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    uploadEvidence(proposalId: number, evidencesRequestDto: UploadEvidenceRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    withdrawAfeProposal(withdrawAfeProposalRequestDto: WithdrawAfeProposalRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    getAfeProposalAmountSplitData(afePropsal: AfeProposal): Promise<any>;
    private calculateDeltaAmounts;
    updateParentAfeSupplementalsDeltaAmounts(parentId: number): Promise<{
        message: string;
    }>;
    updateDeltaAmountPayloadForSupplemental(previousApprovedAfeId: number, currentAfeId: number, currentContext: CurrentContext): Promise<void>;
    sendBackAfeProposal(sendBackAfeProposalRequestDto: SendBackAfeProposalRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    reopenAfeProposal(reopenAfeProposalRequestDto: ReopenAfeProposalRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    revertSentBackAction(requestDto: ReopenAfeProposalRequestDto, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
    updateApproverUser(proposalId: number, updateApproverUserRequestDTO: UpdateApproverUserRequestDTO, currentContext: CurrentContext): Promise<{
        message: string;
    }>;
}
