import { AdminApiClient } from 'src/shared/clients';
import { EntityService } from 'src/shared/services';
import { AfeBudgetTypeResponseDto, AfeNatureTypeResponseDto, AfeRequestTypeResponseDto, AfeSubTypeResponseDto, AfeTypeResponseDto, GetLocationByEntitiesRequestDto, LocationsResponseDto, LocationsWithEntityIdResponseDto, ParallelIdentifierResponseDto, QuestionResponseDto } from '../dtos';
import { AfeBudgetTypeMappingRepository, AfeBudgetTypeRepository, AfeNatureTypeMappingRepository, AfeRequestTypeRepository, AfeSubTypeRepository, AfeTypeRepository, QuestionRepository } from '../repositories';
import { ParallelIdentifierRepository } from '../repositories/parallel-identifier.repository';
import { QUESTION_TYPE } from 'src/shared/enums';
import { LocationRepository } from 'src/afe-proposal/repositories';
export declare class AfeConfigService {
    private readonly adminApiClient;
    private readonly entityService;
    private readonly afeRequestTypeRepository;
    private readonly parallelIdentifierRepository;
    private readonly afeNatureTypeMappingRepository;
    private readonly afeTypeRepository;
    private readonly afeBudgetTypeRepository;
    private readonly questionRepository;
    private readonly afeBudgetTypeMappingRepository;
    private readonly afeSubTypeRepository;
    private readonly locationRepository;
    constructor(adminApiClient: AdminApiClient, entityService: EntityService, afeRequestTypeRepository: AfeRequestTypeRepository, parallelIdentifierRepository: ParallelIdentifierRepository, afeNatureTypeMappingRepository: AfeNatureTypeMappingRepository, afeTypeRepository: AfeTypeRepository, afeBudgetTypeRepository: AfeBudgetTypeRepository, questionRepository: QuestionRepository, afeBudgetTypeMappingRepository: AfeBudgetTypeMappingRepository, afeSubTypeRepository: AfeSubTypeRepository, locationRepository: LocationRepository);
    getNavBarByPosition(position: string): Promise<Record<string, any>>;
    getAfeRequestTypes(): Promise<AfeRequestTypeResponseDto[]>;
    getParallelIdentifiers(): Promise<ParallelIdentifierResponseDto[]>;
    getAfeNatureTypesByRequestTypeAndLocation(requestTypeId: number, locationId: number): Promise<AfeNatureTypeResponseDto[]>;
    getAfeTypesByRequestType(requestTypeId: number): Promise<AfeTypeResponseDto[]>;
    getBudgetTypes(requestTypeId: number, typeId: number): Promise<AfeBudgetTypeResponseDto[]>;
    getQuestionsByRequestTypeAndLocation(requestTypeId: number, locationId: number, questionType?: QUESTION_TYPE): Promise<QuestionResponseDto[]>;
    getAfeSubTypes(typeId?: number, entityId?: number): Promise<AfeSubTypeResponseDto[]>;
    getAllAfeTypes(): Promise<AfeTypeResponseDto[]>;
    getLocations(entityId: number, requestTypeId: number): Promise<LocationsResponseDto[]>;
    getLocationsByEntityIds(getLocationByEntitiesRequestDto: GetLocationByEntitiesRequestDto): Promise<LocationsWithEntityIdResponseDto[]>;
}
