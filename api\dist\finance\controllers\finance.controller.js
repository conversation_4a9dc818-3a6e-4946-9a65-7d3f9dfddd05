"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const enums_1 = require("../../shared/enums");
const dtos_1 = require("../dtos");
const services_1 = require("../services");
let FinanceController = class FinanceController {
    constructor(financeService) {
        this.financeService = financeService;
    }
    isCompanyExistForBusinessEntity(entityId) {
        return this.financeService.checkCompanyCodeExistsForBusinessEntity(entityId);
    }
    getNaturalAccountNumbersByRequestType(requestTypeId, entityId) {
        return this.financeService.getNaturalAccountNumbersByRequestType(requestTypeId, entityId);
    }
    getAnalysisCodesByRequestType(requestTypeId, entityId) {
        return this.financeService.getAnalysisCodesByRequestType(requestTypeId, entityId);
    }
    getCostCentersWithCompanyCodeByEntity(entityId) {
        return this.financeService.getCostCentersWithCompanyCodeByEntity(entityId);
    }
    getCurrencyConversionRateToPrimary(currencyType) {
        return this.financeService.getCurrencyConversionRateToPrimary(currencyType);
    }
    getCurrencyTypeForEntity(entityId) {
        return this.financeService.getCurrencyTypeForEntity(entityId);
    }
    getCompanyCodeList(excludedCode) {
        return this.financeService.getCompanyCodeListExcluding(excludedCode);
    }
    getCostCenterByEntityIds(entityIds) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.financeService.getCostCenterByEntityIds(entityIds);
        });
    }
    getAllCostCenters() {
        return this.financeService.getAllCostCenters();
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Check if company exists for the business entity or not.',
        type: Boolean,
    }),
    (0, common_1.Get)('entity/:entityId/company-codes/exists'),
    __param(0, (0, common_1.Param)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], FinanceController.prototype, "isCompanyExistForBusinessEntity", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get natural account numbers by request type.',
        type: [dtos_1.NaturalAccountResponseDto],
    }),
    (0, common_1.Get)('request-type/:requestTypeId/natural-account-numbers'),
    (0, swagger_1.ApiQuery)({ name: 'entityId', required: true }),
    __param(0, (0, common_1.Param)('requestTypeId')),
    __param(1, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", void 0)
], FinanceController.prototype, "getNaturalAccountNumbersByRequestType", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get analysis codes by request type.',
        type: [dtos_1.AnalysisCodeResponseDto],
    }),
    (0, common_1.Get)('request-type/:requestTypeId/analysis-codes'),
    (0, swagger_1.ApiQuery)({ name: 'entityId', required: true }),
    __param(0, (0, common_1.Param)('requestTypeId')),
    __param(1, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", void 0)
], FinanceController.prototype, "getAnalysisCodesByRequestType", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all the cost centers with its company code by entity id. ',
        type: [dtos_1.CostCenterResponseDto],
    }),
    (0, common_1.Get)('entity/:entityId/cost-centers'),
    __param(0, (0, common_1.Param)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], FinanceController.prototype, "getCostCentersWithCompanyCodeByEntity", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get given currency conversion rate to primary currency',
        type: dtos_1.CurrencyConversionResponseDto,
    }),
    (0, common_1.Get)('currency/:currencyType/conversion-rate'),
    __param(0, (0, common_1.Param)('currencyType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], FinanceController.prototype, "getCurrencyConversionRateToPrimary", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get currency type for an entity.',
        type: dtos_1.CurrencyConversionResponseDto,
    }),
    (0, common_1.Get)('entity/:entityId/currency'),
    __param(0, (0, common_1.Param)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], FinanceController.prototype, "getCurrencyTypeForEntity", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Return the active company code details for an business entity.',
        type: [dtos_1.CompanyCodeResponseDto],
    }),
    (0, common_1.Get)('get-company-code-excluding/:excludedCode'),
    __param(0, (0, common_1.Param)('excludedCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FinanceController.prototype, "getCompanyCodeList", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get cost center of business entity',
        type: dtos_1.CostCenterFilterResponseDto,
    }),
    (0, common_1.Post)('cost-center-by-entity-ids'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FinanceController.prototype, "getCostCenterByEntityIds", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all the cost centers. ',
        type: [dtos_1.AllCostCenterResponseDto],
    }),
    (0, common_1.Get)('get-all-cost-centers'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], FinanceController.prototype, "getAllCostCenters", null);
FinanceController = __decorate([
    (0, swagger_1.ApiTags)('Finance APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('finance'),
    __metadata("design:paramtypes", [services_1.FinanceService])
], FinanceController);
exports.FinanceController = FinanceController;
//# sourceMappingURL=finance.controller.js.map