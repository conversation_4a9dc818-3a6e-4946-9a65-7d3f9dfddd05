"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AfeConfigService = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../../shared/clients");
const helpers_1 = require("../../shared/helpers");
const services_1 = require("../../shared/services");
const dtos_1 = require("../dtos");
const repositories_1 = require("../repositories");
const parallel_identifier_repository_1 = require("../repositories/parallel-identifier.repository");
const enums_1 = require("../../shared/enums");
const repositories_2 = require("../../afe-proposal/repositories");
let AfeConfigService = class AfeConfigService {
    constructor(adminApiClient, entityService, afeRequestTypeRepository, parallelIdentifierRepository, afeNatureTypeMappingRepository, afeTypeRepository, afeBudgetTypeRepository, questionRepository, afeBudgetTypeMappingRepository, afeSubTypeRepository, locationRepository) {
        this.adminApiClient = adminApiClient;
        this.entityService = entityService;
        this.afeRequestTypeRepository = afeRequestTypeRepository;
        this.parallelIdentifierRepository = parallelIdentifierRepository;
        this.afeNatureTypeMappingRepository = afeNatureTypeMappingRepository;
        this.afeTypeRepository = afeTypeRepository;
        this.afeBudgetTypeRepository = afeBudgetTypeRepository;
        this.questionRepository = questionRepository;
        this.afeBudgetTypeMappingRepository = afeBudgetTypeMappingRepository;
        this.afeSubTypeRepository = afeSubTypeRepository;
        this.locationRepository = locationRepository;
    }
    getNavBarByPosition(position) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = yield this.adminApiClient.getNavBarByPosition(position);
            return data.map(d => (0, helpers_1.instanceToPlain)(new dtos_1.NavBarResponseDto(d)));
        });
    }
    getAfeRequestTypes() {
        return __awaiter(this, void 0, void 0, function* () {
            const types = yield this.afeRequestTypeRepository.getAllAfeRequestTypes();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AfeRequestTypeResponseDto, types);
        });
    }
    getParallelIdentifiers() {
        return __awaiter(this, void 0, void 0, function* () {
            const parallelIdentifiers = yield this.parallelIdentifierRepository.getAllParallelIdentifiers();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.ParallelIdentifierResponseDto, parallelIdentifiers);
        });
    }
    getAfeNatureTypesByRequestTypeAndLocation(requestTypeId, locationId) {
        return __awaiter(this, void 0, void 0, function* () {
            const natureTypes = yield this.afeNatureTypeMappingRepository.getAfeNatureTypesByRequestType(requestTypeId);
            const filteredNaturalTypes = yield this.entityService.filterItemsByEntityInclusionAndExclusion(locationId, natureTypes);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AfeNatureTypeResponseDto, filteredNaturalTypes.map(type => type.afeNatureType));
        });
    }
    getAfeTypesByRequestType(requestTypeId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.afeTypeRepository.getAllAfeTypesByRequestType(requestTypeId);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AfeTypeResponseDto, result);
        });
    }
    getBudgetTypes(requestTypeId, typeId) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const responseData = yield this.afeBudgetTypeMappingRepository.getBudgetTypeIdsByAfeRequestAndType(requestTypeId, typeId);
            if ((_a = responseData === null || responseData === void 0 ? void 0 : responseData.budgetTypeIds) === null || _a === void 0 ? void 0 : _a.length) {
                const result = yield this.afeBudgetTypeRepository.getBudgetTypesByIds(responseData.budgetTypeIds);
                return (0, helpers_1.multiObjectToInstance)(dtos_1.AfeBudgetTypeResponseDto, result);
            }
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AfeBudgetTypeResponseDto, []);
        });
    }
    getQuestionsByRequestTypeAndLocation(requestTypeId, locationId, questionType = enums_1.QUESTION_TYPE.PROCUREMENT_QUESTION) {
        return __awaiter(this, void 0, void 0, function* () {
            const questions = yield this.questionRepository.getQuestionsByRequestType(requestTypeId, questionType);
            const locationIdParents = yield this.adminApiClient.getParentIdsOfEntity(locationId);
            const includedQuestions = questions.filter(entity => entity.includedEntityIds.some(id => locationIdParents.includes(id)));
            return (0, helpers_1.multiObjectToInstance)(dtos_1.QuestionResponseDto, includedQuestions);
        });
    }
    getAfeSubTypes(typeId, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            let afeSubTypes = typeId ? yield this.afeSubTypeRepository.getAfeSubTypesByAfeTypeId(typeId) : yield this.afeSubTypeRepository.getAllAfeSubTypes();
            if ((afeSubTypes === null || afeSubTypes === void 0 ? void 0 : afeSubTypes.length) && entityId) {
                afeSubTypes = yield this.entityService.filterItemsByEntityInclusionAndExclusion(entityId, afeSubTypes);
            }
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AfeSubTypeResponseDto, afeSubTypes);
        });
    }
    getAllAfeTypes() {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.afeTypeRepository.getAllAfeTypes();
            return (0, helpers_1.multiObjectToInstance)(dtos_1.AfeTypeResponseDto, result);
        });
    }
    getLocations(entityId, requestTypeId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.locationRepository.getLocationByEntityId(entityId, requestTypeId);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.LocationsResponseDto, result);
        });
    }
    getLocationsByEntityIds(getLocationByEntitiesRequestDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityIds } = getLocationByEntitiesRequestDto;
            const result = yield this.locationRepository.getLocationsByEntityIds(entityIds);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.LocationsWithEntityIdResponseDto, result);
        });
    }
};
AfeConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        services_1.EntityService,
        repositories_1.AfeRequestTypeRepository,
        parallel_identifier_repository_1.ParallelIdentifierRepository,
        repositories_1.AfeNatureTypeMappingRepository,
        repositories_1.AfeTypeRepository,
        repositories_1.AfeBudgetTypeRepository,
        repositories_1.QuestionRepository,
        repositories_1.AfeBudgetTypeMappingRepository,
        repositories_1.AfeSubTypeRepository,
        repositories_2.LocationRepository])
], AfeConfigService);
exports.AfeConfigService = AfeConfigService;
//# sourceMappingURL=afe-config.service.js.map